/// API endpoints and configuration constants
///
/// Note: Each endpoint includes its specific API version (v1, v2, v3, etc.)
/// This allows flexibility when different endpoints use different API versions
abstract class ApiEndpoints {
  // Authentication endpoints
  // Check if login is allowed, if its social its return token
  static const String allowsLogin = '/api/v1/allows-login';
  static const String getUser = '/api/v1/get-app-user';
  static const String login = '/api/v1/sign-in';
  static const String forgotPassword = '/api/account/reset-password';
  static const String refreshToken = '/api/v1/refresh-token';
  static const String hearAboutMaisours = '/api/hear-about-maisours';
  static const String signUp = '/api/v1/sign-up';
  static const String getPublicIp = 'https://api64.ipify.org';
  static const String resendActivationEmail = '/api/v1/resend-email';
  static const String changePassword = '/api/v1/changePassword';
  static const String logout = '/api/v1/logout';
  static const String getUserDocuments = '/api/v2/get-user-documents';
  static const String saveUserDocument = '/api/v1/save-user-document';
  static const String onboardingStatus = '/api/v2/isProfileComplete';
  static const String idWiseDecisionLog = '/api/v1/id-wise-decisions-log';
  static const String saveIdWiseLogs = '/api/v1/save-id-wise-logs';
  static const String industryTypes = '/api/v1/industry-types';
  static const String amlCountries = '/api/aml-countries';
  static const String addEmploymentDetail = '/api/v2/addEmploymentDetail';
  static const String saveTaxResidencyInformation =
      '/api/v1/save-tax-residency-information';
  static const String countryOfResidency = '/api/v1/country-of-residency';
  static const String addPhoneNumber = '/api/v1/add-phone-number';
  static const String registerVerifyOTP = '/api/v1/registerVerifyOTP';
  static const String generateOTP = '/api/v2/generateOTP';
  static const String verifyOTP = '/api/v1/verifyOTP';
  static const String updateFcmToken = '/api/v1/add-firebase-device-token';

  // Properties endpoints
  /// Endpoint expects a propertyType as a path parameter
  static const String propertiesList = '/api/v1/properties-list';

  /// Endpoint expects a propertyId as a path parameter
  static const String propertyDetails = '/api/v1/property';

  // Document content endpoints
  static const String getOtherDocumentContent =
      '/api/v1/get-other-document-content';

  // Investment endpoints
  static const String addInvestment = '/api/v1/add-investment';
  static const String cancelInvestment = '/api/v1/cancel-investment';

  // Ownership endpoints
  static const String getOwnershipData = '/api/v1/get-ownership-data';
  static const String getOwnershipDetails = '/api/v1/get-ownership-details';

  // Transactions endpoints
  static const String getBankTransactions =
      '/api/v1/get-app-user-bank-transactions';

  // Bank accounts endpoints
  static const String getUserBankAccounts = '/api/v1/get-user-bank-accounts';
  static const String saveBankAccount = '/api/v1/save-bank-account';
  static const String setPrimaryBankAccount =
      '/api/v1/set-primary-bank-account';
  static const String updateBankAccountStatus =
      '/api/v1/update-bank-account-status';
  static const String deleteBankAccount = '/api/v1/delete-bank-account';

  // Deposit endpoints
  static const String deposit = '/api/v1/deposit';
  static const String getConstAccountData = '/api/v1/getConstAccountData';
  static const String createOrder = '/api/v1/createOrder';

  // Withdrawal endpoints
  static const String withdraw = '/api/v1/add-withDrawal';

  // Language
  static const String updateLanguage = '/api/v1/update-lang-key';

  // Currency
  static const String updateCurrency = '/api/v1/update-currency-code';

  // User data update
  static const String updateUserData = '/api/v1/update-user-data';

  // FAQ endpoints
  static const String faqsByLocale = '/api/v1/faqs/by-locale';

  // Contact Us endpoints
  static const String contactUs = '/api/v1/contact-us';
  static const String verifyAccountClosure = '/api/v1/verify-account-closure';
  static const String closeAccount = '/api/v1/account-closure-new';
}

/// API configuration constants
abstract class ApiConfig {
  // API Headers
  static const String contentType = 'application/json';
  static const String accept = 'application/json';
  static const String authorization = 'Authorization';
  static const String bearerPrefix = 'Bearer ';

  // API Timeouts (in seconds)
  static const int connectTimeout = 60;
  static const int receiveTimeout = 60;
  static const int sendTimeout = 60;
}
