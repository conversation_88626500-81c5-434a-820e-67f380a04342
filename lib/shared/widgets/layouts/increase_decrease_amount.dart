import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class IncreaseDecreaseAmount extends StatefulWidget {
  final int initialAmount;
  final int minAmount;
  final int maxAmount;
  final List<int> options;
  final bool isPercentage;
  final Function(int) onAmountChanged;
  final Function(int) onOptionSelected;
  final Widget child;

  const IncreaseDecreaseAmount({
    super.key,
    required this.initialAmount,
    required this.minAmount,
    required this.maxAmount,
    required this.options,
    this.isPercentage = false,
    required this.onAmountChanged,
    required this.onOptionSelected,
    required this.child,
  });

  @override
  State<IncreaseDecreaseAmount> createState() => _IncreaseDecreaseAmountState();
}

class _IncreaseDecreaseAmountState extends State<IncreaseDecreaseAmount> {
  int amount = 0;
  final int _step = 100;

  @override
  void initState() {
    super.initState();
    amount = widget.initialAmount;
  }

  void _decreaseAmount() {
    if (amount > widget.minAmount) {
      int newAmount = amount - _step;
      if (newAmount < widget.minAmount) {
        newAmount = widget.minAmount;
      } else {
        amount = newAmount;
      }
      widget.onAmountChanged(newAmount);
    }
  }

  void _increaseAmount() {
    if (amount < widget.maxAmount) {
      int newAmount = amount + _step;
      if (newAmount > widget.maxAmount) {
        newAmount = widget.maxAmount;
      } else {
        amount = newAmount;
      }
      widget.onAmountChanged(newAmount);
    }
  }

  @override
  void didUpdateWidget(covariant IncreaseDecreaseAmount oldWidget) {
    amount = widget.initialAmount;
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 56.w,
                height: 40.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  border: Border.all(color: AppColors.gray.shade200),
                ),
                child: Icon(
                  Icons.remove,
                  color: AppColors.dark.shade900,
                  size: 24.w,
                ),
              ).onTap(() {
                _decreaseAmount();
              }),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.dirham.image(width: 30.w, height: 30.w),
                    4.w.widthBox,
                    Text(
                      amount.toString(),
                      style: AppTextStyles.text24.bold.dark900,
                    ),
                  ],
                ),
              ),
              Container(
                width: 56.w,
                height: 40.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  border: Border.all(color: AppColors.gray.shade200),
                ),
                child: Icon(
                  Icons.add,
                  color: AppColors.dark.shade900,
                  size: 24.w,
                ),
              ).onTap(() {
                _increaseAmount();
              }),
            ],
          ),
          16.w.heightBox,
          Center(
            child: Wrap(
              spacing: 6.w,
              children: widget.options
                  .map(
                    (int option) =>
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16.w,
                            vertical: 6.w,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            border: Border.all(color: AppColors.gray.shade200),
                          ),
                          child: Text(
                            widget.isPercentage
                                ? '$option%'
                                : '+${Currency.aed.formatCurrency(option, decimalDigits: 0)}',
                            style: AppTextStyles.text10.medium.dark900,
                          ),
                        ).onTap(() {
                          widget.onOptionSelected(option);
                        }),
                  )
                  .toList(),
            ),
          ),
          16.w.heightBox,
          Divider(color: AppColors.gray.shade200),
          16.w.heightBox,
          widget.child,
        ],
      ),
    );
  }
}
