import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_colors.dart';

class GradientBackground extends StatelessWidget {
  const GradientBackground({
    super.key,
    this.startColor = AppColors.white,
    this.endColor = AppColors.lightBlue,
    required this.child,
    this.begin = Alignment.topCenter,
    this.end = Alignment.bottomCenter,
  });

  final Color startColor;
  final Color endColor;
  final Widget child;
  final Alignment begin;
  final Alignment end;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: begin,
          end: end,
          colors: [startColor, endColor],
        ),
      ),
      child: child,
    );
  }
}
