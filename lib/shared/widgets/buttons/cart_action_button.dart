import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/gen/assets.gen.dart';

class CartActionButton extends StatelessWidget {
  const CartActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {},
      icon: Assets.icons.cart.svg(
        width: 24,
        height: 24,
        colorFilter: ColorFilter.mode(AppColors.dark.shade900, BlendMode.srcIn),
      ),
    );
  }
}
