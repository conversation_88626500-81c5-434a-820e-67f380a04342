import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';

enum ButtonType { filled, outlined, text }

class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;

  // Button style type
  final ButtonType type;

  // Dimensions
  final double? width;
  final double? height;

  // Text customization
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;

  // Icon
  final Widget? icon;
  final double? iconSpacing;

  // Button styling
  final Color? backgroundColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final double elevation;
  final Color? borderColor;
  final double borderWidth;

  final bool isLoading;
  final bool showLoader;

  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.type = ButtonType.filled,
    this.width,
    this.height,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.icon,
    this.iconSpacing,
    this.backgroundColor,
    this.borderRadius,
    this.padding,
    this.elevation = 0.0,
    this.borderColor,
    this.borderWidth = 1.0,
    this.isLoading = false,
    this.showLoader = true,
  });

  @override
  Widget build(BuildContext context) {
    final Color effectiveBg = isLoading
        ? AppColors.gray.shade400
        : (backgroundColor ?? AppColors.primary);

    final Color effectiveTextColor = isLoading
        ? type == ButtonType.filled
              ? AppColors.white.alphaPercent(60)
              : AppColors.gray.alphaPercent(60)
        : (textColor ??
              (type == ButtonType.filled ? AppColors.white : AppColors.dark));

    final Color effectiveBorderColor = borderColor ?? AppColors.dark;

    final Size size = Size(width ?? double.infinity, height ?? 42.w);

    final EdgeInsetsGeometry effectivePadding =
        padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w);

    final double effectiveBorderRadius = borderRadius ?? 30.r;

    final double spacing = iconSpacing ?? 8.w;

    final ButtonStyle style;

    final VoidCallback effectiveOnPressed = isLoading ? () {} : onPressed;

    switch (type) {
      case ButtonType.filled:
        style = ElevatedButton.styleFrom(
          elevation: elevation,
          backgroundColor: effectiveBg,
          padding: effectivePadding,
          minimumSize: size,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(effectiveBorderRadius),
          ),
        );
        break;
      case ButtonType.outlined:
        style = OutlinedButton.styleFrom(
          side: BorderSide(color: effectiveBorderColor, width: borderWidth),
          padding: effectivePadding,
          minimumSize: size,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(effectiveBorderRadius),
          ),
        );
        break;
      case ButtonType.text:
        style = TextButton.styleFrom(
          // foregroundColor: effectiveTextColor,
          // padding: effectivePadding,
          // minimumSize: size,
          // shape: RoundedRectangleBorder(
          //   borderRadius: BorderRadius.circular(effectiveBorderRadius),
          // ),
        );
        break;
    }

    final Widget child = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading && showLoader)
          AppCircularLoader.small(color: effectiveTextColor)
        else if (icon != null)
          icon!,
        if ((isLoading && showLoader) || icon != null) spacing.widthBox,
        Flexible(
          child: Text(
            text,
            overflow: TextOverflow.ellipsis,
            style: AppTextStyles.text14.copyWith(
              color: effectiveTextColor,
              fontSize: fontSize ?? 14.sp,
              fontWeight: fontWeight ?? FontWeight.bold,
            ),
          ),
        ),
      ],
    );

    switch (type) {
      case ButtonType.filled:
        return ElevatedButton(
          style: style,
          onPressed: effectiveOnPressed,
          child: child,
        );
      case ButtonType.outlined:
        return OutlinedButton(
          style: style,
          onPressed: effectiveOnPressed,
          child: child,
        );
      case ButtonType.text:
        return TextButton(
          style: style,
          onPressed: effectiveOnPressed,
          child: child,
        );
    }
  }
}
