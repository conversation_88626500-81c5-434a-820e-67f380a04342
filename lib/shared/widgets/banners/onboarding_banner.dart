import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/widgets/banners/banner_header.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class OnboardingBanner extends StatelessWidget {
  const OnboardingBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: <PERSON><PERSON>eader(
        minExtent: 70,
        maxExtent: 80,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.gradientYellow, AppColors.gradientBlue],
            ),
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  LocaleKeys.verifyIdentityToStartInvestment.tr(),
                  style: AppTextStyles.text14.bold.dark900,
                ),
              ),
              AppButton(
                width: 100.w,
                type: ButtonType.filled,
                backgroundColor: AppColors.dark,
                fontSize: 13.sp,
                text: LocaleKeys.verifyNow.tr(),
                onPressed: () {
                  GoRouter.of(context).pushNamed(RouteName.onboarding.name);
                },
              ),
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    );
  }
}
