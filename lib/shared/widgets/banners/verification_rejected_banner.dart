import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/banners/banner_header.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class VerificationRejectedBanner extends StatelessWidget {
  const VerificationRejectedBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: Banner<PERSON>eader(
        minExtent: 90,
        maxExtent: 96,
        child: Container(
          color: AppColors.danger.shade100,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.w),
          alignment: Alignment.center,
          child: Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.accountVerificationFailed.tr(),
                      style: AppTextStyles.text12.bold.dark900,
                    ),
                    4.heightBox,
                    Text(
                      LocaleKeys.accountVerificationFailedDesc.tr(),
                      style: AppTextStyles.text10.medium.dark300,
                    ),
                  ],
                ),
              ),
              4.widthBox,
              AppButton(
                text: LocaleKeys.contactUs.tr(),
                onPressed: () {
                  GoRouter.of(context).pushNamed(RouteName.getHelp.name);
                },
                backgroundColor: AppColors.dark,
                width: 60.w,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
