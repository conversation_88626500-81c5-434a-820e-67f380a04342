import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/banners/banner_header.dart';

class VerificationPendingBanner extends StatelessWidget {
  const VerificationPendingBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: BannerHeader(
        minExtent: 70,
        maxExtent: 80,
        child: Container(
          color: AppColors.warning.alphaPercent(10),
          alignment: Alignment.center,
          child: Row(
            children: [
              Assets.icons.warning.svg(height: 40),
              16.widthBox,
              Flexible(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      LocaleKeys.accountVerificationPending.tr(),
                      style: AppTextStyles.text14.bold.dark900,
                    ),
                    4.heightBox,
                    Text(
                      LocaleKeys.accountVerificationPendingDesc.tr(),
                      style: AppTextStyles.text10.medium.dark300,
                    ),
                  ],
                ),
              ),
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    );
  }
}
