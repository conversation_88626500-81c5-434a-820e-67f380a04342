import 'package:flutter/material.dart';

class BannerHeader extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double? _minExtent;
  final double? _maxExtent;

  BannerHeader({required this.child, double? minExtent, double? maxExtent})
    : _minExtent = minExtent,
      _maxExtent = maxExtent;

  @override
  double get minExtent => _minExtent ?? 72;
  @override
  double get maxExtent => _maxExtent ?? 72;

  @override
  Widget build(context, shrinkOffset, overlapsContent) {
    return Material(elevation: overlapsContent ? 4 : 0, child: child);
  }

  @override
  bool shouldRebuild(covariant BannerHeader oldDelegate) => false;
}
