import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
// import 'package:maisour/shared/widgets/buttons/cart_action_button.dart';
// import 'package:maisour/shared/widgets/buttons/notification_action_button.dart';

class AppSliverAppBar extends StatelessWidget {
  const AppSliverAppBar({
    super.key,
    required this.title,
    this.floating = true,
    this.snap = true,
    this.pinned = false,
  });
  final String title;
  final bool floating;
  final bool snap;
  final bool pinned;

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      title: _buildTitle(title),
      floating: floating,
      snap: snap,
      pinned: pinned,
      //  actions: const [CartActionButton(), NotificationActionButton()],
    );
  }

  Widget _buildTitle(String title) {
    return Row(
      children: [
        Assets.images.logo.image(height: 26.h),
        8.w.widthBox,
        Text(title, style: AppTextStyles.text16.bold.dark900),
      ],
    );
  }
}
