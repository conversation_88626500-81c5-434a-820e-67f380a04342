import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/enums/social_media_platform.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';

/// Shared dialog for showing account closed message
/// Used across multiple authentication flows (Google, Apple, Email login)
class AccountClosedDialog {
  /// Shows account closed alert dialog
  ///
  /// [context] - BuildContext for showing dialog
  /// [onClose] - Optional callback when dialog is closed
  static void show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(LocaleKeys.yourAccountHasBeenClosed.tr()),
          content: Text(LocaleKeys.accountClosedMessage.tr()),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: Text(
                LocaleKeys.cancel.tr(),
                style: AppTextStyles.textStyle.danger,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                UrlLauncherService.launchSocialMedia(
                  SocialMediaPlatform.whatsapp,
                );
              },
              child: Text(
                LocaleKeys.goToWhatsapp.tr(),
                style: AppTextStyles.textStyle.primary,
              ),
            ),
          ],
        );
      },
    );
  }
}
