import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:phone_form_field/phone_form_field.dart';

class AppPhoneFormField extends StatelessWidget {
  const AppPhoneFormField({super.key, this.controller});
  final PhoneController? controller;

  @override
  Widget build(BuildContext context) {
    return PhoneFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: LocaleKeys.phoneNumber.tr(),
        labelStyle: AppTextStyles.text14.medium.dark300,
        errorStyle: AppTextStyles.text10.medium.danger,
        contentPadding: EdgeInsetsDirectional.symmetric(
          horizontal: 16.w,
          vertical: 14.w,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.gray.shade200),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.gray.shade200),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.primary.shade400),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.danger),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.danger),
        ),
      ),
      countrySelectorNavigator: CountrySelectorNavigator.page(),
      validator: PhoneValidator.compose([
        PhoneValidator.required(
          context,
          errorText: LocaleKeys.fieldRequired.tr(
            namedArgs: {'field': LocaleKeys.phoneNumber.tr()},
          ),
        ),
        PhoneValidator.validMobile(
          context,
          errorText: LocaleKeys.enterValidPhoneNumber.tr(),
        ),
      ]),
    );
  }
}
