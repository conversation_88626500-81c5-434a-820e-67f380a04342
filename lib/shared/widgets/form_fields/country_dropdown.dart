import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/modules/onboarding/providers/aml_countries_provider.dart';
import 'package:maisour/shared/models/country.dart';
import 'package:maisour/shared/widgets/form_fields/app_dropdown_field.dart';

class CountryDropdown extends ConsumerWidget {
  final Country? value;
  final void Function(Country?) onChanged;
  final String labelText;
  final String? Function(Country?)? validator;
  final String? helperText;

  const CountryDropdown({
    super.key,
    required this.value,
    required this.onChanged,
    required this.labelText,
    this.validator,
    this.helperText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final countriesAsync = ref.watch(amlCountriesProvider);

    return countriesAsync.when(
      data: (countries) => AppDropdownField<Country>(
        labelText: labelText,
        itemAsString: (item) => getCountryName(context, item),
        items: countries,
        showSearchBox: true,
        selectedItem: value,
        onChanged: onChanged,
        validator: validator,
        helperText: helperText,
      ),
      loading: () => AppDropdownField<Country>(
        labelText: labelText,
        itemAsString: (item) => item.country,
        items: const [],
      ),
      error: (error, stackTrace) => AppDropdownField<Country>(
        labelText: labelText,
        itemAsString: (item) => item.country,
        items: [],
        showSearchBox: true,
        selectedItem: value,
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }

  String getCountryName(BuildContext context, Country country) {
    if (context.locale.languageCode == 'ar') {
      return getCountryNameWithFlag(
        country.countryInArabic ?? country.country,
        country.newCountryCode,
      );
    } else {
      return getCountryNameWithFlag(country.country, country.newCountryCode);
    }
  }

  String getCountryNameWithFlag(String name, String? countryCode) {
    if (countryCode != null) {
      return '${getCountryFlag(countryCode)} $name';
    }
    return name;
  }

  String getCountryFlag(String countryCode) {
    // Ensure uppercase
    final code = countryCode.toUpperCase();
    // Convert A-Z to Regional Indicator Symbols (🇦–🇿)
    return String.fromCharCode(code.codeUnitAt(0) + 127397) +
        String.fromCharCode(code.codeUnitAt(1) + 127397);
  }
}
