import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/modules/onboarding/providers/industry_types_provider.dart';
import 'package:maisour/shared/models/industry_type.dart';
import 'package:maisour/shared/widgets/form_fields/app_dropdown_field.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class IndustryTypeDropdown extends ConsumerWidget {
  final IndustryType? value;
  final void Function(IndustryType?) onChanged;

  const IndustryTypeDropdown({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final industryTypesAsync = ref.watch(industryTypesProvider);

    return industryTypesAsync.when(
      data: (industryTypes) => AppDropdownField<IndustryType>(
        labelText: '${LocaleKeys.industry.tr()}*',
        itemAsString: (item) => context.locale.languageCode == 'ar'
            ? item.industryTypeInArabic
            : item.industryType,
        items: industryTypes,
        showSearchBox: false,
        selectedItem: value,
        onChanged: onChanged,
        validator: (val) =>
            AppValidators.required(LocaleKeys.industry.tr(), val?.industryType),
      ),
      loading: () => AppDropdownField<IndustryType>(
        labelText: '${LocaleKeys.industry.tr()}*',
        itemAsString: (item) => item.industryType,
        items: const [],
        showSearchBox: false,
        selectedItem: null,
        onChanged: null,
      ),
      error: (error, stackTrace) => AppDropdownField<IndustryType>(
        labelText: '${LocaleKeys.industry.tr()}*',
        itemAsString: (item) => item.industryType,
        items: [],
        showSearchBox: false,
        selectedItem: value,
        onChanged: onChanged,
        validator: (val) =>
            AppValidators.required(LocaleKeys.industry.tr(), val?.industryType),
      ),
    );
  }
}
