import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class AppDropdownField<T> extends StatelessWidget {
  const AppDropdownField({
    super.key,
    required this.labelText,
    this.helperText,
    required this.itemAsString,
    required this.items,
    this.onChanged,
    this.validator,
    this.selectedItem,
    this.showSearchBox = false,
  });

  final String labelText;
  final String? helperText;
  final List<T> items;
  final String Function(T) itemAsString;
  final Function(T?)? onChanged;
  final String? Function(T?)? validator;
  final T? selectedItem;
  final bool showSearchBox;

  @override
  Widget build(BuildContext context) {
    return DropdownSearch<T>(
      decoratorProps: DropDownDecoratorProps(
        baseStyle: AppTextStyles.text14.medium.dark900,
        decoration: InputDecoration(
          labelText: labelText,
          labelStyle: AppTextStyles.text14.medium.dark300,
          helperText: helperText,
          helperMaxLines: 2,
          errorMaxLines: 2,
          helperStyle: AppTextStyles.text10.medium.dark300,
          errorStyle: AppTextStyles.text10.medium.danger,
          contentPadding: EdgeInsetsDirectional.symmetric(
            horizontal: 16.w,
            vertical: 14.w,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(color: AppColors.gray.shade200),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(color: AppColors.gray.shade200),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(color: AppColors.primary.shade400),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(color: AppColors.danger),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4.r),
            borderSide: BorderSide(color: AppColors.danger),
          ),
        ),
      ),

      suffixProps: DropdownSuffixProps(
        dropdownButtonProps: DropdownButtonProps(
          iconClosed: Icon(
            Icons.keyboard_arrow_down,
            color: AppColors.dark.shade300,
          ),
          iconOpened: Icon(
            Icons.keyboard_arrow_up,
            color: AppColors.dark.shade300,
          ),
        ),
      ),

      popupProps: PopupProps.menu(
        fit: FlexFit.loose,
        showSearchBox: showSearchBox,
        searchFieldProps: TextFieldProps(
          cursorColor: AppColors.primary.shade200,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.r),
              borderSide: BorderSide(color: AppColors.gray.shade200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.r),
              borderSide: BorderSide(color: AppColors.gray.shade200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4.r),
              borderSide: BorderSide(color: AppColors.primary.shade400),
            ),
            contentPadding: EdgeInsetsDirectional.symmetric(
              horizontal: 16.w,
              vertical: 14.w,
            ),
            labelText: LocaleKeys.search.tr(),
            labelStyle: AppTextStyles.text14.medium.dark300,
          ),
        ),

        menuProps: MenuProps(borderRadius: BorderRadius.circular(12.r)),
        itemBuilder: (context, item, isDisabled, isSelected) {
          return Row(
            children: [
              Expanded(
                child: Text(
                  itemAsString(item),
                  style: AppTextStyles.text14.medium.dark400,
                ),
              ),
              // if (selectedItem != null &&
              //     (itemAsString(item) == itemAsString(selectedItem!)))
              //   Icon(Icons.check, color: AppColors.primary, size: 20.w),
            ],
          ).paddingSymmetric(horizontal: 16.w, vertical: 10.w);
        },
      ),
      itemAsString: itemAsString,
      compareFn: (item1, item2) {
        return item1 == item2;
      },
      items: (filter, loadProps) => items,
      onChanged: onChanged,
      validator: validator,
      selectedItem: selectedItem,
    );
  }
}
