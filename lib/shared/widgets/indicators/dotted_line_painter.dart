import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_colors.dart';

/// A custom painter that draws a dotted vertical line
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashHeight;
  final double dashSpace;

  DottedLinePainter({
    Color? color,
    this.strokeWidth = 1.0,
    this.dashHeight = 4.0,
    this.dashSpace = 4.0,
  }) : color = color ?? AppColors.gray.shade200;

  @override
  void paint(Canvas canvas, Size size) {
    double startY = 0;
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
