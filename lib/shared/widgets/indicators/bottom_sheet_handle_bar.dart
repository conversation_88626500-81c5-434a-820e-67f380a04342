import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';

class BottomSheetHandleBar extends StatelessWidget {
  const BottomSheetHandleBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 40.w,
      height: 4.h,
      decoration: BoxDecoration(
        color: AppColors.gray.shade200,
        borderRadius: BorderRadius.circular(10.r),
      ),
    );
  }
}
