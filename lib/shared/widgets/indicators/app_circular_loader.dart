import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';

/// Common circular loader widget that adapts to platform
///
/// Shows CupertinoActivityIndicator on iOS and CircularProgressIndicator on Android
/// Uses app's primary color and consistent sizing
///
/// Usage:
/// ```dart
/// AppCircularLoader() // Default size
/// AppCircularLoader(size: 30.w) // Custom size
/// AppCircularLoader(color: AppColors.white) // Custom color
/// ```
class AppCircularLoader extends StatelessWidget {
  /// Creates a platform-adaptive circular loader
  const AppCircularLoader({super.key, this.size, this.color, this.strokeWidth});

  /// Size of the loader container (height and width)
  /// Defaults to 40.w for consistent sizing
  final double? size;

  /// Color of the loader
  /// Defaults to AppColors.primary
  final Color? color;

  /// Stroke width for Android CircularProgressIndicator
  /// Defaults to 2.0
  final double? strokeWidth;

  @override
  Widget build(BuildContext context) {
    final loaderSize = size ?? 40.w;
    final loaderColor = color ?? AppColors.primary;
    final loaderStrokeWidth = strokeWidth ?? 2.0;

    return SizedBox(
      height: loaderSize,
      width: loaderSize,
      child: Center(
        child: MyPlatform.isIOS
            ? CupertinoActivityIndicator(
                radius: loaderSize * 0.375, // 15/40 ratio for consistent sizing
                color: loaderColor,
              )
            : CircularProgressIndicator(
                strokeWidth: loaderStrokeWidth,
                color: loaderColor,
              ),
      ),
    );
  }

  /// Small loader (20.w) - for buttons
  static Widget small({Color? color}) {
    return AppCircularLoader(size: 20.w, color: color, strokeWidth: 1.5);
  }

  /// Medium loader (32.w) - Default
  static Widget medium({Color? color}) {
    return AppCircularLoader(size: 32.w, color: color);
  }

  /// Large loader (48.w)
  static Widget large({Color? color}) {
    return AppCircularLoader(size: 48.w, color: color, strokeWidth: 3.0);
  }
}
