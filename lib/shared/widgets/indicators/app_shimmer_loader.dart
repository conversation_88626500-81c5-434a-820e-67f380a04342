import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';

/// Reusable shimmer loading widget for list items
///
/// Features:
/// - Dynamic item height
/// - Configurable scroll physics
/// - Customizable shimmer colors
/// - Responsive design with ScreenUtil
/// - Consistent with app design system
/// - Uses awesome_extensions applyShimmer() extension
///
/// Usage Examples:
/// ```dart
/// // Basic usage with default settings
/// AppShimmerLoader(
///   itemCount: 5,
///   itemHeight: 80.h,
/// )
///
/// // Custom shimmer with scroll physics
/// AppShimmerLoader(
///   itemCount: 10,
///   itemHeight: 120.h,
///   physics: const BouncingScrollPhysics(),
///   shimmerColor: AppColors.primary,
/// )
///
/// // No scroll (for single items)
/// AppShimmerLoader(
///   itemCount: 1,
///   itemHeight: 200.h,
///   physics: const NeverScrollableScrollPhysics(),
/// )
///
/// // Replace CircularProgressIndicator in FutureProvider
/// loading: () => AppShimmerLoader.single(height: 200.h),
///
/// // Card-style shimmer for property listings
/// loading: () => AppShimmerLoader.card(
///   itemCount: 3,
///   itemHeight: 120,
/// ),
///
/// // List-style shimmer for simple lists
/// loading: () => AppShimmerLoader.list(
///   itemCount: 5,
///   itemHeight: 60,
/// ),
/// ```
class AppShimmerLoader extends StatelessWidget {
  /// Creates a shimmer loading widget
  const AppShimmerLoader({
    super.key,
    required this.itemCount,
    required this.itemHeight,
    this.physics,
    this.padding,
    this.itemSpacing,
    this.shimmerColor,
    this.baseColor,
    this.borderRadius,
    this.itemPadding,
  });

  /// Number of shimmer items to display
  final int itemCount;

  /// Height of each shimmer item
  final double itemHeight;

  /// Scroll physics for the ListView
  /// Defaults to AlwaysScrollableScrollPhysics()
  final ScrollPhysics? physics;

  /// Padding around the entire ListView
  final EdgeInsetsGeometry? padding;

  /// Spacing between shimmer items
  /// Defaults to 12.h
  final double? itemSpacing;

  /// Color of the shimmer effect
  /// Defaults to AppColors.primary with 30% opacity
  final Color? shimmerColor;

  /// Base color of the shimmer items
  /// Defaults to AppColors.gray.shade200
  final Color? baseColor;

  /// Border radius for shimmer items
  /// Defaults to 8.r
  final double? borderRadius;

  /// Padding for each shimmer item
  /// Defaults to EdgeInsets.symmetric(horizontal: 16.w)
  final EdgeInsetsGeometry? itemPadding;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: physics ?? const AlwaysScrollableScrollPhysics(),
      padding: padding ?? EdgeInsets.all(16.w),
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: index == itemCount - 1 ? 0 : (itemSpacing ?? 12.h),
          ),
          child: _buildShimmerItem(),
        );
      },
    );
  }

  Widget _buildShimmerItem() {
    return Container(
      height: itemHeight,
      padding: itemPadding ?? EdgeInsets.zero,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: baseColor ?? AppColors.gray.shade200,
          borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
        ),
      ).applyShimmer(),
    );
  }

  /// Convenience method for single item shimmer
  static Widget single({
    Key? key,
    required double height,
    EdgeInsetsGeometry? padding,
    Color? shimmerColor,
    Color? baseColor,
    double? borderRadius,
  }) {
    return AppShimmerLoader(
      key: key,
      itemCount: 1,
      itemHeight: height,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding,
      shimmerColor: shimmerColor,
      baseColor: baseColor,
      borderRadius: borderRadius,
    );
  }

  /// Convenience method for card-style shimmer
  static Widget card({
    Key? key,
    required int itemCount,
    double itemHeight = 120,
    EdgeInsetsGeometry? padding,
    Color? shimmerColor,
    Color? baseColor,
  }) {
    return AppShimmerLoader(
      key: key,
      itemCount: itemCount,
      itemHeight: itemHeight.h,
      padding: padding ?? EdgeInsets.all(16.w),
      itemSpacing: 16.h,
      borderRadius: 12.r,
      shimmerColor: shimmerColor,
      baseColor: baseColor,
    );
  }

  /// Convenience method for list item shimmer
  static Widget list({
    Key? key,
    required int itemCount,
    double itemHeight = 80,
    EdgeInsetsGeometry? padding,
    Color? shimmerColor,
    Color? baseColor,
  }) {
    return AppShimmerLoader(
      key: key,
      itemCount: itemCount,
      itemHeight: itemHeight.h,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w),
      itemSpacing: 8.h,
      borderRadius: 6.r,
      shimmerColor: shimmerColor,
      baseColor: baseColor,
    );
  }

  /// Convenience method for fixed shimmer items without ListView
  /// Perfect for containers with defined height
  static Widget fixed({
    Key? key,
    required int itemCount,
    required double itemHeight,
    double itemSpacing = 12,
    Color? shimmerColor,
    Color? baseColor,
    double? borderRadius,
  }) {
    return Column(
      children: List.generate(itemCount, (index) {
        return Container(
          margin: EdgeInsets.only(
            bottom: index == itemCount - 1 ? 0 : itemSpacing.h,
          ),
          height: itemHeight,
          decoration: BoxDecoration(
            color: baseColor ?? AppColors.gray.shade200,
            borderRadius: BorderRadius.circular(borderRadius ?? 8.r),
          ),
        ).applyShimmer();
      }),
    );
  }
}
