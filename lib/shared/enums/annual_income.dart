import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';

/// Annual income options that match the backend implementation
enum AnnualIncome {
  @JsonValue('LessThan10000')
  lessThan10000,
  @JsonValue('From10000To50000')
  from10000To50000,
  @JsonValue('From50000To100000')
  from50000To100000,
  @JsonValue('MoreThan100000')
  moreThan100000;

  String get value {
    switch (this) {
      case AnnualIncome.lessThan10000:
        return 'LessThan10000';
      case AnnualIncome.from10000To50000:
        return 'From10000To50000';
      case AnnualIncome.from50000To100000:
        return 'From50000To100000';
      case AnnualIncome.moreThan100000:
        return 'MoreThan100000';
    }
  }

  /// Returns the localized title for the annual income
  String get title {
    switch (this) {
      case AnnualIncome.lessThan10000:
        return LocaleKeys.lessThan10000.tr(namedArgs: {'dollarSign': '\$'});
      case AnnualIncome.from10000To50000:
        return LocaleKeys.from10000To50000.tr(namedArgs: {'dollarSign': '\$'});
      case AnnualIncome.from50000To100000:
        return LocaleKeys.from50000To100000.tr(namedArgs: {'dollarSign': '\$'});
      case AnnualIncome.moreThan100000:
        return LocaleKeys.moreThan100000.tr(namedArgs: {'dollarSign': '\$'});
    }
  }
}
