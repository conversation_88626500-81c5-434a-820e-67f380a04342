import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

enum PaymentStatus {
  @JsonValue('Pending')
  pending,
  @JsonValue('Success')
  success,
  @JsonValue('Failed')
  failed,
  @JsonValue('Cancelled')
  cancelled,
  @JsonValue('Reversed')
  reversed,
  @JsonValue('Refunded')
  refunded;

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return LocaleKeys.pending.tr();
      case PaymentStatus.success:
        return LocaleKeys.success.tr();
      case PaymentStatus.failed:
        return LocaleKeys.failed.tr();
      case PaymentStatus.cancelled:
        return LocaleKeys.cancelled.tr();
      case PaymentStatus.reversed:
        return LocaleKeys.reversed.tr();
      case PaymentStatus.refunded:
        return LocaleKeys.refunded.tr();
    }
  }
}
