import 'package:maisour/shared/models/app_update_info.dart';

enum SplashDecision {
  jailbroken, // Device is rooted/jailbroken - close app
  noInternet, // No internet connection - show no internet screen
  updateRequired, // App update required - show update dialog
  welcome, // Not logged in - show welcome screen if user comes first time
  login, // Not logged in - show login screen
  home, // Already logged in - go to home
}

/// Wrapper class to hold splash decision with optional update info
class SplashResult {
  final SplashDecision decision;
  final AppUpdateInfo? updateInfo;

  const SplashResult(this.decision, [this.updateInfo]);

  factory SplashResult.jailbroken() =>
      const SplashResult(SplashDecision.jailbroken);
  factory SplashResult.noInternet() =>
      const SplashResult(SplashDecision.noInternet);
  factory SplashResult.updateRequired(AppUpdateInfo info) =>
      SplashResult(SplashDecision.updateRequired, info);
  factory SplashResult.welcome() => const SplashResult(SplashDecision.welcome);
  factory SplashResult.login() => const SplashResult(SplashDecision.login);
  factory SplashResult.home() => const SplashResult(SplashDecision.home);
}
