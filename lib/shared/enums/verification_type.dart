import 'package:maisour/config/app_environment.dart';

enum VerificationType { passportWithSelfie, address, passport }

extension IdWiseJourneyTypeExtension on VerificationType {
  // Convert from string to enum
  String get flowId {
    switch (this) {
      case VerificationType.passportWithSelfie:
        return AppEnvironment.idwisePassportSelfieFlowId;

      case VerificationType.passport:
        return AppEnvironment.idwisePassportFlowId;

      case VerificationType.address:
        return AppEnvironment.idwisePoaFlowId;
    }
  }
}
