import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

enum PropertyStatus {
  @JsonValue('ComingSoon')
  comingSoon,
  @JsonValue('Cancelled')
  cancelled,
  @JsonValue('Sold')
  sold,
  @JsonValue('Funded')
  funded,
  @JsonValue('Request')
  request,
  @JsonValue('Live')
  live;

  String get displayName {
    switch (this) {
      case PropertyStatus.comingSoon:
        return LocaleKeys.comingSoon.tr();
      case PropertyStatus.cancelled:
        return LocaleKeys.cancelled.tr();
      case PropertyStatus.sold:
        return LocaleKeys.sold.tr();
      case PropertyStatus.funded:
        return LocaleKeys.funded.tr();
      case PropertyStatus.request:
        return LocaleKeys.request.tr();
      case PropertyStatus.live:
        return LocaleKeys.live.tr();
    }
  }
}
