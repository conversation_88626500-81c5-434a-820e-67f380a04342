/// Enum for supported social media platforms
/// Each platform has a default username that can be overridden
enum SocialMediaPlatform {
  twitter,
  instagram,
  facebook,
  linkedin,
  whatsapp,
  youtube;

  const SocialMediaPlatform();

  /// Get the full URL for the platform with username
  String get url {
    switch (this) {
      case SocialMediaPlatform.twitter:
        return "https://x.com/BeMaisour";

      case SocialMediaPlatform.instagram:
        return "https://www.instagram.com/bemaisour";

      case SocialMediaPlatform.facebook:
        return "https://www.facebook.com/BeMaisour";

      case SocialMediaPlatform.whatsapp:
        return "https://wame.pro/maisourapp";

      case SocialMediaPlatform.linkedin:
        return "https://www.linkedin.com/company/maisour";

      case SocialMediaPlatform.youtube:
        return "https://www.youtube.com/@BeMaisour";
    }
  }

  /// Get display name for the platform
  String get displayName {
    switch (this) {
      case SocialMediaPlatform.twitter:
        return 'Twitter';
      case SocialMediaPlatform.instagram:
        return 'Instagram';
      case SocialMediaPlatform.facebook:
        return 'Facebook';
      case SocialMediaPlatform.linkedin:
        return 'LinkedIn';
      case SocialMediaPlatform.whatsapp:
        return 'WhatsApp';
      case SocialMediaPlatform.youtube:
        return 'YouTube';
    }
  }
}
