import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';

/// Employment status options that match the backend implementation
enum EmploymentStatus {
  @JsonValue('Employed')
  employed,
  @JsonValue('Retired')
  retired,
  @<PERSON>sonValue('Student')
  student,
  @JsonValue('Unemployed')
  unemployed;

  String get value {
    switch (this) {
      case EmploymentStatus.employed:
        return 'Employed';
      case EmploymentStatus.retired:
        return 'Retired';
      case EmploymentStatus.student:
        return 'Student';
      case EmploymentStatus.unemployed:
        return 'Unemployed';
    }
  }

  /// Returns the localized title for the employment status
  String get title {
    switch (this) {
      case EmploymentStatus.employed:
        return LocaleKeys.employed.tr();
      case EmploymentStatus.retired:
        return LocaleKeys.retired.tr();
      case EmploymentStatus.student:
        return LocaleKeys.student.tr();
      case EmploymentStatus.unemployed:
        return LocaleKeys.unemployed.tr();
    }
  }
}
