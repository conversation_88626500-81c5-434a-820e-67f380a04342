import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

enum TransactionStatus {
  @JsonValue('Successful')
  successful,
  @JsonValue('Success')
  success,
  @JsonValue('Failed')
  failed,
  @JsonValue('Pending')
  pending,
  @JsonValue('Cancelled')
  cancelled,
  @JsonValue('Canceled')
  canceled,
  @JsonValue('Rejected')
  rejected,
  @JsonValue('Reversed')
  reversed,
  @JsonValue('Refunded')
  refunded,
  @JsonValue('NotFunded')
  notFunded,
  @JsonValue('Removed')
  removed,
  @JsonValue('Sold')
  sold,
}

extension TransactionStatusExtension on TransactionStatus {
  String get displayName {
    switch (this) {
      case TransactionStatus.successful:
      case TransactionStatus.success:
        return LocaleKeys.successful.tr();
      case TransactionStatus.failed:
        return LocaleKeys.failed.tr();
      case TransactionStatus.pending:
        return LocaleKeys.pending.tr();
      case TransactionStatus.cancelled:
      case TransactionStatus.canceled:
        return LocaleKeys.cancelled.tr();
      case TransactionStatus.rejected:
        return LocaleKeys.rejected.tr();
      case TransactionStatus.reversed:
        return LocaleKeys.reversed.tr();
      case TransactionStatus.refunded:
        return LocaleKeys.refunded.tr();
      case TransactionStatus.notFunded:
        return LocaleKeys.notFunded.tr();
      case TransactionStatus.removed:
        return LocaleKeys.removed.tr();
      case TransactionStatus.sold:
        return LocaleKeys.sold.tr();
    }
  }

  bool get isPositive {
    switch (this) {
      case TransactionStatus.successful:
      case TransactionStatus.success:
      case TransactionStatus.refunded:
        return true;
      case TransactionStatus.failed:
      case TransactionStatus.rejected:
      case TransactionStatus.cancelled:
      case TransactionStatus.canceled:
      case TransactionStatus.reversed:
      case TransactionStatus.notFunded:
      case TransactionStatus.removed:
      case TransactionStatus.sold:
        return false;
      case TransactionStatus.pending:
        return false; // Neutral state
    }
  }

  bool get isNegative {
    switch (this) {
      case TransactionStatus.failed:
      case TransactionStatus.rejected:
      case TransactionStatus.cancelled:
      case TransactionStatus.canceled:
      case TransactionStatus.reversed:
      case TransactionStatus.notFunded:
      case TransactionStatus.removed:
        return true;
      case TransactionStatus.successful:
      case TransactionStatus.success:
      case TransactionStatus.refunded:
      case TransactionStatus.sold:
        return false;
      case TransactionStatus.pending:
        return false; // Neutral state
    }
  }

  bool get isNeutral {
    switch (this) {
      case TransactionStatus.pending:
      case TransactionStatus.sold:
        return true;
      default:
        return false;
    }
  }
}
