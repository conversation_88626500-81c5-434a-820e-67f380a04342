import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

/// Currency enum for supported currencies in the app
enum Currency {
  @JsonValue('AED')
  aed,
  @JsonValue('USD')
  usd,
  @JsonValue('EUR')
  eur,
}

/// Extension to provide display names for currencies
extension CurrencyExtension on Currency {
  String get value {
    switch (this) {
      case Currency.aed:
        return 'AED';
      case Currency.usd:
        return 'USD';
      case Currency.eur:
        return 'EUR';
    }
  }

  String get displayName {
    switch (this) {
      case Currency.aed:
        return LocaleKeys.aed.tr();
      case Currency.usd:
        return LocaleKeys.usd.tr();
      case Currency.eur:
        return LocaleKeys.eur.tr();
    }
  }

  String get fullName {
    switch (this) {
      case Currency.aed:
        return LocaleKeys.unitedArabEmiratesDirham.tr();
      case Currency.usd:
        return LocaleKeys.unitedStatesDollar.tr();
      case Currency.eur:
        return LocaleKeys.euro.tr();
    }
  }

  /// Gets the currency symbol for the user's selected currency
  /// Returns the appropriate symbol for display
  String get currencySymbol {
    switch (this) {
      case Currency.aed:
        return 'D';
      case Currency.usd:
        return '\$';
      case Currency.eur:
        return '€';
    }
  }

  /// Gets the NumberFormat for currency formatting
  /// Returns a NumberFormat instance configured for this currency
  NumberFormat get currencyFormat {
    return getCurrencyFormat();
  }

  /// Gets the NumberFormat for currency formatting with custom decimal digits
  /// [decimalDigits] - Number of decimal places (defaults to 2 if null)
  /// Returns a NumberFormat instance configured for this currency
  NumberFormat getCurrencyFormat({int? decimalDigits}) {
    switch (this) {
      case Currency.aed:
        return NumberFormat.currency(
          locale: "en_US",
          symbol: "",
          decimalDigits: decimalDigits,
        );
      case Currency.usd:
        return NumberFormat.currency(
          locale: "en_US",
          symbol: "",
          decimalDigits: decimalDigits,
        );
      case Currency.eur:
        return NumberFormat.currency(
          locale: "en_US",
          symbol: "",
          decimalDigits: decimalDigits,
        );
    }
  }

  /// Formats a value as currency string
  /// [value] - The amount to format
  /// [decimalDigits] - Number of decimal places (defaults to 2 if null)
  /// Returns formatted string without currency symbol
  String formatCurrency(dynamic value, {int? decimalDigits}) {
    return getCurrencyFormat(decimalDigits: decimalDigits).format(value);
  }
}
