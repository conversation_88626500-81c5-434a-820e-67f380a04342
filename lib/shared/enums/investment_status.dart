import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

enum InvestmentStatus {
  @JsonValue('Successful')
  successful,
  @JsonValue('Rejected')
  rejected,
  @JsonValue('Cancelled')
  cancelled,
  @JsonValue('Pending')
  pending,
  @JsonValue('NotFunded')
  notFunded,
  @JsonValue('Removed')
  removed,
  @JsonValue('Sold')
  sold;

  String get displayName {
    switch (this) {
      case InvestmentStatus.successful:
        return LocaleKeys.successful.tr();
      case InvestmentStatus.rejected:
        return LocaleKeys.rejected.tr();
      case InvestmentStatus.cancelled:
        return LocaleKeys.cancelled.tr();
      case InvestmentStatus.pending:
        return LocaleKeys.pending.tr();
      case InvestmentStatus.notFunded:
        return LocaleKeys.notFunded.tr();
      case InvestmentStatus.removed:
        return LocaleKeys.removed.tr();
      case InvestmentStatus.sold:
        return LocaleKeys.sold.tr();
    }
  }
}
