import 'package:easy_localization/easy_localization.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

/// Divided frequency enum for property investment returns
enum DividedFrequency {
  @JsonValue('Weekly')
  weekly,
  @JsonValue('Monthly')
  monthly,
  @JsonValue('Quarterly')
  quarterly,
  @JsonValue('Biannually')
  biannually,
  @JsonValue('Annually')
  annually,
}

/// Extension to provide display names for divided frequency
extension DividedFrequencyExtension on DividedFrequency {
  String get displayName {
    switch (this) {
      case DividedFrequency.weekly:
        return LocaleKeys.weekly.tr();
      case DividedFrequency.monthly:
        return LocaleKeys.monthly.tr();
      case DividedFrequency.quarterly:
        return LocaleKeys.quarterly.tr();
      case DividedFrequency.biannually:
        return LocaleKeys.biannually.tr();
      case DividedFrequency.annually:
        return LocaleKeys.annually.tr();
    }
  }
}
