import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'shared_prefs_manager.dart';

final sharedPrefsProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError();
}, name: 'sharedPrefsProvider');

final sharedPrefsManagerProvider = Provider<SharedPrefsManager>((ref) {
  final prefs = ref.watch(sharedPrefsProvider);
  return SharedPrefsManager(prefs);
}, name: 'sharedPrefsManagerProvider');
