import 'package:shared_preferences/shared_preferences.dart';
import 'package:maisour/shared/constants/app_constants.dart';

class SharedPrefsManager {
  static const _selectedLanguageKey = 'selected_language';
  static const _dismissedUpdateVersionKey = 'dismissed_update_version';
  static const _firstLaunchKey = 'first_launch';
  static const _firstHomeVisitKey = 'first_home_visit';
  static const _lastVerificationTimeKey = 'last_verification_time';

  final SharedPreferences _prefs;

  SharedPrefsManager(this._prefs);

  // Language Management
  /// Get saved language preference
  /// Returns null if no language is saved or if saved language is not supported
  String get getSavedLanguage {
    final language = _prefs.getString(_selectedLanguageKey);
    if (language != null &&
        AppConstants.supportedLanguages.contains(language)) {
      return language;
    } else {
      return AppConstants.defaultLanguage;
    }
  }

  /// Save language preference
  Future<void> saveLanguage(String languageCode) async {
    if (AppConstants.supportedLanguages.contains(languageCode)) {
      await _prefs.setString(_selectedLanguageKey, languageCode);
    }
  }

  /// Clear saved language preference
  Future<void> clearLanguage() async {
    await _prefs.remove(_selectedLanguageKey);
  }

  // Update Preference Management - Simple Store/Read Only
  /// Get the version that was dismissed by user
  String? get dismissedUpdateVersion =>
      _prefs.getString(_dismissedUpdateVersionKey);

  /// Store the version that user dismissed
  Future<void> setDismissedUpdateVersion(String version) async {
    await _prefs.setString(_dismissedUpdateVersionKey, version);
  }

  /// Clear the dismissed version preference
  Future<void> clearDismissedUpdateVersion() async {
    await _prefs.remove(_dismissedUpdateVersionKey);
  }

  /// Get first launch preference
  bool get firstLaunch => _prefs.getBool(_firstLaunchKey) ?? true;

  /// Set first launch preference
  Future<void> setFirstLaunch(bool value) async {
    await _prefs.setBool(_firstLaunchKey, value);
  }

  /// Get first home visit preference
  bool get firstHomeVisit => _prefs.getBool(_firstHomeVisitKey) ?? true;

  /// Set first home visit preference
  Future<void> setFirstHomeVisit(bool value) async {
    await _prefs.setBool(_firstHomeVisitKey, value);
  }

  /// Clear the first home visit preference
  Future<void> clearFirstHomeVisit() async {
    await _prefs.remove(_firstHomeVisitKey);
  }

  /// Get last passport verification time
  DateTime? get lastVerificationTime {
    final millis = _prefs.getInt(_lastVerificationTimeKey);
    if (millis != null) {
      return DateTime.fromMillisecondsSinceEpoch(millis);
    }
    return null;
  }

  /// Set last passport verification time
  Future<void> setLastVerificationTime(DateTime time) async {
    await _prefs.setInt(_lastVerificationTimeKey, time.millisecondsSinceEpoch);
  }

  /// Clear the last verification time preference
  Future<void> clearLastVerificationTime() async {
    await _prefs.remove(_lastVerificationTimeKey);
  }

  /// Clear all user-specific preferences (called on logout)
  /// This method clears user-specific data but preserves app-level settings
  Future<void> clearUserData() async {
    // Clear user-specific preferences
    await clearDismissedUpdateVersion();
    await clearFirstHomeVisit();
    await clearLastVerificationTime();
    // Note: Language preference is preserved across user sessions
    // Users expect their language choice to persist even after logout
    // Only clear language if you want to reset to default on logout:
    // await clearLanguage();
  }
}
