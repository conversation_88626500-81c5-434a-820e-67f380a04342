import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

enum MapAppType { google, apple }

class MapLauncherService {
  static const String _googleMapsUrl = 'comgooglemaps://';

  /// Launches the appropriate map application based on platform
  static Future<void> launchMap({
    required BuildContext context,
    required double latitude,
    required double longitude,
    required String title,
    String? address,
  }) async {
    if (MyPlatform.isAndroid) {
      await _launchGoogleMaps(latitude, longitude, title, address);
    } else if (MyPlatform.isIOS) {
      await _showMapSelectionDialog(
        context: context,
        latitude: latitude,
        longitude: longitude,
        title: title,
        address: address,
      );
    }
  }

  /// Launches Google Maps on Android
  static Future<void> _launchGoogleMaps(
    double latitude,
    double longitude,
    String title,
    String? address,
  ) async {
    final query = address ?? '$latitude,$longitude';
    final url = 'https://www.google.com/maps/search/?api=1&query=$query';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch Google Maps');
    }
  }

  /// Shows map selection dialog for iOS
  static Future<void> _showMapSelectionDialog({
    required BuildContext context,
    required double latitude,
    required double longitude,
    required String title,
    String? address,
  }) async {
    final result = await showDialog<MapAppType>(
      context: context,
      builder: (context) => _MapSelectionDialog(
        latitude: latitude,
        longitude: longitude,
        title: title,
        address: address,
      ),
    );

    if (result != null) {
      await _launchSelectedMap(
        mapType: result,
        latitude: latitude,
        longitude: longitude,
        title: title,
        address: address,
      );
    }
  }

  /// Launches the selected map application
  static Future<void> _launchSelectedMap({
    required MapAppType mapType,
    required double latitude,
    required double longitude,
    required String title,
    String? address,
  }) async {
    switch (mapType) {
      case MapAppType.google:
        await _launchGoogleMapsIOS(latitude, longitude, title, address);
        break;
      case MapAppType.apple:
        await _launchAppleMaps(latitude, longitude, title, address);
        break;
    }
  }

  /// Launches Google Maps on iOS (checks if installed first)
  static Future<void> _launchGoogleMapsIOS(
    double latitude,
    double longitude,
    String title,
    String? address,
  ) async {
    // Check if Google Maps app is installed
    if (await canLaunchUrl(Uri.parse(_googleMapsUrl))) {
      // Launch Google Maps app
      final query = address ?? '$latitude,$longitude';
      final url =
          'comgooglemaps://?q=$query&center=$latitude,$longitude&zoom=15';
      await launchUrl(Uri.parse(url));
    } else {
      // Fallback to web version
      final query = address ?? '$latitude,$longitude';
      final url = 'https://www.google.com/maps/search/?api=1&query=$query';
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  /// Launches Apple Maps on iOS
  static Future<void> _launchAppleMaps(
    double latitude,
    double longitude,
    String title,
    String? address,
  ) async {
    final query = address ?? '$latitude,$longitude';
    final url = 'http://maps.apple.com/?q=$query&ll=$latitude,$longitude&z=15';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      debugPrint('Could not launch Apple Maps');
    }
  }
}

/// Dialog for selecting map application on iOS
class _MapSelectionDialog extends StatelessWidget {
  final double latitude;
  final double longitude;
  final String title;
  final String? address;

  const _MapSelectionDialog({
    required this.latitude,
    required this.longitude,
    required this.title,
    this.address,
  });

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) => AlertDialog.adaptive(
        title: Text(LocaleKeys.chooseMapApp.tr()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.map),
              title: Text(LocaleKeys.appleMaps.tr()),
              onTap: () => Navigator.of(context).pop(MapAppType.apple),
            ),
            ListTile(
              leading: const Icon(Icons.map_outlined),
              title: Text(LocaleKeys.googleMaps.tr()),
              onTap: () => Navigator.of(context).pop(MapAppType.google),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocaleKeys.cancel.tr()),
          ),
        ],
      ),
    );
  }
}
