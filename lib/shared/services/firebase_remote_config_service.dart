import 'dart:io';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:maisour/shared/models/app_update_info.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';

final remoteConfigServiceProvider = Provider<RemoteConfigService>((ref) {
  final sharedPrefsManager = ref.read(sharedPrefsManagerProvider);
  return RemoteConfigService(FirebaseRemoteConfig.instance, sharedPrefsManager);
}, name: 'remoteConfigServiceProvider');

class RemoteConfigService {
  final FirebaseRemoteConfig _remoteConfig;
  final SharedPrefsManager _sharedPrefsManager;

  RemoteConfigService(this._remoteConfig, this._sharedPrefsManager);

  /// Initialize Remote Config
  Future<void> initialize() async {
    try {
      // Set config settings
      await _remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: kDebugMode
              ? const Duration(seconds: 10) // Debug: fetch frequently
              : const Duration(hours: 1), // Production: fetch hourly
        ),
      );

      // Fetch and activate
      await _remoteConfig.fetchAndActivate();

      debugPrint('✅ Remote Config initialized successfully');
    } catch (e) {
      debugPrint('❌ Remote Config initialization failed: $e');
    }
  }

  /// Check for updates using build numbers and platform logic
  /// Also checks if optional update was already dismissed today
  Future<AppUpdateInfo> checkForUpdate() async {
    try {
      // Get current app build number and platform
      final packageInfo = await PackageInfo.fromPlatform();
      final currentBuildNumber = int.tryParse(packageInfo.buildNumber) ?? 0;
      final isAndroid = Platform.isAndroid;

      // Get remote config values (build numbers)
      final androidVersion = _remoteConfig.getInt('android_version');
      final iosVersion = _remoteConfig.getInt('ios_version');
      final minVersion = _remoteConfig.getInt('min_version');

      debugPrint('📱 Update Check:');
      debugPrint('   Platform: ${isAndroid ? 'Android' : 'iOS'}');
      debugPrint('   Current Build: $currentBuildNumber');
      debugPrint('   Min Version: $minVersion');
      debugPrint('   Android Version: $androidVersion');
      debugPrint('   iOS Version: $iosVersion');

      // Determine platform-specific version
      final platformVersion = isAndroid ? androidVersion : iosVersion;
      debugPrint('   Platform Version: $platformVersion');

      // Your logic:
      // Android: 10 < 11 (min) → Force Update 🚨
      // iOS: 12 > 11 (min) → Optional Update 💡

      if (currentBuildNumber < minVersion) {
        // Force update: below minimum version
        debugPrint(
          '🚨 Force update required: $currentBuildNumber < $minVersion',
        );
        final result = AppUpdateInfo.forceUpdate();
        debugPrint('📋 Final Result: FORCE UPDATE');
        return result;
      } else if (currentBuildNumber < platformVersion) {
        // Optional update: above min but below platform version

        // Create version string for policy check (platform_version)
        final availableVersionString =
            '${isAndroid ? 'android' : 'ios'}_$platformVersion';

        // Check if user already dismissed this specific version
        final dismissedVersion = _sharedPrefsManager.dismissedUpdateVersion;

        debugPrint('📋 Update Policy Check:');
        debugPrint('   Available Version: $availableVersionString');
        debugPrint('   Dismissed Version: $dismissedVersion');

        if (dismissedVersion == availableVersionString) {
          debugPrint(
            '⏭️ Optional update available but version $availableVersionString already dismissed',
          );
          return AppUpdateInfo.noUpdate();
        }

        debugPrint(
          '💡 Optional update available: $currentBuildNumber < $platformVersion (version: $availableVersionString)',
        );
        final result = AppUpdateInfo.optionalUpdate(availableVersionString);
        debugPrint(
          '📋 Final Result: OPTIONAL UPDATE ($availableVersionString)',
        );
        return result;
      } else {
        // No update needed
        debugPrint(
          '✅ App is up to date: $currentBuildNumber >= $platformVersion',
        );
        final result = AppUpdateInfo.noUpdate();
        debugPrint('📋 Final Result: NO UPDATE');
        return result;
      }
    } catch (e) {
      debugPrint('❌ Error checking for update: $e');
      return AppUpdateInfo.noUpdate();
    }
  }
}
