import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final crashlyticsServiceProvider = Provider<CrashlyticsService>((ref) {
  return CrashlyticsService(FirebaseCrashlytics.instance);
}, name: 'crashlyticsServiceProvider');

class CrashlyticsService {
  final FirebaseCrashlytics _crashlytics;

  CrashlyticsService(this._crashlytics);

  Future<void> logError(dynamic error, [StackTrace? stackTrace]) async {
    await _crashlytics.recordError(error, stackTrace);
  }

  Future<void> setUserIdentifier(String identifier) async {
    await _crashlytics.setUserIdentifier(identifier);
  }

  /// Set user email for better error tracking
  Future<void> setUserEmail(String email) async {
    await _crashlytics.setCustomKey('user_email', email);
  }

  /// Set both user ID and email for comprehensive user identification
  /// This should be called after successful login (email or social)
  Future<void> setUserIdentification({
    required int userId,
    required String email,
  }) async {
    try {
      // Set user identifier (convert int to string)
      await setUserIdentifier(userId.toString());

      // Set user email as custom key
      await setUserEmail(email);

      debugPrint(
        '✅ Crashlytics user identification set - ID: $userId, Email: $email',
      );
    } catch (error) {
      debugPrint('❌ Failed to set Crashlytics user identification: $error');
    }
  }

  /// Clear user identification (called on logout)
  Future<void> clearUserIdentification() async {
    try {
      await _crashlytics.setUserIdentifier('');
      await _crashlytics.setCustomKey('user_email', '');
      debugPrint('✅ Crashlytics user identification cleared');
    } catch (error) {
      debugPrint('❌ Failed to clear Crashlytics user identification: $error');
    }
  }
}
