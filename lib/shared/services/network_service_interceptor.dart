import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/services/token/token_service.dart';
import 'package:maisour/shared/services/token/token_service_impl.dart';

final networkServiceInterceptorProvider =
    Provider.family<NetworkServiceInterceptor, Dio>((ref, dio) {
      final tokenService = ref.watch(tokenServiceProvider(dio));
      return NetworkServiceInterceptor(tokenService, dio);
    }, name: 'networkServiceInterceptorProvider');

final class NetworkServiceInterceptor extends Interceptor {
  final TokenService _tokenService;
  final Dio _dio;

  const NetworkServiceInterceptor(this._tokenService, this._dio);

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final accessToken = await _tokenService.getAccessToken();
    options.headers['Content-Type'] = ApiConfig.contentType;
    options.headers['Accept'] = ApiConfig.accept;
    if (accessToken != null) {
      options.headers[ApiConfig.authorization] =
          '${ApiConfig.bearerPrefix}$accessToken';
    }
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == HttpStatus.unauthorized) {
      // Handle token expiration or unauthorized access
      // You can refresh the token here or redirect to login
      final refreshToken = await _tokenService.getRefreshToken();
      if (refreshToken == null) {
        // Handle refresh token not found
        return handler.next(err);
      }
      try {
        await _tokenService.clearAccessToken();
        final result = await _tokenService.refreshToken(refreshToken);

        final newAccessToken = result.accessToken;
        final newRefreshToken = result.refreshToken;
        // Store the new tokens
        await _tokenService.saveToken(newAccessToken, newRefreshToken);
        // Retry the original request with the new token
        final options = err.requestOptions;
        options.headers[ApiConfig.authorization] =
            '${ApiConfig.bearerPrefix}$newAccessToken';
        // Update the request options with the new token
        final newResponse = await _dio.fetch(options);
        return handler.resolve(newResponse);
      } on DioException catch (e) {
        if (e.response?.statusCode == HttpStatus.unauthorized) {
          // Handle refresh token failure remove tokens
          await _tokenService.clearTokens();

          return handler.next(err);
        }
        return handler.next(err);
      }
    }
    super.onError(err, handler);
  }
}
