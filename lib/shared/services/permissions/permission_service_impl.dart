import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart'
    as permission_handler;
import 'package:maisour/shared/services/permissions/permission_service.dart';

final permissionServiceProvider = Provider<PermissionService>((ref) {
  return PermissionServiceImpl();
}, name: 'permissionServiceProvider');

class PermissionServiceImpl implements PermissionService {
  @override
  Future<bool> requestAppTrackingTransparency() async {
    if (!Platform.isIOS) {
      return true; // Android doesn't need ATT
    }

    try {
      // Check current status
      final currentStatus =
          await permission_handler.Permission.appTrackingTransparency.status;

      // Return early if already granted
      if (currentStatus.isGranted) {
        return true;
      }

      // Return false if permanently denied or restricted
      if (currentStatus.isPermanentlyDenied || currentStatus.isRestricted) {
        debugPrint('� ATT permission denied - limited tracking enabled');
        return false;
      }

      // Request permission
      final status = await permission_handler.Permission.appTrackingTransparency
          .request();

      if (status.isGranted) {
        debugPrint('✅ ATT permission granted - full tracking enabled');
      } else {
        debugPrint('❌ ATT permission denied - limited tracking enabled');
      }

      return status.isGranted;
    } catch (error) {
      debugPrint('❌ Error requesting ATT permission: $error');
      return false;
    }
  }

  @override
  Future<bool> isAppTrackingTransparencyGranted() async {
    if (!Platform.isIOS) {
      return true; // Android doesn't need ATT
    }

    try {
      final status =
          await permission_handler.Permission.appTrackingTransparency.status;
      return status.isGranted;
    } catch (error) {
      debugPrint('❌ Error checking ATT permission: $error');
      return false;
    }
  }

  @override
  Future<bool> openAppSettings() async {
    try {
      debugPrint('⚙️ Opening app settings...');
      return await permission_handler.openAppSettings();
    } catch (error) {
      debugPrint('❌ Error opening app settings: $error');
      return false;
    }
  }

  @override
  Future<bool> isAppTrackingTransparencyPermanentlyDenied() async {
    if (!Platform.isIOS) {
      return false; // Android doesn't have ATT
    }

    try {
      final status =
          await permission_handler.Permission.appTrackingTransparency.status;
      return status == permission_handler.PermissionStatus.permanentlyDenied;
    } catch (error) {
      debugPrint('❌ Error checking ATT permanent denial: $error');
      return false;
    }
  }

  @override
  Future<bool> requestNotificationPermission() async {
    try {
      // Check current status
      final currentStatus =
          await permission_handler.Permission.notification.status;

      // Return early if already granted
      if (currentStatus.isGranted) {
        return true;
      }

      // Return false if permanently denied or restricted
      if (currentStatus.isPermanentlyDenied || currentStatus.isRestricted) {
        debugPrint(
          '🚫 Notification permission denied - notifications disabled',
        );
        return false;
      }

      // Request permission
      final status = await permission_handler.Permission.notification.request();

      if (status.isGranted) {
        debugPrint('✅ Notification permission granted - notifications enabled');
      } else {
        debugPrint('❌ Notification permission denied - notifications disabled');
      }

      return status.isGranted;
    } catch (error) {
      debugPrint('❌ Error requesting notification permission: $error');
      return false;
    }
  }

  @override
  Future<bool> isNotificationPermissionGranted() async {
    try {
      final status = await permission_handler.Permission.notification.status;
      return status.isGranted;
    } catch (error) {
      debugPrint('❌ Error checking notification permission: $error');
      return false;
    }
  }

  @override
  Future<Map<String, bool>> requestRequiredPermissions() async {
    try {
      debugPrint(
        '🚀 Requesting required app startup permissions (ATT + Notifications)...',
      );

      final List<permission_handler.Permission> permissionList = [];

      // Add ATT permission for iOS only
      if (Platform.isIOS) {
        permissionList.add(
          permission_handler.Permission.appTrackingTransparency,
        );
      }

      // Add notification permission
      permissionList.add(permission_handler.Permission.notification);

      // Request all permissions at once
      final Map<
        permission_handler.Permission,
        permission_handler.PermissionStatus
      >
      statuses = await permissionList.request();

      // Process results
      final Map<String, bool> results = {};

      // ATT permission result
      if (Platform.isIOS) {
        final attStatus =
            statuses[permission_handler.Permission.appTrackingTransparency];
        results['att'] = attStatus?.isGranted ?? false;
        debugPrint(
          '📱 ATT permission: ${attStatus?.isGranted == true ? "✅ Granted" : "❌ Denied"}',
        );
      } else {
        results['att'] = true; // Android doesn't need ATT
        debugPrint('📱 ATT permission: ✅ Granted (Android)');
      }

      // Notification permission result
      final notificationStatus =
          statuses[permission_handler.Permission.notification];
      results['notification'] = notificationStatus?.isGranted ?? false;
      debugPrint(
        '🔔 Notification permission: ${notificationStatus?.isGranted == true ? "✅ Granted" : "❌ Denied"}',
      );

      debugPrint('✅ Required permissions request completed');
      return results;
    } catch (error) {
      debugPrint('❌ Error requesting required permissions: $error');
      return {'att': false, 'notification': false};
    }
  }
}
