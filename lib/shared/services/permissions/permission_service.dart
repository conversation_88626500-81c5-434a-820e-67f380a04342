abstract interface class PermissionService {
  /// Request App Tracking Transparency permission (iOS only)
  /// Returns true if permission granted, false if denied
  /// On Android, always returns true (ATT not required)
  Future<bool> requestAppTrackingTransparency();

  /// Check if App Tracking Transparency permission is granted
  /// Returns true if granted, false if denied or not determined
  /// On Android, always returns true (ATT not required)
  Future<bool> isAppTrackingTransparencyGranted();

  /// Open app settings for manual permission management
  /// Useful when user needs to manually enable permissions
  Future<bool> openAppSettings();

  /// Check if ATT permission is permanently denied
  /// Returns true if user must manually enable in iOS Settings
  Future<bool> isAppTrackingTransparencyPermanentlyDenied();

  /// Request notification permission from user
  /// Returns true if permission granted, false if denied
  /// Handles iOS/Android differences automatically
  Future<bool> requestNotificationPermission();

  /// Check if notification permission is granted
  /// Returns true if granted, false if denied or not determined
  Future<bool> isNotificationPermissionGranted();

  /// Request all required permissions for app startup (ATT + Notifications)
  /// Returns a map with 'att' and 'notification' permission results
  Future<Map<String, bool>> requestRequiredPermissions();
}
