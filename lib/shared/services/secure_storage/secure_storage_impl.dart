import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:maisour/shared/services/secure_storage/flutter_secure_storage_provider.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage.dart';

final secureStorageProvider = Provider<SecureStorage>((ref) {
  final secureStorage = ref.watch(flutterSecureStorageProvider);
  return SecureStorageImpl(secureStorage);
}, name: 'secureStorageProvider');

final class SecureStorageImpl implements SecureStorage {
  final FlutterSecureStorage _secureStorage;

  const SecureStorageImpl(this._secureStorage);

  @override
  Future<void> delete(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> write(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> clearAllUserData() async {
    try {
      // Clear all data from secure storage
      // This will remove all stored keys including tokens and any other secure data
      await _secureStorage.deleteAll();
    } catch (e) {
      rethrow;
    }
  }
}
