import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppVersionService {
  static Future<String> getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return '${LocaleKeys.version.tr()} ${packageInfo.version} (${packageInfo.buildNumber})';
    } catch (e) {
      return 'Version Unknown';
    }
  }
}
