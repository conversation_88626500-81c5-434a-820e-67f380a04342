import 'package:maisour/shared/models/app_user.dart';

abstract interface class NotificationService {
  /// Initialize notification service with FCM and local notifications
  /// Sets up message handlers for different app states
  Future<void> initialize();

  /// Get FCM token for push notifications
  /// Returns null if token unavailable or permission denied
  Future<String?> getFCMToken();

  /// Send FCM token to backend
  /// Sends current FCM token to backend for push notification delivery
  /// Only sends if token is different from stored token
  Future<void> sendFCMTokenToBackend([AppUser? currentUser]);

  /// Send local notification (for iOS foreground display)
  /// Uses Firebase RemoteMessage data directly
  Future<void> sendLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  });

  /// Clear notification data (called on logout)
  /// Clears FCM token and any cached notification data
  Future<void> clearNotificationData();
}
