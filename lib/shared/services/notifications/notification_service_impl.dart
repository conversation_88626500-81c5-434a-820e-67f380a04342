import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:maisour/shared/services/notifications/notification_service.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:dio/dio.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';

/// Background message handler for Firebase Messaging
/// This must be a top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('📨 Background message received: ${message.notification?.title}');
  // Handle background message processing here if needed
}

final notificationServiceProvider = Provider<NotificationService>((ref) {
  final dio = ref.watch(networkServiceProvider);
  final appsFlyer = ref.watch(appsFlyerServiceProvider);
  return NotificationServiceImpl(dio, appsFlyer);
}, name: 'notificationServiceProvider');

class NotificationServiceImpl implements NotificationService {
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final Dio _dio;
  final AppsFlyerService _appsFlyerService;
  bool _isInitialized = false;
  String? _fcmToken;

  NotificationServiceImpl(this._dio, this._appsFlyerService);

  @override
  Future<void> initialize() async {
    try {
      debugPrint('🔔 Starting notification service initialization...');

      // Initialize local notifications first
      await _initializeLocalNotifications();

      // Initialize Firebase Messaging
      await _initializeFirebaseMessaging();

      // Set up message handlers
      _setupMessageHandlers();

      // Get FCM token
      await _getFCMTokenInternal();

      _isInitialized = true;
      debugPrint('✅ Notification service initialization completed');
    } catch (error) {
      debugPrint('❌ Notification service initialization failed: $error');
    }
  }

  Future<void> _initializeLocalNotifications() async {
    try {
      debugPrint('📱 Initializing local notifications...');

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: false, // We'll request manually
            requestBadgePermission: false,
            requestSoundPermission: false,
          );

      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      debugPrint('✅ Local notifications initialized');
    } catch (error) {
      debugPrint('❌ Error initializing local notifications: $error');
    }
  }

  Future<void> _initializeFirebaseMessaging() async {
    try {
      debugPrint('🔥 Initializing Firebase Messaging...');

      // Set background message handler
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

      // Configure foreground notification presentation options for iOS
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
            alert: true, // Show heads-up notification
            badge: true, // Update app badge
            sound: true, // Play sound
          );

      // Request permission for notifications
      final NotificationSettings settings = await FirebaseMessaging.instance
          .requestPermission(
            alert: true,
            badge: true,
            sound: true,
            provisional: false,
          );

      debugPrint(
        '📋 Firebase Messaging permission status: ${settings.authorizationStatus}',
      );

      debugPrint('✅ Firebase Messaging initialized');
    } catch (error) {
      debugPrint('❌ Error initializing Firebase Messaging: $error');
    }
  }

  void _setupMessageHandlers() {
    try {
      debugPrint('🔧 Setting up FCM message handlers...');

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background message taps
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessageTap);

      // Handle terminated app message taps
      FirebaseMessaging.instance.getInitialMessage().then((
        RemoteMessage? message,
      ) {
        if (message != null) {
          debugPrint('📱 App opened from terminated state via notification');
          _handleBackgroundMessageTap(message);
        }
      });

      debugPrint('✅ FCM message handlers configured');
    } catch (error) {
      debugPrint('❌ Error setting up message handlers: $error');
    }
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      debugPrint(
        '📨 Received foreground message: ${message.notification?.title}',
      );

      if (message.notification?.title != null) {
        await sendLocalNotification(
          title: message.notification?.title ?? '',
          body: message.notification?.body ?? '',
          data: message.data,
        );
      }
    } catch (error) {
      debugPrint('❌ Error handling foreground message: $error');
    }
  }

  void _handleBackgroundMessageTap(RemoteMessage message) {
    try {
      debugPrint('👆 Notification tapped: ${message.notification?.title}');
      // For now, just log the tap. No specific routing needed as per user requirements
      debugPrint('📱 App opened normally from notification tap');
    } catch (error) {
      debugPrint('❌ Error handling notification tap: $error');
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    try {
      debugPrint('👆 Local notification tapped: ${response.payload}');
      // For now, just log the tap. No specific routing needed as per user requirements
      debugPrint('📱 App opened normally from local notification tap');
    } catch (error) {
      debugPrint('❌ Error handling local notification tap: $error');
    }
  }

  Future<void> _getFCMTokenInternal() async {
    try {
      _fcmToken = await FirebaseMessaging.instance.getToken();
      if (_fcmToken != null) {
        debugPrint('🔑 FCM Token obtained: ${_fcmToken!.substring(0, 20)}...');
      } else {
        debugPrint('⚠️ FCM Token is null');
      }

      // Listen for token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen((String token) async {
        debugPrint('🔄 FCM Token refreshed: ${token.substring(0, 20)}...');
        _fcmToken = token;

        // Send updated token to backend
        await sendFCMTokenToBackend();
      });
    } catch (error) {
      debugPrint('❌ Error getting FCM token: $error');
    }
  }

  @override
  Future<String?> getFCMToken() async {
    if (!_isInitialized) {
      debugPrint('⚠️ Notification service not initialized');
      return null;
    }
    return _fcmToken;
  }

  @override
  Future<void> sendFCMTokenToBackend([AppUser? currentUser]) async {
    if (!_isInitialized) {
      debugPrint('⚠️ Notification service not initialized');
      return;
    }

    if (_fcmToken == null || _fcmToken!.isEmpty) {
      debugPrint('⚠️ FCM token is null or empty, skipping backend update');
      return;
    }

    // Check if current token is different from stored token
    if (currentUser == null) {
      debugPrint('⚠️ Current user is null, skipping FCM token update');
      return;
    }

    final storedToken = currentUser.firebaseDeviceTokens?.firebaseToken;

    if (storedToken == _fcmToken) {
      debugPrint(
        '🔄 FCM token is same as stored token, skipping backend update',
      );
      return;
    }

    try {
      debugPrint(
        '📤 Sending FCM token to backend: ${_fcmToken!.substring(0, 20)}...',
      );
      debugPrint(
        '🔄 Token changed: ${storedToken?.substring(0, 20) ?? 'null'} -> ${_fcmToken!.substring(0, 20)}',
      );

      // Update AppsFlyer uninstall token right before backend call as well
      if (Platform.isAndroid) {
        _appsFlyerService.updateUninstallToken(_fcmToken!);
      } else if (Platform.isIOS) {
        final apns = await FirebaseMessaging.instance.getAPNSToken();
        if (apns != null && apns.isNotEmpty) {
          _appsFlyerService.updateUninstallToken(apns);
        }
      }

      // Make direct HTTP request using Dio with query parameter
      final response = await _dio.post(
        ApiEndpoints.updateFcmToken,
        queryParameters: {'deviceToken': _fcmToken},
      );

      debugPrint('✅ FCM token successfully sent to backend');
      debugPrint('📊 Response status: ${response.statusCode}');
    } catch (error) {
      debugPrint('❌ Error sending FCM token to backend: $error');
      // Don't throw error to avoid breaking app flow
    }
  }

  @override
  Future<void> sendLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            'default_channel',
            'Default Notifications',
            channelDescription: 'Default notification channel',
            importance: Importance.max,
            priority: Priority.high,
          );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        platformChannelSpecifics,
        payload: data?.toString(),
      );

      debugPrint('📨 Local notification sent: $title');
    } catch (error) {
      debugPrint('❌ Error sending local notification: $error');
    }
  }

  @override
  Future<void> clearNotificationData() async {
    try {
      debugPrint('🧹 Clearing notification data...');

      // Clear FCM token
      _fcmToken = null;

      // Cancel all local notifications
      await _localNotifications.cancelAll();

      debugPrint('✅ Notification data cleared');
    } catch (error) {
      debugPrint('❌ Error clearing notification data: $error');
    }
  }
}
