import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/models/refresh_token_response.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/services/token/token_service.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage_impl.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage_const.dart';

final tokenServiceProvider = Provider.family<TokenService, Dio>((ref, dio) {
  final secureStorage = ref.watch(secureStorageProvider);
  return TokenServiceImpl(secureStorage, dio);
}, name: 'tokenServiceProvider');

class TokenServiceImpl implements TokenService {
  final SecureStorage _secureStorage;
  final Dio _dio;
  const TokenServiceImpl(this._secureStorage, this._dio);

  @override
  Future<void> clearTokens() {
    return Future.wait([
      _secureStorage.delete(SecureStorageKeys.accessTokenKey),
      _secureStorage.delete(SecureStorageKeys.refreshTokenKey),
    ]);
  }

  @override
  Future<String?> getAccessToken() =>
      _secureStorage.read(SecureStorageKeys.accessTokenKey);

  @override
  Future<String?> getRefreshToken() =>
      _secureStorage.read(SecureStorageKeys.refreshTokenKey);

  @override
  Future<RefreshTokenResponse> refreshToken(String? refreshToken) async {
    final response = await _dio.post(
      ApiEndpoints.refreshToken,
      data: {'refreshToken': refreshToken},
    );
    if (response.statusCode == HttpStatus.ok) {
      final result = RefreshTokenResponse.fromJson(response.data);
      return result;
    } else {
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
      );
    }
  }

  @override
  Future<void> saveToken(String accessToken, String refreshToken) {
    return Future.wait([
      _secureStorage.write(SecureStorageKeys.accessTokenKey, accessToken),
      _secureStorage.write(SecureStorageKeys.refreshTokenKey, refreshToken),
    ]);
  }

  @override
  Future<void> clearAccessToken() {
    return Future.wait([
      _secureStorage.delete(SecureStorageKeys.accessTokenKey),
    ]);
  }
}
