import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:maisour/shared/enums/social_media_platform.dart';
import 'package:maisour/shared/constants/app_constants.dart';

/// Common service for launching URLs, emails, phone calls, social media, app stores, etc.
///
/// Usage Examples:
/// ```dart
/// // Launch website
/// UrlLauncherService.launchURL('https://example.com');
///
/// // Launch social media with default username
/// UrlLauncherService.launchSocialMedia(SocialMediaPlatform.twitter);
///
/// // Launch social media with custom username
/// UrlLauncherService.launchSocialMedia(
///   SocialMediaPlatform.instagram,
///   customUsername: 'custom_handle'
/// );
///
/// // Launch WhatsApp with message
/// UrlLauncherService.launchWhatsApp('+1234567890', message: 'Hello!');
///
/// // Launch app store with default app ID (auto-detects platform)
/// UrlLauncherService.launchAppStore();
///
/// // Launch app store with custom app ID
/// UrlLauncherService.launchAppStore(appId: 'com.custom.app');
///
/// // Launch app store for review
/// UrlLauncherService.launchAppStoreForReview();
/// ```
class UrlLauncherService {
  /// Launch a web URL in browser
  static Future<bool> launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Could not launch URL: $url');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      return false;
    }
  }

  /// Launch email client
  static Future<bool> launchEmail(
    String email, {
    String? subject,
    String? body,
  }) async {
    try {
      String emailUrl = 'mailto:$email';
      if (subject != null || body != null) {
        emailUrl += '?';
        if (subject != null) {
          emailUrl += 'subject=${Uri.encodeComponent(subject)}';
        }
        if (subject != null && body != null) emailUrl += '&';
        if (body != null) emailUrl += 'body=${Uri.encodeComponent(body)}';
      }

      final Uri uri = Uri.parse(emailUrl);
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri);
      } else {
        debugPrint('Could not launch email: $email');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching email: $e');
      return false;
    }
  }

  /// Launch phone dialer
  static Future<bool> launchPhone(String phoneNumber) async {
    try {
      final Uri uri = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri);
      } else {
        debugPrint('Could not launch phone: $phoneNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching phone: $e');
      return false;
    }
  }

  /// Launch app store with optional custom app ID
  static Future<bool> launchAppStore() async {
    try {
      String storeUrl;
      if (MyPlatform.isIOS) {
        storeUrl = 'https://apps.apple.com/app/id${AppConstants.iosAppId}';
      } else {
        storeUrl =
            'https://play.google.com/store/apps/details?id=${AppConstants.androidPackageId}';
      }

      debugPrint('Launching app store: $storeUrl');
      return await launchURL(storeUrl);
    } catch (e) {
      debugPrint('Error launching app store: $e');
      return false;
    }
  }

  /// Launch social media profiles using enum
  /// [platform] - Social media platform from enum
  /// [customUsername] - Optional custom username, uses default if not provided
  static Future<bool> launchSocialMedia(SocialMediaPlatform platform) async {
    try {
      final socialUrl = platform.url;
      debugPrint('Launching ${platform.displayName}: $socialUrl');
      return await launchURL(socialUrl);
    } catch (e) {
      debugPrint('Error launching ${platform.displayName}: $e');
      return false;
    }
  }
}
