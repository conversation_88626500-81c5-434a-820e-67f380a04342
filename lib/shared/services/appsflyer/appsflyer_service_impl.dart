import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:maisour/config/app_environment.dart';
import 'package:maisour/shared/constants/appsflyer_events.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/permissions/permission_service.dart';
import 'package:maisour/shared/services/permissions/permission_service_impl.dart';

final appsFlyerServiceProvider = Provider<AppsFlyerService>((ref) {
  final permissionService = ref.watch(permissionServiceProvider);
  return AppsFlyerServiceImpl(permissionService);
}, name: 'appsFlyerServiceProvider');

class AppsFlyerServiceImpl implements AppsFlyerService {
  final PermissionService _permissionService;
  AppsflyerSdk? _appsflyerSdk;
  bool _isInitialized = false;
  bool _trackingEnabled = false;

  AppsFlyerServiceImpl(this._permissionService);

  @override
  Future<void> initialize() async {
    try {
      debugPrint('🚀 Starting AppsFlyer initialization...');

      // Check App Tracking Transparency permission for iOS
      if (Platform.isIOS) {
        await _requestTrackingPermission();
      } else {
        // Android doesn't need ATT permission
        _trackingEnabled = true;
        debugPrint('📱 Android device - tracking enabled by default');
      }

      // Initialize AppsFlyer SDK regardless of tracking permission
      // This allows basic functionality even without tracking
      await _initializeAppsFlyerSDK();

      debugPrint('✅ AppsFlyer initialization completed');
      debugPrint('📊 Tracking enabled: $_trackingEnabled');
    } catch (error) {
      debugPrint('❌ AppsFlyer initialization failed: $error');
    }
  }

  Future<void> _requestTrackingPermission() async {
    try {
      debugPrint(
        '🍎 iOS device - checking App Tracking Transparency permission...',
      );

      // Check if ATT permission is already granted
      final isAlreadyGranted = await _permissionService
          .isAppTrackingTransparencyGranted();

      if (isAlreadyGranted) {
        debugPrint('✅ ATT permission already granted - tracking enabled');
        _trackingEnabled = true;
      } else {
        debugPrint('❓ ATT permission not granted - requesting permission...');

        // Request permission from user using PermissionService
        _trackingEnabled = await _permissionService
            .requestAppTrackingTransparency();

        if (_trackingEnabled) {
          debugPrint('✅ ATT permission granted - tracking enabled');
        } else {
          debugPrint('❌ ATT permission denied - limited tracking');
        }
      }

      if (_trackingEnabled) {
        debugPrint(
          '✅ iOS App Tracking Transparency: AUTHORIZED - tracking enabled',
        );
      } else {
        debugPrint(
          '❌ iOS App Tracking Transparency: DENIED - limited tracking',
        );
      }
    } catch (error) {
      debugPrint('❌ Error requesting ATT permission: $error');
      _trackingEnabled = false;
    }
  }

  Future<void> _initializeAppsFlyerSDK() async {
    try {
      // Configure AppsFlyer options
      final AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
        afDevKey: AppEnvironment.appsFlyerDevKey,
        appId: Platform.isIOS ? AppEnvironment.appsFlyerAppId : '',
        showDebug: kDebugMode, // Show debug logs in debug mode
        timeToWaitForATTUserAuthorization: 60, // Wait 60 seconds for ATT
        disableAdvertisingIdentifier:
            false, // Disable if no tracking permission
        disableCollectASA: false, // Disable Apple Search Ads if no permission
        manualStart: true,
      );

      debugPrint('🔧 AppsFlyer Options configured:');
      debugPrint('   Dev Key: ${AppEnvironment.appsFlyerDevKey}');
      debugPrint(
        '   App ID: ${Platform.isIOS ? AppEnvironment.appsFlyerAppId : 'N/A (Android)'}',
      );
      debugPrint('   Debug Mode: $kDebugMode');
      debugPrint('   Tracking Enabled: $_trackingEnabled');

      // Initialize SDK
      _appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

      // Set up event listeners (optional)
      _setupEventListeners();

      // Initialize the SDK
      await _appsflyerSdk!.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );

      // Start the SDK
      _appsflyerSdk!.startSDK();

      _isInitialized = true;
      debugPrint('✅ AppsFlyer SDK initialized and started successfully');
    } catch (error) {
      debugPrint('❌ Error initializing AppsFlyer SDK: $error');
      _isInitialized = false;
    }
  }

  void _setupEventListeners() {
    if (_appsflyerSdk == null) return;

    // Conversion data callback
    _appsflyerSdk!.onInstallConversionData((data) {
      debugPrint('📊 AppsFlyer Install Conversion Data: $data');
    });

    // App open attribution callback
    _appsflyerSdk!.onAppOpenAttribution((data) {
      debugPrint('📱 AppsFlyer App Open Attribution: $data');
    });

    // Deep linking callback
    _appsflyerSdk!.onDeepLinking((data) {
      debugPrint('🔗 AppsFlyer Deep Link: $data');
    });
  }

  @override
  Future<void> setCustomerUserId(String userId) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('⚠️ AppsFlyer not initialized - cannot set customer user ID');
      return;
    }

    try {
      _appsflyerSdk!.setCustomerUserId(userId);
      debugPrint('✅ AppsFlyer customer user ID set: $userId');
    } catch (error) {
      debugPrint('❌ Error setting AppsFlyer customer user ID: $error');
    }
  }

  @override
  Future<void> clearCustomerUserId() async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint(
        '⚠️ AppsFlyer not initialized - cannot clear customer user ID',
      );
      return;
    }

    try {
      _appsflyerSdk!.setCustomerUserId('');
      debugPrint('✅ AppsFlyer customer user ID cleared');
    } catch (error) {
      debugPrint('❌ Error clearing AppsFlyer customer user ID: $error');
    }
  }

  @override
  Future<bool> isTrackingEnabled() async {
    return _trackingEnabled;
  }

  @override
  Future<void> logEvent(
    String eventName, {
    Map<String, dynamic>? eventValues,
  }) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint('⚠️ AppsFlyer not initialized - cannot log event: $eventName');
      return;
    }

    if (!_trackingEnabled) {
      debugPrint('⚠️ Tracking disabled - skipping event: $eventName');
      return;
    }

    try {
      await _appsflyerSdk!.logEvent(
        eventName,
        eventValues ?? <String, dynamic>{},
      );
      debugPrint(
        '📊 AppsFlyer event logged: $eventName with values: ${eventValues ?? {}}',
      );
    } catch (error) {
      debugPrint('❌ Error logging AppsFlyer event: $error');
    }
  }

  @override
  Future<void> logScreenView(String screenName) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint(
        '⚠️ AppsFlyer not initialized - cannot log screen view: $screenName',
      );
      return;
    }

    if (!_trackingEnabled) {
      debugPrint('⚠️ Tracking disabled - skipping screen view: $screenName');
      return;
    }

    try {
      // Log screen view as a custom event
      await _appsflyerSdk!.logEvent(AppsFlyerEvents.screenView, {
        'screen_name': screenName,
      });
      debugPrint('📱 AppsFlyer screen view logged: $screenName');
    } catch (error) {
      debugPrint('❌ Error logging AppsFlyer screen view: $error');
    }
  }

  @override
  Future<void> updateUninstallToken(String token) async {
    if (!_isInitialized || _appsflyerSdk == null) {
      debugPrint(
        '⚠️ AppsFlyer not initialized - cannot update uninstall token',
      );
      return;
    }

    if (token.isEmpty) {
      debugPrint('⚠️ Empty uninstall token - skipping update');
      return;
    }

    try {
      _appsflyerSdk!.updateServerUninstallToken(token);
      debugPrint('✅ AppsFlyer uninstall token updated');
    } catch (error) {
      debugPrint('❌ Error updating AppsFlyer uninstall token: $error');
    }
  }
}
