abstract interface class AppsFlyerService {
  /// Initialize AppsFlyer SDK with proper iOS App Tracking Transparency handling
  Future<void> initialize();

  /// Set customer user ID for better attribution tracking
  Future<void> setCustomerUserId(String userId);

  /// Clear customer user ID (called on logout)
  Future<void> clearCustomerUserId();

  /// Check if tracking is enabled (iOS App Tracking Transparency status)
  Future<bool> isTrackingEnabled();

  /// Log custom event
  Future<void> logEvent(String eventName, {Map<String, dynamic>? eventValues});

  /// Log screen view event for navigation tracking
  Future<void> logScreenView(String screenName);

  /// Update uninstall token with AppsFlyer (FCM on Android, APNs on iOS)
  Future<void> updateUninstallToken(String token);
}
