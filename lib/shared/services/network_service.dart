import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/app_environment.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/utils/dio_interceptor.dart';
import 'package:maisour/shared/services/network_service_interceptor.dart';

final networkServiceProvider = Provider<Dio>((ref) {
  final options = BaseOptions(
    baseUrl: AppEnvironment.baseUrl,
    connectTimeout: Duration(seconds: ApiConfig.connectTimeout),
    receiveTimeout: Duration(seconds: ApiConfig.receiveTimeout),
    sendTimeout: Duration(seconds: ApiConfig.sendTimeout),
    headers: {
      'Content-Type': ApiConfig.contentType,
      'Accept': ApiConfig.accept,
    },
  );

  final dio = Dio(options);

  final networkServiceInterceptor = ref.watch(
    networkServiceInterceptorProvider(dio),
  );

  dio.interceptors.addAll([
    networkServiceInterceptor,
    if (kDebugMode) AwesomeDioInterceptor(),
  ]);

  return dio;
}, name: 'networkServiceProvider');
