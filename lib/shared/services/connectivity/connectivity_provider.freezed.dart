// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'connectivity_provider.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$ConnectivityState {

 List<ConnectivityResult> get connectivityResults; bool get hasInternet; bool get isChecking; bool get hasError;
/// Create a copy of ConnectivityState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConnectivityStateCopyWith<ConnectivityState> get copyWith => _$ConnectivityStateCopyWithImpl<ConnectivityState>(this as ConnectivityState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConnectivityState&&const DeepCollectionEquality().equals(other.connectivityResults, connectivityResults)&&(identical(other.hasInternet, hasInternet) || other.hasInternet == hasInternet)&&(identical(other.isChecking, isChecking) || other.isChecking == isChecking)&&(identical(other.hasError, hasError) || other.hasError == hasError));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(connectivityResults),hasInternet,isChecking,hasError);

@override
String toString() {
  return 'ConnectivityState(connectivityResults: $connectivityResults, hasInternet: $hasInternet, isChecking: $isChecking, hasError: $hasError)';
}


}

/// @nodoc
abstract mixin class $ConnectivityStateCopyWith<$Res>  {
  factory $ConnectivityStateCopyWith(ConnectivityState value, $Res Function(ConnectivityState) _then) = _$ConnectivityStateCopyWithImpl;
@useResult
$Res call({
 List<ConnectivityResult> connectivityResults, bool hasInternet, bool isChecking, bool hasError
});




}
/// @nodoc
class _$ConnectivityStateCopyWithImpl<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  _$ConnectivityStateCopyWithImpl(this._self, this._then);

  final ConnectivityState _self;
  final $Res Function(ConnectivityState) _then;

/// Create a copy of ConnectivityState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? connectivityResults = null,Object? hasInternet = null,Object? isChecking = null,Object? hasError = null,}) {
  return _then(_self.copyWith(
connectivityResults: null == connectivityResults ? _self.connectivityResults : connectivityResults // ignore: cast_nullable_to_non_nullable
as List<ConnectivityResult>,hasInternet: null == hasInternet ? _self.hasInternet : hasInternet // ignore: cast_nullable_to_non_nullable
as bool,isChecking: null == isChecking ? _self.isChecking : isChecking // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [ConnectivityState].
extension ConnectivityStatePatterns on ConnectivityState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConnectivityState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConnectivityState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConnectivityState value)  $default,){
final _that = this;
switch (_that) {
case _ConnectivityState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConnectivityState value)?  $default,){
final _that = this;
switch (_that) {
case _ConnectivityState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<ConnectivityResult> connectivityResults,  bool hasInternet,  bool isChecking,  bool hasError)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConnectivityState() when $default != null:
return $default(_that.connectivityResults,_that.hasInternet,_that.isChecking,_that.hasError);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<ConnectivityResult> connectivityResults,  bool hasInternet,  bool isChecking,  bool hasError)  $default,) {final _that = this;
switch (_that) {
case _ConnectivityState():
return $default(_that.connectivityResults,_that.hasInternet,_that.isChecking,_that.hasError);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<ConnectivityResult> connectivityResults,  bool hasInternet,  bool isChecking,  bool hasError)?  $default,) {final _that = this;
switch (_that) {
case _ConnectivityState() when $default != null:
return $default(_that.connectivityResults,_that.hasInternet,_that.isChecking,_that.hasError);case _:
  return null;

}
}

}

/// @nodoc


class _ConnectivityState implements ConnectivityState {
  const _ConnectivityState({final  List<ConnectivityResult> connectivityResults = const [ConnectivityResult.none], this.hasInternet = false, this.isChecking = false, this.hasError = false}): _connectivityResults = connectivityResults;
  

 final  List<ConnectivityResult> _connectivityResults;
@override@JsonKey() List<ConnectivityResult> get connectivityResults {
  if (_connectivityResults is EqualUnmodifiableListView) return _connectivityResults;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_connectivityResults);
}

@override@JsonKey() final  bool hasInternet;
@override@JsonKey() final  bool isChecking;
@override@JsonKey() final  bool hasError;

/// Create a copy of ConnectivityState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConnectivityStateCopyWith<_ConnectivityState> get copyWith => __$ConnectivityStateCopyWithImpl<_ConnectivityState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConnectivityState&&const DeepCollectionEquality().equals(other._connectivityResults, _connectivityResults)&&(identical(other.hasInternet, hasInternet) || other.hasInternet == hasInternet)&&(identical(other.isChecking, isChecking) || other.isChecking == isChecking)&&(identical(other.hasError, hasError) || other.hasError == hasError));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_connectivityResults),hasInternet,isChecking,hasError);

@override
String toString() {
  return 'ConnectivityState(connectivityResults: $connectivityResults, hasInternet: $hasInternet, isChecking: $isChecking, hasError: $hasError)';
}


}

/// @nodoc
abstract mixin class _$ConnectivityStateCopyWith<$Res> implements $ConnectivityStateCopyWith<$Res> {
  factory _$ConnectivityStateCopyWith(_ConnectivityState value, $Res Function(_ConnectivityState) _then) = __$ConnectivityStateCopyWithImpl;
@override @useResult
$Res call({
 List<ConnectivityResult> connectivityResults, bool hasInternet, bool isChecking, bool hasError
});




}
/// @nodoc
class __$ConnectivityStateCopyWithImpl<$Res>
    implements _$ConnectivityStateCopyWith<$Res> {
  __$ConnectivityStateCopyWithImpl(this._self, this._then);

  final _ConnectivityState _self;
  final $Res Function(_ConnectivityState) _then;

/// Create a copy of ConnectivityState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? connectivityResults = null,Object? hasInternet = null,Object? isChecking = null,Object? hasError = null,}) {
  return _then(_ConnectivityState(
connectivityResults: null == connectivityResults ? _self._connectivityResults : connectivityResults // ignore: cast_nullable_to_non_nullable
as List<ConnectivityResult>,hasInternet: null == hasInternet ? _self.hasInternet : hasInternet // ignore: cast_nullable_to_non_nullable
as bool,isChecking: null == isChecking ? _self.isChecking : isChecking // ignore: cast_nullable_to_non_nullable
as bool,hasError: null == hasError ? _self.hasError : hasError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
