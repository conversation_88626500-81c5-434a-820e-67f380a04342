import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

/// Service for handling internet connectivity detection and monitoring
///
/// This service provides methods to:
/// - Monitor connectivity changes via stream
/// - Check current connectivity status
/// - Verify actual internet access by pinging server
/// - Determine if device can reach the application server
class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  final Dio _dio;

  ConnectivityService(this._dio);

  /// Stream for monitoring connectivity changes
  /// Returns a stream of ConnectivityResult that updates when connectivity changes
  Stream<List<ConnectivityResult>> get connectivityStream =>
      _connectivity.onConnectivityChanged;

  /// Check current connectivity status
  /// Returns the current ConnectivityResult (wifi, mobile, ethernet, none, etc.)
  Future<List<ConnectivityResult>> getCurrentConnectivity() =>
      _connectivity.checkConnectivity();

  /// Check if device has internet connection
  /// This method first checks connectivity status, then verifies actual internet access
  Future<bool> hasInternetConnection() async {
    final connectivityResults = await getCurrentConnectivity();

    // If no connectivity at all, return false immediately
    if (connectivityResults.contains(ConnectivityResult.none)) {
      return false;
    }

    // If connected to wifi/mobile, verify actual internet access
    return await canReachServer();
  }

  /// Verify actual internet access by attempting to reach the server
  /// This method makes a lightweight request to verify server connectivity
  Future<bool> canReachServer() async {
    try {
      // Use a lightweight HEAD request to check server connectivity
      // Timeout quickly to avoid long waits
      final response = await _dio.head(
        '/',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          // Don't follow redirects for faster response
          followRedirects: false,
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      // Accept any response that's not a server error (< 500)
      return response.statusCode != null && response.statusCode! < 500;
    } catch (e) {
      // Any exception means we can't reach the server
      return false;
    }
  }

  /// Check if device can reach any internet service (fallback check)
  /// This method tries to reach a reliable external service as a fallback
  Future<bool> canReachInternet() async {
    try {
      // Try to reach Google's DNS as a fallback internet check
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Comprehensive internet check that combines connectivity and reachability
  /// This is the most reliable method to determine internet availability
  Future<bool> hasReliableInternetConnection() async {
    final connectivityResults = await getCurrentConnectivity();

    // No connectivity at all
    if (connectivityResults.contains(ConnectivityResult.none)) {
      return false;
    }

    // Try to reach our server first
    if (await canReachServer()) {
      return true;
    }

    // Fallback to general internet check
    return await canReachInternet();
  }
}
