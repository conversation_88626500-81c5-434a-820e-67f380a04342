import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/services/connectivity/connectivity_service.dart';
import 'package:maisour/shared/services/network_service.dart';

part 'connectivity_provider.freezed.dart';

/// State model for connectivity status
///
/// This model tracks:
/// - Current connectivity result (wifi, mobile, none, etc.)
/// - Whether device has actual internet access
/// - Whether connectivity check is in progress
@freezed
abstract class ConnectivityState with _$ConnectivityState {
  const factory ConnectivityState({
    @Default([ConnectivityResult.none])
    List<ConnectivityResult> connectivityResults,
    @Default(false) bool hasInternet,
    @Default(false) bool isChecking,
    @Default(false) bool hasError,
  }) = _ConnectivityState;
}

/// Notifier for managing connectivity state
///
/// This notifier:
/// - Monitors connectivity changes via stream
/// - Performs internet reachability checks
/// - Updates state reactively when connectivity changes
/// - Provides methods for manual connectivity checks
class ConnectivityNotifier extends StateNotifier<ConnectivityState> {
  final ConnectivityService _connectivityService;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _debounceTimer;

  ConnectivityNotifier(this._connectivityService)
    : super(const ConnectivityState()) {
    _initializeConnectivity();
    _listenToConnectivityChanges();
  }

  /// Initialize connectivity state on startup
  Future<void> _initializeConnectivity() async {
    state = state.copyWith(isChecking: true, hasError: false);

    try {
      final hasInternet = await _connectivityService
          .hasReliableInternetConnection();
      final connectivityResults = await _connectivityService
          .getCurrentConnectivity();

      state = state.copyWith(
        hasInternet: hasInternet,
        connectivityResults: connectivityResults,
        isChecking: false,
        hasError: false,
      );
    } catch (e) {
      state = state.copyWith(
        isChecking: false,
        hasError: true,
        hasInternet: false,
      );
    }
  }

  /// Listen to connectivity changes and update state
  void _listenToConnectivityChanges() {
    _connectivitySubscription = _connectivityService.connectivityStream.listen((
      results,
    ) {
      // Debounce rapid connectivity changes
      _debounceTimer?.cancel();
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        _handleConnectivityChange(results);
      });
    });
  }

  /// Handle connectivity change with internet verification
  Future<void> _handleConnectivityChange(
    List<ConnectivityResult> results,
  ) async {
    state = state.copyWith(
      connectivityResults: results,
      isChecking: true,
      hasError: false,
    );

    try {
      // If no connectivity, don't bother checking internet
      if (results.contains(ConnectivityResult.none)) {
        state = state.copyWith(hasInternet: false, isChecking: false);
        return;
      }

      // Verify actual internet access
      final hasInternet = await _connectivityService
          .hasReliableInternetConnection();

      state = state.copyWith(hasInternet: hasInternet, isChecking: false);
    } catch (e) {
      state = state.copyWith(
        isChecking: false,
        hasError: true,
        hasInternet: false,
      );
    }
  }

  /// Manually refresh connectivity status
  /// This can be called from UI when user taps retry
  Future<void> refreshConnectivity() async {
    await _initializeConnectivity();
  }

  /// Check if device has any form of connectivity (not necessarily internet)
  bool get hasConnectivity =>
      !state.connectivityResults.contains(ConnectivityResult.none);

  /// Check if device is connected via WiFi
  bool get isConnectedViaWifi =>
      state.connectivityResults.contains(ConnectivityResult.wifi);

  /// Check if device is connected via mobile data
  bool get isConnectedViaMobile =>
      state.connectivityResults.contains(ConnectivityResult.mobile);

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// Global connectivity service provider
/// This provider creates the ConnectivityService instance
/// Uses existing networkServiceProvider for Dio instance
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return ConnectivityService(dio);
}, name: 'connectivityServiceProvider');

/// Global connectivity state provider
/// This provider manages the connectivity state throughout the app
/// Does NOT use autoDispose since connectivity monitoring should persist
final connectivityProvider =
    StateNotifierProvider<ConnectivityNotifier, ConnectivityState>((ref) {
      final connectivityService = ref.watch(connectivityServiceProvider);
      return ConnectivityNotifier(connectivityService);
    }, name: 'connectivityProvider');
