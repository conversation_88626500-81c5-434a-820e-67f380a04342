import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/services/logout/logout_service.dart';
import 'package:maisour/shared/services/firebase_crashlytics_service.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage.dart';
import 'package:maisour/shared/services/secure_storage/secure_storage_impl.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/modules/auth/api/auth_api.dart';
import 'package:maisour/modules/auth/providers/auth_api_provider.dart';

final logoutServiceProvider = Provider<LogoutService>((ref) {
  final crashlyticsService = ref.watch(crashlyticsServiceProvider);
  final sharedPrefsManager = ref.watch(sharedPrefsManagerProvider);
  final secureStorage = ref.watch(secureStorageProvider);
  final appsFlyerService = ref.watch(appsFlyerServiceProvider);
  final userNotifier = ref.watch(userProvider.notifier);
  final authApi = ref.watch(authApiProvider);

  return LogoutServiceImpl(
    crashlyticsService,
    sharedPrefsManager,
    secureStorage,
    appsFlyerService,
    userNotifier,
    authApi,
  );
}, name: 'logoutServiceProvider');

class LogoutServiceImpl implements LogoutService {
  final CrashlyticsService _crashlyticsService;
  final SharedPrefsManager _sharedPrefsManager;
  final SecureStorage _secureStorage;
  final AppsFlyerService _appsFlyerService;
  final UserNotifier _userNotifier;
  final AuthApi _authApi;

  LogoutServiceImpl(
    this._crashlyticsService,
    this._sharedPrefsManager,
    this._secureStorage,
    this._appsFlyerService,
    this._userNotifier,
    this._authApi,
  );

  @override
  Future<void> performComprehensiveLogout() async {
    try {
      debugPrint('🚀 Starting comprehensive logout cleanup...');

      // Call logout API first to clear backend session
      try {
        await _authApi.logout();
        debugPrint('✅ Backend logout successful');
      } catch (error) {
        debugPrint('⚠️ Backend logout failed: $error');
        // Continue with local cleanup even if backend logout fails
      }

      // Clear user state first
      _userNotifier.clearUserState();

      // Perform all cleanup operations in parallel for better performance
      final cleanupFutures = <Future<void>>[
        _crashlyticsService.clearUserIdentification(),
        _sharedPrefsManager.clearUserData(),
        _secureStorage.clearAllUserData(),
        _appsFlyerService.clearCustomerUserId(),
      ];

      await Future.wait(cleanupFutures);

      debugPrint('✅ Comprehensive logout completed - all user data cleared');
    } catch (error) {
      debugPrint('❌ Error during logout cleanup: $error');
      // Ensure user state is cleared even if some cleanup fails
      _userNotifier.clearUserState();
      rethrow; // Re-throw to let caller handle the error
    }
  }
}
