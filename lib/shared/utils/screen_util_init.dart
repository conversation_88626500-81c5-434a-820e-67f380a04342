import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ScreenUtilInitWrapper extends StatelessWidget {
  final Widget child;
  final bool forceIPhoneSize;

  const ScreenUtilInitWrapper({
    required this.child,
    this.forceIPhoneSize = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: forceIPhoneSize
          ? const Size(500, 850)
          : ScreenUtil.defaultSize,
      minTextAdapt: true,
      splitScreenMode: true,
      child: child,
    );
  }
}
