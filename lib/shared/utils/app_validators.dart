import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

/// Common validation class for all form fields
/// Provides reusable validation methods for consistent validation across the app
class AppValidators {
  // Private constructor to prevent instantiation
  AppValidators._();

  static String? required(String field, String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(namedArgs: {'field': field});
    }

    return null;
  }

  /// Email validation
  /// Returns error message if invalid, null if valid
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.email.tr()},
      );
    }

    // Remove whitespace
    value = value.trim();

    // Email regex pattern
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');

    if (!emailRegex.hasMatch(value)) {
      return LocaleKeys.enterValidEmail.tr();
    }

    return null;
  }

  /// Password validation
  /// Returns error message if invalid, null if valid
  static String? password(String? value, {int minLength = 8}) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.password.tr()},
      );
    }

    if (value.length < minLength) {
      return LocaleKeys.passwordMinLength.tr(
        namedArgs: {'minLength': minLength.toString()},
      );
    }

    return null;
  }

  static Map<String, bool> checkPasswordStrength(String password) {
    return {
      'hasMinLength': password.length >= 8,
      'hasNumber': RegExp(r'[0-9]').hasMatch(password),
      'hasSpecialChar': RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
      'hasUppercase': RegExp(r'[A-Z]').hasMatch(password),
      'hasLowercase': RegExp(r'[a-z]').hasMatch(password),
    };
  }

  /// Strong password validation (with requirements)
  /// Returns error message if invalid, null if valid
  static String? strongPassword(String? value, {int minLength = 8}) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.password.tr()},
      );
    }

    final passwordRegExp = RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$',
    );
    if (!passwordRegExp.hasMatch(value)) {
      return '';
    }
    return null;
  }

  static String? tinNumber(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.tinNumber.tr()},
      );
    }
    final trimmedValue = value.trim();

    if (trimmedValue.length < 10 || trimmedValue.length > 15) {
      return LocaleKeys.tinNumberLength.tr();
    }
    return null;
  }

  static String? otp(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.otpCode.tr()},
      );
    }
    if (value.length != 5) {
      return LocaleKeys.otpCodeLength.tr();
    }
    return null;
  }

  static String? bankName(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.bankName.tr()},
      );
    }
    if (value.length > 70) {
      return LocaleKeys.bankNameLength.tr();
    }
    if (!RegExp(r"^[A-Za-z0-9\s\.\-']{1,70}$").hasMatch(value)) {
      return LocaleKeys.invalidBankName.tr();
    }
    return null;
  }

  static String? accountNumber(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.accountNumber.tr()},
      );
    }

    final number = value.trim();

    // Length check: most banks use 6–20 digits
    if (number.length < 6 || number.length > 20) {
      return LocaleKeys.accountNumberLength.tr();
    }
    return null;
  }

  static String? iban(String? value) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.iban.tr()},
      );
    }
    if (value.length < 15 || value.length > 34) {
      return LocaleKeys.ibanLength.tr();
    }
    if (!RegExp(r'^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$').hasMatch(value)) {
      return LocaleKeys.invalidIban.tr();
    }
    return null;
  }

  static String? swiftCode(String? value) {
    if (value == null || value.isEmpty) {
      return null;
    }
    final swift = value.trim().toUpperCase();

    if (swift.length != 8 && swift.length != 11) {
      return LocaleKeys.swiftCodeLength.tr();
    }
    final regex = RegExp(r'^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$');
    if (!regex.hasMatch(swift)) {
      return LocaleKeys.invalidSwiftCode.tr();
    }
    return null;
  }

  static String? confirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return LocaleKeys.fieldRequired.tr(
        namedArgs: {'field': LocaleKeys.confirmPassword.tr()},
      );
    }
    if (value != password) {
      return LocaleKeys.passwordDoNotMatch.tr();
    }
    return null;
  }
}
