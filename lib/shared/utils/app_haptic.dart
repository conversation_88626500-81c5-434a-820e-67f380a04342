import 'package:flutter/services.dart';

/// Utility class for triggering haptic feedback for different app states.
class AppHaptic {
  /// Call for success actions (e.g., login success)
  static void success() {
    HapticFeedback.lightImpact();
  }

  /// Call for error actions (e.g., login failed, form error)
  static void error() {
    HapticFeedback.heavyImpact();
  }

  /// Call for warning actions (e.g., caution, minor issues)
  static void warning() {
    HapticFeedback.mediumImpact();
  }
}
