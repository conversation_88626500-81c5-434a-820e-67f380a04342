import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

/// Helper class to get localized error messages based on errorKey
class ErrorMessageHelper {
  /// Get localized error message based on errorKey
  /// If errorKey is null or not recognized, returns the fallback message
  static (String, String) getLocalizedErrorMessage({
    String? errorKey,
    required String fallbackMessage,
  }) {
    if (errorKey == null || errorKey.isEmpty) {
      return (LocaleKeys.oops.tr(), fallbackMessage);
    }

    switch (errorKey) {
      case 'invalidEmail':
        return (LocaleKeys.oops.tr(), LocaleKeys.enterValidEmail.tr());

      case 'domainNotAllowed':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.domainNotAllowed.tr(
            namedArgs: {
              'phone': AppConstants.supportPhone,
              'email': AppConstants.supportEmail,
            },
          ),
        );

      case 'accountDisabled':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.accountDisabled.tr(
            namedArgs: {'email': AppConstants.supportEmail},
          ),
        );

      case 'accountClosed':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.accountClosed.tr(
            namedArgs: {'email': AppConstants.supportEmail},
          ),
        );

      case 'notActivated':
        return (LocaleKeys.oops.tr(), LocaleKeys.accountNotActivated.tr());

      case 'accountSuspended':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.accountSuspended.tr(
            namedArgs: {'email': AppConstants.supportEmail},
          ),
        );

      case 'invalidCredentials':
        return (LocaleKeys.oops.tr(), LocaleKeys.invalidCredentials.tr());

      case 'duplicateEmail':
        return (LocaleKeys.oops.tr(), LocaleKeys.duplicateEmail.tr());

      case 'invalidReferralCode':
        return (LocaleKeys.oops.tr(), LocaleKeys.invalidReferralCode.tr());

      case 'duplicatePhone':
        return (LocaleKeys.oops.tr(), LocaleKeys.phoneNumberAlreadyInUse.tr());

      case 'limit_exceeded':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.codeGenerationLimitExceeded.tr(),
        );

      case 'wait_minute':
        return (LocaleKeys.oops.tr(), LocaleKeys.waitMinuteForNewCode.tr());

      case 'invalid_otp':
        return (LocaleKeys.oops.tr(), LocaleKeys.invalidVerificationCode.tr());

      case 'minInvestError':
        return (LocaleKeys.oops.tr(), LocaleKeys.minInvestError.tr());

      case 'walletError':
        return (LocaleKeys.oops.tr(), LocaleKeys.notEnoughBalance.tr());

      case 'amountNotEnough':
        return (LocaleKeys.oops.tr(), LocaleKeys.maxInvestError.tr());

      case 'yearlyLimitExceeded':
        return (LocaleKeys.oops.tr(), LocaleKeys.yearlyLimitExceeded.tr());

      case 'propertyInvestmentLimitExceeded':
        return (
          LocaleKeys.oops.tr(),
          LocaleKeys.propertyInvestmentLimitExceeded.tr(),
        );

      case 'noEnoughCredit':
        return (LocaleKeys.oops.tr(), LocaleKeys.noEnoughCredit.tr());

      case 'duplicateDocument':
        return (LocaleKeys.oops.tr(), LocaleKeys.duplicateDocumentName.tr());

      default:
        // If errorKey is not recognized, return fallback message
        return (LocaleKeys.oops.tr(), fallbackMessage);
    }
  }
}
