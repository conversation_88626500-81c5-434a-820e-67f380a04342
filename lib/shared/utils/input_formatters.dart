import 'package:flutter/services.dart';

class InputFormatters {
  static List<TextInputFormatter> maxLengthFormatters({int maxLength = 20}) {
    return [LengthLimitingTextInputFormatter(maxLength)];
  }

  static List<TextInputFormatter> emailFormatters({int maxLength = 50}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9@._\-+]')),
    ];
  }

  static List<TextInputFormatter> passwordFormatters({int maxLength = 20}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(
        RegExp(r'[a-zA-Z0-9!@#\$%^&*()_+{}\[\]:;<>,.?~\-=/\\|]'),
      ),
    ];
  }

  static List<TextInputFormatter> fullNameFormatters({int maxLength = 50}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z\s'-]")),
    ];
  }

  static List<TextInputFormatter> referralCodeFormatters({int maxLength = 10}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
    ];
  }

  static List<TextInputFormatter> hearAboutUsFormatters({int maxLength = 30}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z0-9\s&.,'’-]")),
    ];
  }

  static List<TextInputFormatter> tinFormatters({int maxLength = 15}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')),
    ];
  }

  static List<TextInputFormatter> bankNameFormatters({int maxLength = 70}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z0-9\s\.\-']")),
    ];
  }

  static List<TextInputFormatter> accountNumberFormatters({
    int maxLength = 20,
  }) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.digitsOnly,
    ];
  }

  static List<TextInputFormatter> ibanFormatters({int maxLength = 34}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
      UpperCaseTextFormatter(),
    ];
  }

  static List<TextInputFormatter> swiftCodeFormatters({int maxLength = 11}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
      UpperCaseTextFormatter(),
    ];
  }

  static List<TextInputFormatter> fileNameFormatters({int maxLength = 30}) {
    return [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_\-.]')),
    ];
  }
}

/// Custom formatter to force uppercase input
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return newValue.copyWith(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
