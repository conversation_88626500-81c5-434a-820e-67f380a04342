import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart' as easy_localization;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:toastification/toastification.dart';
import 'package:maisour/shared/utils/app_haptic.dart';

/// Modern toast notification utility using toastification package.
/// Provides beautiful, customizable toast notifications with animations.
class AppToastNotification {
  static void success(
    BuildContext context,
    String message, {
    String? description,
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppHaptic.success();
    _showToast(
      context: context,
      message: message,
      description: description,
      type: ToastificationType.success,
      icon: Assets.icons.successFilled.svg(height: 36.w),
      duration: duration,
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onTap: onTap,
    );
  }

  static void error(
    BuildContext context,
    String message, {
    String? description,
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppHaptic.error();
    _showToast(
      context: context,
      message: message,
      description: description,
      type: ToastificationType.error,
      icon: Assets.icons.failed.svg(height: 36.w),
      duration: duration,
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onTap: onTap,
    );
  }

  static void warning(
    BuildContext context,
    String message, {
    String? description,
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppHaptic.warning();
    _showToast(
      context: context,
      message: message,
      description: description,
      type: ToastificationType.warning,
      icon: Assets.icons.warning.svg(height: 36.w),
      duration: duration,
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onTap: onTap,
    );
  }

  static void info(
    BuildContext context,
    String message, {
    String? description,
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppHaptic.warning(); // Use warning haptic for info
    _showToast(
      context: context,
      message: message,
      description: description,
      type: ToastificationType.info,
      icon: Assets.icons.warning.svg(height: 36.w),
      duration: duration,
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onTap: onTap,
    );
  }

  /// Show custom toast with full control over appearance
  static void custom({
    required BuildContext context,
    required String message,
    String? description,
    required ToastificationType type,
    required Widget icon,
    Duration? duration,
    Alignment? alignment,
    ToastificationStyle? style,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    _showToast(
      context: context,
      message: message,
      description: description,
      type: type,
      icon: icon,
      duration: duration,
      alignment: alignment,
      style: style,
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onTap: onTap,
    );
  }

  /// Internal method to show toast with toastification
  static void _showToast({
    required BuildContext context,
    required String message,
    String? description,
    required ToastificationType type,
    required Widget icon,
    Duration? duration,
    Alignment? alignment,
    ToastificationStyle? style,
    TextDirection? textDirection,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    toastification.show(
      context: context,
      type: type,
      style: style ?? ToastificationStyle.flat,
      title: Text(message, style: AppTextStyles.text16.bold.danger900),
      description: description != null
          ? Text(description, style: AppTextStyles.text12.medium.dark300)
          : null,
      icon: icon,
      autoCloseDuration: duration ?? const Duration(seconds: 3),
      alignment: alignment ?? Alignment.topCenter,
      direction: textDirection ?? TextDirection.ltr,
      animationDuration: const Duration(milliseconds: 300),
      // animationBuilder: (context, animation, alignment, child) {
      //   return SlideTransition(
      //     position: animation.drive(
      //       Tween(
      //         begin: const Offset(0, 1),
      //         end: Offset.zero,
      //       ).chain(CurveTween(curve: Curves.easeOut)),
      //     ),
      //     child: child,
      //   );
      // },
      borderRadius: BorderRadius.circular(12.r),
      // boxShadow: const [
      //   BoxShadow(
      //     color: Color(0x07000000),
      //     blurRadius: 16,
      //     offset: Offset(0, 16),
      //     spreadRadius: 0,
      //   ),
      // ],
      closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
      // showProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      dragToClose: true,
      callbacks: ToastificationCallbacks(onTap: onTap),
    );
  }
}
