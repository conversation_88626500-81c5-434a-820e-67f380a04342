import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

mixin DioExceptionMapper {
  Failure mapDioExceptionToFailure(DioException e, StackTrace stackTrace) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.sendTimeout:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.receiveTimeout:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.badCertificate:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.badResponse:
        final data = e.response?.data;
        final statusCode = e.response?.statusCode;
        String message = LocaleKeys.somethingWentWrong.tr();
        String? errorKey;

        if (data is Map<String, dynamic>) {
          // Extract errorKey for UI-level localization
          errorKey = data['errorKey'];

          // Extract fallback message from backend
          message =
              data['detail'] ??
              data['title'] ??
              data['message'] ??
              LocaleKeys.somethingWentWrong.tr();
        } else if (data is String) {
          message = data.isNotEmpty ? data : LocaleKeys.somethingWentWrong.tr();
        }

        return Failure(
          message: message,
          errorKey: errorKey,
          statusCode: statusCode,
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.cancel:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.connectionError:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
      case DioExceptionType.unknown:
        return Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: e,
          stackTrace: stackTrace,
        );
    }
  }
}
