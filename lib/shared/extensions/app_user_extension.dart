import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/employment_status.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/shared/models/app_user.dart';

/// Extension to provide currency conversion functionality for AppUser
extension AppUserCurrencyExtension on AppUser {
  /// Converts a value from AED to the user's selected currency
  /// [value] - The amount in AED (base currency)
  /// [decimalDigits] - Number of decimal places (defaults to 2 if null)
  /// Returns formatted string with currency symbol and converted amount
  String getCurrencyValue(double value, {int? decimalDigits}) {
    switch (currencyCode) {
      case Currency.aed:
        return currencyCode.formatCurrency(value, decimalDigits: decimalDigits);
      case Currency.usd:
        final aedToUsdRate = variableFields.aedToUsd;
        final rate = double.tryParse(aedToUsdRate);
        if (rate != null) {
          return currencyCode.formatCurrency(
            value * rate,
            decimalDigits: decimalDigits,
          );
        }
        return currencyCode.formatCurrency(value, decimalDigits: decimalDigits);
      case Currency.eur:
        final aedToEurRate = variableFields.aedToEur;
        final rate = double.tryParse(aedToEurRate);
        if (rate != null) {
          return currencyCode.formatCurrency(
            value * rate,
            decimalDigits: decimalDigits,
          );
        }
        return currencyCode.formatCurrency(value, decimalDigits: decimalDigits);
    }
  }

  /// Checks if the currency uses an image symbol (like AED dirham image)
  bool get usesImageSymbol {
    return currencyCode == Currency.aed;
  }

  bool get canInvest =>
      verifiedStatus == VerifiedStatus.verified &&
      (accountStatus == AccountStatus.onboarded ||
          accountStatus == AccountStatus.investors);

  double get balance => credit + rewardPoints;

  double convertedValue(double value) {
    switch (currencyCode) {
      case Currency.aed:
        return value;

      case Currency.usd:
        final aedToUsdRate = variableFields.aedToUsd;
        final rate = double.tryParse(aedToUsdRate) ?? 0;
        return value * rate;

      case Currency.eur:
        final aedToEurRate = variableFields.aedToEur;
        final rate = double.tryParse(aedToEurRate) ?? 0;
        return value * rate;
    }
  }

  ({bool isExpired, int daysRemaining}) get passportExpirationStatus {
    if (documentExpirationDate != null) {
      final daysDifference = documentExpirationDate!
          .difference(DateTime.now().toUtc())
          .inDays;
      return (isExpired: daysDifference < 0, daysRemaining: daysDifference);
    }
    return (isExpired: false, daysRemaining: 100);
  }

  /// Check if user is employed
  bool get isEmployed => workStatus == EmploymentStatus.employed;
}
