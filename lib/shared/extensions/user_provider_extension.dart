import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/models/app_user.dart';

/// Extension on WidgetRef for easy access to user data
///
/// Usage Examples:
/// ```dart
/// // Get current user
/// final user = ref.currentUser;
/// final appUser = ref.currentAppUser;
///
/// // Check user status
/// final isLoggedIn = ref.isUserLoggedIn;
/// final hasPhone = ref.userHasPhoneNumber;
///
/// // Get user info
/// final userName = ref.userDisplayName;
/// final userEmail = ref.userEmail;
/// final userInitials = ref.userInitials;
///
/// // Update user info
/// ref.updateUserBasicInfo(fullName: 'New Name');
/// ref.updateUserExtendedInfo(phoneNumber: '+**********');
/// ```
extension UserProviderExtension on WidgetRef {
  /// Get current app user (AppUser?)
  AppUser? get watchCurrentUser => watch(userProvider);

  /// Get current app user (AppUser?)
  AppUser? get readCurrentUser => read(userProvider);

  /// Get user notifier for updates
  UserNotifier get notifierCurrentUser => read(userProvider.notifier);

  /// Set user data (called after login)
  void updateCurrentUser(AppUser appUser) {
    notifierCurrentUser.updateUser(appUser);
  }

  /// Clear user state (lightweight operation)
  void clearUserState() {
    notifierCurrentUser.clearUserState();
  }
}
