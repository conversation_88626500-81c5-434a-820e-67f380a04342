import 'package:flutter/material.dart';
import 'package:toastification/toastification.dart';
import 'package:maisour/shared/utils/app_toast_notification.dart';

/// Context extension for easy toast notifications
///
/// Usage Examples:
/// ```dart
/// // Success toast
/// context.showSuccessToast('Login successful!');
///
/// // Success toast with description
/// context.showSuccessToast(
///   'Login successful!',
///   description: 'Welcome back to Maisour',
/// );
///
/// // Error toast
/// context.showErrorToast('Login failed. Please try again.');
///
/// // Error toast with description
/// context.showErrorToast(
///   'Account Closed',
///   description: 'Your account has been closed. Please contact support.',
/// );
///
/// // Warning toast
/// context.showWarningToast('Please check your internet connection.');
///
/// // Info toast
/// context.showInfoToast('New update available.');
///
/// // Custom toast
/// context.showCustomToast(
///   message: 'Custom message',
///   description: 'Additional details here',
///   type: ToastificationType.success,
///   icon: Icons.star,
///   duration: Duration(seconds: 5),
/// );
/// ```
extension ContextToastExtension on BuildContext {
  /// Show success toast notification
  void showSuccessToast(
    String message,
    String description, {
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppToastNotification.success(
      this,
      message,
      description: description,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show error toast notification
  void showErrorToast(
    String message,
    String description, {
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppToastNotification.error(
      this,
      message,
      description: description,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show warning toast notification
  void showWarningToast(
    String message,
    String description, {
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppToastNotification.warning(
      this,
      message,
      description: description,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show info toast notification
  void showInfoToast(
    String message,
    String description, {
    Duration? duration,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppToastNotification.info(
      this,
      message,
      description: description,
      duration: duration,
      onTap: onTap,
    );
  }

  /// Show custom toast notification with full control
  void showCustomToast({
    required String message,
    String? description,
    required ToastificationType type,
    required Widget icon,
    Duration? duration,
    Alignment? alignment,
    ToastificationStyle? style,
    ValueChanged<ToastificationItem>? onTap,
  }) {
    AppToastNotification.custom(
      context: this,
      message: message,
      description: description,
      type: type,
      icon: icon,
      duration: duration,
      alignment: alignment,
      style: style,
      onTap: onTap,
    );
  }
}
