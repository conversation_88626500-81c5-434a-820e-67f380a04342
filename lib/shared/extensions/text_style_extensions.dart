import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_colors.dart';

/// Extension for applying AppColors to TextStyle easily
/// Usage: Text('Hello', style: TextStyle().primary500)
extension AppTextStyleColorExtension on TextStyle {
  // Primary color shades
  TextStyle get primary100 => copyWith(color: AppColors.primary.shade100);
  TextStyle get primary200 => copyWith(color: AppColors.primary.shade200);
  TextStyle get primary300 => copyWith(color: AppColors.primary.shade300);
  TextStyle get primary400 => copyWith(color: AppColors.primary.shade400);
  TextStyle get primary500 => copyWith(color: AppColors.primary.shade500);
  TextStyle get primary600 => copyWith(color: AppColors.primary.shade600);
  TextStyle get primary700 => copyWith(color: AppColors.primary.shade700);
  TextStyle get primary800 => copyWith(color: AppColors.primary.shade800);
  TextStyle get primary900 => copyWith(color: AppColors.primary.shade900);

  // Convenience getter for primary color (most commonly used)
  TextStyle get primary => copyWith(color: AppColors.primary);

  // Secondary color shades
  TextStyle get secondary100 => copyWith(color: AppColors.secondary.shade100);
  TextStyle get secondary200 => copyWith(color: AppColors.secondary.shade200);
  TextStyle get secondary300 => copyWith(color: AppColors.secondary.shade300);
  TextStyle get secondary400 => copyWith(color: AppColors.secondary.shade400);
  TextStyle get secondary500 => copyWith(color: AppColors.secondary.shade500);
  TextStyle get secondary600 => copyWith(color: AppColors.secondary.shade600);
  TextStyle get secondary700 => copyWith(color: AppColors.secondary.shade700);
  TextStyle get secondary800 => copyWith(color: AppColors.secondary.shade800);
  TextStyle get secondary900 => copyWith(color: AppColors.secondary.shade900);

  // Convenience getter for secondary color
  TextStyle get secondary => copyWith(color: AppColors.secondary);

  // Tertiary color shades
  TextStyle get tertiary100 => copyWith(color: AppColors.tertiary.shade100);
  TextStyle get tertiary200 => copyWith(color: AppColors.tertiary.shade200);
  TextStyle get tertiary300 => copyWith(color: AppColors.tertiary.shade300);
  TextStyle get tertiary400 => copyWith(color: AppColors.tertiary.shade400);
  TextStyle get tertiary500 => copyWith(color: AppColors.tertiary.shade500);
  TextStyle get tertiary600 => copyWith(color: AppColors.tertiary.shade600);
  TextStyle get tertiary700 => copyWith(color: AppColors.tertiary.shade700);
  TextStyle get tertiary800 => copyWith(color: AppColors.tertiary.shade800);
  TextStyle get tertiary900 => copyWith(color: AppColors.tertiary.shade900);

  // Convenience getter for tertiary color
  TextStyle get tertiary => copyWith(color: AppColors.tertiary);

  // Dark color shades
  TextStyle get dark100 => copyWith(color: AppColors.dark.shade100);
  TextStyle get dark200 => copyWith(color: AppColors.dark.shade200);
  TextStyle get dark300 => copyWith(color: AppColors.dark.shade300);
  TextStyle get dark400 => copyWith(color: AppColors.dark.shade400);
  TextStyle get dark500 => copyWith(color: AppColors.dark.shade500);
  TextStyle get dark600 => copyWith(color: AppColors.dark.shade600);
  TextStyle get dark700 => copyWith(color: AppColors.dark.shade700);
  TextStyle get dark800 => copyWith(color: AppColors.dark.shade800);
  TextStyle get dark900 => copyWith(color: AppColors.dark.shade900);

  // Convenience getter for dark color
  TextStyle get dark => copyWith(color: AppColors.dark);

  // Light color shades
  TextStyle get light100 => copyWith(color: AppColors.light.shade100);
  TextStyle get light200 => copyWith(color: AppColors.light.shade200);
  TextStyle get light300 => copyWith(color: AppColors.light.shade300);
  TextStyle get light400 => copyWith(color: AppColors.light.shade400);
  TextStyle get light500 => copyWith(color: AppColors.light.shade500);
  TextStyle get light600 => copyWith(color: AppColors.light.shade600);
  TextStyle get light700 => copyWith(color: AppColors.light.shade700);
  TextStyle get light800 => copyWith(color: AppColors.light.shade800);
  TextStyle get light900 => copyWith(color: AppColors.light.shade900);

  // Convenience getter for light color
  TextStyle get light => copyWith(color: AppColors.light);

  // Gray color shades
  TextStyle get gray100 => copyWith(color: AppColors.gray.shade100);
  TextStyle get gray200 => copyWith(color: AppColors.gray.shade200);
  TextStyle get gray300 => copyWith(color: AppColors.gray.shade300);
  TextStyle get gray400 => copyWith(color: AppColors.gray.shade400);
  TextStyle get gray500 => copyWith(color: AppColors.gray.shade500);
  TextStyle get gray600 => copyWith(color: AppColors.gray.shade600);
  TextStyle get gray700 => copyWith(color: AppColors.gray.shade700);
  TextStyle get gray800 => copyWith(color: AppColors.gray.shade800);
  TextStyle get gray900 => copyWith(color: AppColors.gray.shade900);

  // Convenience getter for gray color
  TextStyle get gray => copyWith(color: AppColors.gray);

  // Status color shades
  TextStyle get danger100 => copyWith(color: AppColors.danger.shade100);
  TextStyle get danger200 => copyWith(color: AppColors.danger.shade200);
  TextStyle get danger300 => copyWith(color: AppColors.danger.shade300);
  TextStyle get danger400 => copyWith(color: AppColors.danger.shade400);
  TextStyle get danger500 => copyWith(color: AppColors.danger.shade500);
  TextStyle get danger600 => copyWith(color: AppColors.danger.shade600);
  TextStyle get danger700 => copyWith(color: AppColors.danger.shade700);
  TextStyle get danger800 => copyWith(color: AppColors.danger.shade800);
  TextStyle get danger900 => copyWith(color: AppColors.danger.shade900);

  // Convenience getter for success color
  TextStyle get success => copyWith(color: AppColors.success);

  // Status color shades
  TextStyle get success100 => copyWith(color: AppColors.success.shade100);
  TextStyle get success200 => copyWith(color: AppColors.success.shade200);
  TextStyle get success300 => copyWith(color: AppColors.success.shade300);
  TextStyle get success400 => copyWith(color: AppColors.success.shade400);
  TextStyle get success500 => copyWith(color: AppColors.success.shade500);
  TextStyle get success600 => copyWith(color: AppColors.success.shade600);
  TextStyle get success700 => copyWith(color: AppColors.success.shade700);
  TextStyle get success800 => copyWith(color: AppColors.success.shade800);
  TextStyle get success900 => copyWith(color: AppColors.success.shade900);

  // Convenience getter for danger color
  TextStyle get danger => copyWith(color: AppColors.danger);

  // Warning color shades
  TextStyle get warning100 => copyWith(color: AppColors.warning.shade100);
  TextStyle get warning200 => copyWith(color: AppColors.warning.shade200);
  TextStyle get warning300 => copyWith(color: AppColors.warning.shade300);
  TextStyle get warning400 => copyWith(color: AppColors.warning.shade400);
  TextStyle get warning500 => copyWith(color: AppColors.warning.shade500);
  TextStyle get warning600 => copyWith(color: AppColors.warning.shade600);
  TextStyle get warning700 => copyWith(color: AppColors.warning.shade700);
  TextStyle get warning800 => copyWith(color: AppColors.warning.shade800);
  TextStyle get warning900 => copyWith(color: AppColors.warning.shade900);

  // Convenience getter for warning color
  TextStyle get warning => copyWith(color: AppColors.warning);

  // Info color shades
  TextStyle get info100 => copyWith(color: AppColors.info.shade100);
  TextStyle get info200 => copyWith(color: AppColors.info.shade200);
  TextStyle get info300 => copyWith(color: AppColors.info.shade300);
  TextStyle get info400 => copyWith(color: AppColors.info.shade400);
  TextStyle get info500 => copyWith(color: AppColors.info.shade500);
  TextStyle get info600 => copyWith(color: AppColors.info.shade600);
  TextStyle get info700 => copyWith(color: AppColors.info.shade700);
  TextStyle get info800 => copyWith(color: AppColors.info.shade800);
  TextStyle get info900 => copyWith(color: AppColors.info.shade900);

  // Convenience getter for info color
  TextStyle get info => copyWith(color: AppColors.info);

  // Background colors (using Colors.white since they're constants)
  TextStyle get white => copyWith(color: AppColors.white);
  TextStyle get black => copyWith(color: AppColors.black);
}

extension AppTextStyleExtension on TextStyle {
  TextStyle underline({Color? underlineColor}) => copyWith(
    decoration: TextDecoration.underline,
    decorationStyle: TextDecorationStyle.solid,
    decorationColor: underlineColor ?? AppColors.primary,
  );
}
