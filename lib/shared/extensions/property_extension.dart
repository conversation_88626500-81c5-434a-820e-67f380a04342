import 'package:maisour/modules/properties/models/property_feature.dart';
import 'package:maisour/modules/properties/models/property_image.dart';
import 'package:maisour/modules/properties/models/slim_property.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/property_details/models/property_manager.dart';
import 'package:maisour/modules/property_details/models/seller_information.dart';
import 'package:maisour/shared/enums/property_status.dart';

extension SlimPropertyExtension on SlimProperty {
  /// Returns the title based on current locale
  String getTitle(String languageCode) {
    return languageCode == 'ar' ? titleInArabic : titleInEnglish;
  }

  /// Returns the city based on current locale
  String getCity(String languageCode) {
    return languageCode == 'ar' ? cityInArabic : cityInEnglish;
  }

  /// Returns the country based on current locale
  String getCountry(String languageCode) {
    return languageCode == 'ar' ? countryInArabic : countryInEnglish;
  }

  /// Returns the expected 5 years net rent percentage
  String get expected5YearsNetRentPercentage =>
      (((rentAmount - propertyManagementFee - totalRentalOtherCost) /
                  totalPrice) *
              500)
          .toStringAsFixed(2);

  /// Returns the expected annual net rent percentage
  String get expectedAnnualNetRentPercentage =>
      (((rentAmount - propertyManagementFee - totalRentalOtherCost) /
                  totalPrice) *
              100)
          .toStringAsFixed(2);

  /// Returns true if the property was added in the last 14 days
  bool get isNewlyAdded => DateTime.now().difference(purchaseDate).inDays <= 14;

  /// Returns a sorted list of property images with cover image first
  List<PropertyImage> get sortedImagesWithCoverFirst {
    // Find cover image
    final coverImage = propertyImages.firstWhere(
      (image) => image.coverImage,
      orElse: () => propertyImages.first,
    );

    // Create new list with cover image first, then add all other images
    final sortedImages = <PropertyImage>[coverImage];
    sortedImages.addAll(propertyImages.where((image) => image != coverImage));

    return sortedImages;
  }
}

extension PropertyDetailsExtension on PropertyDetails {
  /// Returns a sorted list of property images with cover image first
  List<PropertyImage> get sortedImagesWithCoverFirst {
    // Find cover image
    final coverImage = propertyImages.firstWhere(
      (image) => image.coverImage,
      orElse: () => propertyImages.first,
    );

    // Create new list with cover image first, then add all other images
    final sortedImages = <PropertyImage>[coverImage];
    sortedImages.addAll(propertyImages.where((image) => image != coverImage));

    return sortedImages;
  }

  /// Returns the title based on current locale
  String getTitle(String languageCode) {
    return languageCode == 'ar' ? titleInArabic : titleInEnglish;
  }

  /// Returns the city based on current locale
  String getCity(String languageCode) {
    return languageCode == 'ar' ? cityInArabic : cityInEnglish;
  }

  /// Returns the country based on current locale
  String getCountry(String languageCode) {
    return languageCode == 'ar' ? countryInArabic : countryInEnglish;
  }

  String getAddress(String languageCode) {
    return languageCode == 'ar' ? addressInArabic : addressInEnglish;
  }

  String aboutProperty(String languageCode) {
    return languageCode == 'ar' ? aboutInArabic : aboutInEnglish;
  }

  bool get canInvest =>
      propertyStatus == PropertyStatus.live ||
      propertyStatus == PropertyStatus.comingSoon;

  double get funded => (totalSuccessfulInvestment / propertyAcquisition) * 100;

  double get annualGrossRent => (rentAmount / totalPrice) * 100;

  double get expectedAnnualNetRent =>
      (rentAmount - propertyManagementFee - totalRentalOtherCost);

  String get expectedAnnualNetRentPercentage =>
      (((rentAmount - propertyManagementFee - totalRentalOtherCost) /
                  totalPrice) *
              100)
          .toStringAsFixed(2);
}

extension PropertyFeatureExtension on PropertyFeature {
  /// Returns the feature name based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }
}

extension PropertySellerInformationExtension on SellerInformation {
  /// Returns the seller information name based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }

  /// Returns the seller information about based on current locale
  String getAbout(String languageCode) {
    return languageCode == 'ar' ? aboutInArabic : aboutInEnglish;
  }
}

extension PropertyManagerExtension on PropertyManager {
  /// Returns the manager name based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }

  /// Returns the manager about based on current locale
  String getAbout(String languageCode) {
    return languageCode == 'ar' ? aboutInArabic : aboutInEnglish;
  }
}

extension PropertyTransactionCostConstantExtension on TransactionCostConstant {
  /// Returns the title based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }

  bool get isPercentage => type == 'Precentage';
}

extension PropertyPurchaseCostConstantExtension on PurchaseCostConstant {
  /// Returns the title based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }

  bool get isPercentage => type == 'Precentage';
}

extension PropertyRentalOtherCostConstantExtension on RentalOtherCostConstant {
  /// Returns the title based on current locale
  String getName(String languageCode) {
    return languageCode == 'ar' ? nameInArabic : nameInEnglish;
  }

  bool get isPercentage => type == 'Precentage';
}
