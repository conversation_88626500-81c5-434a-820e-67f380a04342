import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';

/// Language state notifier to manage app language changes
class LanguageNotifier extends StateNotifier<Locale> {
  final SharedPrefsManager _prefsManager;

  LanguageNotifier(this._prefsManager)
    : super(Locale(AppConstants.defaultLanguage)) {
    _loadSavedLanguage();
  }

  /// Load saved language from SharedPreferences on app startup
  void _loadSavedLanguage() {
    try {
      final savedLanguage = _prefsManager.getSavedLanguage;
      state = Locale(savedLanguage);
    } catch (e) {
      debugPrint('Error loading saved language: $e');
    }
  }

  /// Change the app language and notify all listeners
  Future<void> changeLanguage(BuildContext context, String languageCode) async {
    try {
      final locale = Locale(languageCode);

      // Update easy_localization first
      await context.setLocale(locale);

      // Save language preference using SharedPrefsManager
      await _prefsManager.saveLanguage(languageCode);

      // Update our state to trigger rebuilds
      state = locale;

      debugPrint('Language changed to: $languageCode');
    } catch (e) {
      debugPrint('Error changing language: $e');
    }
  }

  /// Get current language code
  String get currentLanguageCode => state.languageCode;

  /// Get current language display name
  String get currentLanguageName =>
      AppConstants.languageNames[state.languageCode] ?? state.languageCode;
}

/// Provider for language management
final languageProvider =
    StateNotifierProvider.autoDispose<LanguageNotifier, Locale>((ref) {
      final prefsManager = ref.watch(sharedPrefsManagerProvider);
      return LanguageNotifier(prefsManager);
    }, name: 'languageProvider');

/// Provider to get current language code
final currentLanguageProvider = Provider.autoDispose<String>((ref) {
  return ref.watch(languageProvider).languageCode;
}, name: 'currentLanguageProvider');
