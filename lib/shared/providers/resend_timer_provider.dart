import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Resend timer state
class ResendTimerState {
  final bool canResend;
  final int countdown;
  final bool isActive;

  const ResendTimerState({
    this.canResend = true,
    this.countdown = 0,
    this.isActive = false,
  });

  ResendTimerState copyWith({bool? canResend, int? countdown, bool? isActive}) {
    return ResendTimerState(
      canResend: canResend ?? this.canResend,
      countdown: countdown ?? this.countdown,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Format countdown as MM:SS
  String get formattedTime {
    final minutes = countdown ~/ 60;
    final remainingSeconds = countdown % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

/// Resend timer notifier - reusable for forgot password, OTP, etc.
class ResendTimerNotifier extends StateNotifier<ResendTimerState> {
  Timer? _timer;
  final int _duration;

  ResendTimerNotifier({int duration = 60})
    : _duration = duration,
      super(const ResendTimerState());

  /// Start the countdown timer
  void startTimer() {
    // Cancel any existing timer
    _timer?.cancel();

    state = state.copyWith(
      canResend: false,
      countdown: _duration,
      isActive: true,
    );

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      final newCountdown = state.countdown - 1;

      if (newCountdown <= 0) {
        timer.cancel();
        state = state.copyWith(canResend: true, countdown: 0, isActive: false);
      } else {
        state = state.copyWith(countdown: newCountdown);
      }
    });
  }

  /// Stop the timer manually
  void stopTimer() {
    _timer?.cancel();
    state = state.copyWith(canResend: true, countdown: 0, isActive: false);
  }

  /// Reset timer to initial state
  void resetTimer() {
    _timer?.cancel();
    state = const ResendTimerState();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

/// Provider for forgot password resend timer (60 seconds)
final forgotPasswordTimerProvider =
    StateNotifierProvider.autoDispose<ResendTimerNotifier, ResendTimerState>((
      ref,
    ) {
      return ResendTimerNotifier(duration: 60);
    }, name: 'forgotPasswordTimerProvider');

/// Provider for OTP resend timer (60 seconds) - can be reused for mobile OTP
final otpResendTimerProvider =
    StateNotifierProvider.autoDispose<ResendTimerNotifier, ResendTimerState>((
      ref,
    ) {
      return ResendTimerNotifier(duration: 60);
    }, name: 'otpResendTimerProvider');
