import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/services/firebase_crashlytics_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';
import 'package:maisour/modules/auth/providers/auth_api_provider.dart';

/// Global user provider for accessing user data throughout the app
/// This provider manages the current user state and provides easy access to user information
final userProvider = StateNotifierProvider<UserNotifier, AppUser?>((ref) {
  final crashlyticsService = ref.watch(crashlyticsServiceProvider);
  final appsFlyerService = ref.watch(appsFlyerServiceProvider);
  return UserNotifier(ref, crashlyticsService, appsFlyerService);
}, name: 'userProvider');

class UserNotifier extends StateNotifier<AppUser?> {
  final Ref _ref;
  final CrashlyticsService _crashlyticsService;
  final AppsFlyerService _appsFlyerService;

  UserNotifier(this._ref, this._crashlyticsService, this._appsFlyerService)
    : super(null);

  /// Set user data (called after successful login)
  void setUser(AppUser appUser) {
    state = appUser;

    // Set user identification in Crashlytics for better error tracking
    _crashlyticsService.setUserIdentification(
      userId: appUser.id,
      email: appUser.user.email,
    );

    // Set customer user ID in AppsFlyer for better attribution tracking
    _appsFlyerService.setCustomerUserId(appUser.id.toString());
  }

  /// Update user profile information
  void updateUser(AppUser updatedAppUser) {
    state = updatedAppUser;
  }

  /// Refresh user from server using AuthApi and update state
  Future<void> refreshUserFromServer() async {
    final authApi = _ref.read(authApiProvider);
    final updatedUser = await authApi.getCurrentUser();
    updateUser(updatedUser);
  }

  /// Clear user state (called by LogoutService)
  /// This method only clears the user state, actual cleanup is handled by LogoutService
  void clearUserState() {
    state = null;
  }
}
