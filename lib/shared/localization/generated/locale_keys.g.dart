// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const deviceIsRooted = 'deviceIsRooted';
  static const deviceIsJailbroken = 'deviceIsJailbroken';
  static const deviceIsRootedDesc = 'deviceIsRootedDesc';
  static const deviceIsJailbrokenDesc = 'deviceIsJailbrokenDesc';
  static const cancel = 'cancel';
  static const welcomeTitle1 = 'welcomeTitle1';
  static const welcomeTitle2 = 'welcomeTitle2';
  static const welcomeTitle3 = 'welcomeTitle3';
  static const welcomeTitle4 = 'welcomeTitle4';
  static const welcomeDesc1 = 'welcomeDesc1';
  static const welcomeDesc2 = 'welcomeDesc2';
  static const welcomeDesc3 = 'welcomeDesc3';
  static const welcomeDesc4 = 'welcomeDesc4';
  static const startInvesting = 'startInvesting';
  static const maisourRegulatedDSFA = 'maisourRegulatedDSFA';
  static const somethingWentWrong = 'somethingWentWrong';
  static const later = 'later';
  static const updateNow = 'updateNow';
  static const updateMaisourApp = 'updateMaisourApp';
  static const updateRecommendationMessage = 'updateRecommendationMessage';
  static const noInternetConnection = 'noInternetConnection';
  static const noInternetDescription = 'noInternetDescription';
  static const tryAgain = 'tryAgain';
  static const checkingConnection = 'checkingConnection';
  static const connectionCheckFailed = 'connectionCheckFailed';
  static const connectedButNoInternet = 'connectedButNoInternet';
  static const getStarted = 'getStarted';
  static const loginSubtitle = 'loginSubtitle';
  static const login = 'login';
  static const continueText = 'continueText';
  static const orText = 'orText';
  static const continueWithApple = 'continueWithApple';
  static const continueWithGoogle = 'continueWithGoogle';
  static const byContinuingYouAccepting = 'byContinuingYouAccepting';
  static const termOfUse = 'termOfUse';
  static const privacyPolicy = 'privacyPolicy';
  static const email = 'email';
  static const password = 'password';
  static const forgotPassword = 'forgotPassword';
  static const didntReceiveEmail = 'didntReceiveEmail';
  static const resendIn = 'resendIn';
  static const sendResetLink = 'sendResetLink';
  static const resendResetLink = 'resendResetLink';
  static const fieldRequired = 'fieldRequired';
  static const enterValidEmail = 'enterValidEmail';
  static const welcomeBack = 'welcomeBack';
  static const passwordSubtitle = 'passwordSubtitle';
  static const passwordMinLength = 'passwordMinLength';
  static const forgotPasswordSubtitle = 'forgotPasswordSubtitle';
  static const yourAccountHasBeenClosed = 'yourAccountHasBeenClosed';
  static const accountClosedMessage = 'accountClosedMessage';
  static const goToWhatsapp = 'goToWhatsapp';
  static const emailRequired = 'emailRequired';
  static const emailRequiredMessage = 'emailRequiredMessage';
  static const oops = 'oops';
  static const success = 'success';
  static const domainNotAllowed = 'domainNotAllowed';
  static const accountDisabled = 'accountDisabled';
  static const accountClosed = 'accountClosed';
  static const accountNotActivated = 'accountNotActivated';
  static const accountSuspended = 'accountSuspended';
  static const invalidCredentials = 'invalidCredentials';
  static const forgotPasswordSuccess = 'forgotPasswordSuccess';
  static const createAccount = 'createAccount';
  static const createAccountWith = 'createAccountWith';
  static const fullName = 'fullName';
  static const fullNameHelperText = 'fullNameHelperText';
  static const referralCode = 'referralCode';
  static const hearAboutMaisour = 'hearAboutMaisour';
  static const pleaseSpecify = 'pleaseSpecify';
  static const passwordHelperText = 'passwordHelperText';
  static const characters = 'characters';
  static const uppercase = 'uppercase';
  static const lowercase = 'lowercase';
  static const number = 'number';
  static const specialCharacter = 'specialCharacter';
  static const verifyYourEmail = 'verifyYourEmail';
  static const anEmailSentTo = 'anEmailSentTo';
  static const checkYourInbox = 'checkYourInbox';
  static const resendEmail = 'resendEmail';
  static const backToLogin = 'backToLogin';
  static const search = 'search';
  static const others = 'others';
  static const duplicateEmail = 'duplicateEmail';
  static const emailSentSuccessfully = 'emailSentSuccessfully';
  static const emailVerificationLink = 'emailVerificationLink';
  static const invalidReferralCode = 'invalidReferralCode';
  static const properties = 'properties';
  static const portfolio = 'portfolio';
  static const wallet = 'wallet';
  static const account = 'account';
  static const live = 'live';
  static const comingSoon = 'comingSoon';
  static const funded = 'funded';
  static const sold = 'sold';
  static const request = 'request';
  static const cancelled = 'cancelled';
  static const verifyIdentityToStartInvestment = 'verifyIdentityToStartInvestment';
  static const verifyNow = 'verifyNow';
  static const onboardingTitle = 'onboardingTitle';
  static const onboardingSubtitle = 'onboardingSubtitle';
  static const verifyYourPassport = 'verifyYourPassport';
  static const verifyYourPassportDesc = 'verifyYourPassportDesc';
  static const verifyYourAddress = 'verifyYourAddress';
  static const verifyYourAddressDesc = 'verifyYourAddressDesc';
  static const employmentInfo = 'employmentInfo';
  static const employmentInfoDesc = 'employmentInfoDesc';
  static const taxResidencyInformation = 'taxResidencyInformation';
  static const taxResidencyInformationDesc = 'taxResidencyInformationDesc';
  static const completed = 'completed';
  static const pending = 'pending';
  static const startVerification = 'startVerification';
  static const doItLater = 'doItLater';
  static const verifyYourAccount = 'verifyYourAccount';
  static const verifyYourAccountDesc = 'verifyYourAccountDesc';
  static const verifyAccount = 'verifyAccount';
  static const passportSubtitle = 'passportSubtitle';
  static const passportAttempt = 'passportAttempt';
  static const passportVerification = 'passportVerification';
  static const addressVerification = 'addressVerification';
  static const verificationInProgressWait = 'verificationInProgressWait';
  static const verificationJourneyCancelled = 'verificationJourneyCancelled';
  static const verificationJourneyBlocked = 'verificationJourneyBlocked';
  static const verificationInProgress = 'verificationInProgress';
  static const verificationInProgressDesc = 'verificationInProgressDesc';
  static const tooManyAttempts = 'tooManyAttempts';
  static const tooManyAttemptsDesc = 'tooManyAttemptsDesc';
  static const passportVerificationCompleted = 'passportVerificationCompleted';
  static const passportVerificationCompletedDesc = 'passportVerificationCompletedDesc';
  static const addressVerificationCompleted = 'addressVerificationCompleted';
  static const addressVerificationCompletedDesc = 'addressVerificationCompletedDesc';
  static const passportVerificationFailed = 'passportVerificationFailed';
  static const passportVerificationFailedDesc = 'passportVerificationFailedDesc';
  static const viewDetails = 'viewDetails';
  static const possibleReasonsForVerificationFailure = 'possibleReasonsForVerificationFailure';
  static const possibleReasonsForVerificationFailureDesc = 'possibleReasonsForVerificationFailureDesc';
  static const addressVerificationDesc = 'addressVerificationDesc';
  static const employed = 'employed';
  static const retired = 'retired';
  static const student = 'student';
  static const unemployed = 'unemployed';
  static const employmentStatus = 'employmentStatus';
  static const companyName = 'companyName';
  static const role = 'role';
  static const workAddress = 'workAddress';
  static const annualIncome = 'annualIncome';
  static const lessThan10000 = 'lessThan10000';
  static const from10000To50000 = 'from10000To50000';
  static const from50000To100000 = 'from50000To100000';
  static const moreThan100000 = 'moreThan100000';
  static const industry = 'industry';
  static const countryOfBirth = 'countryOfBirth';
  static const countryOfBirthHelperText = 'countryOfBirthHelperText';
  static const submit = 'submit';
  static const employmentInfoSubmittedSuccessfully = 'employmentInfoSubmittedSuccessfully';
  static const taxResidencyInformationScreenDesc = 'taxResidencyInformationScreenDesc';
  static const countryOfResidence = 'countryOfResidence';
  static const doYouHaveATaxIdentificationNumber = 'doYouHaveATaxIdentificationNumber';
  static const areYouAlsoAResidentInAnyOtherCountry = 'areYouAlsoAResidentInAnyOtherCountry';
  static const additionalTaxResidencyCountry = 'additionalTaxResidencyCountry';
  static const byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge = 'byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge';
  static const taxResidencySubmittedSuccessfully = 'taxResidencySubmittedSuccessfully';
  static const yes = 'yes';
  static const no = 'no';
  static const notApplicable = 'notApplicable';
  static const tinNumber = 'tinNumber';
  static const tinNumberLength = 'tinNumberLength';
  static const verifyPhoneNumber = 'verifyPhoneNumber';
  static const confirmYourNumberToSecureYourAccount = 'confirmYourNumberToSecureYourAccount';
  static const sendVerificationCode = 'sendVerificationCode';
  static const phoneNumber = 'phoneNumber';
  static const enterValidPhoneNumber = 'enterValidPhoneNumber';
  static const accountVerificationPending = 'accountVerificationPending';
  static const accountVerificationPendingDesc = 'accountVerificationPendingDesc';
  static const phoneNumberAlreadyInUse = 'phoneNumberAlreadyInUse';
  static const codeGenerationLimitExceeded = 'codeGenerationLimitExceeded';
  static const waitMinuteForNewCode = 'waitMinuteForNewCode';
  static const verificationCodeSent = 'verificationCodeSent';
  static const enterVerificationCode = 'enterVerificationCode';
  static const enterThe5DigitCode = 'enterThe5DigitCode';
  static const didntReceiveCode = 'didntReceiveCode';
  static const resend = 'resend';
  static const verify = 'verify';
  static const otpCode = 'otpCode';
  static const otpCodeLength = 'otpCodeLength';
  static const invalidVerificationCode = 'invalidVerificationCode';
  static const codeVerifiedSuccessfully = 'codeVerifiedSuccessfully';
  static const phoneNumberVerifiedSuccessfully = 'phoneNumberVerifiedSuccessfully';
  static const accountVerificationFailed = 'accountVerificationFailed';
  static const accountVerificationFailedDesc = 'accountVerificationFailedDesc';
  static const contactUs = 'contactUs';
  static const newlyAdded = 'newlyAdded';
  static const trending = 'trending';
  static const expectedAnnualNetRent = 'expectedAnnualNetRent';
  static const expected5YearsNetRent = 'expected5YearsNetRent';
  static const expected5YearsNetReturns = 'expected5YearsNetReturns';
  static const aed = 'aed';
  static const usd = 'usd';
  static const eur = 'eur';
  static const weekly = 'weekly';
  static const monthly = 'monthly';
  static const quarterly = 'quarterly';
  static const biannually = 'biannually';
  static const annually = 'annually';
  static const years = 'years';
  static const noPropertiesAvailable = 'noPropertiesAvailable';
  static const noPropertiesAvailableDesc = 'noPropertiesAvailableDesc';
  static const sortBy = 'sortBy';
  static const propertyOldestToLatest = 'propertyOldestToLatest';
  static const fundingStatusHighestToLowest = 'fundingStatusHighestToLowest';
  static const investors = 'investors';
  static const annualGrossRent = 'annualGrossRent';
  static const minInvestment = 'minInvestment';
  static const rentDistribution = 'rentDistribution';
  static const suggestedHoldingPeriod = 'suggestedHoldingPeriod';
  static const aboutRentalIncome = 'aboutRentalIncome';
  static const aboutRentalIncomeDesc = 'aboutRentalIncomeDesc';
  static const investmentBreakdown = 'investmentBreakdown';
  static const purchase = 'purchase';
  static const rental = 'rental';
  static const purchaseCost = 'purchaseCost';
  static const transactionCost = 'transactionCost';
  static const totalAcquisitionCost = 'totalAcquisitionCost';
  static const grossRentPerYear = 'grossRentPerYear';
  static const serviceCharges = 'serviceCharges';
  static const otherCosts = 'otherCosts';
  static const numberOfShares = 'numberOfShares';
  static const totalPricePerShare = 'totalPricePerShare';
  static const guaranteedRentalIncome = 'guaranteedRentalIncome';
  static const aboutProperty = 'aboutProperty';
  static const readMore = 'readMore';
  static const amenities = 'amenities';
  static const viewAll = 'viewAll';
  static const exploreOnMap = 'exploreOnMap';
  static const chooseMapApp = 'chooseMapApp';
  static const appleMaps = 'appleMaps';
  static const googleMaps = 'googleMaps';
  static const documents = 'documents';
  static const contactSupport = 'contactSupport';
  static const doYouHaveQuestionsAboutTheProperty = 'doYouHaveQuestionsAboutTheProperty';
  static const developerAndManager = 'developerAndManager';
  static const investNow = 'investNow';
  static const preOrderNow = 'preOrderNow';
  static const noPaymentYet = 'noPaymentYet';
  static const preOrder = 'preOrder';
  static const yourInvestment = 'yourInvestment';
  static const financialDetails = 'financialDetails';
  static const amountToInvest = 'amountToInvest';
  static const walletBalance = 'walletBalance';
  static const yourWalletWillNotBeCharged = 'yourWalletWillNotBeCharged';
  static const pricePerShare = 'pricePerShare';
  static const investmentAmount = 'investmentAmount';
  static const potentialRewards = 'potentialRewards';
  static const totalCost = 'totalCost';
  static const minimum = 'minimum';
  static const placeOrder = 'placeOrder';
  static const howPreOrderWorks = 'howPreOrderWorks';
  static const showInterest = 'showInterest';
  static const noPaymentNeededJustShowInterested = 'noPaymentNeededJustShowInterested';
  static const getNotified = 'getNotified';
  static const weWillNotifyYouWhenThePropertyIsLive = 'weWillNotifyYouWhenThePropertyIsLive';
  static const confirmAndInvest = 'confirmAndInvest';
  static const reviewDetailsAndMakeYourPayment = 'reviewDetailsAndMakeYourPayment';
  static const notEnoughBalance = 'notEnoughBalance';
  static const yourOrder = 'yourOrder';
  static const shares = 'shares';
  static const totalInvestment = 'totalInvestment';
  static const youAgreeTo = 'youAgreeTo';
  static const investmentAgreement = 'investmentAgreement';
  static const proceedToPay = 'proceedToPay';
  static const actionRequired = 'actionRequired';
  static const pleaseAcceptTheInvestmentAgreementToContinue = 'pleaseAcceptTheInvestmentAgreementToContinue';
  static const agreeAndContinue = 'agreeAndContinue';
  static const minInvestError = 'minInvestError';
  static const maxInvestError = 'maxInvestError';
  static const yearlyLimitExceeded = 'yearlyLimitExceeded';
  static const propertyInvestmentLimitExceeded = 'propertyInvestmentLimitExceeded';
  static const investmentConfirmation = 'investmentConfirmation';
  static const interestConfirmation = 'interestConfirmation';
  static const paymentSuccessful = 'paymentSuccessful';
  static const interestSuccessful = 'interestSuccessful';
  static const viewPortfolio = 'viewPortfolio';
  static const investmentCalculator = 'investmentCalculator';
  static const expectedInvestmentReturnIn5Years = 'expectedInvestmentReturnIn5Years';
  static const investment = 'investment';
  static const returns = 'returns';
  static const totalRentalYield = 'totalRentalYield';
  static const valueAppreciation = 'valueAppreciation';
  static const totalInvestmentReturn = 'totalInvestmentReturn';
  static const grossInvestmentGain = 'grossInvestmentGain';
  static const initialInvestment = 'initialInvestment';
  static const propertyValueGrowth = 'propertyValueGrowth';
  static const expectedRentalYield = 'expectedRentalYield';
  static const myOwnerships = 'myOwnerships';
  static const cashBalance = 'cashBalance';
  static const topUp = 'topUp';
  static const rewardBalance = 'rewardBalance';
  static const noTransactionsYet = 'noTransactionsYet';
  static const thisIsWhereYourTransactionsWillBeListed = 'thisIsWhereYourTransactionsWillBeListed';
  static const transactions = 'transactions';
  static const bankAccounts = 'bankAccounts';
  static const safeTransfersIn23Days = 'safeTransfersIn23Days';
  static const addBankAccount = 'addBankAccount';
  static const itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName = 'itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName';
  static const addAccount = 'addAccount';
  static const yourMoneyIsProtected = 'yourMoneyIsProtected';
  static const thePropertiesAreRegisteredInDLD = 'thePropertiesAreRegisteredInDLD';
  static const weHaveAPlanForSafeKeepingAssets = 'weHaveAPlanForSafeKeepingAssets';
  static const actionRestricted = 'actionRestricted';
  static const pleaseVerifyYourAccount = 'pleaseVerifyYourAccount';
  static const yourIdentityVerificationIsUnderReview = 'yourIdentityVerificationIsUnderReview';
  static const yourPassportIsExpired = 'yourPassportIsExpired';
  static const yourPassportIsExpiringSoon = 'yourPassportIsExpiringSoon';
  static const asPerAmlRegulationsAccountRestricted = 'asPerAmlRegulationsAccountRestricted';
  static const purchasePrice = 'purchasePrice';
  static const currentValuation = 'currentValuation';
  static const investmentSummary = 'investmentSummary';
  static const ownershipPercentage = 'ownershipPercentage';
  static const timeline = 'timeline';
  static const investedInProperty = 'investedInProperty';
  static const interestShownInProperty = 'interestShownInProperty';
  static const dividendHistory = 'dividendHistory';
  static const dividendCredited = 'dividendCredited';
  static const failed = 'failed';
  static const reversed = 'reversed';
  static const refunded = 'refunded';
  static const successful = 'successful';
  static const rejected = 'rejected';
  static const notFunded = 'notFunded';
  static const removed = 'removed';
  static const purchaseDetails = 'purchaseDetails';
  static const amountInvested = 'amountInvested';
  static const status = 'status';
  static const cancelInvestment = 'cancelInvestment';
  static const deleteInvestment = 'deleteInvestment';
  static const confirm = 'confirm';
  static const invest = 'invest';
  static const update = 'update';
  static const all = 'all';
  static const withdrawal = 'withdrawal';
  static const deposit = 'deposit';
  static const dividend = 'dividend';
  static const reward = 'reward';
  static const saleProceed = 'saleProceed';
  static const otherAdjustment = 'otherAdjustment';
  static const filters = 'filters';
  static const last30Days = 'last30Days';
  static const last3Months = 'last3Months';
  static const last6Months = 'last6Months';
  static const primary = 'primary';
  static const accountName = 'accountName';
  static const bankName = 'bankName';
  static const bankNameLength = 'bankNameLength';
  static const invalidBankName = 'invalidBankName';
  static const accountNumber = 'accountNumber';
  static const accountNumberLength = 'accountNumberLength';
  static const invalidAccountNumber = 'invalidAccountNumber';
  static const country = 'country';
  static const iban = 'iban';
  static const ibanLength = 'ibanLength';
  static const invalidIban = 'invalidIban';
  static const swiftCode = 'swiftCode';
  static const swiftCodeLength = 'swiftCodeLength';
  static const invalidSwiftCode = 'invalidSwiftCode';
  static const primaryAccount = 'primaryAccount';
  static const bankAccountAddedSuccessfully = 'bankAccountAddedSuccessfully';
  static const bankAccountSetAsPrimarySuccessfully = 'bankAccountSetAsPrimarySuccessfully';
  static const bankAccountStatusUpdatedSuccessfully = 'bankAccountStatusUpdatedSuccessfully';
  static const bankAccountDeletedSuccessfully = 'bankAccountDeletedSuccessfully';
  static const cannotSetPrimaryBankAccountAsInactive = 'cannotSetPrimaryBankAccountAsInactive';
  static const deleteAccount = 'deleteAccount';
  static const areYouSureYouWantToDeleteThisBankAccount = 'areYouSureYouWantToDeleteThisBankAccount';
  static const delete = 'delete';
  static const withdraw = 'withdraw';
  static const addFunds = 'addFunds';
  static const topUpYourWallet = 'topUpYourWallet';
  static const allTransactionsInAED = 'allTransactionsInAED';
  static const amountInCurrency = 'amountInCurrency';
  static const selectTopUpMethod = 'selectTopUpMethod';
  static const bankTransfer = 'bankTransfer';
  static const debitCard = 'debitCard';
  static const take23DaysToComplete = 'take23DaysToComplete';
  static const more = 'more';
  static const copyReferenceNumber = 'copyReferenceNumber';
  static const bankTransferPending = 'bankTransferPending';
  static const yourTransferWasInitiated = 'yourTransferWasInitiated';
  static const howToTopUpYourWallet = 'howToTopUpYourWallet';
  static const copyAccountDetails = 'copyAccountDetails';
  static const useTheDisplayedBankInfoForYourTransfer = 'useTheDisplayedBankInfoForYourTransfer';
  static const makeThePayment = 'makeThePayment';
  static const completeTheTransferThroughYourBankingApp = 'completeTheTransferThroughYourBankingApp';
  static const waitForTheConfirmation = 'waitForTheConfirmation';
  static const weWillVerifyYourPaymentAndUpdateYourWallet = 'weWillVerifyYourPaymentAndUpdateYourWallet';
  static const depositAmount = 'depositAmount';
  static const referenceNumber = 'referenceNumber';
  static const done = 'done';
  static const payment = 'payment';
  static const fundsAddedSuccessfully = 'fundsAddedSuccessfully';
  static const withdrawCash = 'withdrawCash';
  static const payoutRequest = 'payoutRequest';
  static const selectBankAccount = 'selectBankAccount';
  static const withdrawalsProcessingTime = 'withdrawalsProcessingTime';
  static const profile = 'profile';
  static const myProfile = 'myProfile';
  static const updatePassword = 'updatePassword';
  static const referrals = 'referrals';
  static const sellProperties = 'sellProperties';
  static const withdrawalRequestSubmittedSuccessfully = 'withdrawalRequestSubmittedSuccessfully';
  static const memberSince = 'memberSince';
  static const personalDetails = 'personalDetails';
  static const notProvided = 'notProvided';
  static const edit = 'edit';
  static const employmentDetails = 'employmentDetails';
  static const noEnoughCredit = 'noEnoughCredit';
  static const toChangeYourFullNameOrEmail = 'toChangeYourFullNameOrEmail';
  static const contactTheAdmin = 'contactTheAdmin';
  static const oldPassword = 'oldPassword';
  static const newPassword = 'newPassword';
  static const confirmPassword = 'confirmPassword';
  static const passwordDoNotMatch = 'passwordDoNotMatch';
  static const save = 'save';
  static const passwordUpdatedSuccessfully = 'passwordUpdatedSuccessfully';
  static const noDocumentsFound = 'noDocumentsFound';
  static const uploadDocument = 'uploadDocument';
  static const fileTooLarge = 'fileTooLarge';
  static const pleaseSelectASmallerFile = 'pleaseSelectASmallerFile';
  static const invalidFileType = 'invalidFileType';
  static const pleaseSelectAPDFOrImageFile = 'pleaseSelectAPDFOrImageFile';
  static const fileName = 'fileName';
  static const upload = 'upload';
  static const fileUploadedSuccessfully = 'fileUploadedSuccessfully';
  static const duplicateDocumentName = 'duplicateDocumentName';
  static const preferences = 'preferences';
  static const appInfoSupport = 'appInfoSupport';
  static const logoutOptions = 'logoutOptions';
  static const language = 'language';
  static const selectLanguage = 'selectLanguage';
  static const selectYourPreferredLanguageItWillApplyThroughoutTheApp = 'selectYourPreferredLanguageItWillApplyThroughoutTheApp';
  static const selectCurrency = 'selectCurrency';
  static const currencyDescription = 'currencyDescription';
  static const unitedArabEmiratesDirham = 'unitedArabEmiratesDirham';
  static const unitedStatesDollar = 'unitedStatesDollar';
  static const euro = 'euro';
  static const currency = 'currency';
  static const notifications = 'notifications';
  static const ourStory = 'ourStory';
  static const getHelp = 'getHelp';
  static const shareApp = 'shareApp';
  static const hiHowCanWeHelp = 'hiHowCanWeHelp';
  static const helpCenter = 'helpCenter';
  static const raiseTicket = 'raiseTicket';
  static const getInTouch = 'getInTouch';
  static const chatWithUs = 'chatWithUs';
  static const frequentlyAskedQuestions = 'frequentlyAskedQuestions';
  static const noFaqsAvailable = 'noFaqsAvailable';
  static const message = 'message';
  static const supportRequestSubmittedSuccessfully = 'supportRequestSubmittedSuccessfully';
  static const shareAppDesc = 'shareAppDesc';
  static const closeAccount = 'closeAccount';
  static const logout = 'logout';
  static const areYouSureYouWantToLogout = 'areYouSureYouWantToLogout';
  static const reasonForClosingAccount = 'reasonForClosingAccount';
  static const deleteAccountActionRequired = 'deleteAccountActionRequired';
  static const yourAccountHasBeenSuccessfullyClosed = 'yourAccountHasBeenSuccessfullyClosed';
  static const followUs = 'followUs';
  static const version = 'version';
  static const completeYourOnboarding = 'completeYourOnboarding';
  static const toViewMoreDetailsPlease = 'toViewMoreDetailsPlease';
  static const yourAccountHasBeenSuspended = 'yourAccountHasBeenSuspended';
  static const countryOfBirthUpdatedSuccessfully = 'countryOfBirthUpdatedSuccessfully';
  static const addTaxDetails = 'addTaxDetails';
  static const add = 'add';

}
