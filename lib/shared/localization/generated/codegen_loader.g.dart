// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader{
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String,dynamic> _en = {
  "deviceIsRooted": "Your device might be rooted",
  "deviceIsJailbroken": "Your device might be jailbroken",
  "deviceIsRootedDesc": "Using a rooted device may pose serious secuirty risks and prevent <PERSON><PERSON><PERSON> from ensuring your online protection",
  "deviceIsJailbrokenDesc": "Using a jailbroken device may pose serious secuirty risks and prevent <PERSON><PERSON><PERSON> from ensuring your online protection",
  "cancel": "Cancel",
  "welcomeTitle1": "Invest in Dubai’s Growth",
  "welcomeTitle2": "Earn Monthly Returns",
  "welcomeTitle3": "Exclusive & Vetted Listings",
  "welcomeTitle4": "Smart & Simple Investing",
  "welcomeDesc1": "Tap into one of the world’s most dynamic real estate markets—securely, from anywhere.",
  "welcomeDesc2": "Get guaranteed rental income with fully managed, fractional property investments.",
  "welcomeDesc3": "We shortlist only the top-performing assets so you invest with confidence, not complexity.",
  "welcomeDesc4": "Start investing with ease, track your portfolio, and grow your wealth with just a few taps.",
  "startInvesting": "Start Investing",
  "maisourRegulatedDSFA": "Maisour is regulated by DFSA",
  "somethingWentWrong": "Something went wrong. Please try again.",
  "later": "Later",
  "updateNow": "Update Now",
  "updateMaisourApp": "Update Maisour App",
  "updateRecommendationMessage": "Maisour recommends you to update the app to the latest version for better performance and security.",
  "noInternetConnection": "No Internet Connection",
  "noInternetDescription": "Please check your network settings and try again.",
  "tryAgain": "Try Again",
  "checkingConnection": "Checking connection...",
  "connectionCheckFailed": "Connection check failed",
  "connectedButNoInternet": "Connected to {connectionType} but no internet access",
  "getStarted": "Get Started",
  "loginSubtitle": "Please enter your email address to continue",
  "login": "Login",
  "continueText": "Continue",
  "orText": "or",
  "continueWithApple": "Continue with Apple",
  "continueWithGoogle": "Continue with Google",
  "byContinuingYouAccepting": "By continuing you accepting",
  "termOfUse": "Term of Use",
  "privacyPolicy": "Privacy Policy",
  "email": "Email",
  "password": "Password",
  "forgotPassword": "Forgot Password?",
  "didntReceiveEmail": "Didn't receive the email?",
  "resendIn": "Resend in",
  "sendResetLink": "Send Reset Link",
  "resendResetLink": "Resend Reset Link",
  "fieldRequired": "{field} is required",
  "enterValidEmail": "Please enter a valid email address",
  "welcomeBack": "Welcome Back",
  "passwordSubtitle": "Enter the password for the account",
  "passwordMinLength": "Password must be at least {minLength} characters",
  "forgotPasswordSubtitle": "No worries! Enter your email address below and we'll send you a link to reset your password.",
  "yourAccountHasBeenClosed": "Your account has been closed",
  "accountClosedMessage": "Your Account Is Currently Closed. For Support, Please Reach out to us on Whatsapp.",
  "goToWhatsapp": "Go To Whatsapp",
  "emailRequired": "Email Required",
  "emailRequiredMessage": "We couldn’t retrieve your email from Apple Sign-In. Our app requires an email address to continue. Please use a different sign-in method or provide a valid email to proceed.",
  "oops": "Oops!!",
  "success": "Success",
  "domainNotAllowed": "Your email ID seems to have an issue with our registration process. Please contact our CRM team. They will revert back to you shortly. \n\nPhone: {phone}\nEmail: {email}",
  "accountDisabled": "Your account has been disabled. Please contact the Maisour support team at {email} for assistance.",
  "accountClosed": "Your account has been closed. Please contact the Maisour support team at {email} for assistance.",
  "accountNotActivated": "Please verify your email address to continue. We've sent a verification link to your email. Check your inbox (and spam folder) and click the link to activate your account.",
  "accountSuspended": "Your account has been suspended. Please contact the Maisour support team at {email} for assistance.",
  "invalidCredentials": "That doesn’t seem right — check your password and try again.",
  "forgotPasswordSuccess": "We've sent a password reset link to your email. Please check your inbox (and spam folder) and follow the instructions to reset your password.",
  "createAccount": "Create Account",
  "createAccountWith": "You are about to create account with",
  "fullName": "Full Name",
  "fullNameHelperText": "Enter your legal name, as it appears on your government ID.",
  "referralCode": "Referral Code",
  "hearAboutMaisour": "How did you hear about Maisour?",
  "pleaseSpecify": "Please Specify",
  "passwordHelperText": "Include {minLength}+ characters, {upperCaseCount} uppercase, {lowerCaseCount} lowercase, {numberCount} number & {specialCharacterCount} special character.",
  "characters": "characters",
  "uppercase": "uppercase",
  "lowercase": "lowercase",
  "number": "number",
  "specialCharacter": "special character",
  "verifyYourEmail": "Verify Your Email",
  "anEmailSentTo": "An email sent to",
  "checkYourInbox": "Please check your inbox and spam folder to continue.",
  "resendEmail": "Resend Email",
  "backToLogin": "Back to Login",
  "search": "Search",
  "others": "Others",
  "duplicateEmail": "This email is already used. Please use a different email.",
  "emailSentSuccessfully": "Email sent successfully!",
  "emailVerificationLink": "We’ve sent a verification link to {email}. Please check your inbox and spam folder to continue.",
  "invalidReferralCode": "Referral code is incorrect, please try again.",
  "properties": "Properties",
  "portfolio": "Portfolio",
  "wallet": "Wallet",
  "account": "Account",
  "live": "Live",
  "comingSoon": "Coming Soon",
  "funded": "Funded",
  "sold": "Sold",
  "request": "Request",
  "cancelled": "Cancelled",
  "verifyIdentityToStartInvestment": "Verify your identity to start investment.",
  "verifyNow": "Verify Now",
  "onboardingTitle": "Complete Your Profile",
  "onboardingSubtitle": "Add your details to verify your identity and continue your investment journey.",
  "verifyYourPassport": "Verify Your Passport",
  "verifyYourPassportDesc": "Upload a valid passport to verify your identity and comply with regulations.",
  "verifyYourAddress": "Verify Your Address",
  "verifyYourAddressDesc": "Provide a valid document to verify your current residential address.",
  "employmentInfo": "Employment Info",
  "employmentInfoDesc": "Provide your job information to help us understand your investor profile.",
  "taxResidencyInformation": "Tax Residency Information",
  "taxResidencyInformationDesc": "Provide details to determine your tax residency and meet legal compliance requirements.",
  "completed": "Completed",
  "pending": "Pending",
  "startVerification": "Start Verification",
  "doItLater": "I’ll do it later",
  "verifyYourAccount": "Verify Your Account",
  "verifyYourAccountDesc": "Secure your account and unlock full access by completing your verification. It only takes a few minutes.",
  "verifyAccount": "Verify Account",
  "passportSubtitle": "Financial regulations require us to verify your identity before you can invest. This helps protect your investment and allows us to register you as the legal owner of each property you invest in.",
  "passportAttempt": "{attempt} Attempt",
  "passportVerification": "Passport Verification",
  "addressVerification": "Address Verification",
  "verificationInProgressWait": "Your verification is in progress, please try again in 2 minutes.",
  "verificationJourneyCancelled": "Your journey has been cancelled. Please try again.",
  "verificationJourneyBlocked": "Your journey has been blocked. Please try again.",
  "verificationInProgress": "Verification in Progress",
  "verificationInProgressDesc": "We're reviewing your details. This won't take long.",
  "tooManyAttempts": "Too Many Attempts",
  "tooManyAttemptsDesc": "You have reached the maximum number of attempts for passport verification.",
  "passportVerificationCompleted": "Passport Verification Completed",
  "passportVerificationCompletedDesc": "Please proceed with the next step of address information.",
  "addressVerificationCompleted": "Address Verification Completed",
  "addressVerificationCompletedDesc": "Please proceed with the next step of employment information.",
  "passportVerificationFailed": "Passport Verification Failed",
  "passportVerificationFailedDesc": "We couldn’t confirm your passport details. You can try again now or finish this step later.",
  "viewDetails": "View Details",
  "possibleReasonsForVerificationFailure": "Possible Reasons for Verification Failure",
  "possibleReasonsForVerificationFailureDesc": "<p><b>This may have occurred due to one or more reasons, such as:</b></p> <ul> <li>The passport scan was unclear or not fully visible.</li> <li>The selfie was taken in poor lighting or didn’t clearly show your face.</li> <li>The document details didn’t match the selfie.</li> </ul> <p><b>To resolve this, please try again and ensure the following:</b></p> <ul> <li>Your passport scan is clear, legible, and free of any glare or obstructions.</li> <li>Your selfie is taken in a well-lit area, with your face fully visible and centered.</li> <li>You follow all on-screen instructions carefully for both the passport scan and selfie.</li> </ul> <p>If you continue to face issues, ensure your device camera is functioning properly and avoid using screenshots or images of documents.</p>",
  "addressVerificationDesc": "Financial regulations require us to verify your address proof before you can invest. This helps protect your investment and allow us to register you as the legal owner of each property you invest in.",
  "employed": "Employed",
  "retired": "Retired",
  "student": "Student",
  "unemployed": "Unemployed",
  "employmentStatus": "Employment Status",
  "companyName": "Company Name",
  "role": "Role",
  "workAddress": "Work Address",
  "annualIncome": "Annual Income",
  "lessThan10000": "Less than {dollarSign}10,000",
  "from10000To50000": "10,000 To {dollarSign}50,000",
  "from50000To100000": "50,000 To {dollarSign}100,000",
  "moreThan100000": "More than {dollarSign}100,000",
  "industry": "Industry",
  "countryOfBirth": "Country of Birth",
  "countryOfBirthHelperText": "Select the country of birth as per your passport.",
  "submit": "Submit",
  "employmentInfoSubmittedSuccessfully": "Employment information submitted successfully",
  "taxResidencyInformationScreenDesc": "We are required by law to collect your tax residency details to comply with international tax reporting standards (FATCA & CRS). Please provide the following information to proceed.",
  "countryOfResidence": "Country of Residence",
  "doYouHaveATaxIdentificationNumber": "Do You have a Tax Identification Number (TIN) in {country}?",
  "areYouAlsoAResidentInAnyOtherCountry": "Are you also a resident in any other country?",
  "additionalTaxResidencyCountry": "Additional tax residency country",
  "byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge": "By continuing you confirm that the above information is accurate & complete to the best of your knowledge.",
  "taxResidencySubmittedSuccessfully": "Tax Residency submitted successfully",
  "yes": "Yes",
  "no": "No",
  "notApplicable": "Not Applicable",
  "tinNumber": "TIN Number",
  "tinNumberLength": "TIN number must be between 10 and 15 characters",
  "verifyPhoneNumber": "Verify Phone Number",
  "confirmYourNumberToSecureYourAccount": "Confirm your number to secure your account.",
  "sendVerificationCode": "Send Verification Code",
  "phoneNumber": "Phone Number",
  "enterValidPhoneNumber": "Please enter a valid phone number",
  "accountVerificationPending": "Account Verification Pending",
  "accountVerificationPendingDesc": "Account verification pending. We’ll notify you soon.",
  "phoneNumberAlreadyInUse": "The entered phone number is already in use, please try another one.",
  "codeGenerationLimitExceeded": "You have exceeded the code generation limit for the day, please try tomorrow.",
  "waitMinuteForNewCode": "Please wait a minute before requesting a new code.",
  "verificationCodeSent": "Verification code has been sent to your number.",
  "enterVerificationCode": "Enter Verification Code",
  "enterThe5DigitCode": "Enter the 5-digit code sent to your number.",
  "didntReceiveCode": "Didn't receive the code?",
  "resend": "Resend",
  "verify": "Verify",
  "otpCode": "OTP Code",
  "otpCodeLength": "OTP code must be 5 digits",
  "invalidVerificationCode": "The verification code you entered is invalid. Please try again.",
  "codeVerifiedSuccessfully": "Code Verified Successfully",
  "phoneNumberVerifiedSuccessfully": "Your phone number has been verified. you're ready to start investing in the properties.",
  "accountVerificationFailed": "Your account could not be verified.",
  "accountVerificationFailedDesc": "Please contact our CRM team. They will revert back to you shortly.",
  "contactUs": "Contact Us",
  "newlyAdded": "Newly added",
  "trending": "Trending",
  "expectedAnnualNetRent": "Expected annual net rent",
  "expected5YearsNetRent": "Expected 5 years net rent",
  "expected5YearsNetReturns": "Expected 5 years net returns",
  "aed": "AED",
  "usd": "USD",
  "eur": "EUR",
  "weekly": "Weekly",
  "monthly": "Monthly",
  "quarterly": "Quarterly",
  "biannually": "Biannually",
  "annually": "Annually",
  "years": "Years",
  "noPropertiesAvailable": "No Properties Available",
  "noPropertiesAvailableDesc": "There are currently no properties available. Please check back later.",
  "sortBy": "Sort By",
  "propertyOldestToLatest": "Property - Oldest to latest",
  "fundingStatusHighestToLowest": "Funding status - Highest to lowest",
  "investors": "Investors",
  "annualGrossRent": "Annual Gross Rent",
  "minInvestment": "Min. Investment",
  "rentDistribution": "Rent Distribution",
  "suggestedHoldingPeriod": "Suggested Holding Period",
  "aboutRentalIncome": "About Rental Income",
  "aboutRentalIncomeDesc": "Once the property is fully funded, you'll start receiving a fixed monthly rental income, providing consistent cash flow from your investment.",
  "investmentBreakdown": "Investment Breakdown",
  "purchase": "Purchase",
  "rental": "Rental",
  "purchaseCost": "Purchase cost",
  "transactionCost": "Transaction cost",
  "totalAcquisitionCost": "Total acquisition cost",
  "grossRentPerYear": "Gross rent per year",
  "serviceCharges": "Service charges",
  "otherCosts": "Other costs",
  "numberOfShares": "Number of shares",
  "totalPricePerShare": "Total price per share",
  "guaranteedRentalIncome": "Guaranteed Rental Income",
  "aboutProperty": "About Property",
  "readMore": "Read more",
  "amenities": "Amenities",
  "viewAll": "View all",
  "exploreOnMap": "Explore on Map",
  "chooseMapApp": "Choose Map App",
  "appleMaps": "Apple Maps",
  "googleMaps": "Google Maps",
  "documents": "Documents",
  "contactSupport": "Contact Support",
  "doYouHaveQuestionsAboutTheProperty": "Do you have questions about the property?",
  "developerAndManager": "Developer & Manager",
  "investNow": "Invest Now",
  "preOrderNow": "Pre-order Now",
  "noPaymentYet": "No payment yet",
  "preOrder": "Pre-order",
  "yourInvestment": "Your Investment",
  "financialDetails": "Financial details",
  "amountToInvest": "Amount to Invest",
  "walletBalance": "Wallet Balance",
  "yourWalletWillNotBeCharged": "Your wallet will not be charged.",
  "pricePerShare": "Price per share",
  "investmentAmount": "Investment amount",
  "potentialRewards": "Potential rewards",
  "totalCost": "Total cost",
  "minimum": "Min.",
  "placeOrder": "Place Order",
  "howPreOrderWorks": "How Pre-order Works",
  "showInterest": "Show Interest",
  "noPaymentNeededJustShowInterested": "No payment needed—just show interested.",
  "getNotified": "Get Notified",
  "weWillNotifyYouWhenThePropertyIsLive": "We’ll notify you when the property is live.",
  "confirmAndInvest": "Confirm & Invest",
  "reviewDetailsAndMakeYourPayment": "Review details and make your payment.",
  "notEnoughBalance": "You don’t have enough balance in your wallet and rewards to proceed.",
  "yourOrder": "Your Order",
  "shares": "Shares",
  "totalInvestment": "Total Investment",
  "youAgreeTo": "You agree to",
  "investmentAgreement": "investment agreement",
  "proceedToPay": "Proceed to Pay",
  "actionRequired": "Action Required",
  "pleaseAcceptTheInvestmentAgreementToContinue": "Please accept the Investment Agreement to continue.",
  "agreeAndContinue": "Agree & Continue",
  "minInvestError": "Enter at least the minimum investment amount to proceed.",
  "maxInvestError": "The entered amount is greater than the remaining value of the unit",
  "yearlyLimitExceeded": "You have exceeded your yearly investment limit.",
  "propertyInvestmentLimitExceeded": "You have reached the maximum investment limit for this property.",
  "investmentConfirmation": "Investment Confirmation",
  "interestConfirmation": "Interest Confirmation",
  "paymentSuccessful": "Payment Successful",
  "interestSuccessful": "Interest Successful",
  "viewPortfolio": "View Portfolio",
  "investmentCalculator": "Investment Calculator",
  "expectedInvestmentReturnIn5Years": "Expected investment return in 5 years",
  "investment": "Investment",
  "returns": "Returns",
  "totalRentalYield": "Total rental yield",
  "valueAppreciation": "Value appreciation",
  "totalInvestmentReturn": "Total Investment return",
  "grossInvestmentGain": "Gross investment gain (ROI)",
  "initialInvestment": "Initial Investment",
  "propertyValueGrowth": "Property Value Growth",
  "expectedRentalYield": "Expected Rental Yield",
  "myOwnerships": "My Ownerships",
  "cashBalance": "Cash Balance",
  "topUp": "Top up",
  "rewardBalance": "Reward Balance",
  "noTransactionsYet": "No Transactions Yet",
  "thisIsWhereYourTransactionsWillBeListed": "This is where your transactions will be listed.\nBrowse properties to invest.",
  "transactions": "Transactions",
  "bankAccounts": "Bank Accounts",
  "safeTransfersIn23Days": "Safe transfers in 2-3 days.",
  "addBankAccount": "Add Bank Account",
  "itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName": "It must be a personal or jointly owned bank account\nunder your name (per AML regulations).",
  "addAccount": "Add Account",
  "yourMoneyIsProtected": "Your money is protected.",
  "thePropertiesAreRegisteredInDLD": "The properties are registered in DLD.",
  "weHaveAPlanForSafeKeepingAssets": "We have a plan for safe-keeping assets.",
  "actionRestricted": "Action Restricted",
  "pleaseVerifyYourAccount": "You need to verify your identity before performing this action.",
  "yourIdentityVerificationIsUnderReview": "Your identity verification is under review. For assistance, please visit the Contact Us section.",
  "yourPassportIsExpired": "Your passport is expired. Please update your passport to continue.",
  "yourPassportIsExpiringSoon": "Passport expiring soon. Please update your passport to keep investing.",
  "asPerAmlRegulationsAccountRestricted": "As per AML regulations, your account is restricted from accessing investment opportunities. Please visit the Contact Us section for assistance.",
  "purchasePrice": "Purchase Price",
  "currentValuation": "Current Valuation",
  "investmentSummary": "Investment Summary",
  "ownershipPercentage": "Ownership percentage",
  "timeline": "Timeline",
  "investedInProperty": "Invested in Property",
  "interestShownInProperty": "Interest Shown in Property",
  "dividendHistory": "Dividend History",
  "dividendCredited": "Dividend Credited",
  "failed": "Failed",
  "reversed": "Reversed",
  "refunded": "Refunded",
  "successful": "Successful",
  "rejected": "Rejected",
  "notFunded": "Not Funded",
  "removed": "Removed",
  "purchaseDetails": "Purchase Details",
  "amountInvested": "Amount Invested",
  "status": "Status",
  "cancelInvestment": "Cancel Investment",
  "deleteInvestment": "Delete Investment",
  "confirm": "Confirm",
  "invest": "Invest",
  "update": "Update",
  "all": "All",
  "withdrawal": "Withdrawal",
  "deposit": "Deposit",
  "dividend": "Dividend",
  "reward": "Reward",
  "saleProceed": "Sale Proceed",
  "otherAdjustment": "Other Adjustment",
  "filters": "Filters",
  "last30Days": "Last 30 days",
  "last3Months": "Last 3 months",
  "last6Months": "Last 6 months",
  "primary": "Primary",
  "accountName": "Account Name",
  "bankName": "Bank Name",
  "bankNameLength": "Bank name must not exceed 70 characters",
  "invalidBankName": "Invalid Bank Name",
  "accountNumber": "Account Number",
  "accountNumberLength": "Account number must be 6–20 digits",
  "invalidAccountNumber": "Invalid Account Number",
  "country": "Country",
  "iban": "IBAN Code",
  "ibanLength": "IBAN must be 15–34 characters",
  "invalidIban": "Invalid IBAN",
  "swiftCode": "Swift Code",
  "swiftCodeLength": "Swift code must be 8 or 11 characters",
  "invalidSwiftCode": "Invalid Swift Code",
  "primaryAccount": "Primary Account",
  "bankAccountAddedSuccessfully": "Bank account added successfully",
  "bankAccountSetAsPrimarySuccessfully": "Bank account set as primary successfully",
  "bankAccountStatusUpdatedSuccessfully": "Bank account status updated successfully",
  "bankAccountDeletedSuccessfully": "Bank account deleted successfully",
  "cannotSetPrimaryBankAccountAsInactive": "You cannot set the primary bank account as inactive",
  "deleteAccount": "Delete Account",
  "areYouSureYouWantToDeleteThisBankAccount": "Are you sure you want to delete this bank account?",
  "delete": "Delete",
  "withdraw": "Withdraw",
  "addFunds": "Add Funds",
  "topUpYourWallet": "Top Up Your Wallet",
  "allTransactionsInAED": "All transactions are carried out in AED, values shown are currency estimates for reference only.",
  "amountInCurrency": "Amount in {currency}",
  "selectTopUpMethod": "Select Top Up Method",
  "bankTransfer": "Bank Transfer",
  "debitCard": "Debit Card",
  "take23DaysToComplete": "Takes 2-3 days to complete",
  "more": "more",
  "copyReferenceNumber": "Copy \"Reference number\" for seamless processing.",
  "bankTransferPending": "Bank Transfer Pending",
  "yourTransferWasInitiated": "Your transfer was initiated. To add funds to your wallet, transfer the specified amount from a personal or jointly owned account.",
  "howToTopUpYourWallet": "How to Top Up Your Wallet",
  "copyAccountDetails": "Copy Account Details",
  "useTheDisplayedBankInfoForYourTransfer": "Use the displayed bank info for your transfer.",
  "makeThePayment": "Make the Payment",
  "completeTheTransferThroughYourBankingApp": "Complete the transfer through your banking app.",
  "waitForTheConfirmation": "Wait for the Confirmation",
  "weWillVerifyYourPaymentAndUpdateYourWallet": "We’ll verify your payment and update your wallet.",
  "depositAmount": "Deposit amount",
  "referenceNumber": "Reference number",
  "done": "Done",
  "payment": "Payment",
  "fundsAddedSuccessfully": "Funds added successfully!",
  "withdrawCash": "Withdraw Cash",
  "payoutRequest": "Payout Request",
  "selectBankAccount": "Select Bank Account",
  "withdrawalsProcessingTime": "Withdrawals typically require 3-7 days for processing.",
  "profile": "Profile",
  "myProfile": "My Profile",
  "updatePassword": "Update Password",
  "referrals": "Referrals",
  "sellProperties": "Sell Properties",
  "withdrawalRequestSubmittedSuccessfully": "Withdrawal request submitted successfully",
  "memberSince": "Member since",
  "personalDetails": "Personal Details",
  "notProvided": "Not provided",
  "edit": "Edit",
  "employmentDetails": "Employment Details",
  "noEnoughCredit": "You don’t have enough credit to proceed.",
  "toChangeYourFullNameOrEmail": "To change your full name or email, please",
  "contactTheAdmin": "contact the admin",
  "oldPassword": "Old Password",
  "newPassword": "New Password",
  "confirmPassword": "Confirm Password",
  "passwordDoNotMatch": "Passwords do not match",
  "save": "Save",
  "passwordUpdatedSuccessfully": "Password updated successfully",
  "noDocumentsFound": "No documents found",
  "uploadDocument": "Upload Document",
  "fileTooLarge": "File too large",
  "pleaseSelectASmallerFile": "Please select a file smaller than 4MB",
  "invalidFileType": "Invalid file type",
  "pleaseSelectAPDFOrImageFile": "Please select a PDF or image file (JPEG, PNG, etc.)",
  "fileName": "File Name",
  "upload": "Upload",
  "fileUploadedSuccessfully": "File uploaded successfully!",
  "duplicateDocumentName": "Duplicate document name detected. Please rename your document and try again.",
  "preferences": "Preferences",
  "appInfoSupport": "App Info & Support",
  "logoutOptions": "Logout Options",
  "language": "Language",
  "selectLanguage": "Select Language",
  "selectYourPreferredLanguageItWillApplyThroughoutTheApp": "Select your preferred language. It will apply throughout the app.",
  "selectCurrency": "Select Currency",
  "currencyDescription": "All transactions in Maisour are processed in United Arab Emirates Dirhams. However you can use this section to approximate the values in listed currencies for your ease of use. We regularly update the currency rates.",
  "unitedArabEmiratesDirham": "United Arab Emirates Dirham",
  "unitedStatesDollar": "United States Dollar",
  "euro": "Euro",
  "currency": "Currency",
  "notifications": "Notifications",
  "ourStory": "Our Story",
  "getHelp": "Get Help",
  "shareApp": "Share App",
  "hiHowCanWeHelp": "Hi, How can we help?",
  "helpCenter": "Help Center",
  "raiseTicket": "Raise Ticket",
  "getInTouch": "Get in Touch",
  "chatWithUs": "Chat with Us",
  "frequentlyAskedQuestions": "Frequently Asked Questions",
  "noFaqsAvailable": "No FAQs available",
  "message": "Message",
  "supportRequestSubmittedSuccessfully": "Support request submitted successfully. Our team will reach out to you soon with further details.",
  "shareAppDesc": "Just discovered an amazing app called Maisour that I think you will love. It lets you easily own a part of prime rental properties in Dubai and earn rent plus capital growth, all hassle-free! 🏢💰\nDownload from the store or click on:\nhttps://redirect.maisour.ae\nGive it a try and let me know your thoughts! Excited to hear what you think!",
  "closeAccount": "Close Account",
  "logout": "Logout",
  "areYouSureYouWantToLogout": "Are you sure you want to logout?",
  "reasonForClosingAccount": "Reason for closing account",
  "deleteAccountActionRequired": "Due to regulation, we can't delete financial data, you can request account deactivation, while we securely store your data as required.",
  "yourAccountHasBeenSuccessfullyClosed": "Your account has been successfully closed",
  "followUs": "Follow us",
  "version": "Version",
  "completeYourOnboarding": "Complete Your Onboarding.",
  "toViewMoreDetailsPlease": "To view more details, please",
  "yourAccountHasBeenSuspended": "Your account has been suspended.",
  "countryOfBirthUpdatedSuccessfully": "Country of Birth updated successfully!",
  "addTaxDetails": "Add your tax details to meet compliance requirements.",
  "add": "Add"
};
static const Map<String,dynamic> _ar = {
  "deviceIsRooted": "قد يكون جهازك مكسور الحماية",
  "deviceIsJailbroken": "قد يكون جهازك مكسور الحماية",
  "deviceIsRootedDesc": "استخدام جهاز مكسور الحماية قد يشكل مخاطر أمنية خطيرة ويمنع ميسور من ضمان حمايتك الإلكترونية",
  "deviceIsJailbrokenDesc": "استخدام جهاز مكسور الحماية قد يشكل مخاطر أمنية خطيرة ويمنع ميسور من ضمان حمايتك الإلكترونية",
  "cancel": "إلغاء",
  "welcomeTitle1": "استثمر في نمو دبي",
  "welcomeTitle2": "احصل على عوائد شهرية",
  "welcomeTitle3": "قوائم حصرية ومدققة",
  "welcomeTitle4": "استثمار ذكي وبسيط",
  "welcomeDesc1": "استفد من واحد من أكثر أسواق العقارات ديناميكية في العالم - بأمان، من أي مكان.",
  "welcomeDesc2": "احصل على دخل إيجار مضمون مع استثمارات عقارية جزئية مُدارة بالكامل.",
  "welcomeDesc3": "نختار فقط الأصول الأفضل أداءً لتستثمر بثقة، وليس بتعقيد.",
  "welcomeDesc4": "ابدأ الاستثمار بسهولة، وتتبع محفظتك، وانمِ ثروتك ببضع نقرات فقط.",
  "startInvesting": "ابدأ الاستثمار",
  "maisourRegulatedDSFA": "ميسور منظمة من قبل هيئة دبي للخدمات المالية",
  "somethingWentWrong": "حدث خطأ ما. يرجى المحاولة مرة أخرى",
  "later": "لاحقًا",
  "updateNow": "تحديث الآن",
  "updateMaisourApp": "تحديث تطبيق ميسور",
  "updateRecommendationMessage": "ميسور تُوصيك بتحديث التطبيق إلى أحدث إصدار للحصول على أداء وأمان أفضل.",
  "noInternetConnection": "لا يوجد اتصال بالإنترنت",
  "noInternetDescription": "الرجاء التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.",
  "tryAgain": "حاول مرة أخرى",
  "checkingConnection": "جاري التحقق من الاتصال...",
  "connectionCheckFailed": "فشل التحقق من الاتصال",
  "connectedButNoInternet": "متصل بـ {connectionType} ولكن لا يوجد وصول إلى الإنترنت.",
  "getStarted": "ابدأ الآن",
  "loginSubtitle": "الرجاء إدخال عنوان بريدك الإلكتروني للمتابعة.",
  "login": "تسجيل الدخول",
  "continueText": "أكمل",
  "orText": "أو",
  "continueWithApple": "المتابعة باستخدام Apple",
  "continueWithGoogle": "المتابعة باستخدام Google",
  "byContinuingYouAccepting": "باستمرارك فإنك توافق",
  "termOfUse": "شروط الاستخدام",
  "privacyPolicy": "سياسة الخصوصية",
  "email": "عنوان البريد الإلكتروني",
  "password": "كلمة المرور",
  "forgotPassword": "هل نسيت كلمة المرور؟",
  "didntReceiveEmail": "لم تستلم البريد الإلكتروني؟",
  "resendIn": "إعادة الإرسال",
  "sendResetLink": "إرسال رابط إعادة التعيين",
  "resendResetLink": "إعادة إرسال رابط إعادة التعيين",
  "fieldRequired": "{field} مطلوب",
  "enterValidEmail": "الرجاء إدخال عنوان بريد إلكتروني صالح.",
  "welcomeBack": "أهلاً بعودتك",
  "passwordSubtitle": "الرجاء إدخال كلمة المرور للحساب.",
  "passwordMinLength": "يجب أن تتكون كلمة المرور من {minLength} أحرف على الأقل.",
  "forgotPasswordSubtitle": "لا تقلق! أدخل عنوان بريدك الإلكتروني أدناه وسنرسل لك رابطًا لإعادة تعيين كلمة مرورك.",
  "yourAccountHasBeenClosed": "تم إغلاق حسابك.",
  "accountClosedMessage": "حسابك مغلق حالياً. للدعم، يرجى التواصل معنا عبر واتساب.",
  "goToWhatsapp": "انتقل إلى واتساب",
  "emailRequired": "البريد الإلكتروني مطلوب",
  "emailRequiredMessage": "تعذر الحصول على بريدك الإلكتروني من تسجيل الدخول باستخدام Apple.",
  "oops": "حدث خطأ ما!",
  "success": "تمت العملية بنجاح.",
  "accountDisabled": "تم تعطيل حسابك. يرجى التواصل مع فريق دعم ميسور على البريد الإلكتروني {email} للحصول على المساعدة.",
  "accountClosed": "تم إغلاق حسابك. يرجى التواصل مع فريق دعم ميسور عبر البريد الإلكتروني {email} للحصول على المساعدة.",
  "accountNotActivated": "الرجاء التحقق من عنوان بريدك الإلكتروني للمتابعة. لقد أرسلنا رابط تفعيل إلى بريدك الإلكتروني. تفقد صندوق الوارد الخاص بك (ومجلد البريد المزعج) وانقر على الرابط لتفعيل حسابك.",
  "accountSuspended": "تم تعليق حسابك. يرجى التواصل مع فريق دعم ميسور على البريد الإلكتروني {email} للحصول على المساعدة.",
  "invalidCredentials": "هذا لا يبدو صحيحًا — يرجى التحقق من كلمة المرور الخاصة بك والمحاولة مرة أخرى.",
  "forgotPasswordSuccess": "لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. الرجاء التحقق من صندوق الوارد الخاص بك (ومجلد البريد المزعج) واتباع التعليمات لإعادة تعيين كلمة مرورك.",
  "createAccount": "إنشاء حساب",
  "createAccountWith": "إنشاء حساب باستخدام",
  "fullName": "الاسم الكامل",
  "fullNameHelperText": "الرجاء إدخال اسمك القانوني، كما هو مدوّن في هويتك الحكومية.",
  "referralCode": "رمز الدعوة",
  "hearAboutMaisour": "كيف سمعت عن ميسور؟",
  "pleaseSpecify": "الرجاء التحديد",
  "passwordHelperText": "يتضمن {minLength}+ أحرف، {upperCaseCount} أحرف كبيرة، {lowerCaseCount} أحرف صغيرة، {numberCount} أرقام و {specialCharacterCount} أحرف خاصة.",
  "characters": "أحرف",
  "uppercase": "أحرف كبيرة",
  "lowercase": "أحرف صغيرة",
  "number": "رقم",
  "specialCharacter": "رموز خاصة",
  "verifyYourEmail": "الرجاء التحقق من بريدك الإلكتروني",
  "anEmailSentTo": "تم إرسال بريد إلكتروني إلى",
  "checkYourInbox": "الرجاء التحقق من صندوق الوارد ومجلد البريد المزعج للمتابعة.",
  "resendEmail": "إعادة إرسال البريد الإلكتروني",
  "backToLogin": "العودة إلى تسجيل الدخول",
  "search": "بحث",
  "others": "غير ذلك",
  "duplicateEmail": "هذا البريد الإلكتروني مستخدم بالفعل. الرجاء استخدام بريد إلكتروني آخر.",
  "emailSentSuccessfully": "تم إرسال البريد الإلكتروني بنجاح!",
  "emailVerificationLink": "لقد أرسلنا رابط تفعيل إلى {email}. يرجى التحقق من صندوق الوارد ومجلد البريد المزعج للمتابعة.",
  "invalidReferralCode": "رمز الدعوة غير صحيح، يرجى المحاولة مرة أخرى.",
  "properties": "العقارات",
  "portfolio": "المحفظة المالية",
  "wallet": "المحفظة الرقمية / الإلكترونية",
  "account": "الحساب",
  "live": "متاح حاليا",
  "comingSoon": "قريبًا",
  "funded": "ممول",
  "sold": "مباع",
  "request": "تقديم طلب",
  "cancelled": "تم الإلغاء",
  "verifyIdentityToStartInvestment": "للبدء في الاستثمار، يجب عليك التحقق من هويتك.",
  "verifyNow": "تحقق الآن",
  "onboardingTitle": "أكمل ملفك الشخصي",
  "onboardingSubtitle": "يُرجى إضافة معلوماتك الشخصية للتحقق من هويتك ومتابعة رحلتك الاستثمارية",
  "verifyYourPassport": "التحقق من جواز سفرك",
  "verifyYourPassportDesc": "قم بتحميل جواز سفر ساري المفعول للتحقق من هويتك والامتثال للوائح",
  "verifyYourAddress": "تحقق من عنوانك",
  "verifyYourAddressDesc": "قدم مستندًا ساريًا للتحقق من عنوان إقامتك الحالي",
  "employmentInfo": "معلومات التوظيف",
  "employmentInfoDesc": "قدِّم معلومات عملك لمساعدتنا في فهم ملفك الاستثماري",
  "taxResidencyInformation": "بيانات الإقامة الضريبية",
  "taxResidencyInformationDesc": "زوّدنا بالتفاصيل لتحديد إقامتك الضريبية وتلبية متطلبات الامتثال القانوني.",
  "completed": "مكتمل",
  "pending": "معلَق",
  "startVerification": "بدء عملية التحقق",
  "doItLater": "سأقوم بذلك لاحقاً",
  "verifyYourAccount": "تأكيد حسابك",
  "verifyYourAccountDesc": "أمّن حسابك وفعِّل الوصول الكامل بإكمال عملية التحقق الخاصة بك. لا يستغرق الأمر سوى بضع دقائق.",
  "verifyAccount": "تأكيد الحساب",
  "passportSubtitle": "تتطلب اللوائح المالية منا التحقق من هويتك قبل أن تتمكن من الاستثمار. يساعد هذا في حماية استثماراتك ويسمح لنا بتسجيلك كمالك قانوني لكل عقار تستثمر فيه.",
  "passportAttempt": "{attempt} محاولة",
  "passportVerification": "التحقق من جواز السفر",
  "addressVerification": "التحقق من العنوان",
  "verificationInProgressWait": "التحقق الخاص بك قيد المعالجة، يرجى المحاولة مرة أخرى بعد دقيقتين.",
  "verificationJourneyCancelled": "تم إلغاء مسار التحقق الخاص بك. يرجى المحاولة مجددًا.",
  "verificationJourneyBlocked": "تم إيقاف مسار التحقق الخاص بك. يرجى المحاولة مرة أخرى",
  "verificationInProgress": "جارٍ التحقق",
  "verificationInProgressDesc": "جارٍ مراجعة تفاصيلك. لن يستغرق الأمر طويلاً.",
  "tooManyAttempts": "عدد كبير جداً من المحاولات",
  "tooManyAttemptsDesc": "لقد بلغت الحد الأقصى للمحاولات المسموحة للتحقق من جواز السفر.",
  "passportVerificationCompleted": "تم التحقق من جواز السفر",
  "passportVerificationCompletedDesc": "يرجى المتابعة إلى الخطوة التالية: تفاصيل العنوان",
  "addressVerificationCompleted": "تم التحقق من العنوان",
  "addressVerificationCompletedDesc": "يرجى المتابعة إلى الخطوة التالية: تفاصيل الوظيفة",
  "passportVerificationFailed": "فشلت عملية التحقق من جواز السفر",
  "passportVerificationFailedDesc": "لم نتمكن من تأكيد تفاصيل جواز سفرك. يمكنك المحاولة مرة أخرى الآن أو إكمال هذه الخطوة لاحقًا.",
  "viewDetails": "عرض التفاصيل",
  "possibleReasonsForVerificationFailure": "الأسباب المحتملة لفشل عملية التحقق",
  "possibleReasonsForVerificationFailureDesc": "قد يكون هذا قد حدث لسبب أو أكثر، مثل: كانت صورة جواز السفر الممسوحة ضوئيًا غير واضحة أو غير مرئية بالكامل. تم التقاط صورة السيلفي في إضاءة سيئة أو لم تظهر وجهك بوضوح. لم تتطابق تفاصيل المستند مع صورة السيلفي. لحل هذه المشكلة، يرجى المحاولة مرة أخرى والتأكد مما يلي: أن تكون صورة جواز السفر الممسوحة ضوئيًا واضحة ومقروءة وخالية من أي انعكاسات ضوئية أو عوائق. أن يتم التقاط صورة السيلفي في منطقة جيدة الإضاءة، مع ظهور وجهك بالكامل وفي المنتصف. أن تتبع جميع التعليمات التي تظهر على الشاشة بعناية، لكل من مسح جواز السفر وصورة السيلفي. إذا استمررت في مواجهة مشكلات، تأكد من أن كاميرا جهازك تعمل بشكل صحيح وتجنب استخدام لقطات الشاشة أو صور المستندات.",
  "addressVerificationDesc": "تتطلب اللوائح المالية منا التحقق من إثبات عنوانك قبل أن تتمكن من الاستثمار. يساعد هذا في حماية استثماراتك ويسمح لنا بتسجيلك كمالك قانوني لكل عقار تستثمر فيه.",
  "employed": "موظف",
  "retired": "متقاعد",
  "student": "طالب",
  "unemployed": "عاطل عن العمل",
  "employmentStatus": "حالة التوظيف",
  "companyName": "اسم الشركة",
  "role": "المسمى الوظيفي",
  "workAddress": "عنوان العمل",
  "annualIncome": "الدخل السنوي",
  "lessThan10000": "أقل من 10,000 {dollarSign}",
  "from10000To50000": "10,000 إلى 50,000 {dollarSign}",
  "from50000To100000": "50,000 إلى 100,000 {dollarSign}",
  "moreThan100000": "أكثر من 100,000 {dollarSign}",
  "industry": "المجال/القطاع",
  "countryOfBirth": "دولة الميلاد",
  "countryOfBirthHelperText": "حدد بلد الميلاد كما هو موضح في جواز سفرك.",
  "submit": "إرسال",
  "employmentInfoSubmittedSuccessfully": "تم إرسال معلومات التوظيف بنجاح",
  "taxResidencyInformationScreenDesc": "نحن مطالبون قانونياً بجمع تفاصيل إقامتك الضريبية للامتثال للمعايير الدولية للإبلاغ الضريبي  (FATCA & CRS). يرجى تقديم المعلومات التالية للمتابعة.",
  "countryOfResidence": "دولة الإقامة",
  "doYouHaveATaxIdentificationNumber": "هل لديك رقم تعريف ضريبي (TIN) في {country}؟",
  "areYouAlsoAResidentInAnyOtherCountry": "هل أنت مقيم أيضاً في أي دولة أخرى؟",
  "additionalTaxResidencyCountry": "ضريبة إضافية في دولة الإقامة",
  "byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge": "بالمتابعة، فإنك تؤكد أن المعلومات المذكورة أعلاه دقيقة وكاملة حسب علمك.",
  "taxResidencySubmittedSuccessfully": "تم إرسال معلومات الإقامة الضريبية بنجاح",
  "yes": "نعم",
  "no": "لا",
  "notApplicable": "لا ينطبق",
  "tinNumber": "رقم TIN",
  "tinNumberLength": "يجب أن يتكون رقم TIN من 10 إلى 15 حرفاً",
  "verifyPhoneNumber": "التحقق من رقم الهاتف",
  "confirmYourNumberToSecureYourAccount": "أكّد رقم هاتفك لتأمين حسابك.",
  "sendVerificationCode": "إرسال رمز التحقق",
  "phoneNumber": "رقم الهاتف",
  "enterValidPhoneNumber": "الرجاء إدخال رقم هاتف صحيح",
  "accountVerificationPending": "التحقق من الحساب معلق",
  "accountVerificationPendingDesc": "التحقق من الحساب معلق.سنُعلمك قريبًا.",
  "phoneNumberAlreadyInUse": "رقم الهاتف المُدخل مستخدم بالفعل، يرجى تجربة رقم آخر.",
  "codeGenerationLimitExceeded": "لقد تجاوزت حد إنشاء الرمز اليومي، يرجى المحاولة غدًا.",
  "waitMinuteForNewCode": "الرجاء الانتظار دقيقة قبل طلب رمز جديد.",
  "verificationCodeSent": "تم إرسال رمز التحقق إلى رقمك.",
  "enterVerificationCode": "أدخل رمز التحقق",
  "enterThe5DigitCode": "أدخل الرمز المكون من 5 أرقام المُرسل إلى رقمك.",
  "didntReceiveCode": "لم تستلم الرمز؟",
  "resend": "إعادة إرسال الرمز",
  "verify": "تحقق",
  "otpCode": "رمز OTP",
  "otpCodeLength": "يجب أن يتكون رمز OTP من 5 أرقام",
  "invalidVerificationCode": "رمز التحقق الذي أدخلته غير صحيح. يرجى المحاولة مرة أخرى.",
  "codeVerifiedSuccessfully": "تم التحقق من الرمز بنجاح",
  "phoneNumberVerifiedSuccessfully": "تم التحقق من رقم هاتفك. أنت جاهز الآن لبدء الاستثمار في العقارات.",
  "accountVerificationFailed": "تعذّر التحقق من حسابك",
  "accountVerificationFailedDesc": "يرجى التواصل مع فريق إدارة علاقات العملاء (CRM) الخاص بنا. سيعاودون الاتصال بك قريباً.",
  "contactUs": "اتصل بنا",
  "newlyAdded": "المضاف حديثًا",
  "trending": "الأكثر تداولاً",
  "expectedAnnualNetRent": "صافي الإيجار السنوي المتوقع",
  "expected5YearsNetRent": "صافي الإيجار المتوقع لمدة 5 سنوات",
  "expected5YearsNetReturns": "صافي العوائد المتوقعة لمدة 5 سنوات",
  "aed": "درهم إماراتي",
  "usd": "دولار أمريكي",
  "eur": "يورو",
  "weekly": "أسبوعي",
  "monthly": "شهري",
  "quarterly": "ربع سنوي",
  "biannually": "نصف سنوي",
  "annually": "سنوي",
  "years": "سنوات",
  "noPropertiesAvailable": "لا توجد عقارات متاحة حاليًا.",
  "noPropertiesAvailableDesc": "لا توجد عقارات متاحة حاليًا. يرجى التحقق مرة أخرى لاحقًا",
  "sortBy": "فرز حسب",
  "propertyOldestToLatest": "العقار - من الأقدم إلى الأحدث",
  "fundingStatusHighestToLowest": "حالة التمويل - من الأعلى إلى الأقل",
  "investors": "المستثمرين",
  "annualGrossRent": "إجمالي الإيجار السنوي",
  "minInvestment": "الحد الأدنى للاستثمار",
  "rentDistribution": "توزيع الإيجار",
  "suggestedHoldingPeriod": "فترة الاحتفاظ المقترحة",
  "aboutRentalIncome": "حول دخل الإيجار",
  "aboutRentalIncomeDesc": "بمجرد تمويل العقار بالكامل، ستبدأ في تلقي دخل إيجار شهري ثابت، مما يوفر تدفقاً نقدياً منتظماً من استثمارك.",
  "investmentBreakdown": "تفاصيل الاستثمار",
  "purchase": "الشراء",
  "rental": "الإيجار",
  "purchaseCost": "تكلفة الشراء",
  "transactionCost": "تكلفة المعاملة",
  "totalAcquisitionCost": "إجمالي تكلفة الاستحواذ",
  "grossRentPerYear": "إجمالي الإيجار السنوي",
  "serviceCharges": "رسوم الخدمات",
  "otherCosts": "تكاليف أخرى",
  "numberOfShares": "عدد الأسهم",
  "totalPricePerShare": "إجمالي سعر السهم",
  "guaranteedRentalIncome": "دخل إيجار مضمون",
  "aboutProperty": "حول العقار",
  "readMore": "اقرأ المزيد",
  "amenities": "المرافق",
  "viewAll": "عرض الكل",
  "exploreOnMap": "استكشف على الخريطة",
  "chooseMapApp": "اختر تطبيق الخرائط",
  "appleMaps": "خرائط آبل",
  "googleMaps": "خرائط جوجل",
  "documents": "الوثائق",
  "contactSupport": "تواصل مع الدعم",
  "doYouHaveQuestionsAboutTheProperty": "هل لديك أسئلة حول العقار؟",
  "developerAndManager": "المطور والمدير",
  "investNow": "استثمر الآن",
  "preOrderNow": "اطلب مسبقاً الآن",
  "noPaymentYet": "لم يتم الدفع بعد",
  "preOrder": "طلب مسبق",
  "yourInvestment": "استثمارك",
  "financialDetails": "التفاصيل المالية",
  "amountToInvest": "المبلغ المراد استثماره",
  "walletBalance": "رصيد المحفظة",
  "yourWalletWillNotBeCharged": "لن يتم خصم أي مبلغ من محفظتك.",
  "pricePerShare": "سعر السهم",
  "investmentAmount": "مبلغ الاستثمار",
  "potentialRewards": "المكافآت المحتملة",
  "totalCost": "التكلفة الإجمالية",
  "minimum": "الحد الأدنى",
  "placeOrder": "تقديم الطلب",
  "howPreOrderWorks": "كيف تعمل الطلبات المسبقة (Pre-orders)",
  "showInterest": "أعرب عن اهتمامك",
  "noPaymentNeededJustShowInterested": "لا يلزم الدفع الآن—فقط عبّر عن اهتمامك",
  "getNotified": "نبّهني",
  "weWillNotifyYouWhenThePropertyIsLive": "ستتلقى إشعارًا عندما يصبح العقار متاحًا.",
  "confirmAndInvest": "أكّد واستثمر",
  "reviewDetailsAndMakeYourPayment": "راجع تفاصيل الطلب وتابع للدفع",
  "notEnoughBalance": "رصيد محفظتك ومكافآتك غير كافٍ لإتمام هذا الإجراء",
  "yourOrder": "ملخص طلبك",
  "shares": "عدد الأسهم",
  "totalInvestment": "إجمالي مبلغ الاستثمار",
  "youAgreeTo": "أنت توافق على",
  "investmentAgreement": "اتفاقية الاستثمار",
  "proceedToPay": "تابع للدفع",
  "actionRequired": "إجراء مطلوب",
  "pleaseAcceptTheInvestmentAgreementToContinue": "يُرجى الموافقة على اتفاقية الاستثمار للمتابعة",
  "agreeAndContinue": "أوافق و تابع",
  "minInvestError": "للمتابعة، يجب أن تساوي قيمة استثمارك الحد الأدنى المطلوب للاستثمار على الأقل.",
  "maxInvestError": "المبلغ المدخل أكبر من القيمة المتبقية للوحدة",
  "yearlyLimitExceeded": "إجمالي استثماراتك لهذا العام قد تجاوز الحد المسموح به",
  "propertyInvestmentLimitExceeded": "لقد وصلت إلى الحد الأقصى للاستثمار في هذه الوحدة العقارية.",
  "investmentConfirmation": "تأكيد الاستثمار",
  "interestConfirmation": "تأكيد الاهتمام",
  "paymentSuccessful": "تم الدفع بنجاح",
  "interestSuccessful": "تم تسجيل الاهتمام بنجاح",
  "viewPortfolio": "عرض المحفظة",
  "investmentCalculator": "حاسبة الاستثمار",
  "expectedInvestmentReturnIn5Years": "العائد الاستثماري المتوقع خلال 5 سنوات",
  "investment": "استثمار",
  "returns": "عوائد الاستثمار",
  "totalRentalYield": "إجمالي العائد الإيجاري",
  "valueAppreciation": "تقدير القيمة",
  "totalInvestmentReturn": "إجمالي العائد على الاستثمار",
  "grossInvestmentGain": "إجمالي الربح الاستثماري",
  "initialInvestment": "استثمار أولي",
  "propertyValueGrowth": "نمو قيمة العقار",
  "expectedRentalYield": "العائد الإيجاري المتوقع",
  "myOwnerships": "ممتلكاتي",
  "cashBalance": "رصيد المحفظة",
  "topUp": "شحن الرصيد",
  "rewardBalance": "رصيد المكافآت",
  "noTransactionsYet": "لا توجد معاملات بعد",
  "transactions": "سجل المعاملات",
  "bankAccounts": "الحسابات البنكية",
  "safeTransfersIn23Days": "تحويلات آمنة خلال 2-3 أيام عمل.",
  "addBankAccount": "إضافة حساب بنكي",
  "addAccount": "إضافة حساب",
  "yourMoneyIsProtected": "أموالك في أمان.",
  "thePropertiesAreRegisteredInDLD": "العقارات مسجلة لدى دائرة الأراضي والأملاك في دبي.",
  "weHaveAPlanForSafeKeepingAssets": "لدينا خطة لحفظ الأصول بأمان.",
  "actionRestricted": "الإجراء مقيَّد",
  "pleaseVerifyYourAccount": "يجب عليك التحقق من هويتك قبل تنفيذ هذا الإجراء.",
  "yourIdentityVerificationIsUnderReview": "التحقق من هويتك قيد المراجعة. للمساعدة، يرجى زيارة قسم تواصل معنا.",
  "yourPassportIsExpired": "جواز سفرك منتهي الصلاحية. يرجى تحديث جواز سفرك للمتابعة.",
  "yourPassportIsExpiringSoon": "جواز سفرك على وشك الانتهاء. يرجى تحديثه للاستمرار في الاستثمار.",
  "asPerAmlRegulationsAccountRestricted": "وفقًا للوائح مكافحة غسيل الأموال، تم تقييد حسابك من الوصول إلى الفرص الاستثمارية. يرجى زيارة قسم تواصل معنا للمساعدة.",
  "purchasePrice": "سعر الشراء",
  "currentValuation": "القيمة الحالية",
  "investmentSummary": "ملخص الاستثمار",
  "ownershipPercentage": "النسبة المئوية للملكية",
  "timeline": "المخطط الزمني",
  "investedInProperty": "مستثمر في عقار",
  "interestShownInProperty": "تم إبداء الاهتمام بالعقار",
  "dividendHistory": "سجل الأرباح",
  "dividendCredited": "تم إيداع توزيعات الأرباح",
  "failed": "فشل",
  "reversed": "مرتجع",
  "refunded": "تم استرداد المبلغ",
  "successful": "تم بنجاح",
  "rejected": "مرفوض",
  "notFunded": "غير مموّل",
  "removed": "محذوف",
  "purchaseDetails": "تفاصيل المشتريات",
  "amountInvested": "المبلغ المستثمر",
  "status": "الحالة",
  "cancelInvestment": "إلغاء الاستثمار",
  "deleteInvestment": "حذف الاستثمار",
  "confirm": "تأكيد",
  "invest": "استثمر",
  "update": "تحديث",
  "all": "الجميع",
  "withdrawal": "سحب",
  "deposit": "إيداع",
  "dividend": "توزيعات الأرباح",
  "reward": "مكافأة",
  "saleProceed": "عائد البيع",
  "otherAdjustment": "تعديلات أخرى",
  "filters": "تصفية",
  "last30Days": "آخر 30 يومًا",
  "last3Months": "آخر 3 أشهر",
  "last6Months": "آخر 6 أشهر",
  "primary": "الأساسي",
  "accountName": "اسم الحساب",
  "bankName": "اسم البنك",
  "bankNameLength": "يجب ألا يتجاوز اسم البنك 70 حرفًا",
  "invalidBankName": "اسم بنك غير صحيح",
  "accountNumber": "رقم الحساب",
  "accountNumberLength": "يجب أن يتكون رقم الحساب من 6 إلى 20 خانة",
  "invalidAccountNumber": "رقم حساب غير صحيح",
  "country": "الدولة",
  "iban": "رمز الآيبان",
  "ibanLength": "يجب أن يتكون الآيبان من 15 إلى 34 حرفًا",
  "invalidIban": "آيبان غير صحيح",
  "swiftCode": "رمز سويفت",
  "swiftCodeLength": "يجب أن يتكون رمز سويفت من 8 أو 11 حرفًا",
  "invalidSwiftCode": "رمز سويفت غير صحيح",
  "primaryAccount": "الحساب الأساسي",
  "bankAccountAddedSuccessfully": "تمت إضافة الحساب البنكي بنجاح.",
  "bankAccountSetAsPrimarySuccessfully": "تم تعيين الحساب البنكي كأساسي بنجاح.",
  "bankAccountStatusUpdatedSuccessfully": "تم تحديث حالة الحساب البنكي بنجاح.",
  "bankAccountDeletedSuccessfully": "تم حذف الحساب البنكي بنجاح.",
  "cannotSetPrimaryBankAccountAsInactive": "لا يمكنك تعيين الحساب البنكي الأساسي كغير نشط.",
  "deleteAccount": "حذف الحساب",
  "areYouSureYouWantToDeleteThisBankAccount": "هل أنت متأكد من حذف هذا الحساب البنكي؟",
  "delete": "حذف",
  "withdraw": "سحب",
  "addFunds": "إضافة أموال",
  "topUpYourWallet": "شحن المحفظة",
  "allTransactionsInAED": "تتم جميع المعاملات بالدرهم الإماراتي، والقيم المعروضة هي تقديرات للعملة للمرجعية فقط.",
  "amountInCurrency": "المبلغ بالـ {currency}",
  "selectTopUpMethod": "اختر طريقة شحن المحفظة",
  "bankTransfer": "تحويل بنكي",
  "debitCard": "بطاقة خصم Debit",
  "take23DaysToComplete": "يستغرق إتمامه 2-3 أيام",
  "more": "المزيد",
  "copyReferenceNumber": "انسخ \"الرقم المرجعي\" لإتمام العملية بسلاسة.",
  "bankTransferPending": "تحويل بنكي قيد الانتظار",
  "yourTransferWasInitiated": "تم بدء التحويل. لإضافة الأموال إلى محفظتك، يرجى تحويل المبلغ المحدد من حساب بنكي شخصي أو مشترك.",
  "howToTopUpYourWallet": "كيفية شحن رصيد محفظتك",
  "copyAccountDetails": "انسخ تفاصيل الحساب",
  "useTheDisplayedBankInfoForYourTransfer": "استخدم المعلومات البنكية المعروضة لإجراء التحويل.",
  "makeThePayment": "قم بالدفع",
  "completeTheTransferThroughYourBankingApp": "أكمل التحويل من خلال تطبيقك البنكي.",
  "waitForTheConfirmation": "انتظر التأكيد",
  "weWillVerifyYourPaymentAndUpdateYourWallet": "سنتحقق من دفعتك ونقوم بتحديث محفظتك.",
  "depositAmount": "مبلغ الإيداع",
  "referenceNumber": "الرقم المرجعي",
  "done": "تم",
  "payment": "الدفع",
  "fundsAddedSuccessfully": "تمت إضافة الأموال بنجاح!",
  "withdrawCash": "سحب نقدي",
  "payoutRequest": "طلب سحب",
  "selectBankAccount": "اختر حسابًا بنكيًا",
  "withdrawalsProcessingTime": "تستغرق عمليات السحب عادةً من 3 إلى 7 أيام للمعالجة.",
  "profile": "الملف الشخصي",
  "myProfile": "ملفي الشخصي",
  "updatePassword": "تغيير كلمة المرور",
  "referrals": "",
  "sellProperties": "بيع العقارات",
  "withdrawalRequestSubmittedSuccessfully": "تم تقديم طلب السحب بنجاح.",
  "memberSince": "عضو منذ",
  "personalDetails": "البيانات الشخصية",
  "notProvided": "غير متوفر",
  "edit": "تعديل",
  "employmentDetails": "بيانات التوظيف",
  "noEnoughCredit": "ليس لديك رصيد كافٍ للمتابعة.",
  "toChangeYourFullNameOrEmail": "لتغيير اسمك الكامل أو بريدك الإلكتروني، يرجى",
  "contactTheAdmin": "التواصل مع المسؤول",
  "oldPassword": "كلمة المرور القديمة",
  "newPassword": "كلمة المرور الجديدة",
  "confirmPassword": "تأكيد كلمة المرور",
  "passwordDoNotMatch": "كلمات المرور غير متطابقة",
  "save": "حفظ",
  "passwordUpdatedSuccessfully": "تم تحديث كلمة المرور بنجاح",
  "noDocumentsFound": "لم يتم العثور على أي وثائق.",
  "uploadDocument": "تحميل وثيقة",
  "fileTooLarge": "الملف كبير جداً",
  "pleaseSelectASmallerFile": "يرجى اختيار ملف بحجم أقل من 4 ميغابايت.",
  "invalidFileType": "نوع ملف غير صالح",
  "pleaseSelectAPDFOrImageFile": "يرجى اختيار ملف PDF أو صورة (JPEG, PNG، إلخ.)",
  "fileName": "اسم الملف",
  "upload": "تحميل",
  "fileUploadedSuccessfully": "تم تحميل الملف بنجاح!",
  "duplicateDocumentName": "تم اكتشاف اسم مستند مكرر. يرجى إعادة تسمية المستند والمحاولة مرة أخرى.",
  "preferences": "التفضيلات",
  "appInfoSupport": "معلومات ودعم التطبيق",
  "logoutOptions": "خيارات تسجيل الخروج",
  "language": "اللغة",
  "selectLanguage": "اختر اللغة",
  "selectYourPreferredLanguageItWillApplyThroughoutTheApp": "اختر لغتك المفضلة. سيتم تطبيقها على مستوى التطبيق.",
  "selectCurrency": "اختر العملة",
  "currencyDescription": "تتم جميع المعاملات في ميسور بالدرهم الإماراتي. ومع ذلك، يمكنك استخدام هذا القسم لتقدير القيم بالعملات المدرجة لتسهيل استخدامك. يتم تحديث أسعار الصرف بشكل دوري.",
  "unitedArabEmiratesDirham": "الدرهم الإماراتي",
  "unitedStatesDollar": "الدولار الأمريكي",
  "euro": "اليورو",
  "currency": "العملة",
  "notifications": "الإشعارات",
  "ourStory": "قصتنا",
  "getHelp": "طلب المساعدة",
  "shareApp": "شارك التطبيق",
  "hiHowCanWeHelp": "أهلاً، كيف يمكننا المساعدة؟",
  "helpCenter": "مركز المساعدة",
  "raiseTicket": "تقديم طلب دعم",
  "getInTouch": "تواصل معنا",
  "chatWithUs": "تحدث معنا",
  "frequentlyAskedQuestions": "الأسئلة الشائعة",
  "noFaqsAvailable": "لا توجد أسئلة شائعة متاحة.",
  "message": "رسالة",
  "supportRequestSubmittedSuccessfully": "تم تقديم طلب الدعم بنجاح. سيتواصل معك فريقنا قريبًا بمزيد من التفاصيل.",
  "closeAccount": "إغلاق الحساب",
  "logout": "تسجيل الخروج",
  "areYouSureYouWantToLogout": "هل أنت متأكد من تسجيل الخروج؟",
  "reasonForClosingAccount": "سبب إغلاق الحساب",
  "deleteAccountActionRequired": "وفقًا للوائح التنظيمية، لا يمكننا حذف البيانات المالية. يمكنك طلب إلغاء تفعيل الحساب، بينما نقوم بتخزين بياناتك بشكل آمن حسب المطلوب.",
  "yourAccountHasBeenSuccessfullyClosed": "تم إغلاق حسابك بنجاح.",
  "followUs": "تابعنا",
  "version": "الإصدار",
  "completeYourOnboarding": "أكمل عملية التسجيل",
  "domainNotAllowed": "يبدو أن هناك مشكلة في بريدك الإلكتروني أثناء التسجيل.\nيُرجى التواصل مع فريق الدعم لدينا.\nسيعاودون الاتصال بك قريبًا.\n\nللتواصل:\n\nالهاتف: {phone}\n\nالبريد الإلكتروني: {email}",
  "itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName": "يجب أن يكون الحساب باسمك (شخصي أو مشترك)، التزامًا بلوائح مكافحة غسيل الأموال.",
  "shareAppDesc": "اكتشفت للتو تطبيقاً مذهلاً يُدعى Maisour وأعتقد أنك ستحبه!\nيسمح لك بامتلاك جزء من عقارات تأجير مميزة في دبي، والحصول على إيجار بالإضافة إلى نمو رأس المال، كل ذلك بكل سهولة ودون أي عناء! 🏢💰\nحمّل التطبيق من المتجر أو اضغط على الرابط التالي:\nhttps://redirect.maisour.ae\nجرّبه وأخبرني برأيك! متحمس لمعرفة رأيك!",
  "thisIsWhereYourTransactionsWillBeListed": "ستظهر معاملاتك هنا.\nتصفح العقارات للاستثمار.",
  "toViewMoreDetailsPlease": "للاطلاع على المزيد من التفاصيل،",
  "yourAccountHasBeenSuspended": "لقد تم تعليق حسابك.",
  "countryOfBirthUpdatedSuccessfully": "تم تحديث بلد الميلاد بنجاح!",
  "addTaxDetails": "أضف بياناتك الضريبية للالتزام بالقوانين واللوائح",
  "add": "أضف "
};
static const Map<String, Map<String,dynamic>> mapLocales = {"en": _en, "ar": _ar};
}
