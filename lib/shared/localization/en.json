{"deviceIsRooted": "Your device might be rooted", "deviceIsJailbroken": "Your device might be jailbroken", "deviceIsRootedDesc": "Using a rooted device may pose serious secuirty risks and prevent Maisour from ensuring your online protection", "deviceIsJailbrokenDesc": "Using a jailbroken device may pose serious secuirty risks and prevent <PERSON><PERSON><PERSON> from ensuring your online protection", "cancel": "Cancel", "welcomeTitle1": "Invest in Dubai’s Growth", "welcomeTitle2": "Earn Monthly Returns", "welcomeTitle3": "Exclusive & Vetted Listings", "welcomeTitle4": "Smart & Simple Investing", "welcomeDesc1": "Tap into one of the world’s most dynamic real estate markets—securely, from anywhere.", "welcomeDesc2": "Get guaranteed rental income with fully managed, fractional property investments.", "welcomeDesc3": "We shortlist only the top-performing assets so you invest with confidence, not complexity.", "welcomeDesc4": "Start investing with ease, track your portfolio, and grow your wealth with just a few taps.", "startInvesting": "Start Investing", "maisourRegulatedDSFA": "Maisour is regulated by DFSA", "somethingWentWrong": "Something went wrong. Please try again.", "later": "Later", "updateNow": "Update Now", "updateMaisourApp": "Update Maisour App", "updateRecommendationMessage": "Maisour recommends you to update the app to the latest version for better performance and security.", "noInternetConnection": "No Internet Connection", "noInternetDescription": "Please check your network settings and try again.", "tryAgain": "Try Again", "checkingConnection": "Checking connection...", "connectionCheckFailed": "Connection check failed", "connectedButNoInternet": "Connected to {connectionType} but no internet access", "getStarted": "Get Started", "loginSubtitle": "Please enter your email address to continue", "login": "<PERSON><PERSON>", "continueText": "Continue", "orText": "or", "continueWithApple": "Continue with Apple", "continueWithGoogle": "Continue with Google", "byContinuingYouAccepting": "By continuing you accepting", "termOfUse": "Term of Use", "privacyPolicy": "Privacy Policy", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "didntReceiveEmail": "Didn't receive the email?", "resendIn": "Resend in", "sendResetLink": "Send Reset Link", "resendResetLink": "Resend Reset Link", "fieldRequired": "{field} is required", "enterValidEmail": "Please enter a valid email address", "welcomeBack": "Welcome Back", "passwordSubtitle": "Enter the password for the account", "passwordMinLength": "Password must be at least {minLength} characters", "forgotPasswordSubtitle": "No worries! Enter your email address below and we'll send you a link to reset your password.", "yourAccountHasBeenClosed": "Your account has been closed", "accountClosedMessage": "Your Account Is Currently Closed. For Support, Please Reach out to us on Whatsapp.", "goToWhatsapp": "Go To Whatsapp", "emailRequired": "Email Required", "emailRequiredMessage": "We couldn’t retrieve your email from Apple Sign-In. Our app requires an email address to continue. Please use a different sign-in method or provide a valid email to proceed.", "oops": "Oops!!", "success": "Success", "domainNotAllowed": "Your email ID seems to have an issue with our registration process. Please contact our CRM team. They will revert back to you shortly. \n\nPhone: {phone}\nEmail: {email}", "accountDisabled": "Your account has been disabled. Please contact the Maisour support team at {email} for assistance.", "accountClosed": "Your account has been closed. Please contact the Maisour support team at {email} for assistance.", "accountNotActivated": "Please verify your email address to continue. We've sent a verification link to your email. Check your inbox (and spam folder) and click the link to activate your account.", "accountSuspended": "Your account has been suspended. Please contact the Maisour support team at {email} for assistance.", "invalidCredentials": "That doesn’t seem right — check your password and try again.", "forgotPasswordSuccess": "We've sent a password reset link to your email. Please check your inbox (and spam folder) and follow the instructions to reset your password.", "createAccount": "Create Account", "createAccountWith": "You are about to create account with", "fullName": "Full Name", "fullNameHelperText": "Enter your legal name, as it appears on your government ID.", "referralCode": "Referral Code", "hearAboutMaisour": "How did you hear about <PERSON><PERSON><PERSON>?", "pleaseSpecify": "Please Specify", "passwordHelperText": "Include {minLength}+ characters, {upperCaseCount} uppercase, {lowerCaseCount} lowercase, {numberCount} number & {specialCharacterCount} special character.", "characters": "characters", "uppercase": "uppercase", "lowercase": "lowercase", "number": "number", "specialCharacter": "special character", "verifyYourEmail": "Verify Your Email", "anEmailSentTo": "An email sent to", "checkYourInbox": "Please check your inbox and spam folder to continue.", "resendEmail": "<PERSON><PERSON><PERSON>", "backToLogin": "Back to Login", "search": "Search", "others": "Others", "duplicateEmail": "This email is already used. Please use a different email.", "emailSentSuccessfully": "<PERSON><PERSON> sent successfully!", "emailVerificationLink": "We’ve sent a verification link to {email}. Please check your inbox and spam folder to continue.", "invalidReferralCode": "Referral code is incorrect, please try again.", "properties": "Properties", "portfolio": "Portfolio", "wallet": "Wallet", "account": "Account", "live": "Live", "comingSoon": "Coming Soon", "funded": "Funded", "sold": "Sold", "request": "Request", "cancelled": "Cancelled", "verifyIdentityToStartInvestment": "Verify your identity to start investment.", "verifyNow": "Verify Now", "onboardingTitle": "Complete Your Profile", "onboardingSubtitle": "Add your details to verify your identity and continue your investment journey.", "verifyYourPassport": "Verify Your Passport", "verifyYourPassportDesc": "Upload a valid passport to verify your identity and comply with regulations.", "verifyYourAddress": "Verify Your Address", "verifyYourAddressDesc": "Provide a valid document to verify your current residential address.", "employmentInfo": "Employment Info", "employmentInfoDesc": "Provide your job information to help us understand your investor profile.", "taxResidencyInformation": "Tax Residency Information", "taxResidencyInformationDesc": "Provide details to determine your tax residency and meet legal compliance requirements.", "completed": "Completed", "pending": "Pending", "startVerification": "Start Verification", "doItLater": "I’ll do it later", "verifyYourAccount": "Verify Your Account", "verifyYourAccountDesc": "Secure your account and unlock full access by completing your verification. It only takes a few minutes.", "verifyAccount": "Verify Account", "passportSubtitle": "Financial regulations require us to verify your identity before you can invest. This helps protect your investment and allows us to register you as the legal owner of each property you invest in.", "passportAttempt": "{attempt} Attempt", "passportVerification": "Passport Verification", "addressVerification": "Address Verification", "verificationInProgressWait": "Your verification is in progress, please try again in 2 minutes.", "verificationJourneyCancelled": "Your journey has been cancelled. Please try again.", "verificationJourneyBlocked": "Your journey has been blocked. Please try again.", "verificationInProgress": "Verification in Progress", "verificationInProgressDesc": "We're reviewing your details. This won't take long.", "tooManyAttempts": "Too Many Attempts", "tooManyAttemptsDesc": "You have reached the maximum number of attempts for passport verification.", "passportVerificationCompleted": "Passport Verification Completed", "passportVerificationCompletedDesc": "Please proceed with the next step of address information.", "addressVerificationCompleted": "Address Verification Completed", "addressVerificationCompletedDesc": "Please proceed with the next step of employment information.", "passportVerificationFailed": "Passport Verification Failed", "passportVerificationFailedDesc": "We couldn’t confirm your passport details. You can try again now or finish this step later.", "viewDetails": "View Details", "possibleReasonsForVerificationFailure": "Possible Reasons for Verification Failure", "possibleReasonsForVerificationFailureDesc": "<p><b>This may have occurred due to one or more reasons, such as:</b></p> <ul> <li>The passport scan was unclear or not fully visible.</li> <li>The selfie was taken in poor lighting or didn’t clearly show your face.</li> <li>The document details didn’t match the selfie.</li> </ul> <p><b>To resolve this, please try again and ensure the following:</b></p> <ul> <li>Your passport scan is clear, legible, and free of any glare or obstructions.</li> <li>Your selfie is taken in a well-lit area, with your face fully visible and centered.</li> <li>You follow all on-screen instructions carefully for both the passport scan and selfie.</li> </ul> <p>If you continue to face issues, ensure your device camera is functioning properly and avoid using screenshots or images of documents.</p>", "addressVerificationDesc": "Financial regulations require us to verify your address proof before you can invest. This helps protect your investment and allow us to register you as the legal owner of each property you invest in.", "employed": "Employed", "retired": "Retired", "student": "Student", "unemployed": "Unemployed", "employmentStatus": "Employment Status", "companyName": "Company Name", "role": "Role", "workAddress": "Work Address", "annualIncome": "Annual Income", "lessThan10000": "Less than {dollarSign}10,000", "from10000To50000": "10,000 To {dollarSign}50,000", "from50000To100000": "50,000 To {dollarSign}100,000", "moreThan100000": "More than {dollarSign}100,000", "industry": "Industry", "countryOfBirth": "Country of Birth", "countryOfBirthHelperText": "Select the country of birth as per your passport.", "submit": "Submit", "employmentInfoSubmittedSuccessfully": "Employment information submitted successfully", "taxResidencyInformationScreenDesc": "We are required by law to collect your tax residency details to comply with international tax reporting standards (FATCA & CRS). Please provide the following information to proceed.", "countryOfResidence": "Country of Residence", "doYouHaveATaxIdentificationNumber": "Do You have a Tax Identification Number (TIN) in {country}?", "areYouAlsoAResidentInAnyOtherCountry": "Are you also a resident in any other country?", "additionalTaxResidencyCountry": "Additional tax residency country", "byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge": "By continuing you confirm that the above information is accurate & complete to the best of your knowledge.", "taxResidencySubmittedSuccessfully": "Tax Residency submitted successfully", "yes": "Yes", "no": "No", "notApplicable": "Not Applicable", "tinNumber": "TIN Number", "tinNumberLength": "TIN number must be between 10 and 15 characters", "verifyPhoneNumber": "Verify Phone Number", "confirmYourNumberToSecureYourAccount": "Confirm your number to secure your account.", "sendVerificationCode": "Send Verification Code", "phoneNumber": "Phone Number", "enterValidPhoneNumber": "Please enter a valid phone number", "accountVerificationPending": "Account Verification Pending", "accountVerificationPendingDesc": "Account verification pending. We’ll notify you soon.", "phoneNumberAlreadyInUse": "The entered phone number is already in use, please try another one.", "codeGenerationLimitExceeded": "You have exceeded the code generation limit for the day, please try tomorrow.", "waitMinuteForNewCode": "Please wait a minute before requesting a new code.", "verificationCodeSent": "Verification code has been sent to your number.", "enterVerificationCode": "Enter Verification Code", "enterThe5DigitCode": "Enter the 5-digit code sent to your number.", "didntReceiveCode": "Didn't receive the code?", "resend": "Resend", "verify": "Verify", "otpCode": "OTP Code", "otpCodeLength": "OTP code must be 5 digits", "invalidVerificationCode": "The verification code you entered is invalid. Please try again.", "codeVerifiedSuccessfully": "Code Verified Successfully", "phoneNumberVerifiedSuccessfully": "Your phone number has been verified. you're ready to start investing in the properties.", "accountVerificationFailed": "Your account could not be verified.", "accountVerificationFailedDesc": "Please contact our CRM team. They will revert back to you shortly.", "contactUs": "Contact Us", "newlyAdded": "Newly added", "trending": "Trending", "expectedAnnualNetRent": "Expected annual net rent", "expected5YearsNetRent": "Expected 5 years net rent", "expected5YearsNetReturns": "Expected 5 years net returns", "aed": "AED", "usd": "USD", "eur": "EUR", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "biannually": "<PERSON><PERSON><PERSON><PERSON>", "annually": "Annually", "years": "Years", "noPropertiesAvailable": "No Properties Available", "noPropertiesAvailableDesc": "There are currently no properties available. Please check back later.", "sortBy": "Sort By", "propertyOldestToLatest": "Property - Oldest to latest", "fundingStatusHighestToLowest": "Funding status - Highest to lowest", "investors": "Investors", "annualGrossRent": "Annual Gross Rent", "minInvestment": "Min. Investment", "rentDistribution": "Rent Distribution", "suggestedHoldingPeriod": "Suggested Holding Period", "aboutRentalIncome": "About Rental Income", "aboutRentalIncomeDesc": "Once the property is fully funded, you'll start receiving a fixed monthly rental income, providing consistent cash flow from your investment.", "investmentBreakdown": "Investment Breakdown", "purchase": "Purchase", "rental": "Rental", "purchaseCost": "Purchase cost", "transactionCost": "Transaction cost", "totalAcquisitionCost": "Total acquisition cost", "grossRentPerYear": "Gross rent per year", "serviceCharges": "Service charges", "otherCosts": "Other costs", "numberOfShares": "Number of shares", "totalPricePerShare": "Total price per share", "guaranteedRentalIncome": "Guaranteed Rental Income", "aboutProperty": "About Property", "readMore": "Read more", "amenities": "Amenities", "viewAll": "View all", "exploreOnMap": "Explore on Map", "chooseMapApp": "Choose Map App", "appleMaps": "Apple Maps", "googleMaps": "Google Maps", "documents": "Documents", "contactSupport": "Contact Support", "doYouHaveQuestionsAboutTheProperty": "Do you have questions about the property?", "developerAndManager": "Developer & Manager", "investNow": "Invest Now", "preOrderNow": "Pre-order Now", "noPaymentYet": "No payment yet", "preOrder": "Pre-order", "yourInvestment": "Your Investment", "financialDetails": "Financial details", "amountToInvest": "Amount to Invest", "walletBalance": "Wallet Balance", "yourWalletWillNotBeCharged": "Your wallet will not be charged.", "pricePerShare": "Price per share", "investmentAmount": "Investment amount", "potentialRewards": "Potential rewards", "totalCost": "Total cost", "minimum": "<PERSON>.", "placeOrder": "Place Order", "howPreOrderWorks": "How Pre-order Works", "showInterest": "Show Interest", "noPaymentNeededJustShowInterested": "No payment needed—just show interested.", "getNotified": "Get Notified", "weWillNotifyYouWhenThePropertyIsLive": "We’ll notify you when the property is live.", "confirmAndInvest": "Confirm & Invest", "reviewDetailsAndMakeYourPayment": "Review details and make your payment.", "notEnoughBalance": "You don’t have enough balance in your wallet and rewards to proceed.", "yourOrder": "Your Order", "shares": "Shares", "totalInvestment": "Total Investment", "youAgreeTo": "You agree to", "investmentAgreement": "investment agreement", "proceedToPay": "Proceed to Pay", "actionRequired": "Action Required", "pleaseAcceptTheInvestmentAgreementToContinue": "Please accept the Investment Agreement to continue.", "agreeAndContinue": "Agree & Continue", "minInvestError": "Enter at least the minimum investment amount to proceed.", "maxInvestError": "The entered amount is greater than the remaining value of the unit", "yearlyLimitExceeded": "You have exceeded your yearly investment limit.", "propertyInvestmentLimitExceeded": "You have reached the maximum investment limit for this property.", "investmentConfirmation": "Investment Confirmation", "interestConfirmation": "Interest Confirmation", "paymentSuccessful": "Payment Successful", "interestSuccessful": "Interest Successful", "viewPortfolio": "View Portfolio", "investmentCalculator": "Investment Calculator", "expectedInvestmentReturnIn5Years": "Expected investment return in 5 years", "investment": "Investment", "returns": "Returns", "totalRentalYield": "Total rental yield", "valueAppreciation": "Value appreciation", "totalInvestmentReturn": "Total Investment return", "grossInvestmentGain": "Gross investment gain (ROI)", "initialInvestment": "Initial Investment", "propertyValueGrowth": "Property Value Growth", "expectedRentalYield": "Expected <PERSON><PERSON>", "myOwnerships": "My Ownerships", "cashBalance": "Cash Balance", "topUp": "Top up", "rewardBalance": "<PERSON><PERSON>", "noTransactionsYet": "No Transactions Yet", "thisIsWhereYourTransactionsWillBeListed": "This is where your transactions will be listed.\nBrowse properties to invest.", "transactions": "Transactions", "bankAccounts": "Bank Accounts", "safeTransfersIn23Days": "Safe transfers in 2-3 days.", "addBankAccount": "Add Bank Account", "itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName": "It must be a personal or jointly owned bank account\nunder your name (per AML regulations).", "addAccount": "Add Account", "yourMoneyIsProtected": "Your money is protected.", "thePropertiesAreRegisteredInDLD": "The properties are registered in DLD.", "weHaveAPlanForSafeKeepingAssets": "We have a plan for safe-keeping assets.", "actionRestricted": "Action Restricted", "pleaseVerifyYourAccount": "You need to verify your identity before performing this action.", "yourIdentityVerificationIsUnderReview": "Your identity verification is under review. For assistance, please visit the Contact Us section.", "yourPassportIsExpired": "Your passport is expired. Please update your passport to continue.", "yourPassportIsExpiringSoon": "Passport expiring soon. Please update your passport to keep investing.", "asPerAmlRegulationsAccountRestricted": "As per AML regulations, your account is restricted from accessing investment opportunities. Please visit the Contact Us section for assistance.", "purchasePrice": "Purchase Price", "currentValuation": "Current Valuation", "investmentSummary": "Investment Summary", "ownershipPercentage": "Ownership percentage", "timeline": "Timeline", "investedInProperty": "Invested in Property", "interestShownInProperty": "Interest Shown in Property", "dividendHistory": "Dividend History", "dividendCredited": "Dividend Credited", "failed": "Failed", "reversed": "Reversed", "refunded": "Refunded", "successful": "Successful", "rejected": "Rejected", "notFunded": "Not Funded", "removed": "Removed", "purchaseDetails": "Purchase Details", "amountInvested": "Amount Invested", "status": "Status", "cancelInvestment": "Cancel Investment", "deleteInvestment": "Delete Investment", "confirm": "Confirm", "invest": "Invest", "update": "Update", "all": "All", "withdrawal": "<PERSON><PERSON><PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "dividend": "Dividend", "reward": "<PERSON><PERSON>", "saleProceed": "Sale Proceed", "otherAdjustment": "Other Adjustment", "filters": "Filters", "last30Days": "Last 30 days", "last3Months": "Last 3 months", "last6Months": "Last 6 months", "primary": "Primary", "accountName": "Account Name", "bankName": "Bank Name", "bankNameLength": "Bank name must not exceed 70 characters", "invalidBankName": "Invalid Bank Name", "accountNumber": "Account Number", "accountNumberLength": "Account number must be 6–20 digits", "invalidAccountNumber": "Invalid Account Number", "country": "Country", "iban": "IBAN Code", "ibanLength": "IBAN must be 15–34 characters", "invalidIban": "Invalid IBAN", "swiftCode": "Swift Code", "swiftCodeLength": "Swift code must be 8 or 11 characters", "invalidSwiftCode": "Invalid Swift Code", "primaryAccount": "Primary Account", "bankAccountAddedSuccessfully": "Bank account added successfully", "bankAccountSetAsPrimarySuccessfully": "Bank account set as primary successfully", "bankAccountStatusUpdatedSuccessfully": "Bank account status updated successfully", "bankAccountDeletedSuccessfully": "Bank account deleted successfully", "cannotSetPrimaryBankAccountAsInactive": "You cannot set the primary bank account as inactive", "deleteAccount": "Delete Account", "areYouSureYouWantToDeleteThisBankAccount": "Are you sure you want to delete this bank account?", "delete": "Delete", "withdraw": "Withdraw", "addFunds": "Add Funds", "topUpYourWallet": "Top Up Your Wallet", "allTransactionsInAED": "All transactions are carried out in AED, values shown are currency estimates for reference only.", "amountInCurrency": "Amount in {currency}", "selectTopUpMethod": "Select Top Up Method", "bankTransfer": "Bank Transfer", "debitCard": "Debit Card", "take23DaysToComplete": "Takes 2-3 days to complete", "more": "more", "copyReferenceNumber": "Copy \"Reference number\" for seamless processing.", "bankTransferPending": "Bank Transfer Pending", "yourTransferWasInitiated": "Your transfer was initiated. To add funds to your wallet, transfer the specified amount from a personal or jointly owned account.", "howToTopUpYourWallet": "How to Top Up Your Wallet", "copyAccountDetails": "<PERSON>py Account Details", "useTheDisplayedBankInfoForYourTransfer": "Use the displayed bank info for your transfer.", "makeThePayment": "Make the Payment", "completeTheTransferThroughYourBankingApp": "Complete the transfer through your banking app.", "waitForTheConfirmation": "Wait for the Confirmation", "weWillVerifyYourPaymentAndUpdateYourWallet": "We’ll verify your payment and update your wallet.", "depositAmount": "Deposit amount", "referenceNumber": "Reference number", "done": "Done", "payment": "Payment", "fundsAddedSuccessfully": "Funds added successfully!", "withdrawCash": "Withdraw Cash", "payoutRequest": "Payout Request", "selectBankAccount": "Select Bank Account", "withdrawalsProcessingTime": "Withdrawals typically require 3-7 days for processing.", "profile": "Profile", "myProfile": "My Profile", "updatePassword": "Update Password", "referrals": "Referrals", "sellProperties": "Sell Properties", "withdrawalRequestSubmittedSuccessfully": "<PERSON>drawal request submitted successfully", "memberSince": "Member since", "personalDetails": "Personal Details", "notProvided": "Not provided", "edit": "Edit", "employmentDetails": "Employment Details", "noEnoughCredit": "You don’t have enough credit to proceed.", "toChangeYourFullNameOrEmail": "To change your full name or email, please", "contactTheAdmin": "contact the admin", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordDoNotMatch": "Passwords do not match", "save": "Save", "passwordUpdatedSuccessfully": "Password updated successfully", "noDocumentsFound": "No documents found", "uploadDocument": "Upload Document", "fileTooLarge": "File too large", "pleaseSelectASmallerFile": "Please select a file smaller than 4MB", "invalidFileType": "Invalid file type", "pleaseSelectAPDFOrImageFile": "Please select a PDF or image file (JPEG, PNG, etc.)", "fileName": "File Name", "upload": "Upload", "fileUploadedSuccessfully": "File uploaded successfully!", "duplicateDocumentName": "Duplicate document name detected. Please rename your document and try again.", "preferences": "Preferences", "appInfoSupport": "App Info & Support", "logoutOptions": "Logout Options", "language": "Language", "selectLanguage": "Select Language", "selectYourPreferredLanguageItWillApplyThroughoutTheApp": "Select your preferred language. It will apply throughout the app.", "selectCurrency": "Select Currency", "currencyDescription": "All transactions in Maisour are processed in United Arab Emirates Dirhams. However you can use this section to approximate the values in listed currencies for your ease of use. We regularly update the currency rates.", "unitedArabEmiratesDirham": "United Arab Emirates Dirham", "unitedStatesDollar": "United States Dollar", "euro": "Euro", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "Notifications", "ourStory": "Our Story", "getHelp": "Get Help", "shareApp": "Share App", "hiHowCanWeHelp": "Hi, How can we help?", "helpCenter": "Help Center", "raiseTicket": "<PERSON><PERSON> Ticket", "getInTouch": "Get in Touch", "chatWithUs": "Chat with Us", "frequentlyAskedQuestions": "Frequently Asked Questions", "noFaqsAvailable": "No FAQs available", "message": "Message", "supportRequestSubmittedSuccessfully": "Support request submitted successfully. Our team will reach out to you soon with further details.", "shareAppDesc": "Just discovered an amazing app called Maisour that I think you will love. It lets you easily own a part of prime rental properties in Dubai and earn rent plus capital growth, all hassle-free! 🏢💰\nDownload from the store or click on:\nhttps://redirect.maisour.ae\nGive it a try and let me know your thoughts! Excited to hear what you think!", "closeAccount": "Close Account", "logout": "Logout", "areYouSureYouWantToLogout": "Are you sure you want to logout?", "reasonForClosingAccount": "Reason for closing account", "deleteAccountActionRequired": "Due to regulation, we can't delete financial data, you can request account deactivation, while we securely store your data as required.", "yourAccountHasBeenSuccessfullyClosed": "Your account has been successfully closed", "followUs": "Follow us", "version": "Version", "completeYourOnboarding": "Complete Your Onboarding.", "toViewMoreDetailsPlease": "To view more details, please", "yourAccountHasBeenSuspended": "Your account has been suspended.", "countryOfBirthUpdatedSuccessfully": "Country of Birth updated successfully!", "addTaxDetails": "Add your tax details to meet compliance requirements.", "add": "Add"}