import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';

/// Route observer for tracking screen views with AppsFlyer
/// Works with GoRouter and respects iOS App Tracking Transparency permissions
class AppRouteObserver extends NavigatorObserver {
  final AppsFlyerService _appsFlyerService;

  AppRouteObserver(this._appsFlyerService);

  /// Extract screen name from route settings
  String _getScreenName(Route<dynamic> route) {
    String? routeName = route.settings.name;

    if (routeName == null || routeName.isEmpty) {
      // Fallback to route type if name is not available
      return route.runtimeType.toString();
    }

    // Clean up the route name for AppsFlyer
    // Remove leading slash and convert to snake_case
    String screenName = routeName.startsWith('/')
        ? routeName.substring(1)
        : routeName;

    // Replace hyphens with underscores for consistency
    screenName = screenName.replaceAll('-', '_');

    // If empty after cleanup, use a default name
    if (screenName.isEmpty) {
      screenName = 'unknown_screen';
    }

    return screenName;
  }

  /// Log screen view to AppsFlyer with error handling
  Future<void> _logScreenView(Route<dynamic> route) async {
    try {
      // Filter out non-page routes (popups, dialogs, bottom sheets)
      final routeType = route.runtimeType.toString();
      const ignoredRouteTypes = [
        '_PopupMenuRoute',
        'DialogRoute',
        'PopupRoute',
        'ModalBottomSheetRoute',
      ];

      if (route is! PageRoute ||
          ignoredRouteTypes.any((type) => routeType.startsWith(type))) {
        debugPrint('🚫 Route Observer: Ignored ${route.runtimeType}');
        return;
      }

      final screenName = _getScreenName(route);

      debugPrint('📱 Route Observer: Screen changed to $screenName');

      // Log to AppsFlyer (will automatically check tracking permissions)
      await _appsFlyerService.logScreenView(screenName);
    } catch (error) {
      // Don't let tracking errors affect navigation
      debugPrint('❌ Route Observer: Error logging screen view - $error');
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);

    debugPrint('🔄 Route Observer: didPush - ${route.settings.name}');

    // Log screen view for the new route
    _logScreenView(route);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);

    if (newRoute != null) {
      debugPrint('🔄 Route Observer: didReplace - ${newRoute.settings.name}');

      // Log screen view for the replacement route
      _logScreenView(newRoute);
    }
  }

  // Note: didPop and didRemove are intentionally not implemented
  // because we only want to track when users navigate TO a screen,
  // not when they navigate AWAY from a screen (to avoid double tracking)
}

/// Provider for the route observer
/// Uses autoDispose since it's tied to the widget lifecycle
final appRouteObserverProvider = Provider.autoDispose<AppRouteObserver>((ref) {
  final appsFlyerService = ref.watch(appsFlyerServiceProvider);
  return AppRouteObserver(appsFlyerService);
}, name: 'appRouteObserverProvider');
