// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppUser {

 int get id; String? get phoneNumber; String? get countryOfBirth; AccountStatus get accountStatus; VerifiedStatus get verifiedStatus; User get user; double get credit; DateTime? get documentIssueDate; DateTime? get documentExpirationDate; UserAccountTypeDetails get userAccountTypeDetails; FirebaseDeviceTokens? get firebaseDeviceTokens; bool get taxResidencyInfoExists; VariableFields get variableFields; Currency get currencyCode; double get rewardPoints; String get invitationCode; bool get invitationRequestUserExist; int get totalDocument; EmploymentStatus? get workStatus; String? get employer; String? get industryLov; String? get roleLov; AnnualIncome? get annualIncome; String? get workAddress;
/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppUserCopyWith<AppUser> get copyWith => _$AppUserCopyWithImpl<AppUser>(this as AppUser, _$identity);

  /// Serializes this AppUser to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppUser&&(identical(other.id, id) || other.id == id)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.countryOfBirth, countryOfBirth) || other.countryOfBirth == countryOfBirth)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.verifiedStatus, verifiedStatus) || other.verifiedStatus == verifiedStatus)&&(identical(other.user, user) || other.user == user)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.documentIssueDate, documentIssueDate) || other.documentIssueDate == documentIssueDate)&&(identical(other.documentExpirationDate, documentExpirationDate) || other.documentExpirationDate == documentExpirationDate)&&(identical(other.userAccountTypeDetails, userAccountTypeDetails) || other.userAccountTypeDetails == userAccountTypeDetails)&&(identical(other.firebaseDeviceTokens, firebaseDeviceTokens) || other.firebaseDeviceTokens == firebaseDeviceTokens)&&(identical(other.taxResidencyInfoExists, taxResidencyInfoExists) || other.taxResidencyInfoExists == taxResidencyInfoExists)&&(identical(other.variableFields, variableFields) || other.variableFields == variableFields)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.rewardPoints, rewardPoints) || other.rewardPoints == rewardPoints)&&(identical(other.invitationCode, invitationCode) || other.invitationCode == invitationCode)&&(identical(other.invitationRequestUserExist, invitationRequestUserExist) || other.invitationRequestUserExist == invitationRequestUserExist)&&(identical(other.totalDocument, totalDocument) || other.totalDocument == totalDocument)&&(identical(other.workStatus, workStatus) || other.workStatus == workStatus)&&(identical(other.employer, employer) || other.employer == employer)&&(identical(other.industryLov, industryLov) || other.industryLov == industryLov)&&(identical(other.roleLov, roleLov) || other.roleLov == roleLov)&&(identical(other.annualIncome, annualIncome) || other.annualIncome == annualIncome)&&(identical(other.workAddress, workAddress) || other.workAddress == workAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,phoneNumber,countryOfBirth,accountStatus,verifiedStatus,user,credit,documentIssueDate,documentExpirationDate,userAccountTypeDetails,firebaseDeviceTokens,taxResidencyInfoExists,variableFields,currencyCode,rewardPoints,invitationCode,invitationRequestUserExist,totalDocument,workStatus,employer,industryLov,roleLov,annualIncome,workAddress]);

@override
String toString() {
  return 'AppUser(id: $id, phoneNumber: $phoneNumber, countryOfBirth: $countryOfBirth, accountStatus: $accountStatus, verifiedStatus: $verifiedStatus, user: $user, credit: $credit, documentIssueDate: $documentIssueDate, documentExpirationDate: $documentExpirationDate, userAccountTypeDetails: $userAccountTypeDetails, firebaseDeviceTokens: $firebaseDeviceTokens, taxResidencyInfoExists: $taxResidencyInfoExists, variableFields: $variableFields, currencyCode: $currencyCode, rewardPoints: $rewardPoints, invitationCode: $invitationCode, invitationRequestUserExist: $invitationRequestUserExist, totalDocument: $totalDocument, workStatus: $workStatus, employer: $employer, industryLov: $industryLov, roleLov: $roleLov, annualIncome: $annualIncome, workAddress: $workAddress)';
}


}

/// @nodoc
abstract mixin class $AppUserCopyWith<$Res>  {
  factory $AppUserCopyWith(AppUser value, $Res Function(AppUser) _then) = _$AppUserCopyWithImpl;
@useResult
$Res call({
 int id, String? phoneNumber, String? countryOfBirth, AccountStatus accountStatus, VerifiedStatus verifiedStatus, User user, double credit, DateTime? documentIssueDate, DateTime? documentExpirationDate, UserAccountTypeDetails userAccountTypeDetails, FirebaseDeviceTokens? firebaseDeviceTokens, bool taxResidencyInfoExists, VariableFields variableFields, Currency currencyCode, double rewardPoints, String invitationCode, bool invitationRequestUserExist, int totalDocument, EmploymentStatus? workStatus, String? employer, String? industryLov, String? roleLov, AnnualIncome? annualIncome, String? workAddress
});


$UserCopyWith<$Res> get user;$UserAccountTypeDetailsCopyWith<$Res> get userAccountTypeDetails;$FirebaseDeviceTokensCopyWith<$Res>? get firebaseDeviceTokens;$VariableFieldsCopyWith<$Res> get variableFields;

}
/// @nodoc
class _$AppUserCopyWithImpl<$Res>
    implements $AppUserCopyWith<$Res> {
  _$AppUserCopyWithImpl(this._self, this._then);

  final AppUser _self;
  final $Res Function(AppUser) _then;

/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? phoneNumber = freezed,Object? countryOfBirth = freezed,Object? accountStatus = null,Object? verifiedStatus = null,Object? user = null,Object? credit = null,Object? documentIssueDate = freezed,Object? documentExpirationDate = freezed,Object? userAccountTypeDetails = null,Object? firebaseDeviceTokens = freezed,Object? taxResidencyInfoExists = null,Object? variableFields = null,Object? currencyCode = null,Object? rewardPoints = null,Object? invitationCode = null,Object? invitationRequestUserExist = null,Object? totalDocument = null,Object? workStatus = freezed,Object? employer = freezed,Object? industryLov = freezed,Object? roleLov = freezed,Object? annualIncome = freezed,Object? workAddress = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,countryOfBirth: freezed == countryOfBirth ? _self.countryOfBirth : countryOfBirth // ignore: cast_nullable_to_non_nullable
as String?,accountStatus: null == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as AccountStatus,verifiedStatus: null == verifiedStatus ? _self.verifiedStatus : verifiedStatus // ignore: cast_nullable_to_non_nullable
as VerifiedStatus,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double,documentIssueDate: freezed == documentIssueDate ? _self.documentIssueDate : documentIssueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,documentExpirationDate: freezed == documentExpirationDate ? _self.documentExpirationDate : documentExpirationDate // ignore: cast_nullable_to_non_nullable
as DateTime?,userAccountTypeDetails: null == userAccountTypeDetails ? _self.userAccountTypeDetails : userAccountTypeDetails // ignore: cast_nullable_to_non_nullable
as UserAccountTypeDetails,firebaseDeviceTokens: freezed == firebaseDeviceTokens ? _self.firebaseDeviceTokens : firebaseDeviceTokens // ignore: cast_nullable_to_non_nullable
as FirebaseDeviceTokens?,taxResidencyInfoExists: null == taxResidencyInfoExists ? _self.taxResidencyInfoExists : taxResidencyInfoExists // ignore: cast_nullable_to_non_nullable
as bool,variableFields: null == variableFields ? _self.variableFields : variableFields // ignore: cast_nullable_to_non_nullable
as VariableFields,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as Currency,rewardPoints: null == rewardPoints ? _self.rewardPoints : rewardPoints // ignore: cast_nullable_to_non_nullable
as double,invitationCode: null == invitationCode ? _self.invitationCode : invitationCode // ignore: cast_nullable_to_non_nullable
as String,invitationRequestUserExist: null == invitationRequestUserExist ? _self.invitationRequestUserExist : invitationRequestUserExist // ignore: cast_nullable_to_non_nullable
as bool,totalDocument: null == totalDocument ? _self.totalDocument : totalDocument // ignore: cast_nullable_to_non_nullable
as int,workStatus: freezed == workStatus ? _self.workStatus : workStatus // ignore: cast_nullable_to_non_nullable
as EmploymentStatus?,employer: freezed == employer ? _self.employer : employer // ignore: cast_nullable_to_non_nullable
as String?,industryLov: freezed == industryLov ? _self.industryLov : industryLov // ignore: cast_nullable_to_non_nullable
as String?,roleLov: freezed == roleLov ? _self.roleLov : roleLov // ignore: cast_nullable_to_non_nullable
as String?,annualIncome: freezed == annualIncome ? _self.annualIncome : annualIncome // ignore: cast_nullable_to_non_nullable
as AnnualIncome?,workAddress: freezed == workAddress ? _self.workAddress : workAddress // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}
/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserAccountTypeDetailsCopyWith<$Res> get userAccountTypeDetails {
  
  return $UserAccountTypeDetailsCopyWith<$Res>(_self.userAccountTypeDetails, (value) {
    return _then(_self.copyWith(userAccountTypeDetails: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FirebaseDeviceTokensCopyWith<$Res>? get firebaseDeviceTokens {
    if (_self.firebaseDeviceTokens == null) {
    return null;
  }

  return $FirebaseDeviceTokensCopyWith<$Res>(_self.firebaseDeviceTokens!, (value) {
    return _then(_self.copyWith(firebaseDeviceTokens: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VariableFieldsCopyWith<$Res> get variableFields {
  
  return $VariableFieldsCopyWith<$Res>(_self.variableFields, (value) {
    return _then(_self.copyWith(variableFields: value));
  });
}
}


/// Adds pattern-matching-related methods to [AppUser].
extension AppUserPatterns on AppUser {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppUser value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppUser() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppUser value)  $default,){
final _that = this;
switch (_that) {
case _AppUser():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppUser value)?  $default,){
final _that = this;
switch (_that) {
case _AppUser() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String? phoneNumber,  String? countryOfBirth,  AccountStatus accountStatus,  VerifiedStatus verifiedStatus,  User user,  double credit,  DateTime? documentIssueDate,  DateTime? documentExpirationDate,  UserAccountTypeDetails userAccountTypeDetails,  FirebaseDeviceTokens? firebaseDeviceTokens,  bool taxResidencyInfoExists,  VariableFields variableFields,  Currency currencyCode,  double rewardPoints,  String invitationCode,  bool invitationRequestUserExist,  int totalDocument,  EmploymentStatus? workStatus,  String? employer,  String? industryLov,  String? roleLov,  AnnualIncome? annualIncome,  String? workAddress)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppUser() when $default != null:
return $default(_that.id,_that.phoneNumber,_that.countryOfBirth,_that.accountStatus,_that.verifiedStatus,_that.user,_that.credit,_that.documentIssueDate,_that.documentExpirationDate,_that.userAccountTypeDetails,_that.firebaseDeviceTokens,_that.taxResidencyInfoExists,_that.variableFields,_that.currencyCode,_that.rewardPoints,_that.invitationCode,_that.invitationRequestUserExist,_that.totalDocument,_that.workStatus,_that.employer,_that.industryLov,_that.roleLov,_that.annualIncome,_that.workAddress);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String? phoneNumber,  String? countryOfBirth,  AccountStatus accountStatus,  VerifiedStatus verifiedStatus,  User user,  double credit,  DateTime? documentIssueDate,  DateTime? documentExpirationDate,  UserAccountTypeDetails userAccountTypeDetails,  FirebaseDeviceTokens? firebaseDeviceTokens,  bool taxResidencyInfoExists,  VariableFields variableFields,  Currency currencyCode,  double rewardPoints,  String invitationCode,  bool invitationRequestUserExist,  int totalDocument,  EmploymentStatus? workStatus,  String? employer,  String? industryLov,  String? roleLov,  AnnualIncome? annualIncome,  String? workAddress)  $default,) {final _that = this;
switch (_that) {
case _AppUser():
return $default(_that.id,_that.phoneNumber,_that.countryOfBirth,_that.accountStatus,_that.verifiedStatus,_that.user,_that.credit,_that.documentIssueDate,_that.documentExpirationDate,_that.userAccountTypeDetails,_that.firebaseDeviceTokens,_that.taxResidencyInfoExists,_that.variableFields,_that.currencyCode,_that.rewardPoints,_that.invitationCode,_that.invitationRequestUserExist,_that.totalDocument,_that.workStatus,_that.employer,_that.industryLov,_that.roleLov,_that.annualIncome,_that.workAddress);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String? phoneNumber,  String? countryOfBirth,  AccountStatus accountStatus,  VerifiedStatus verifiedStatus,  User user,  double credit,  DateTime? documentIssueDate,  DateTime? documentExpirationDate,  UserAccountTypeDetails userAccountTypeDetails,  FirebaseDeviceTokens? firebaseDeviceTokens,  bool taxResidencyInfoExists,  VariableFields variableFields,  Currency currencyCode,  double rewardPoints,  String invitationCode,  bool invitationRequestUserExist,  int totalDocument,  EmploymentStatus? workStatus,  String? employer,  String? industryLov,  String? roleLov,  AnnualIncome? annualIncome,  String? workAddress)?  $default,) {final _that = this;
switch (_that) {
case _AppUser() when $default != null:
return $default(_that.id,_that.phoneNumber,_that.countryOfBirth,_that.accountStatus,_that.verifiedStatus,_that.user,_that.credit,_that.documentIssueDate,_that.documentExpirationDate,_that.userAccountTypeDetails,_that.firebaseDeviceTokens,_that.taxResidencyInfoExists,_that.variableFields,_that.currencyCode,_that.rewardPoints,_that.invitationCode,_that.invitationRequestUserExist,_that.totalDocument,_that.workStatus,_that.employer,_that.industryLov,_that.roleLov,_that.annualIncome,_that.workAddress);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AppUser implements AppUser {
  const _AppUser({required this.id, this.phoneNumber, this.countryOfBirth, required this.accountStatus, required this.verifiedStatus, required this.user, required this.credit, this.documentIssueDate, this.documentExpirationDate, required this.userAccountTypeDetails, this.firebaseDeviceTokens, required this.taxResidencyInfoExists, required this.variableFields, required this.currencyCode, required this.rewardPoints, required this.invitationCode, required this.invitationRequestUserExist, required this.totalDocument, this.workStatus, this.employer, this.industryLov, this.roleLov, this.annualIncome, this.workAddress});
  factory _AppUser.fromJson(Map<String, dynamic> json) => _$AppUserFromJson(json);

@override final  int id;
@override final  String? phoneNumber;
@override final  String? countryOfBirth;
@override final  AccountStatus accountStatus;
@override final  VerifiedStatus verifiedStatus;
@override final  User user;
@override final  double credit;
@override final  DateTime? documentIssueDate;
@override final  DateTime? documentExpirationDate;
@override final  UserAccountTypeDetails userAccountTypeDetails;
@override final  FirebaseDeviceTokens? firebaseDeviceTokens;
@override final  bool taxResidencyInfoExists;
@override final  VariableFields variableFields;
@override final  Currency currencyCode;
@override final  double rewardPoints;
@override final  String invitationCode;
@override final  bool invitationRequestUserExist;
@override final  int totalDocument;
@override final  EmploymentStatus? workStatus;
@override final  String? employer;
@override final  String? industryLov;
@override final  String? roleLov;
@override final  AnnualIncome? annualIncome;
@override final  String? workAddress;

/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppUserCopyWith<_AppUser> get copyWith => __$AppUserCopyWithImpl<_AppUser>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppUserToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppUser&&(identical(other.id, id) || other.id == id)&&(identical(other.phoneNumber, phoneNumber) || other.phoneNumber == phoneNumber)&&(identical(other.countryOfBirth, countryOfBirth) || other.countryOfBirth == countryOfBirth)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.verifiedStatus, verifiedStatus) || other.verifiedStatus == verifiedStatus)&&(identical(other.user, user) || other.user == user)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.documentIssueDate, documentIssueDate) || other.documentIssueDate == documentIssueDate)&&(identical(other.documentExpirationDate, documentExpirationDate) || other.documentExpirationDate == documentExpirationDate)&&(identical(other.userAccountTypeDetails, userAccountTypeDetails) || other.userAccountTypeDetails == userAccountTypeDetails)&&(identical(other.firebaseDeviceTokens, firebaseDeviceTokens) || other.firebaseDeviceTokens == firebaseDeviceTokens)&&(identical(other.taxResidencyInfoExists, taxResidencyInfoExists) || other.taxResidencyInfoExists == taxResidencyInfoExists)&&(identical(other.variableFields, variableFields) || other.variableFields == variableFields)&&(identical(other.currencyCode, currencyCode) || other.currencyCode == currencyCode)&&(identical(other.rewardPoints, rewardPoints) || other.rewardPoints == rewardPoints)&&(identical(other.invitationCode, invitationCode) || other.invitationCode == invitationCode)&&(identical(other.invitationRequestUserExist, invitationRequestUserExist) || other.invitationRequestUserExist == invitationRequestUserExist)&&(identical(other.totalDocument, totalDocument) || other.totalDocument == totalDocument)&&(identical(other.workStatus, workStatus) || other.workStatus == workStatus)&&(identical(other.employer, employer) || other.employer == employer)&&(identical(other.industryLov, industryLov) || other.industryLov == industryLov)&&(identical(other.roleLov, roleLov) || other.roleLov == roleLov)&&(identical(other.annualIncome, annualIncome) || other.annualIncome == annualIncome)&&(identical(other.workAddress, workAddress) || other.workAddress == workAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,phoneNumber,countryOfBirth,accountStatus,verifiedStatus,user,credit,documentIssueDate,documentExpirationDate,userAccountTypeDetails,firebaseDeviceTokens,taxResidencyInfoExists,variableFields,currencyCode,rewardPoints,invitationCode,invitationRequestUserExist,totalDocument,workStatus,employer,industryLov,roleLov,annualIncome,workAddress]);

@override
String toString() {
  return 'AppUser(id: $id, phoneNumber: $phoneNumber, countryOfBirth: $countryOfBirth, accountStatus: $accountStatus, verifiedStatus: $verifiedStatus, user: $user, credit: $credit, documentIssueDate: $documentIssueDate, documentExpirationDate: $documentExpirationDate, userAccountTypeDetails: $userAccountTypeDetails, firebaseDeviceTokens: $firebaseDeviceTokens, taxResidencyInfoExists: $taxResidencyInfoExists, variableFields: $variableFields, currencyCode: $currencyCode, rewardPoints: $rewardPoints, invitationCode: $invitationCode, invitationRequestUserExist: $invitationRequestUserExist, totalDocument: $totalDocument, workStatus: $workStatus, employer: $employer, industryLov: $industryLov, roleLov: $roleLov, annualIncome: $annualIncome, workAddress: $workAddress)';
}


}

/// @nodoc
abstract mixin class _$AppUserCopyWith<$Res> implements $AppUserCopyWith<$Res> {
  factory _$AppUserCopyWith(_AppUser value, $Res Function(_AppUser) _then) = __$AppUserCopyWithImpl;
@override @useResult
$Res call({
 int id, String? phoneNumber, String? countryOfBirth, AccountStatus accountStatus, VerifiedStatus verifiedStatus, User user, double credit, DateTime? documentIssueDate, DateTime? documentExpirationDate, UserAccountTypeDetails userAccountTypeDetails, FirebaseDeviceTokens? firebaseDeviceTokens, bool taxResidencyInfoExists, VariableFields variableFields, Currency currencyCode, double rewardPoints, String invitationCode, bool invitationRequestUserExist, int totalDocument, EmploymentStatus? workStatus, String? employer, String? industryLov, String? roleLov, AnnualIncome? annualIncome, String? workAddress
});


@override $UserCopyWith<$Res> get user;@override $UserAccountTypeDetailsCopyWith<$Res> get userAccountTypeDetails;@override $FirebaseDeviceTokensCopyWith<$Res>? get firebaseDeviceTokens;@override $VariableFieldsCopyWith<$Res> get variableFields;

}
/// @nodoc
class __$AppUserCopyWithImpl<$Res>
    implements _$AppUserCopyWith<$Res> {
  __$AppUserCopyWithImpl(this._self, this._then);

  final _AppUser _self;
  final $Res Function(_AppUser) _then;

/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? phoneNumber = freezed,Object? countryOfBirth = freezed,Object? accountStatus = null,Object? verifiedStatus = null,Object? user = null,Object? credit = null,Object? documentIssueDate = freezed,Object? documentExpirationDate = freezed,Object? userAccountTypeDetails = null,Object? firebaseDeviceTokens = freezed,Object? taxResidencyInfoExists = null,Object? variableFields = null,Object? currencyCode = null,Object? rewardPoints = null,Object? invitationCode = null,Object? invitationRequestUserExist = null,Object? totalDocument = null,Object? workStatus = freezed,Object? employer = freezed,Object? industryLov = freezed,Object? roleLov = freezed,Object? annualIncome = freezed,Object? workAddress = freezed,}) {
  return _then(_AppUser(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,phoneNumber: freezed == phoneNumber ? _self.phoneNumber : phoneNumber // ignore: cast_nullable_to_non_nullable
as String?,countryOfBirth: freezed == countryOfBirth ? _self.countryOfBirth : countryOfBirth // ignore: cast_nullable_to_non_nullable
as String?,accountStatus: null == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as AccountStatus,verifiedStatus: null == verifiedStatus ? _self.verifiedStatus : verifiedStatus // ignore: cast_nullable_to_non_nullable
as VerifiedStatus,user: null == user ? _self.user : user // ignore: cast_nullable_to_non_nullable
as User,credit: null == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double,documentIssueDate: freezed == documentIssueDate ? _self.documentIssueDate : documentIssueDate // ignore: cast_nullable_to_non_nullable
as DateTime?,documentExpirationDate: freezed == documentExpirationDate ? _self.documentExpirationDate : documentExpirationDate // ignore: cast_nullable_to_non_nullable
as DateTime?,userAccountTypeDetails: null == userAccountTypeDetails ? _self.userAccountTypeDetails : userAccountTypeDetails // ignore: cast_nullable_to_non_nullable
as UserAccountTypeDetails,firebaseDeviceTokens: freezed == firebaseDeviceTokens ? _self.firebaseDeviceTokens : firebaseDeviceTokens // ignore: cast_nullable_to_non_nullable
as FirebaseDeviceTokens?,taxResidencyInfoExists: null == taxResidencyInfoExists ? _self.taxResidencyInfoExists : taxResidencyInfoExists // ignore: cast_nullable_to_non_nullable
as bool,variableFields: null == variableFields ? _self.variableFields : variableFields // ignore: cast_nullable_to_non_nullable
as VariableFields,currencyCode: null == currencyCode ? _self.currencyCode : currencyCode // ignore: cast_nullable_to_non_nullable
as Currency,rewardPoints: null == rewardPoints ? _self.rewardPoints : rewardPoints // ignore: cast_nullable_to_non_nullable
as double,invitationCode: null == invitationCode ? _self.invitationCode : invitationCode // ignore: cast_nullable_to_non_nullable
as String,invitationRequestUserExist: null == invitationRequestUserExist ? _self.invitationRequestUserExist : invitationRequestUserExist // ignore: cast_nullable_to_non_nullable
as bool,totalDocument: null == totalDocument ? _self.totalDocument : totalDocument // ignore: cast_nullable_to_non_nullable
as int,workStatus: freezed == workStatus ? _self.workStatus : workStatus // ignore: cast_nullable_to_non_nullable
as EmploymentStatus?,employer: freezed == employer ? _self.employer : employer // ignore: cast_nullable_to_non_nullable
as String?,industryLov: freezed == industryLov ? _self.industryLov : industryLov // ignore: cast_nullable_to_non_nullable
as String?,roleLov: freezed == roleLov ? _self.roleLov : roleLov // ignore: cast_nullable_to_non_nullable
as String?,annualIncome: freezed == annualIncome ? _self.annualIncome : annualIncome // ignore: cast_nullable_to_non_nullable
as AnnualIncome?,workAddress: freezed == workAddress ? _self.workAddress : workAddress // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserCopyWith<$Res> get user {
  
  return $UserCopyWith<$Res>(_self.user, (value) {
    return _then(_self.copyWith(user: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserAccountTypeDetailsCopyWith<$Res> get userAccountTypeDetails {
  
  return $UserAccountTypeDetailsCopyWith<$Res>(_self.userAccountTypeDetails, (value) {
    return _then(_self.copyWith(userAccountTypeDetails: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FirebaseDeviceTokensCopyWith<$Res>? get firebaseDeviceTokens {
    if (_self.firebaseDeviceTokens == null) {
    return null;
  }

  return $FirebaseDeviceTokensCopyWith<$Res>(_self.firebaseDeviceTokens!, (value) {
    return _then(_self.copyWith(firebaseDeviceTokens: value));
  });
}/// Create a copy of AppUser
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$VariableFieldsCopyWith<$Res> get variableFields {
  
  return $VariableFieldsCopyWith<$Res>(_self.variableFields, (value) {
    return _then(_self.copyWith(variableFields: value));
  });
}
}


/// @nodoc
mixin _$User {

 String get fullName; String get email; String get langKey; DateTime get createdDate;
/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserCopyWith<User> get copyWith => _$UserCopyWithImpl<User>(this as User, _$identity);

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is User&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.langKey, langKey) || other.langKey == langKey)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,email,langKey,createdDate);

@override
String toString() {
  return 'User(fullName: $fullName, email: $email, langKey: $langKey, createdDate: $createdDate)';
}


}

/// @nodoc
abstract mixin class $UserCopyWith<$Res>  {
  factory $UserCopyWith(User value, $Res Function(User) _then) = _$UserCopyWithImpl;
@useResult
$Res call({
 String fullName, String email, String langKey, DateTime createdDate
});




}
/// @nodoc
class _$UserCopyWithImpl<$Res>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._self, this._then);

  final User _self;
  final $Res Function(User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fullName = null,Object? email = null,Object? langKey = null,Object? createdDate = null,}) {
  return _then(_self.copyWith(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,langKey: null == langKey ? _self.langKey : langKey // ignore: cast_nullable_to_non_nullable
as String,createdDate: null == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [User].
extension UserPatterns on User {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _User value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _User value)  $default,){
final _that = this;
switch (_that) {
case _User():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _User value)?  $default,){
final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String fullName,  String email,  String langKey,  DateTime createdDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that.fullName,_that.email,_that.langKey,_that.createdDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String fullName,  String email,  String langKey,  DateTime createdDate)  $default,) {final _that = this;
switch (_that) {
case _User():
return $default(_that.fullName,_that.email,_that.langKey,_that.createdDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String fullName,  String email,  String langKey,  DateTime createdDate)?  $default,) {final _that = this;
switch (_that) {
case _User() when $default != null:
return $default(_that.fullName,_that.email,_that.langKey,_that.createdDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _User implements User {
  const _User({required this.fullName, required this.email, required this.langKey, required this.createdDate});
  factory _User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

@override final  String fullName;
@override final  String email;
@override final  String langKey;
@override final  DateTime createdDate;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserCopyWith<_User> get copyWith => __$UserCopyWithImpl<_User>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _User&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.langKey, langKey) || other.langKey == langKey)&&(identical(other.createdDate, createdDate) || other.createdDate == createdDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,email,langKey,createdDate);

@override
String toString() {
  return 'User(fullName: $fullName, email: $email, langKey: $langKey, createdDate: $createdDate)';
}


}

/// @nodoc
abstract mixin class _$UserCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$UserCopyWith(_User value, $Res Function(_User) _then) = __$UserCopyWithImpl;
@override @useResult
$Res call({
 String fullName, String email, String langKey, DateTime createdDate
});




}
/// @nodoc
class __$UserCopyWithImpl<$Res>
    implements _$UserCopyWith<$Res> {
  __$UserCopyWithImpl(this._self, this._then);

  final _User _self;
  final $Res Function(_User) _then;

/// Create a copy of User
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fullName = null,Object? email = null,Object? langKey = null,Object? createdDate = null,}) {
  return _then(_User(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,langKey: null == langKey ? _self.langKey : langKey // ignore: cast_nullable_to_non_nullable
as String,createdDate: null == createdDate ? _self.createdDate : createdDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$UserAccountTypeDetails {

 String get accountType; double get annualInvestmentLimit; double get propertyInvestmentLimit;
/// Create a copy of UserAccountTypeDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserAccountTypeDetailsCopyWith<UserAccountTypeDetails> get copyWith => _$UserAccountTypeDetailsCopyWithImpl<UserAccountTypeDetails>(this as UserAccountTypeDetails, _$identity);

  /// Serializes this UserAccountTypeDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserAccountTypeDetails&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.annualInvestmentLimit, annualInvestmentLimit) || other.annualInvestmentLimit == annualInvestmentLimit)&&(identical(other.propertyInvestmentLimit, propertyInvestmentLimit) || other.propertyInvestmentLimit == propertyInvestmentLimit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountType,annualInvestmentLimit,propertyInvestmentLimit);

@override
String toString() {
  return 'UserAccountTypeDetails(accountType: $accountType, annualInvestmentLimit: $annualInvestmentLimit, propertyInvestmentLimit: $propertyInvestmentLimit)';
}


}

/// @nodoc
abstract mixin class $UserAccountTypeDetailsCopyWith<$Res>  {
  factory $UserAccountTypeDetailsCopyWith(UserAccountTypeDetails value, $Res Function(UserAccountTypeDetails) _then) = _$UserAccountTypeDetailsCopyWithImpl;
@useResult
$Res call({
 String accountType, double annualInvestmentLimit, double propertyInvestmentLimit
});




}
/// @nodoc
class _$UserAccountTypeDetailsCopyWithImpl<$Res>
    implements $UserAccountTypeDetailsCopyWith<$Res> {
  _$UserAccountTypeDetailsCopyWithImpl(this._self, this._then);

  final UserAccountTypeDetails _self;
  final $Res Function(UserAccountTypeDetails) _then;

/// Create a copy of UserAccountTypeDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountType = null,Object? annualInvestmentLimit = null,Object? propertyInvestmentLimit = null,}) {
  return _then(_self.copyWith(
accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String,annualInvestmentLimit: null == annualInvestmentLimit ? _self.annualInvestmentLimit : annualInvestmentLimit // ignore: cast_nullable_to_non_nullable
as double,propertyInvestmentLimit: null == propertyInvestmentLimit ? _self.propertyInvestmentLimit : propertyInvestmentLimit // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [UserAccountTypeDetails].
extension UserAccountTypeDetailsPatterns on UserAccountTypeDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserAccountTypeDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserAccountTypeDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserAccountTypeDetails value)  $default,){
final _that = this;
switch (_that) {
case _UserAccountTypeDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserAccountTypeDetails value)?  $default,){
final _that = this;
switch (_that) {
case _UserAccountTypeDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String accountType,  double annualInvestmentLimit,  double propertyInvestmentLimit)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserAccountTypeDetails() when $default != null:
return $default(_that.accountType,_that.annualInvestmentLimit,_that.propertyInvestmentLimit);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String accountType,  double annualInvestmentLimit,  double propertyInvestmentLimit)  $default,) {final _that = this;
switch (_that) {
case _UserAccountTypeDetails():
return $default(_that.accountType,_that.annualInvestmentLimit,_that.propertyInvestmentLimit);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String accountType,  double annualInvestmentLimit,  double propertyInvestmentLimit)?  $default,) {final _that = this;
switch (_that) {
case _UserAccountTypeDetails() when $default != null:
return $default(_that.accountType,_that.annualInvestmentLimit,_that.propertyInvestmentLimit);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserAccountTypeDetails implements UserAccountTypeDetails {
  const _UserAccountTypeDetails({required this.accountType, required this.annualInvestmentLimit, required this.propertyInvestmentLimit});
  factory _UserAccountTypeDetails.fromJson(Map<String, dynamic> json) => _$UserAccountTypeDetailsFromJson(json);

@override final  String accountType;
@override final  double annualInvestmentLimit;
@override final  double propertyInvestmentLimit;

/// Create a copy of UserAccountTypeDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserAccountTypeDetailsCopyWith<_UserAccountTypeDetails> get copyWith => __$UserAccountTypeDetailsCopyWithImpl<_UserAccountTypeDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserAccountTypeDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserAccountTypeDetails&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.annualInvestmentLimit, annualInvestmentLimit) || other.annualInvestmentLimit == annualInvestmentLimit)&&(identical(other.propertyInvestmentLimit, propertyInvestmentLimit) || other.propertyInvestmentLimit == propertyInvestmentLimit));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountType,annualInvestmentLimit,propertyInvestmentLimit);

@override
String toString() {
  return 'UserAccountTypeDetails(accountType: $accountType, annualInvestmentLimit: $annualInvestmentLimit, propertyInvestmentLimit: $propertyInvestmentLimit)';
}


}

/// @nodoc
abstract mixin class _$UserAccountTypeDetailsCopyWith<$Res> implements $UserAccountTypeDetailsCopyWith<$Res> {
  factory _$UserAccountTypeDetailsCopyWith(_UserAccountTypeDetails value, $Res Function(_UserAccountTypeDetails) _then) = __$UserAccountTypeDetailsCopyWithImpl;
@override @useResult
$Res call({
 String accountType, double annualInvestmentLimit, double propertyInvestmentLimit
});




}
/// @nodoc
class __$UserAccountTypeDetailsCopyWithImpl<$Res>
    implements _$UserAccountTypeDetailsCopyWith<$Res> {
  __$UserAccountTypeDetailsCopyWithImpl(this._self, this._then);

  final _UserAccountTypeDetails _self;
  final $Res Function(_UserAccountTypeDetails) _then;

/// Create a copy of UserAccountTypeDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountType = null,Object? annualInvestmentLimit = null,Object? propertyInvestmentLimit = null,}) {
  return _then(_UserAccountTypeDetails(
accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as String,annualInvestmentLimit: null == annualInvestmentLimit ? _self.annualInvestmentLimit : annualInvestmentLimit // ignore: cast_nullable_to_non_nullable
as double,propertyInvestmentLimit: null == propertyInvestmentLimit ? _self.propertyInvestmentLimit : propertyInvestmentLimit // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$FirebaseDeviceTokens {

 int get id; String get firebaseToken;
/// Create a copy of FirebaseDeviceTokens
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FirebaseDeviceTokensCopyWith<FirebaseDeviceTokens> get copyWith => _$FirebaseDeviceTokensCopyWithImpl<FirebaseDeviceTokens>(this as FirebaseDeviceTokens, _$identity);

  /// Serializes this FirebaseDeviceTokens to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FirebaseDeviceTokens&&(identical(other.id, id) || other.id == id)&&(identical(other.firebaseToken, firebaseToken) || other.firebaseToken == firebaseToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firebaseToken);

@override
String toString() {
  return 'FirebaseDeviceTokens(id: $id, firebaseToken: $firebaseToken)';
}


}

/// @nodoc
abstract mixin class $FirebaseDeviceTokensCopyWith<$Res>  {
  factory $FirebaseDeviceTokensCopyWith(FirebaseDeviceTokens value, $Res Function(FirebaseDeviceTokens) _then) = _$FirebaseDeviceTokensCopyWithImpl;
@useResult
$Res call({
 int id, String firebaseToken
});




}
/// @nodoc
class _$FirebaseDeviceTokensCopyWithImpl<$Res>
    implements $FirebaseDeviceTokensCopyWith<$Res> {
  _$FirebaseDeviceTokensCopyWithImpl(this._self, this._then);

  final FirebaseDeviceTokens _self;
  final $Res Function(FirebaseDeviceTokens) _then;

/// Create a copy of FirebaseDeviceTokens
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? firebaseToken = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firebaseToken: null == firebaseToken ? _self.firebaseToken : firebaseToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [FirebaseDeviceTokens].
extension FirebaseDeviceTokensPatterns on FirebaseDeviceTokens {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _FirebaseDeviceTokens value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _FirebaseDeviceTokens() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _FirebaseDeviceTokens value)  $default,){
final _that = this;
switch (_that) {
case _FirebaseDeviceTokens():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _FirebaseDeviceTokens value)?  $default,){
final _that = this;
switch (_that) {
case _FirebaseDeviceTokens() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String firebaseToken)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _FirebaseDeviceTokens() when $default != null:
return $default(_that.id,_that.firebaseToken);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String firebaseToken)  $default,) {final _that = this;
switch (_that) {
case _FirebaseDeviceTokens():
return $default(_that.id,_that.firebaseToken);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String firebaseToken)?  $default,) {final _that = this;
switch (_that) {
case _FirebaseDeviceTokens() when $default != null:
return $default(_that.id,_that.firebaseToken);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _FirebaseDeviceTokens implements FirebaseDeviceTokens {
  const _FirebaseDeviceTokens({required this.id, required this.firebaseToken});
  factory _FirebaseDeviceTokens.fromJson(Map<String, dynamic> json) => _$FirebaseDeviceTokensFromJson(json);

@override final  int id;
@override final  String firebaseToken;

/// Create a copy of FirebaseDeviceTokens
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FirebaseDeviceTokensCopyWith<_FirebaseDeviceTokens> get copyWith => __$FirebaseDeviceTokensCopyWithImpl<_FirebaseDeviceTokens>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FirebaseDeviceTokensToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FirebaseDeviceTokens&&(identical(other.id, id) || other.id == id)&&(identical(other.firebaseToken, firebaseToken) || other.firebaseToken == firebaseToken));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,firebaseToken);

@override
String toString() {
  return 'FirebaseDeviceTokens(id: $id, firebaseToken: $firebaseToken)';
}


}

/// @nodoc
abstract mixin class _$FirebaseDeviceTokensCopyWith<$Res> implements $FirebaseDeviceTokensCopyWith<$Res> {
  factory _$FirebaseDeviceTokensCopyWith(_FirebaseDeviceTokens value, $Res Function(_FirebaseDeviceTokens) _then) = __$FirebaseDeviceTokensCopyWithImpl;
@override @useResult
$Res call({
 int id, String firebaseToken
});




}
/// @nodoc
class __$FirebaseDeviceTokensCopyWithImpl<$Res>
    implements _$FirebaseDeviceTokensCopyWith<$Res> {
  __$FirebaseDeviceTokensCopyWithImpl(this._self, this._then);

  final _FirebaseDeviceTokens _self;
  final $Res Function(_FirebaseDeviceTokens) _then;

/// Create a copy of FirebaseDeviceTokens
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? firebaseToken = null,}) {
  return _then(_FirebaseDeviceTokens(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,firebaseToken: null == firebaseToken ? _self.firebaseToken : firebaseToken // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$VariableFields {

@JsonKey(name: 'AEDTOUSD') String get aedToUsd;@JsonKey(name: 'AEDTOEUR') String get aedToEur;
/// Create a copy of VariableFields
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VariableFieldsCopyWith<VariableFields> get copyWith => _$VariableFieldsCopyWithImpl<VariableFields>(this as VariableFields, _$identity);

  /// Serializes this VariableFields to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VariableFields&&(identical(other.aedToUsd, aedToUsd) || other.aedToUsd == aedToUsd)&&(identical(other.aedToEur, aedToEur) || other.aedToEur == aedToEur));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,aedToUsd,aedToEur);

@override
String toString() {
  return 'VariableFields(aedToUsd: $aedToUsd, aedToEur: $aedToEur)';
}


}

/// @nodoc
abstract mixin class $VariableFieldsCopyWith<$Res>  {
  factory $VariableFieldsCopyWith(VariableFields value, $Res Function(VariableFields) _then) = _$VariableFieldsCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'AEDTOUSD') String aedToUsd,@JsonKey(name: 'AEDTOEUR') String aedToEur
});




}
/// @nodoc
class _$VariableFieldsCopyWithImpl<$Res>
    implements $VariableFieldsCopyWith<$Res> {
  _$VariableFieldsCopyWithImpl(this._self, this._then);

  final VariableFields _self;
  final $Res Function(VariableFields) _then;

/// Create a copy of VariableFields
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? aedToUsd = null,Object? aedToEur = null,}) {
  return _then(_self.copyWith(
aedToUsd: null == aedToUsd ? _self.aedToUsd : aedToUsd // ignore: cast_nullable_to_non_nullable
as String,aedToEur: null == aedToEur ? _self.aedToEur : aedToEur // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [VariableFields].
extension VariableFieldsPatterns on VariableFields {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VariableFields value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VariableFields() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VariableFields value)  $default,){
final _that = this;
switch (_that) {
case _VariableFields():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VariableFields value)?  $default,){
final _that = this;
switch (_that) {
case _VariableFields() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function(@JsonKey(name: 'AEDTOUSD')  String aedToUsd, @JsonKey(name: 'AEDTOEUR')  String aedToEur)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VariableFields() when $default != null:
return $default(_that.aedToUsd,_that.aedToEur);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function(@JsonKey(name: 'AEDTOUSD')  String aedToUsd, @JsonKey(name: 'AEDTOEUR')  String aedToEur)  $default,) {final _that = this;
switch (_that) {
case _VariableFields():
return $default(_that.aedToUsd,_that.aedToEur);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function(@JsonKey(name: 'AEDTOUSD')  String aedToUsd, @JsonKey(name: 'AEDTOEUR')  String aedToEur)?  $default,) {final _that = this;
switch (_that) {
case _VariableFields() when $default != null:
return $default(_that.aedToUsd,_that.aedToEur);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VariableFields implements VariableFields {
  const _VariableFields({@JsonKey(name: 'AEDTOUSD') required this.aedToUsd, @JsonKey(name: 'AEDTOEUR') required this.aedToEur});
  factory _VariableFields.fromJson(Map<String, dynamic> json) => _$VariableFieldsFromJson(json);

@override@JsonKey(name: 'AEDTOUSD') final  String aedToUsd;
@override@JsonKey(name: 'AEDTOEUR') final  String aedToEur;

/// Create a copy of VariableFields
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VariableFieldsCopyWith<_VariableFields> get copyWith => __$VariableFieldsCopyWithImpl<_VariableFields>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VariableFieldsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VariableFields&&(identical(other.aedToUsd, aedToUsd) || other.aedToUsd == aedToUsd)&&(identical(other.aedToEur, aedToEur) || other.aedToEur == aedToEur));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,aedToUsd,aedToEur);

@override
String toString() {
  return 'VariableFields(aedToUsd: $aedToUsd, aedToEur: $aedToEur)';
}


}

/// @nodoc
abstract mixin class _$VariableFieldsCopyWith<$Res> implements $VariableFieldsCopyWith<$Res> {
  factory _$VariableFieldsCopyWith(_VariableFields value, $Res Function(_VariableFields) _then) = __$VariableFieldsCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'AEDTOUSD') String aedToUsd,@JsonKey(name: 'AEDTOEUR') String aedToEur
});




}
/// @nodoc
class __$VariableFieldsCopyWithImpl<$Res>
    implements _$VariableFieldsCopyWith<$Res> {
  __$VariableFieldsCopyWithImpl(this._self, this._then);

  final _VariableFields _self;
  final $Res Function(_VariableFields) _then;

/// Create a copy of VariableFields
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? aedToUsd = null,Object? aedToEur = null,}) {
  return _then(_VariableFields(
aedToUsd: null == aedToUsd ? _self.aedToUsd : aedToUsd // ignore: cast_nullable_to_non_nullable
as String,aedToEur: null == aedToEur ? _self.aedToEur : aedToEur // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
