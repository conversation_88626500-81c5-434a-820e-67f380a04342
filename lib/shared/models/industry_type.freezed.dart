// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'industry_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$IndustryType {

 int get id; String get industryType; String get industryTypeInArabic;
/// Create a copy of IndustryType
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IndustryTypeCopyWith<IndustryType> get copyWith => _$IndustryTypeCopyWithImpl<IndustryType>(this as IndustryType, _$identity);

  /// Serializes this IndustryType to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is IndustryType&&(identical(other.id, id) || other.id == id)&&(identical(other.industryType, industryType) || other.industryType == industryType)&&(identical(other.industryTypeInArabic, industryTypeInArabic) || other.industryTypeInArabic == industryTypeInArabic));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,industryType,industryTypeInArabic);

@override
String toString() {
  return 'IndustryType(id: $id, industryType: $industryType, industryTypeInArabic: $industryTypeInArabic)';
}


}

/// @nodoc
abstract mixin class $IndustryTypeCopyWith<$Res>  {
  factory $IndustryTypeCopyWith(IndustryType value, $Res Function(IndustryType) _then) = _$IndustryTypeCopyWithImpl;
@useResult
$Res call({
 int id, String industryType, String industryTypeInArabic
});




}
/// @nodoc
class _$IndustryTypeCopyWithImpl<$Res>
    implements $IndustryTypeCopyWith<$Res> {
  _$IndustryTypeCopyWithImpl(this._self, this._then);

  final IndustryType _self;
  final $Res Function(IndustryType) _then;

/// Create a copy of IndustryType
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? industryType = null,Object? industryTypeInArabic = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,industryType: null == industryType ? _self.industryType : industryType // ignore: cast_nullable_to_non_nullable
as String,industryTypeInArabic: null == industryTypeInArabic ? _self.industryTypeInArabic : industryTypeInArabic // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [IndustryType].
extension IndustryTypePatterns on IndustryType {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _IndustryType value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _IndustryType() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _IndustryType value)  $default,){
final _that = this;
switch (_that) {
case _IndustryType():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _IndustryType value)?  $default,){
final _that = this;
switch (_that) {
case _IndustryType() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String industryType,  String industryTypeInArabic)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _IndustryType() when $default != null:
return $default(_that.id,_that.industryType,_that.industryTypeInArabic);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String industryType,  String industryTypeInArabic)  $default,) {final _that = this;
switch (_that) {
case _IndustryType():
return $default(_that.id,_that.industryType,_that.industryTypeInArabic);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String industryType,  String industryTypeInArabic)?  $default,) {final _that = this;
switch (_that) {
case _IndustryType() when $default != null:
return $default(_that.id,_that.industryType,_that.industryTypeInArabic);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _IndustryType implements IndustryType {
  const _IndustryType({required this.id, required this.industryType, required this.industryTypeInArabic});
  factory _IndustryType.fromJson(Map<String, dynamic> json) => _$IndustryTypeFromJson(json);

@override final  int id;
@override final  String industryType;
@override final  String industryTypeInArabic;

/// Create a copy of IndustryType
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IndustryTypeCopyWith<_IndustryType> get copyWith => __$IndustryTypeCopyWithImpl<_IndustryType>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IndustryTypeToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _IndustryType&&(identical(other.id, id) || other.id == id)&&(identical(other.industryType, industryType) || other.industryType == industryType)&&(identical(other.industryTypeInArabic, industryTypeInArabic) || other.industryTypeInArabic == industryTypeInArabic));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,industryType,industryTypeInArabic);

@override
String toString() {
  return 'IndustryType(id: $id, industryType: $industryType, industryTypeInArabic: $industryTypeInArabic)';
}


}

/// @nodoc
abstract mixin class _$IndustryTypeCopyWith<$Res> implements $IndustryTypeCopyWith<$Res> {
  factory _$IndustryTypeCopyWith(_IndustryType value, $Res Function(_IndustryType) _then) = __$IndustryTypeCopyWithImpl;
@override @useResult
$Res call({
 int id, String industryType, String industryTypeInArabic
});




}
/// @nodoc
class __$IndustryTypeCopyWithImpl<$Res>
    implements _$IndustryTypeCopyWith<$Res> {
  __$IndustryTypeCopyWithImpl(this._self, this._then);

  final _IndustryType _self;
  final $Res Function(_IndustryType) _then;

/// Create a copy of IndustryType
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? industryType = null,Object? industryTypeInArabic = null,}) {
  return _then(_IndustryType(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,industryType: null == industryType ? _self.industryType : industryType // ignore: cast_nullable_to_non_nullable
as String,industryTypeInArabic: null == industryTypeInArabic ? _self.industryTypeInArabic : industryTypeInArabic // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
