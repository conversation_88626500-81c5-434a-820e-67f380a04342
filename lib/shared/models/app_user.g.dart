// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppUser _$AppUserFromJson(Map<String, dynamic> json) => _AppUser(
  id: (json['id'] as num).toInt(),
  phoneNumber: json['phoneNumber'] as String?,
  countryOfBirth: json['countryOfBirth'] as String?,
  accountStatus: $enumDecode(_$AccountStatusEnumMap, json['accountStatus']),
  verifiedStatus: $enumDecode(_$VerifiedStatusEnumMap, json['verifiedStatus']),
  user: User.fromJson(json['user'] as Map<String, dynamic>),
  credit: (json['credit'] as num).toDouble(),
  documentIssueDate: json['documentIssueDate'] == null
      ? null
      : DateTime.parse(json['documentIssueDate'] as String),
  documentExpirationDate: json['documentExpirationDate'] == null
      ? null
      : DateTime.parse(json['documentExpirationDate'] as String),
  userAccountTypeDetails: UserAccountTypeDetails.fromJson(
    json['userAccountTypeDetails'] as Map<String, dynamic>,
  ),
  firebaseDeviceTokens: json['firebaseDeviceTokens'] == null
      ? null
      : FirebaseDeviceTokens.fromJson(
          json['firebaseDeviceTokens'] as Map<String, dynamic>,
        ),
  taxResidencyInfoExists: json['taxResidencyInfoExists'] as bool,
  variableFields: VariableFields.fromJson(
    json['variableFields'] as Map<String, dynamic>,
  ),
  currencyCode: $enumDecode(_$CurrencyEnumMap, json['currencyCode']),
  rewardPoints: (json['rewardPoints'] as num).toDouble(),
  invitationCode: json['invitationCode'] as String,
  invitationRequestUserExist: json['invitationRequestUserExist'] as bool,
  totalDocument: (json['totalDocument'] as num).toInt(),
  workStatus: $enumDecodeNullable(
    _$EmploymentStatusEnumMap,
    json['workStatus'],
  ),
  employer: json['employer'] as String?,
  industryLov: json['industryLov'] as String?,
  roleLov: json['roleLov'] as String?,
  annualIncome: $enumDecodeNullable(
    _$AnnualIncomeEnumMap,
    json['annualIncome'],
  ),
  workAddress: json['workAddress'] as String?,
);

Map<String, dynamic> _$AppUserToJson(_AppUser instance) => <String, dynamic>{
  'id': instance.id,
  'phoneNumber': instance.phoneNumber,
  'countryOfBirth': instance.countryOfBirth,
  'accountStatus': _$AccountStatusEnumMap[instance.accountStatus]!,
  'verifiedStatus': _$VerifiedStatusEnumMap[instance.verifiedStatus]!,
  'user': instance.user,
  'credit': instance.credit,
  'documentIssueDate': instance.documentIssueDate?.toIso8601String(),
  'documentExpirationDate': instance.documentExpirationDate?.toIso8601String(),
  'userAccountTypeDetails': instance.userAccountTypeDetails,
  'firebaseDeviceTokens': instance.firebaseDeviceTokens,
  'taxResidencyInfoExists': instance.taxResidencyInfoExists,
  'variableFields': instance.variableFields,
  'currencyCode': _$CurrencyEnumMap[instance.currencyCode]!,
  'rewardPoints': instance.rewardPoints,
  'invitationCode': instance.invitationCode,
  'invitationRequestUserExist': instance.invitationRequestUserExist,
  'totalDocument': instance.totalDocument,
  'workStatus': _$EmploymentStatusEnumMap[instance.workStatus],
  'employer': instance.employer,
  'industryLov': instance.industryLov,
  'roleLov': instance.roleLov,
  'annualIncome': _$AnnualIncomeEnumMap[instance.annualIncome],
  'workAddress': instance.workAddress,
};

const _$AccountStatusEnumMap = {
  AccountStatus.registered: 'Registered',
  AccountStatus.onboarded: 'Onboarded',
  AccountStatus.investors: 'Investors',
  AccountStatus.suspended: 'Suspended',
  AccountStatus.verificationHoldSuspended: 'VerificationHoldSuspended',
  AccountStatus.disabled: 'Disabled',
  AccountStatus.closed: 'Closed',
};

const _$VerifiedStatusEnumMap = {
  VerifiedStatus.pending: 'Pending',
  VerifiedStatus.notYet: 'NotYet',
  VerifiedStatus.verified: 'Verified',
  VerifiedStatus.notVerified: 'NotVerified',
  VerifiedStatus.started: 'Started',
};

const _$CurrencyEnumMap = {
  Currency.aed: 'AED',
  Currency.usd: 'USD',
  Currency.eur: 'EUR',
};

const _$EmploymentStatusEnumMap = {
  EmploymentStatus.employed: 'Employed',
  EmploymentStatus.retired: 'Retired',
  EmploymentStatus.student: 'Student',
  EmploymentStatus.unemployed: 'Unemployed',
};

const _$AnnualIncomeEnumMap = {
  AnnualIncome.lessThan10000: 'LessThan10000',
  AnnualIncome.from10000To50000: 'From10000To50000',
  AnnualIncome.from50000To100000: 'From50000To100000',
  AnnualIncome.moreThan100000: 'MoreThan100000',
};

_User _$UserFromJson(Map<String, dynamic> json) => _User(
  fullName: json['fullName'] as String,
  email: json['email'] as String,
  langKey: json['langKey'] as String,
  createdDate: DateTime.parse(json['createdDate'] as String),
);

Map<String, dynamic> _$UserToJson(_User instance) => <String, dynamic>{
  'fullName': instance.fullName,
  'email': instance.email,
  'langKey': instance.langKey,
  'createdDate': instance.createdDate.toIso8601String(),
};

_UserAccountTypeDetails _$UserAccountTypeDetailsFromJson(
  Map<String, dynamic> json,
) => _UserAccountTypeDetails(
  accountType: json['accountType'] as String,
  annualInvestmentLimit: (json['annualInvestmentLimit'] as num).toDouble(),
  propertyInvestmentLimit: (json['propertyInvestmentLimit'] as num).toDouble(),
);

Map<String, dynamic> _$UserAccountTypeDetailsToJson(
  _UserAccountTypeDetails instance,
) => <String, dynamic>{
  'accountType': instance.accountType,
  'annualInvestmentLimit': instance.annualInvestmentLimit,
  'propertyInvestmentLimit': instance.propertyInvestmentLimit,
};

_FirebaseDeviceTokens _$FirebaseDeviceTokensFromJson(
  Map<String, dynamic> json,
) => _FirebaseDeviceTokens(
  id: (json['id'] as num).toInt(),
  firebaseToken: json['firebaseToken'] as String,
);

Map<String, dynamic> _$FirebaseDeviceTokensToJson(
  _FirebaseDeviceTokens instance,
) => <String, dynamic>{
  'id': instance.id,
  'firebaseToken': instance.firebaseToken,
};

_VariableFields _$VariableFieldsFromJson(Map<String, dynamic> json) =>
    _VariableFields(
      aedToUsd: json['AEDTOUSD'] as String,
      aedToEur: json['AEDTOEUR'] as String,
    );

Map<String, dynamic> _$VariableFieldsToJson(_VariableFields instance) =>
    <String, dynamic>{
      'AEDTOUSD': instance.aedToUsd,
      'AEDTOEUR': instance.aedToEur,
    };
