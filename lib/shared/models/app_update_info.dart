/// App update info model with clear update types
/// - Force Update: forceUpdate = true, availableVersion = null
/// - Optional Update: forceUpdate = false, availableVersion = "android_10"
/// - No Update: forceUpdate = false, availableVersion = null
class AppUpdateInfo {
  final bool forceUpdate;
  final String? availableVersion; // Version string for tracking dismissals

  const AppUpdateInfo({required this.forceUpdate, this.availableVersion});

  factory AppUpdateInfo.noUpdate() {
    return const AppUpdateInfo(forceUpdate: false, availableVersion: null);
  }

  factory AppUpdateInfo.forceUpdate() {
    return const AppUpdateInfo(forceUpdate: true, availableVersion: null);
  }

  factory AppUpdateInfo.optionalUpdate(String version) {
    return AppUpdateInfo(forceUpdate: false, availableVersion: version);
  }

  // Convenience getters
  bool get hasUpdate => forceUpdate || availableVersion != null;
  bool get isForceUpdate => forceUpdate;
  bool get isOptionalUpdate => !forceUpdate && availableVersion != null;
  bool get noUpdate => !forceUpdate && availableVersion == null;
}
