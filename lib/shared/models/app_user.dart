import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/annual_income.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/employment_status.dart';
import 'package:maisour/shared/enums/verified_status.dart';

part 'app_user.freezed.dart';
part 'app_user.g.dart';

/// AppUser model containing user profile information
/// Used globally throughout the app for user data management
@freezed
abstract class AppUser with _$AppUser {
  const factory AppUser({
    required int id,
    String? phoneNumber,
    String? countryOfBirth,
    required AccountStatus accountStatus,
    required VerifiedStatus verifiedStatus,
    required User user,
    required double credit,
    DateTime? documentIssueDate,
    DateTime? documentExpirationDate,
    required UserAccountTypeDetails userAccountTypeDetails,
    FirebaseDeviceTokens? firebaseDeviceTokens,
    required bool taxResidencyInfoExists,
    required VariableFields variableFields,
    required Currency currencyCode,
    required double rewardPoints,
    required String invitationCode,
    required bool invitationRequestUserExist,
    required int totalDocument,
    EmploymentStatus? workStatus,
    String? employer,
    String? industryLov,
    String? roleLov,
    AnnualIncome? annualIncome,
    String? workAddress,
  }) = _AppUser;

  factory AppUser.fromJson(Map<String, dynamic> json) =>
      _$AppUserFromJson(json);
}

/// User model containing basic user information
/// Used globally throughout the app
@freezed
abstract class User with _$User {
  const factory User({
    required String fullName,
    required String email,
    required String langKey,
    required DateTime createdDate,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

/// User account type details model containing user account type details
/// Used globally throughout the app for user account type details data management
@freezed
abstract class UserAccountTypeDetails with _$UserAccountTypeDetails {
  const factory UserAccountTypeDetails({
    required String accountType,
    required double annualInvestmentLimit,
    required double propertyInvestmentLimit,
  }) = _UserAccountTypeDetails;

  factory UserAccountTypeDetails.fromJson(Map<String, dynamic> json) =>
      _$UserAccountTypeDetailsFromJson(json);
}

/// FirebaseDeviceTokens model containing FCM token information
/// Used for storing user's Firebase device token
@freezed
abstract class FirebaseDeviceTokens with _$FirebaseDeviceTokens {
  const factory FirebaseDeviceTokens({
    required int id,
    required String firebaseToken,
  }) = _FirebaseDeviceTokens;

  factory FirebaseDeviceTokens.fromJson(Map<String, dynamic> json) =>
      _$FirebaseDeviceTokensFromJson(json);
}

/// VariableFields model containing dynamic field values
/// Used for storing currency conversion rates and other variable data
@freezed
abstract class VariableFields with _$VariableFields {
  const factory VariableFields({
    @JsonKey(name: 'AEDTOUSD') required String aedToUsd,
    @JsonKey(name: 'AEDTOEUR') required String aedToEur,
  }) = _VariableFields;

  factory VariableFields.fromJson(Map<String, dynamic> json) =>
      _$VariableFieldsFromJson(json);
}
