import 'package:maisour/shared/models/failure.dart';

/// Simple Result class for handling success and error states
class Result<T> {
  final T? data;
  final Failure? error;
  final bool isSuccess;

  const Result._({this.data, this.error, required this.isSuccess});

  factory Result.success(T data) => Result._(data: data, isSuccess: true);
  factory Result.failure(Failure error) =>
      Result._(error: error, isSuccess: false);

  bool get isError => !isSuccess;
}
