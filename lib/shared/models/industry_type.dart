import 'package:freezed_annotation/freezed_annotation.dart';

part 'industry_type.freezed.dart';
part 'industry_type.g.dart';

/// Industry type model for employment information
/// Contains industry type data with both English and Arabic names
@freezed
abstract class IndustryType with _$IndustryType {
  const factory IndustryType({
    required int id,
    required String industryType,
    required String industryTypeInArabic,
  }) = _IndustryType;

  factory IndustryType.fromJson(Map<String, dynamic> json) =>
      _$IndustryTypeFromJson(json);
}
