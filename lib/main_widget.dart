import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_theme.dart';
import 'package:maisour/shared/utils/screen_util_init.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';

class MainWidget extends ConsumerStatefulWidget {
  final bool forceIPhoneSize;

  const MainWidget({super.key, required this.forceIPhoneSize});

  @override
  ConsumerState<MainWidget> createState() => _MainWidgetState();
}

class _MainWidgetState extends ConsumerState<MainWidget> {
  @override
  void initState() {
    super.initState();
    // Initialize AppsFlyer after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeAppsFlyer();
    });
  }

  Future<void> _initializeAppsFlyer() async {
    try {
      final appsFlyerService = ref.read(appsFlyerServiceProvider);
      await appsFlyerService.initialize();
    } catch (error) {
      // AppsFlyer initialization failure shouldn't crash the app
      debugPrint('❌ Failed to initialize AppsFlyer: $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    final goRouter = ref.watch(goRouterProvider);
    return ScreenUtilInitWrapper(
      forceIPhoneSize: widget.forceIPhoneSize,
      child: MaterialApp.router(
        title: 'Maisour',
        debugShowCheckedModeBanner: false,

        // ✅ Routing via Riverpod-provided GoRouter
        routerDelegate: goRouter.routerDelegate,
        routeInformationParser: goRouter.routeInformationParser,
        routeInformationProvider: goRouter.routeInformationProvider,

        // ✅ Localization with easy_localization
        locale: context.locale,
        supportedLocales: context.supportedLocales,
        localizationsDelegates: context.localizationDelegates,

        // ✅ theme and font
        theme: AppTheme.theme,
      ),
    );
  }
}
