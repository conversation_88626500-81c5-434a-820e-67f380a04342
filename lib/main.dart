import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/app_environment.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';
import 'package:maisour/shared/localization/generated/codegen_loader.g.dart';
import 'package:maisour/shared/providers/app_provider_observer.dart';
import 'package:maisour/firebase_options.dart';
import 'package:maisour/main_widget.dart';
import 'package:maisour/shared/services/ios_device_check.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  AppEnvironment.flavor = AppFlavor.production;
  runMainApp(); // calls common setup
}

void runMainApp() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Only enable Crashlytics in production & release mode
  final enableCrashlytics = AppEnvironment.isProduction && kReleaseMode;
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(
    enableCrashlytics,
  );

  if (enableCrashlytics) {
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
  }

  final sharedPreferences = await SharedPreferences.getInstance();

  // Load saved language preference for EasyLocalization using SharedPrefsManager
  final prefsManager = SharedPrefsManager(sharedPreferences);
  final startLocale = Locale(prefsManager.getSavedLanguage);

  // Check if running on iPad and force iPhone size
  bool forceIPhoneSize = false;
  if (Platform.isIOS) {
    final bool isIPad = await IOSDeviceCheck.isIPad;
    forceIPhoneSize = isIPad;
  }

  runApp(
    EasyLocalization(
      supportedLocales: AppConstants.supportedLanguages
          .map((code) => Locale(code))
          .toList(),
      path: 'lib/shared/localization',
      fallbackLocale: Locale(AppConstants.fallbackLanguage),
      startLocale: startLocale, // Use saved language preference
      assetLoader: const CodegenLoader(), // Use generated loader
      child: ProviderScope(
        observers: [AppProviderObserver()],
        overrides: [sharedPrefsProvider.overrideWithValue(sharedPreferences)],
        child: MainWidget(forceIPhoneSize: forceIPhoneSize),
      ),
    ),
  );
}
