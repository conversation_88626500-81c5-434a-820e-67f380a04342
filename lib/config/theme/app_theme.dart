import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:maisour/config/theme/app_colors.dart';

abstract class AppTheme {
  static ThemeData theme = ThemeData(
    // platform: TargetPlatform.iOS,
    textTheme: GoogleFonts.manropeTextTheme(),
    scaffoldBackgroundColor: AppColors.white,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.white,
      elevation: 0,
      centerTitle: true,
      surfaceTintColor: Colors.transparent,
    ),
    dialogTheme: DialogThemeData(backgroundColor: AppColors.white),
    popupMenuTheme: PopupMenuThemeData(color: AppColors.white),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: AppColors.primary.shade200,
      selectionColor: AppColors.primary,
      selectionHandleColor: AppColors.primary,
    ),
  );
}
