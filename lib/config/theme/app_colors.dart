import 'package:flutter/material.dart';

abstract class AppColors {
  static MaterialColor get primary => const MaterialColor(
    0xFF2484C8, // <-- default (500)
    <int, Color>{
      100: Color(0xFFBBD9EE),
      200: Color(0xFF9AC6E6),
      300: Color(0xFF6CADDA),
      400: Color(0xFF509DD3),
      500: Color(0xFF2484C8), // <-- Default primary shade
      600: Color(0xFF2178B6),
      700: Color(0xFF1A5E8E),
      800: Color(0xFF14496E),
      900: Color(0xFF0F3754),
    },
  );

  static MaterialColor get secondary => const MaterialColor(
    0xFF5BA763, // <-- default (500)
    <int, Color>{
      100: Color(0xFFDEEDE0),
      200: Color(0xFFBDDCC1),
      300: Color(0xFF9DCAA1),
      400: Color(0xFF7CB982),
      500: Color(0xFF5BA763), // <-- Default primary shade
      600: Color(0xFF48864F),
      700: Color(0xFF36653B),
      800: Color(0xFF244327),
      900: Color(0xFF122214),
    },
  );

  static MaterialColor get tertiary => const MaterialColor(
    0xFFDBDFAC, // <-- default (500)
    <int, Color>{
      100: Color(0xFFF9FAF2),
      200: Color(0xFFF4F5E6),
      300: Color(0xFFE9EBCD),
      400: Color(0xFFE2E5BC),
      500: Color(0xFFDBDFAC), // <-- Default primary shade
      600: Color(0xFFAFB289),
      700: Color(0xFF838567),
      800: Color(0xFF575944),
      900: Color(0xFF36372B),
    },
  );

  static MaterialColor get dark => const MaterialColor(
    0xFF10111B, // <-- default (500)
    <int, Color>{
      100: Color(0xFFDBDBDC),
      200: Color(0xFFB7B7BA),
      300: Color(0xFF6F7076),
      400: Color(0xFF3F4048),
      500: Color(0xFF10111B), // <-- Default primary shade
      600: Color(0xFF0C0D15),
      700: Color(0xFF090A10),
      800: Color(0xFF06060A),
      900: Color(0xFF040406),
    },
  );

  static MaterialColor get light => const MaterialColor(
    0xFFEEE5E5, // <-- default (500)
    <int, Color>{
      100: Color(0xFFFCFBFB),
      200: Color(0xFFF9F7F7),
      300: Color(0xFFF4EFEF),
      400: Color(0xFFF1EAEA),
      500: Color(0xFFEEE5E5), // <-- Default primary shade
      600: Color(0xFFBEB7B7),
      700: Color(0xFF8E8989),
      800: Color(0xFF5F5B5B),
      900: Color(0xFF3B3939),
    },
  );

  static MaterialColor get gray => const MaterialColor(
    0xFF929CB2, // <-- default (500)
    <int, Color>{
      100: Color(0xFFEEF0F3),
      200: Color(0xFFDEE1E7),
      300: Color(0xFFBDC3D0),
      400: Color(0xFFA7AFC1),
      500: Color(0xFF929CB2), // <-- Default primary shade
      600: Color(0xFF747C8E),
      700: Color(0xFF575D6A),
      800: Color(0xFF3A3E47),
      900: Color(0xFF24272C),
    },
  );

  static MaterialColor get success => MaterialColor(
    0xFF28B068, // Primary (500)
    <int, Color>{
      100: Color(0xFFDEF3E8),
      200: Color(0xFFBEE7D1),
      300: Color(0xFF7ECFA4),
      400: Color(0xFF52BF86),
      500: Color(0xFF28B068), // <-- Default primary shade
      600: Color(0xFF208C53),
      700: Color(0xFF18693E),
      800: Color(0xFF104629),
      900: Color(0xFF0A2C1A),
    },
  );

  static MaterialColor get danger => const MaterialColor(
    0xFFE24138, // <-- default (500)
    <int, Color>{
      100: Color(0xFFFAE2E1),
      200: Color(0xFFF6C6C3),
      300: Color(0xFFED8D87),
      400: Color(0xFFE7665F),
      500: Color(0xFFE24138), // <-- Default primary shade
      600: Color(0xFFB4342C),
      700: Color(0xFF872721),
      800: Color(0xFF5A1A16),
      900: Color(0xFF38100E),
    },
  );

  static MaterialColor get warning => const MaterialColor(
    0xFFF59E0B, // <-- default (500)
    <int, Color>{
      100: Color(0xFFFEF3C7),
      200: Color(0xFFFDE68A),
      300: Color(0xFFFCD34D),
      400: Color(0xFFFBBF24),
      500: Color(0xFFF59E0B), // <-- Default primary shade
      600: Color(0xFFD97706),
      700: Color(0xFFB45309),
      800: Color(0xFF92400E),
      900: Color(0xFF78350F),
    },
  );

  static MaterialColor get info => const MaterialColor(
    0xFF4BA1E9, // <-- default (500)
    <int, Color>{
      100: Color(0xFFE4F0FB),
      200: Color(0xFFC9E2F8),
      300: Color(0xFF93C6F1),
      400: Color(0xFF6EB3ED),
      500: Color(0xFF4BA1E9), // <-- Default primary shade
      600: Color(0xFF3C80BA),
      700: Color(0xFF2D608B),
      800: Color(0xFF1E405D),
      900: Color(0xFF12283A),
    },
  );

  // Other Colors
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color lightBlue = Color(0xFFECF3FB);

  static const Color gradientYellow = Color(0xFFFBEFD5);
  static const Color gradientBlue = Color(0xFFD5EBFB);
  static const Color gradientGreen = Color(0xFFA8FF78);
  static const Color gradientDarkGreen = Color(0xFF78FFD6);
}
