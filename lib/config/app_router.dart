import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/modules/account/account_screen.dart';
import 'package:maisour/modules/my_profile/my_profile_screen.dart';
import 'package:maisour/modules/update_password/update_password_screen.dart';
import 'package:maisour/modules/auth/email_activation_screen.dart';
import 'package:maisour/modules/auth/forgot_password_screen.dart';
import 'package:maisour/modules/auth/login_screen.dart';
import 'package:maisour/modules/auth/password_screen.dart';
import 'package:maisour/modules/auth/registration_screen.dart';
import 'package:maisour/modules/get_help/get_help_screen.dart';
import 'package:maisour/modules/no_internet/no_internet_screen.dart';
import 'package:maisour/modules/onboarding/onboarding_screen.dart';
import 'package:maisour/modules/onboarding/passport_verification_screen.dart';
import 'package:maisour/modules/portfolio/portfolio_screen.dart';
import 'package:maisour/modules/properties/properties_screen.dart';
import 'package:maisour/modules/property_details/property_details_screen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/property_investment/property_investment_screen.dart';
import 'package:maisour/modules/splash/splash_screen.dart';
import 'package:maisour/modules/wallet/wallet_screen.dart';
import 'package:maisour/modules/welcome/welcome_screen.dart';
import 'package:maisour/modules/home/<USER>';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/shared/observers/app_route_observer.dart';
import 'package:maisour/modules/onboarding/address_verification_screen.dart';
import 'package:maisour/modules/onboarding/employment_information_screen.dart';
import 'package:maisour/modules/onboarding/tax_residency_information_screen.dart';
import 'package:maisour/modules/onboarding/verification_in_progress_screen.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/modules/phone_verification/phone_verification_screen.dart';
import 'package:maisour/modules/phone_verification/otp_verification_screen.dart';
import 'package:maisour/modules/phone_verification/phone_verification_success_screen.dart';
import 'package:maisour/modules/your_order/your_order_screen.dart';
import 'package:maisour/modules/your_order/order_successful_screen.dart';
import 'package:maisour/modules/add_fund/add_fund_screen.dart';
import 'package:maisour/modules/ownership_details/ownership_details_screen.dart';
import 'package:maisour/modules/transactions/transactions_screen.dart';
import 'package:maisour/modules/bank_accounts/add_bank_account_screen.dart';
import 'package:maisour/modules/bank_accounts/view_bank_account_screen.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/modules/bank_transfer/bank_transfer_pending_screen.dart';
import 'package:maisour/modules/add_fund/payment_webview_screen.dart';
import 'package:maisour/modules/withdraw/withdraw_screen.dart';
import 'package:maisour/modules/documents/documents_screen.dart';
import 'package:maisour/modules/select_language/select_language_screen.dart';
import 'package:maisour/modules/select_currency/select_currency_screen.dart';
import 'package:maisour/modules/get_help/raise_ticket_screen.dart';
import 'package:maisour/modules/close_account/close_account_screen.dart';
import 'package:maisour/modules/my_profile/country_of_birth_screen.dart';

enum RouteName {
  splash,
  noInternet,
  welcome,
  login,
  password,
  registration,
  forgotPassword,
  emailActivation,
  onboarding,
  properties,
  propertyDetails,
  portfolio,
  wallet,
  account,
  myProfile,
  updatePassword,
  passportVerification,
  addressVerification,
  employmentInformation,
  taxResidencyInformation,
  verificationInProgress,
  phoneVerification,
  otpVerification,
  phoneVerificationSuccess,
  getHelp,
  propertyInvestment,
  ownershipDetails,
  yourOrder,
  orderSuccessful,
  addFund,
  transactions,
  addBankAccount,
  viewBankAccount,
  bankTransferPending,
  paymentWebView,
  withdraw,
  documents,
  selectLanguage,
  selectCurrency,
  raiseTicket,
  closeAccount,
  countryOfBirth,
}

abstract class RoutePath {
  static const splash = '/';
  static const noInternet = '/no-internet';
  static const welcome = '/welcome';
  static const login = '/login';
  static const password = '/password';
  static const registration = '/registration';
  static const forgotPassword = '/forgot-password';
  static const emailActivation = '/email-activation';
  static const onboarding = '/onboarding';
  static const properties = '/properties';
  static const propertyDetails = '/property-details';
  static const portfolio = '/portfolio';
  static const wallet = '/wallet';
  static const account = '/account';
  static const myProfile = '/my-profile';
  static const updatePassword = '/update-password';
  static const passportVerification = '/onboarding/passport-verification';
  static const addressVerification = '/onboarding/address-verification';
  static const employmentInformation = '/onboarding/employment-information';
  static const taxResidencyInformation =
      '/onboarding/tax-residency-information';
  static const verificationInProgress = '/onboarding/verification-in-progress';
  static const phoneVerification = '/phone-verification';
  static const otpVerification = '/phone-verification/otp';
  static const phoneVerificationSuccess = '/phone-verification/success';
  static const getHelp = '/get-help';
  static const propertyInvestment = '/property-investment';
  static const ownershipDetails = '/ownership-details';
  static const yourOrder = '/your-order';
  static const orderSuccessful = '/order-successful';
  static const addFund = '/add-fund';
  static const transactions = '/transactions';
  static const addBankAccount = '/add-bank-account';
  static const viewBankAccount = '/view-bank-account';
  static const bankTransferPending = '/bank-transfer-pending';
  static const paymentWebView = '/payment-webview';
  static const withdraw = '/withdraw';
  static const documents = '/documents';
  static const selectLanguage = '/select-language';
  static const selectCurrency = '/select-currency';
  static const raiseTicket = '/raise-ticket';
  static const closeAccount = '/close-account';
  static const countryOfBirth = '/country-of-birth';
}

final goRouterProvider = Provider<GoRouter>((ref) {
  // Get the route observer from Riverpod
  final routeObserver = ref.watch(appRouteObserverProvider);

  return GoRouter(
    initialLocation: RoutePath.splash,
    observers: [routeObserver], // Add route observer for AppsFlyer tracking
    routes: [
      GoRoute(
        path: RoutePath.splash,
        name: RouteName.splash.name,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: RoutePath.welcome,
        name: RouteName.welcome.name,
        builder: (context, state) => const WelcomeScreen(),
      ),

      GoRoute(
        path: RoutePath.login,
        name: RouteName.login.name,
        builder: (context, state) => const LoginScreen(),
      ),

      GoRoute(
        path: RoutePath.registration,
        name: RouteName.registration.name,
        builder: (context, state) {
          final args = state.extra as Map<String, dynamic>;
          return RegistrationScreen(
            email: args['email'] as String,
            signUpType: args['signUpType'] as String,
            fullName: args['fullName'] as String?,
          );
        },
      ),

      GoRoute(
        path: RoutePath.forgotPassword,
        name: RouteName.forgotPassword.name,
        builder: (context, state) {
          final email = state.extra as String;
          return ForgotPasswordScreen(email: email);
        },
      ),

      GoRoute(
        path: RoutePath.emailActivation,
        name: RouteName.emailActivation.name,
        builder: (context, state) {
          final email = state.extra as String;
          return EmailActivationScreen(email: email);
        },
      ),

      GoRoute(
        path: RoutePath.noInternet,
        name: RouteName.noInternet.name,
        builder: (context, state) => NoInternetScreen(
          onRetry: () {
            // Navigate back to splash for re-evaluation
            context.goNamed(RouteName.splash.name);
          },
        ),
      ),

      GoRoute(
        path: RoutePath.password,
        name: RouteName.password.name,
        builder: (context, state) {
          final email = state.extra as String;
          return PasswordScreen(email: email);
        },
      ),

      ShellRoute(
        builder: (context, state, child) => HomeScreen(child: child),
        routes: [
          GoRoute(
            path: RoutePath.properties,
            name: RouteName.properties.name,
            builder: (context, state) => const PropertiesScreen(),
          ),
          GoRoute(
            path: RoutePath.portfolio,
            name: RouteName.portfolio.name,
            builder: (context, state) => const PortfolioScreen(),
          ),
          GoRoute(
            path: RoutePath.wallet,
            name: RouteName.wallet.name,
            builder: (context, state) => const WalletScreen(),
          ),
          GoRoute(
            path: RoutePath.account,
            name: RouteName.account.name,
            builder: (context, state) => const AccountScreen(),
          ),
        ],
      ),

      GoRoute(
        path: RoutePath.propertyDetails,
        name: RouteName.propertyDetails.name,
        builder: (context, state) {
          final propertyId = state.extra as int;
          return PropertyDetailsScreen(propertyId: propertyId);
        },
      ),

      GoRoute(
        path: RoutePath.propertyInvestment,
        name: RouteName.propertyInvestment.name,
        builder: (context, state) {
          final propertyDetails = state.extra as PropertyDetails;
          return PropertyInvestmentScreen(propertyDetails: propertyDetails);
        },
      ),

      GoRoute(
        path: RoutePath.ownershipDetails,
        name: RouteName.ownershipDetails.name,
        builder: (context, state) {
          final propertyId = state.extra as int;
          return OwnershipDetailsScreen(propertyId: propertyId);
        },
      ),

      GoRoute(
        path: RoutePath.onboarding,
        name: RouteName.onboarding.name,
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: RoutePath.passportVerification,
        name: RouteName.passportVerification.name,
        builder: (context, state) {
          final extra = state.extra;
          VerificationType type = VerificationType.passportWithSelfie;
          if (extra is VerificationType) {
            type = extra;
          }
          return PassportVerificationScreen(verificationType: type);
        },
      ),
      GoRoute(
        path: RoutePath.addressVerification,
        name: RouteName.addressVerification.name,
        builder: (context, state) => const AddressVerificationScreen(),
      ),
      GoRoute(
        path: RoutePath.employmentInformation,
        name: RouteName.employmentInformation.name,
        builder: (context, state) => const EmploymentInformationScreen(),
      ),
      GoRoute(
        path: RoutePath.taxResidencyInformation,
        name: RouteName.taxResidencyInformation.name,
        builder: (context, state) {
          final extra = state.extra as bool?;
          return TaxResidencyInformationScreen(fromOnboarding: extra ?? false);
        },
      ),
      GoRoute(
        path: RoutePath.verificationInProgress,
        name: RouteName.verificationInProgress.name,
        builder: (context, state) {
          final extra = state.extra;
          VerificationType type = VerificationType.passportWithSelfie;
          if (extra is VerificationType) {
            type = extra;
          }
          return VerificationInProgressScreen(verificationType: type);
        },
      ),
      GoRoute(
        path: RoutePath.phoneVerification,
        name: RouteName.phoneVerification.name,
        builder: (context, state) {
          final phoneNumber = state.extra as String?;
          return PhoneVerificationScreen(phoneNumber: phoneNumber);
        },
      ),
      GoRoute(
        path: RoutePath.otpVerification,
        name: RouteName.otpVerification.name,
        builder: (context, state) {
          final phoneNumber = state.extra as String?;
          return OtpVerificationScreen(phoneNumber: phoneNumber);
        },
      ),
      GoRoute(
        path: RoutePath.phoneVerificationSuccess,
        name: RouteName.phoneVerificationSuccess.name,
        builder: (context, state) => const PhoneVerificationSuccessScreen(),
      ),

      GoRoute(
        path: RoutePath.getHelp,
        name: RouteName.getHelp.name,
        builder: (context, state) => const GetHelpScreen(),
      ),

      GoRoute(
        path: RoutePath.yourOrder,
        name: RouteName.yourOrder.name,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final finacialDetailsData =
              extra['finacialDetailsData'] as AddInvestmentRequest;
          final propertyDetails = extra['propertyDetails'] as PropertyDetails;
          return YourOrderScreen(
            finacialDetailsData: finacialDetailsData,
            propertyDetails: propertyDetails,
          );
        },
      ),

      GoRoute(
        path: RoutePath.orderSuccessful,
        name: RouteName.orderSuccessful.name,
        builder: (context, state) {
          final propertyStatus = state.extra as PropertyStatus;
          return OrderSuccessfulScreen(propertyStatus: propertyStatus);
        },
      ),

      GoRoute(
        path: RoutePath.addFund,
        name: RouteName.addFund.name,
        builder: (context, state) => const AddFundScreen(),
      ),

      GoRoute(
        path: RoutePath.transactions,
        name: RouteName.transactions.name,
        builder: (context, state) => const TransactionsScreen(),
      ),

      GoRoute(
        path: RoutePath.addBankAccount,
        name: RouteName.addBankAccount.name,
        builder: (context, state) => const AddBankAccountScreen(),
      ),

      GoRoute(
        path: RoutePath.viewBankAccount,
        name: RouteName.viewBankAccount.name,
        builder: (context, state) {
          final bankAccount = state.extra as BankAccount;
          return ViewBankAccountScreen(bankAccount: bankAccount);
        },
      ),
      GoRoute(
        path: RoutePath.bankTransferPending,
        name: RouteName.bankTransferPending.name,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final referenceNumber = extra['referenceNumber'] as String;
          final amount = extra['amount'] as int;
          return BankTransferPendingScreen(
            referenceId: referenceNumber,
            amount: amount,
          );
        },
      ),
      GoRoute(
        path: RoutePath.paymentWebView,
        name: RouteName.paymentWebView.name,
        builder: (context, state) {
          final paymentLink = state.extra as String;
          return PaymentWebViewScreen(paymentLink: paymentLink);
        },
      ),
      GoRoute(
        path: RoutePath.withdraw,
        name: RouteName.withdraw.name,
        builder: (context, state) => const WithdrawScreen(),
      ),

      GoRoute(
        path: RoutePath.myProfile,
        name: RouteName.myProfile.name,
        builder: (context, state) => const MyProfileScreen(),
      ),

      GoRoute(
        path: RoutePath.updatePassword,
        name: RouteName.updatePassword.name,
        builder: (context, state) => const UpdatePasswordScreen(),
      ),

      GoRoute(
        path: RoutePath.documents,
        name: RouteName.documents.name,
        builder: (context, state) => const DocumentsScreen(),
      ),

      GoRoute(
        path: RoutePath.selectLanguage,
        name: RouteName.selectLanguage.name,
        builder: (context, state) => const SelectLanguageScreen(),
      ),

      GoRoute(
        path: RoutePath.selectCurrency,
        name: RouteName.selectCurrency.name,
        builder: (context, state) => const SelectCurrencyScreen(),
      ),
      GoRoute(
        path: RoutePath.raiseTicket,
        name: RouteName.raiseTicket.name,
        builder: (context, state) => const RaiseTicketScreen(),
      ),
      GoRoute(
        path: RoutePath.closeAccount,
        name: RouteName.closeAccount.name,
        builder: (context, state) => const CloseAccountScreen(),
      ),
      GoRoute(
        path: RoutePath.countryOfBirth,
        name: RouteName.countryOfBirth.name,
        builder: (context, state) => const CountryOfBirthScreen(),
      ),
    ],
  );
}, name: 'goRouterProvider');
