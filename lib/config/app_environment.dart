import 'package:maisour/env/env.dart';

enum AppFlavor { development, staging, production }

class AppEnvironment {
  static late AppFlavor flavor;

  static String get baseUrl {
    switch (flavor) {
      case AppFlavor.development:
        return Env.baseUrlDevelopment;
      case AppFlavor.staging:
        return Env.baseUrlStaging;
      case AppFlavor.production:
        return Env.baseUrlProduction;
    }
  }

  // static String get appName {
  //   switch (flavor) {
  //     case AppFlavor.development:
  //       return 'Maisour Dev';

  //     case AppFlavor.staging:
  //       return 'Maisour Staging';

  //     case AppFlavor.production:
  //       return 'Maisour';
  //   }
  // }

  static bool get isDevelopment => flavor == AppFlavor.development;
  static bool get isStaging => flavor == AppFlavor.staging;
  static bool get isProduction => flavor == AppFlavor.production;

  /// AppsFlyer Configuration
  /// Values loaded from environment variables (.env file)
  static String get appsFlyerDevKey {
    return Env.afDevKey;
  }

  /// iOS App ID for AppsFlyer (only needed for iOS)
  /// Value loaded from environment variables (.env file)
  static String get appsFlyerAppId {
    return Env.afAppId;
  }

  static String get idwiseClientKey {
    switch (flavor) {
      case AppFlavor.development:
      case AppFlavor.staging:
        return Env.idwiseSandboxClientKey;
      case AppFlavor.production:
        return Env.idwiseLiveClientKey;
    }
  }

  static String get idwisePassportSelfieFlowId {
    switch (flavor) {
      case AppFlavor.development:
      case AppFlavor.staging:
        return Env.idwisePassportSelfieFlowIdSandbox;
      case AppFlavor.production:
        return Env.idwisePassportSelfieFlowId;
    }
  }

  static String get idwisePassportFlowId {
    switch (flavor) {
      case AppFlavor.development:
      case AppFlavor.staging:
        return Env.idwisePassportFlowIdSandbox;
      case AppFlavor.production:
        return Env.idwisePassportFlowId;
    }
  }

  static String get idwisePoaFlowId {
    switch (flavor) {
      case AppFlavor.development:
      case AppFlavor.staging:
        return Env.idwisePoaFlowIdSandbox;
      case AppFlavor.production:
        return Env.idwisePoaFlowId;
    }
  }
}
