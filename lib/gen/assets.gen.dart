/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/account.svg
  SvgGenImage get account => const SvgGenImage('assets/icons/account.svg');

  /// File path: assets/icons/account_filled.svg
  SvgGenImage get accountFilled =>
      const SvgGenImage('assets/icons/account_filled.svg');

  /// File path: assets/icons/aed_flag.svg
  SvgGenImage get aedFlag => const SvgGenImage('assets/icons/aed_flag.svg');

  /// File path: assets/icons/apple.svg
  SvgGenImage get apple => const SvgGenImage('assets/icons/apple.svg');

  /// File path: assets/icons/arrow_bottom_left.svg
  SvgGenImage get arrowBottomLeft =>
      const SvgGenImage('assets/icons/arrow_bottom_left.svg');

  /// File path: assets/icons/arrow_top_right.svg
  SvgGenImage get arrowTopRight =>
      const SvgGenImage('assets/icons/arrow_top_right.svg');

  /// File path: assets/icons/bank_account.svg
  SvgGenImage get bankAccount =>
      const SvgGenImage('assets/icons/bank_account.svg');

  /// File path: assets/icons/birth.svg
  SvgGenImage get birth => const SvgGenImage('assets/icons/birth.svg');

  /// File path: assets/icons/building.svg
  SvgGenImage get building => const SvgGenImage('assets/icons/building.svg');

  /// File path: assets/icons/building_filled.svg
  SvgGenImage get buildingFilled =>
      const SvgGenImage('assets/icons/building_filled.svg');

  /// File path: assets/icons/building_green.svg
  SvgGenImage get buildingGreen =>
      const SvgGenImage('assets/icons/building_green.svg');

  /// File path: assets/icons/card.svg
  SvgGenImage get card => const SvgGenImage('assets/icons/card.svg');

  /// File path: assets/icons/cart.svg
  SvgGenImage get cart => const SvgGenImage('assets/icons/cart.svg');

  /// File path: assets/icons/chat.svg
  SvgGenImage get chat => const SvgGenImage('assets/icons/chat.svg');

  /// File path: assets/icons/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icons/check.svg');

  /// File path: assets/icons/coin.svg
  SvgGenImage get coin => const SvgGenImage('assets/icons/coin.svg');

  /// File path: assets/icons/copy.svg
  SvgGenImage get copy => const SvgGenImage('assets/icons/copy.svg');

  /// File path: assets/icons/currency.svg
  SvgGenImage get currency => const SvgGenImage('assets/icons/currency.svg');

  /// File path: assets/icons/doc.svg
  SvgGenImage get doc => const SvgGenImage('assets/icons/doc.svg');

  /// File path: assets/icons/documents.svg
  SvgGenImage get documents => const SvgGenImage('assets/icons/documents.svg');

  /// File path: assets/icons/eur_flag.svg
  SvgGenImage get eurFlag => const SvgGenImage('assets/icons/eur_flag.svg');

  /// File path: assets/icons/excel.svg
  SvgGenImage get excel => const SvgGenImage('assets/icons/excel.svg');

  /// File path: assets/icons/eye.svg
  SvgGenImage get eye => const SvgGenImage('assets/icons/eye.svg');

  /// File path: assets/icons/eye_closed.svg
  SvgGenImage get eyeClosed => const SvgGenImage('assets/icons/eye_closed.svg');

  /// File path: assets/icons/facebook.svg
  SvgGenImage get facebook => const SvgGenImage('assets/icons/facebook.svg');

  /// File path: assets/icons/failed.svg
  SvgGenImage get failed => const SvgGenImage('assets/icons/failed.svg');

  /// File path: assets/icons/filter.svg
  SvgGenImage get filter => const SvgGenImage('assets/icons/filter.svg');

  /// File path: assets/icons/filter_active.svg
  SvgGenImage get filterActive =>
      const SvgGenImage('assets/icons/filter_active.svg');

  /// File path: assets/icons/get_help.svg
  SvgGenImage get getHelp => const SvgGenImage('assets/icons/get_help.svg');

  /// File path: assets/icons/gift.svg
  SvgGenImage get gift => const SvgGenImage('assets/icons/gift.svg');

  /// File path: assets/icons/globe.svg
  SvgGenImage get globe => const SvgGenImage('assets/icons/globe.svg');

  /// File path: assets/icons/google.svg
  SvgGenImage get google => const SvgGenImage('assets/icons/google.svg');

  /// File path: assets/icons/half_circle_green.svg
  SvgGenImage get halfCircleGreen =>
      const SvgGenImage('assets/icons/half_circle_green.svg');

  /// File path: assets/icons/hand.svg
  SvgGenImage get hand => const SvgGenImage('assets/icons/hand.svg');

  /// File path: assets/icons/hand_green.svg
  SvgGenImage get handGreen => const SvgGenImage('assets/icons/hand_green.svg');

  /// File path: assets/icons/help.svg
  SvgGenImage get help => const SvgGenImage('assets/icons/help.svg');

  /// File path: assets/icons/image.svg
  SvgGenImage get image => const SvgGenImage('assets/icons/image.svg');

  /// File path: assets/icons/images.svg
  SvgGenImage get images => const SvgGenImage('assets/icons/images.svg');

  /// File path: assets/icons/info.svg
  SvgGenImage get info => const SvgGenImage('assets/icons/info.svg');

  /// File path: assets/icons/instagram.svg
  SvgGenImage get instagram => const SvgGenImage('assets/icons/instagram.svg');

  /// File path: assets/icons/language.svg
  SvgGenImage get language => const SvgGenImage('assets/icons/language.svg');

  /// File path: assets/icons/lock.svg
  SvgGenImage get lock => const SvgGenImage('assets/icons/lock.svg');

  /// File path: assets/icons/logout.svg
  SvgGenImage get logout => const SvgGenImage('assets/icons/logout.svg');

  /// File path: assets/icons/mail.svg
  SvgGenImage get mail => const SvgGenImage('assets/icons/mail.svg');

  /// File path: assets/icons/mastercard.svg
  SvgGenImage get mastercard =>
      const SvgGenImage('assets/icons/mastercard.svg');

  /// File path: assets/icons/money_in.svg
  SvgGenImage get moneyIn => const SvgGenImage('assets/icons/money_in.svg');

  /// File path: assets/icons/navigation.svg
  SvgGenImage get navigation =>
      const SvgGenImage('assets/icons/navigation.svg');

  /// File path: assets/icons/notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/icons/notification.svg');

  /// File path: assets/icons/notification_active.svg
  SvgGenImage get notificationActive =>
      const SvgGenImage('assets/icons/notification_active.svg');

  /// File path: assets/icons/notification_green.svg
  SvgGenImage get notificationGreen =>
      const SvgGenImage('assets/icons/notification_green.svg');

  /// File path: assets/icons/our_story.svg
  SvgGenImage get ourStory => const SvgGenImage('assets/icons/our_story.svg');

  /// File path: assets/icons/pdf.svg
  SvgGenImage get pdf => const SvgGenImage('assets/icons/pdf.svg');

  /// File path: assets/icons/pencil_line.svg
  SvgGenImage get pencilLine =>
      const SvgGenImage('assets/icons/pencil_line.svg');

  /// File path: assets/icons/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/icons/phone.svg');

  /// File path: assets/icons/portfolio.svg
  SvgGenImage get portfolio => const SvgGenImage('assets/icons/portfolio.svg');

  /// File path: assets/icons/portfolio_filled.svg
  SvgGenImage get portfolioFilled =>
      const SvgGenImage('assets/icons/portfolio_filled.svg');

  /// File path: assets/icons/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icons/share.svg');

  /// File path: assets/icons/shield.svg
  SvgGenImage get shield => const SvgGenImage('assets/icons/shield.svg');

  /// File path: assets/icons/sort.svg
  SvgGenImage get sort => const SvgGenImage('assets/icons/sort.svg');

  /// File path: assets/icons/sorting_active.svg
  SvgGenImage get sortingActive =>
      const SvgGenImage('assets/icons/sorting_active.svg');

  /// File path: assets/icons/star.svg
  SvgGenImage get star => const SvgGenImage('assets/icons/star.svg');

  /// File path: assets/icons/success.svg
  SvgGenImage get success => const SvgGenImage('assets/icons/success.svg');

  /// File path: assets/icons/success_filled.svg
  SvgGenImage get successFilled =>
      const SvgGenImage('assets/icons/success_filled.svg');

  /// File path: assets/icons/support_call.svg
  SvgGenImage get supportCall =>
      const SvgGenImage('assets/icons/support_call.svg');

  /// File path: assets/icons/ticket.svg
  SvgGenImage get ticket => const SvgGenImage('assets/icons/ticket.svg');

  /// File path: assets/icons/trash.svg
  SvgGenImage get trash => const SvgGenImage('assets/icons/trash.svg');

  /// File path: assets/icons/trending.svg
  SvgGenImage get trending => const SvgGenImage('assets/icons/trending.svg');

  /// File path: assets/icons/twitter.svg
  SvgGenImage get twitter => const SvgGenImage('assets/icons/twitter.svg');

  /// File path: assets/icons/usd_flag.svg
  SvgGenImage get usdFlag => const SvgGenImage('assets/icons/usd_flag.svg');

  /// File path: assets/icons/users.svg
  SvgGenImage get users => const SvgGenImage('assets/icons/users.svg');

  /// File path: assets/icons/visa.svg
  SvgGenImage get visa => const SvgGenImage('assets/icons/visa.svg');

  /// File path: assets/icons/wallet.svg
  SvgGenImage get wallet => const SvgGenImage('assets/icons/wallet.svg');

  /// File path: assets/icons/wallet_filled.svg
  SvgGenImage get walletFilled =>
      const SvgGenImage('assets/icons/wallet_filled.svg');

  /// File path: assets/icons/warning.svg
  SvgGenImage get warning => const SvgGenImage('assets/icons/warning.svg');

  /// File path: assets/icons/whatsapp.svg
  SvgGenImage get whatsapp => const SvgGenImage('assets/icons/whatsapp.svg');

  /// File path: assets/icons/whatsapp_chat.svg
  SvgGenImage get whatsappChat =>
      const SvgGenImage('assets/icons/whatsapp_chat.svg');

  /// File path: assets/icons/youtube.svg
  SvgGenImage get youtube => const SvgGenImage('assets/icons/youtube.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    account,
    accountFilled,
    aedFlag,
    apple,
    arrowBottomLeft,
    arrowTopRight,
    bankAccount,
    birth,
    building,
    buildingFilled,
    buildingGreen,
    card,
    cart,
    chat,
    check,
    coin,
    copy,
    currency,
    doc,
    documents,
    eurFlag,
    excel,
    eye,
    eyeClosed,
    facebook,
    failed,
    filter,
    filterActive,
    getHelp,
    gift,
    globe,
    google,
    halfCircleGreen,
    hand,
    handGreen,
    help,
    image,
    images,
    info,
    instagram,
    language,
    lock,
    logout,
    mail,
    mastercard,
    moneyIn,
    navigation,
    notification,
    notificationActive,
    notificationGreen,
    ourStory,
    pdf,
    pencilLine,
    phone,
    portfolio,
    portfolioFilled,
    share,
    shield,
    sort,
    sortingActive,
    star,
    success,
    successFilled,
    supportCall,
    ticket,
    trash,
    trending,
    twitter,
    usdFlag,
    users,
    visa,
    wallet,
    walletFilled,
    warning,
    whatsapp,
    whatsappChat,
    youtube,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/address.png
  AssetGenImage get address => const AssetGenImage('assets/images/address.png');

  /// File path: assets/images/dirham.png
  AssetGenImage get dirham => const AssetGenImage('assets/images/dirham.png');

  /// File path: assets/images/employment_info.png
  AssetGenImage get employmentInfo =>
      const AssetGenImage('assets/images/employment_info.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/logo_text.png
  AssetGenImage get logoText =>
      const AssetGenImage('assets/images/logo_text.png');

  /// File path: assets/images/no_internet.png
  AssetGenImage get noInternet =>
      const AssetGenImage('assets/images/no_internet.png');

  /// File path: assets/images/no_property.png
  AssetGenImage get noProperty =>
      const AssetGenImage('assets/images/no_property.png');

  /// File path: assets/images/passport.png
  AssetGenImage get passport =>
      const AssetGenImage('assets/images/passport.png');

  /// File path: assets/images/passport_instruction.png
  AssetGenImage get passportInstruction =>
      const AssetGenImage('assets/images/passport_instruction.png');

  /// File path: assets/images/tax_residency_information.png
  AssetGenImage get taxResidencyInformation =>
      const AssetGenImage('assets/images/tax_residency_information.png');

  /// File path: assets/images/utility_bill_instruction.png
  AssetGenImage get utilityBillInstruction =>
      const AssetGenImage('assets/images/utility_bill_instruction.png');

  /// File path: assets/images/verify_email.png
  AssetGenImage get verifyEmail =>
      const AssetGenImage('assets/images/verify_email.png');

  /// File path: assets/images/welcome_1.png
  AssetGenImage get welcome1 =>
      const AssetGenImage('assets/images/welcome_1.png');

  /// File path: assets/images/welcome_2.png
  AssetGenImage get welcome2 =>
      const AssetGenImage('assets/images/welcome_2.png');

  /// File path: assets/images/welcome_3.png
  AssetGenImage get welcome3 =>
      const AssetGenImage('assets/images/welcome_3.png');

  /// File path: assets/images/welcome_4.png
  AssetGenImage get welcome4 =>
      const AssetGenImage('assets/images/welcome_4.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    address,
    dirham,
    employmentInfo,
    logo,
    logoText,
    noInternet,
    noProperty,
    passport,
    passportInstruction,
    taxResidencyInformation,
    utilityBillInstruction,
    verifyEmail,
    welcome1,
    welcome2,
    welcome3,
    welcome4,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/splash.json
  LottieGenImage get splash =>
      const LottieGenImage('assets/lottie/splash.json');

  /// File path: assets/lottie/sucess.json
  LottieGenImage get sucess =>
      const LottieGenImage('assets/lottie/sucess.json');

  /// List of all assets
  List<LottieGenImage> get values => [splash, sucess];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)?
    frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
