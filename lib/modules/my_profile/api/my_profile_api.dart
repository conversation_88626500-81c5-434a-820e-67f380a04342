import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/services/network_service.dart';

part 'my_profile_api.g.dart';

/// Provider for my profile API
final myProfileApiProvider = Provider.autoDispose<MyProfileApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return MyProfileApi(dio);
});

@RestApi()
abstract class MyProfileApi {
  factory MyProfileApi(Dio dio, {String? baseUrl}) = _MyProfileApi;

  /// Update user country of birth
  @POST(ApiEndpoints.updateUserData)
  Future<AppUser> updateCountryOfBirth(
    @Query('countryOfBirth') String countryOfBirth,
  );
}
