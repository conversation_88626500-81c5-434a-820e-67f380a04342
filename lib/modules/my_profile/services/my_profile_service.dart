import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/my_profile/api/my_profile_api.dart';
import 'package:maisour/shared/models/app_user.dart';

/// Provider for my profile service
final myProfileServiceProvider = Provider.autoDispose<MyProfileService>((ref) {
  final api = ref.watch(myProfileApiProvider);
  return MyProfileService(api);
});

class MyProfileService {
  final MyProfileApi _api;

  MyProfileService(this._api);

  /// Update country of birth and return updated user
  Future<AppUser> updateCountryOfBirth(String countryOfBirth) async {
    return await _api.updateCountryOfBirth(countryOfBirth);
  }
}
