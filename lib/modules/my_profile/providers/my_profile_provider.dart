import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/my_profile/services/my_profile_service.dart';
import 'package:maisour/shared/providers/user_provider.dart';

/// Provider for updating user profile data
final myProfileUpdateProvider =
    AsyncNotifierProvider.autoDispose<MyProfileUpdateNotifier, bool>(() {
      return MyProfileUpdateNotifier();
    });

class MyProfileUpdateNotifier extends AutoDisposeAsyncNotifier<bool> {
  @override
  Future<bool> build() async {
    return false;
  }

  /// Update country of birth
  Future<void> updateCountryOfBirth(String countryOfBirth) async {
    state = const AsyncLoading();

    try {
      final service = ref.read(myProfileServiceProvider);
      final updatedUser = await service.updateCountryOfBirth(countryOfBirth);

      // Update the current user in the provider
      ref.read(userProvider.notifier).updateUser(updatedUser);

      state = const AsyncData(true);
    } catch (error, stackTrace) {
      state = AsyncError(error, stackTrace);
    }
  }
}
