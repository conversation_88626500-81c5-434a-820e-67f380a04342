import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class MyProfileScreen extends ConsumerWidget {
  const MyProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appUser = ref.watch(userProvider)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.myProfile.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Personal Details Section
              _buildPersonalDetailsHeader(),
              _buildPersonalDetailsCard(context, appUser),

              // Employment Details Section
              if (appUser.workStatus != null) ...[
                _buildEmploymentDetailsHeader(context, appUser),
                _buildEmploymentDetailsCard(appUser),
              ],

              // Contact Admin Text
              _buildContactAdminText(context),

              SizedBox(height: 24.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalDetailsHeader() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.w),
      child: Text(
        LocaleKeys.personalDetails.tr(),
        style: AppTextStyles.text16.bold.dark900,
      ),
    );
  }

  Widget _buildPersonalDetailsCard(BuildContext context, AppUser user) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Employment Status (showing user name)
          _buildDetailRow(
            icon: Assets.icons.account.svg(
              height: 24.w,
              width: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            label: LocaleKeys.fullName.tr(),
            value: user.user.fullName,
            showDivider: true,
          ),

          // Email
          _buildDetailRow(
            icon: Assets.icons.mail.svg(
              height: 24.w,
              width: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            label: LocaleKeys.email.tr(),
            value: user.user.email,
            showDivider: true,
          ),

          // Phone Number
          if (user.phoneNumber != null)
            _buildDetailRow(
              icon: Assets.icons.phone.svg(
                height: 24.w,
                width: 24.w,
                colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
              ),
              label: LocaleKeys.phoneNumber.tr(),
              value: user.phoneNumber!,
              showDivider: true,
              showEditButton: true,
            ).onTap(() {
              GoRouter.of(context).pushNamed(
                RouteName.phoneVerification.name,
                extra: user.phoneNumber!,
              );
            }),

          // Country of Birth
          _buildDetailRow(
            icon: Assets.icons.birth.svg(
              height: 24.w,
              width: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            label: LocaleKeys.countryOfBirth.tr(),
            value: user.countryOfBirth ?? LocaleKeys.notProvided.tr(),
            showDivider: false,
            showEditButton: user.workStatus != null,
          ).onTap(() {
            if (user.workStatus != null) {
              GoRouter.of(context).pushNamed(RouteName.countryOfBirth.name);
            }
          }),
        ],
      ),
    );
  }

  Widget _buildEmploymentDetailsHeader(BuildContext context, AppUser user) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LocaleKeys.employmentDetails.tr(),
            style: AppTextStyles.text16.bold.dark900,
          ),
          if (user.verifiedStatus == VerifiedStatus.verified ||
              user.verifiedStatus == VerifiedStatus.pending)
            AppButton(
              text: LocaleKeys.edit.tr(),
              type: ButtonType.text,
              textColor: AppColors.primary,
              onPressed: () {
                GoRouter.of(
                  context,
                ).pushNamed(RouteName.employmentInformation.name);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildEmploymentDetailsCard(AppUser user) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Employment Status
              Expanded(
                child: _buildEmploymentDetailRow(
                  label: LocaleKeys.employmentStatus.tr(),
                  value: user.workStatus?.title ?? LocaleKeys.notProvided.tr(),
                ),
              ),

              // Business Name
              if (user.isEmployed)
                Expanded(
                  child: _buildEmploymentDetailRow(
                    label: LocaleKeys.companyName.tr(),
                    value: user.employer ?? LocaleKeys.notProvided.tr(),
                  ),
                ),
            ],
          ),
          if (user.isEmployed) 16.w.heightBox,
          if (user.isEmployed)
            Row(
              children: [
                // Industry
                Expanded(
                  child: _buildEmploymentDetailRow(
                    label: LocaleKeys.industry.tr(),
                    value: user.industryLov ?? LocaleKeys.notProvided.tr(),
                  ),
                ),

                // Role
                Expanded(
                  child: _buildEmploymentDetailRow(
                    label: LocaleKeys.role.tr(),
                    value: user.roleLov ?? LocaleKeys.notProvided.tr(),
                  ),
                ),
              ],
            ),

          16.w.heightBox,

          // Annual Income
          _buildEmploymentDetailRow(
            label: LocaleKeys.annualIncome.tr(),
            value: user.annualIncome?.title ?? LocaleKeys.notProvided.tr(),
          ),

          16.w.heightBox,

          // Work Address
          _buildEmploymentDetailRow(
            label: LocaleKeys.workAddress.tr(),
            value: user.workAddress ?? LocaleKeys.notProvided.tr(),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow({
    required Widget icon,
    required String label,
    required String value,
    required bool showDivider,
    bool showEditButton = false,
  }) {
    return Column(
      children: [
        Row(
          children: [
            // Icon
            icon,

            16.w.widthBox,

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(label, style: AppTextStyles.text12.medium.gray600),
                  SizedBox(height: 4.h),
                  Text(value, style: AppTextStyles.text14.bold.dark900),
                ],
              ),
            ),
            if (showEditButton)
              // Arrow icon
              Icon(
                Icons.chevron_right,
                size: 24.w,
                color: AppColors.gray.shade400,
              ).paddingEnd(8.w),
          ],
        ),
        // Divider
        if (showDivider)
          Divider(
            color: AppColors.gray.shade200,
            height: 1.h,
            thickness: 1.h,
          ).paddingSTEB(40.w, 16.w, 0, 0)
        else
          SizedBox(height: 16.w),
      ],
    ).paddingSTEB(16.w, 16.w, 0, 0);
  }

  Widget _buildEmploymentDetailRow({
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: AppTextStyles.text12.medium.gray600),
        SizedBox(height: 4.h),
        Text(value, style: AppTextStyles.text14.bold.dark900),
      ],
    );
  }

  Widget _buildContactAdminText(BuildContext context) {
    return Row(
      children: [
        Text(
          '${LocaleKeys.toChangeYourFullNameOrEmail.tr()} ',
          style: AppTextStyles.text10.medium.dark300,
        ),
        Text(
          LocaleKeys.contactTheAdmin.tr(),
          style: AppTextStyles.text10.medium.primary.underline(
            underlineColor: AppColors.primary,
          ),
        ).onTap(() {
          GoRouter.of(context).pushNamed(RouteName.getHelp.name);
        }),
        Text('.', style: AppTextStyles.text10.medium.dark300),
      ],
    ).paddingAll(16.w);
  }
}
