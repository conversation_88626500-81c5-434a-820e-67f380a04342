import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/country.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/country_dropdown.dart';
import 'package:maisour/modules/my_profile/providers/my_profile_provider.dart';
import 'package:maisour/shared/providers/user_provider.dart';

class CountryOfBirthScreen extends ConsumerStatefulWidget {
  const CountryOfBirthScreen({super.key});

  @override
  ConsumerState<CountryOfBirthScreen> createState() =>
      _CountryOfBirthScreenState();
}

class _CountryOfBirthScreenState extends ConsumerState<CountryOfBirthScreen> {
  final _formKey = GlobalKey<FormState>();
  Country? _selectedCountryOfBirth;

  @override
  void initState() {
    super.initState();
    _prefillData();
  }

  void _prefillData() {
    final currentUser = ref.read(userProvider);
    if (currentUser?.countryOfBirth != null) {
      setState(() {
        _selectedCountryOfBirth = Country(
          id: 0,
          country: currentUser!.countryOfBirth!,
          countryInArabic: currentUser.countryOfBirth!,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.countryOfBirth.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Form section
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CountryDropdown(
                        labelText: '${LocaleKeys.countryOfBirth.tr()}*',
                        value: _selectedCountryOfBirth,
                        onChanged: (val) => _selectedCountryOfBirth = val,
                        validator: (value) => AppValidators.required(
                          LocaleKeys.countryOfBirth.tr(),
                          value?.country,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom buttons section
            Consumer(
              builder: (context, ref, _) {
                final async = ref.watch(myProfileUpdateProvider);

                ref.listen<AsyncValue<bool>>(myProfileUpdateProvider, (
                  prev,
                  next,
                ) {
                  if (next is AsyncData && next.value == true) {
                    context.showSuccessToast(
                      LocaleKeys.success.tr(),
                      LocaleKeys.countryOfBirthUpdatedSuccessfully.tr(),
                    );
                    GoRouter.of(context).pop();
                  } else if (next is AsyncError) {
                    context.showErrorToast(
                      LocaleKeys.oops.tr(),
                      next.error.toString(),
                    );
                  }
                });

                return Column(
                  children: [
                    AppButton(
                      text: LocaleKeys.save.tr(),
                      isLoading: async.isLoading,
                      onPressed: () => _handleSave(ref),
                    ),
                    8.h.heightBox,
                    AppButton(
                      text: LocaleKeys.cancel.tr(),
                      type: ButtonType.text,
                      onPressed: () => GoRouter.of(context).pop(),
                    ),
                  ],
                );
              },
            ),
          ],
        ).paddingAll(16.w),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

  void _handleSave(WidgetRef ref) {
    if (_formKey.currentState?.validate() ?? false) {
      final countryName = _selectedCountryOfBirth?.country;
      if (countryName != null) {
        ref
            .read(myProfileUpdateProvider.notifier)
            .updateCountryOfBirth(countryName);
      }
    }
  }
}
