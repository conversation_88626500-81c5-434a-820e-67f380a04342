import 'package:freezed_annotation/freezed_annotation.dart';

part 'deposit_response.freezed.dart';
part 'deposit_response.g.dart';

@freezed
abstract class DepositResponse with _$DepositResponse {
  const factory DepositResponse({
    required int id,
    required String referenceNumber,
  }) = _DepositResponse;

  factory DepositResponse.fromJson(Map<String, dynamic> json) =>
      _$DepositResponseFromJson(json);
}
