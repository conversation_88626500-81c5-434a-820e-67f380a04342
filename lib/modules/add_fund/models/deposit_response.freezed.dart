// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DepositResponse {

 int get id; String get referenceNumber;
/// Create a copy of DepositResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositResponseCopyWith<DepositResponse> get copyWith => _$DepositResponseCopyWithImpl<DepositResponse>(this as DepositResponse, _$identity);

  /// Serializes this DepositResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.referenceNumber, referenceNumber) || other.referenceNumber == referenceNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,referenceNumber);

@override
String toString() {
  return 'DepositResponse(id: $id, referenceNumber: $referenceNumber)';
}


}

/// @nodoc
abstract mixin class $DepositResponseCopyWith<$Res>  {
  factory $DepositResponseCopyWith(DepositResponse value, $Res Function(DepositResponse) _then) = _$DepositResponseCopyWithImpl;
@useResult
$Res call({
 int id, String referenceNumber
});




}
/// @nodoc
class _$DepositResponseCopyWithImpl<$Res>
    implements $DepositResponseCopyWith<$Res> {
  _$DepositResponseCopyWithImpl(this._self, this._then);

  final DepositResponse _self;
  final $Res Function(DepositResponse) _then;

/// Create a copy of DepositResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? referenceNumber = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,referenceNumber: null == referenceNumber ? _self.referenceNumber : referenceNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [DepositResponse].
extension DepositResponsePatterns on DepositResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DepositResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DepositResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DepositResponse value)  $default,){
final _that = this;
switch (_that) {
case _DepositResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DepositResponse value)?  $default,){
final _that = this;
switch (_that) {
case _DepositResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String referenceNumber)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DepositResponse() when $default != null:
return $default(_that.id,_that.referenceNumber);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String referenceNumber)  $default,) {final _that = this;
switch (_that) {
case _DepositResponse():
return $default(_that.id,_that.referenceNumber);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String referenceNumber)?  $default,) {final _that = this;
switch (_that) {
case _DepositResponse() when $default != null:
return $default(_that.id,_that.referenceNumber);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DepositResponse implements DepositResponse {
  const _DepositResponse({required this.id, required this.referenceNumber});
  factory _DepositResponse.fromJson(Map<String, dynamic> json) => _$DepositResponseFromJson(json);

@override final  int id;
@override final  String referenceNumber;

/// Create a copy of DepositResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositResponseCopyWith<_DepositResponse> get copyWith => __$DepositResponseCopyWithImpl<_DepositResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepositResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositResponse&&(identical(other.id, id) || other.id == id)&&(identical(other.referenceNumber, referenceNumber) || other.referenceNumber == referenceNumber));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,referenceNumber);

@override
String toString() {
  return 'DepositResponse(id: $id, referenceNumber: $referenceNumber)';
}


}

/// @nodoc
abstract mixin class _$DepositResponseCopyWith<$Res> implements $DepositResponseCopyWith<$Res> {
  factory _$DepositResponseCopyWith(_DepositResponse value, $Res Function(_DepositResponse) _then) = __$DepositResponseCopyWithImpl;
@override @useResult
$Res call({
 int id, String referenceNumber
});




}
/// @nodoc
class __$DepositResponseCopyWithImpl<$Res>
    implements _$DepositResponseCopyWith<$Res> {
  __$DepositResponseCopyWithImpl(this._self, this._then);

  final _DepositResponse _self;
  final $Res Function(_DepositResponse) _then;

/// Create a copy of DepositResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? referenceNumber = null,}) {
  return _then(_DepositResponse(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,referenceNumber: null == referenceNumber ? _self.referenceNumber : referenceNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
