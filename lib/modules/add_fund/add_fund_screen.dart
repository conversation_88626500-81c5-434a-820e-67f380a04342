import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/modules/add_fund/widgets/topup_method.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/modules/add_fund/widgets/add_fund.dart';
import 'package:maisour/modules/add_fund/providers/deposit_provider.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';
import 'package:maisour/config/app_router.dart';

class AddFundScreen extends ConsumerStatefulWidget {
  const AddFundScreen({super.key});

  @override
  ConsumerState<AddFundScreen> createState() => _AddFundScreenState();
}

class _AddFundScreenState extends ConsumerState<AddFundScreen> {
  TopUpMethod selectedMethod = TopUpMethod.bankTransfer;
  int amount = 500;

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider)!;
    final depositState = ref.watch(depositNotifierProvider);
    final depositNotifier = ref.read(depositNotifierProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.topUpYourWallet.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    16.w.heightBox,
                    AddFund(
                      user: user,
                      onAmountChanged: (int fund) {
                        amount = fund;
                      },
                    ),
                    16.w.heightBox,
                    AppSpacer(),
                    TopUpMethodCard(
                      initialMethod: selectedMethod,
                      onMethodSelected: (method) {
                        selectedMethod = method;
                      },
                    ),
                  ],
                ),
              ),
            ),
            AppButton(
              text: LocaleKeys.addFunds.tr(),
              onPressed: () => _handlePayment(
                depositNotifier,
                amount,
                selectedMethod,
                context,
                user,
              ),
              isLoading: depositState.isLoading,
            ).paddingHorizontal(16.w),
            AppButton(
              type: ButtonType.text,
              text: LocaleKeys.cancel.tr(),
              onPressed: depositState.isLoading
                  ? () {}
                  : () {
                      GoRouter.of(context).pop();
                    },
            ),
          ],
        ),
      ),
    );
  }

  void _handlePayment(
    DepositNotifier depositNotifier,
    int amount,
    TopUpMethod selectedMethod,
    BuildContext context,
    AppUser user,
  ) {
    if (selectedMethod == TopUpMethod.bankTransfer) {
      _handleBankTransferDeposit(depositNotifier, amount, context);
    } else if (selectedMethod == TopUpMethod.debitCard) {
      _handleCardPayment(depositNotifier, amount, context, user);
    }
  }

  void _handleBankTransferDeposit(
    DepositNotifier depositNotifier,
    int amount,
    BuildContext context,
  ) async {
    try {
      final result = await depositNotifier.createBankTransferDeposit(amount);

      if (result.isSuccess && result.data != null) {
        // Handle successful deposit - navigate to pending screen
        if (context.mounted) {
          GoRouter.of(context).pushReplacementNamed(
            RouteName.bankTransferPending.name,
            extra: {
              'referenceNumber': result.data!.referenceNumber,
              'amount': amount,
            },
          );
        }
      } else if (result.isError && result.error != null) {
        // Handle error - show error toast
        if (context.mounted) {
          context.showErrorToast(LocaleKeys.oops.tr(), result.error!.message);
        }
      }
    } catch (error) {
      // Handle unexpected error
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }

  void _handleCardPayment(
    DepositNotifier depositNotifier,
    int amount,
    BuildContext context,
    AppUser user,
  ) async {
    try {
      // Get current locale and currency
      final currentLocale = context.locale.languageCode;
      // Currently payment gatway support only AED currency
      final currency = Currency.aed.value;
      //user.currencyCode.name.toUpperCase();

      final result = await depositNotifier.createCardPaymentOrder(
        amount.toDouble().toString(),
        currency,
        currentLocale,
      );

      if (result.isSuccess && result.data != null) {
        // Print the payment link
        debugPrint('Payment Link: ${result.data}');

        // Navigate to payment webview
        if (context.mounted) {
          GoRouter.of(context).pushReplacementNamed(
            RouteName.paymentWebView.name,
            extra: result.data,
          );
        }
      } else if (result.isError && result.error != null) {
        // Handle error - show error toast
        if (context.mounted) {
          context.showErrorToast(LocaleKeys.oops.tr(), result.error!.message);
        }
      }
    } catch (error) {
      // Handle unexpected error
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }
}
