import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/add_fund/api/deposit_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for deposit module
final depositApiProvider = Provider.autoDispose<DepositApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return DepositApi(dio);
}, name: 'depositApiProvider');
