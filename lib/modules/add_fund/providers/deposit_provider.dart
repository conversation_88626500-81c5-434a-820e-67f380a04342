import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/add_fund/api/deposit_api.dart';
import 'package:maisour/modules/add_fund/models/deposit_response.dart';
import 'package:maisour/modules/add_fund/deposit_service.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/models/result.dart';

/// Provider for deposit API
final depositApiProvider = Provider.autoDispose<DepositApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return DepositApi(dio);
}, name: 'depositApiProvider');

/// Provider for deposit service
final depositServiceProvider = Provider.autoDispose<DepositService>((ref) {
  final depositApi = ref.watch(depositApiProvider);
  return DepositService(depositApi);
}, name: 'depositServiceProvider');

/// State notifier for managing deposit operations
class DepositNotifier extends AutoDisposeAsyncNotifier<DepositResponse?> {
  @override
  Future<DepositResponse?> build() async {
    // Initial state - no deposit
    return null;
  }

  /// Create a bank transfer deposit
  Future<Result<DepositResponse>> createBankTransferDeposit(int amount) async {
    try {
      // Set loading state
      state = const AsyncValue.loading();

      final depositApi = ref.read(depositApiProvider);
      final response = await depositApi.deposit(amount);

      // Update state with successful response
      state = AsyncValue.data(response);

      return Result.success(response);
    } on DioException catch (error, stackTrace) {
      final failure = _DioExceptionMapperHelper().mapDioExceptionToFailure(
        error,
        stackTrace,
      );

      // Update state with error
      state = AsyncValue.error(failure, stackTrace);

      return Result.failure(failure);
    } catch (error, stackTrace) {
      final failure = Failure(
        message: LocaleKeys.somethingWentWrong.tr(),
        exception: error as Exception?,
        stackTrace: stackTrace,
      );

      // Update state with error
      state = AsyncValue.error(failure, stackTrace);

      return Result.failure(failure);
    }
  }

  /// Create a card payment order
  Future<Result<String>> createCardPaymentOrder(
    String amount,
    String currency,
    String language,
  ) async {
    try {
      // Set loading state
      state = const AsyncValue.loading();

      final depositService = ref.read(depositServiceProvider);
      final response = await depositService.createCardPaymentOrder(
        amount,
        currency,
        language,
      );

      // Update state with successful response (keep previous data for bank transfer)
      state = const AsyncValue.data(null);

      return Result.success(response);
    } on DioException catch (error, stackTrace) {
      final failure = _DioExceptionMapperHelper().mapDioExceptionToFailure(
        error,
        stackTrace,
      );

      // Update state with error
      state = AsyncValue.error(failure, stackTrace);

      return Result.failure(failure);
    } catch (error, stackTrace) {
      final failure = Failure(
        message: LocaleKeys.somethingWentWrong.tr(),
        exception: error as Exception?,
        stackTrace: stackTrace,
      );

      // Update state with error
      state = AsyncValue.error(failure, stackTrace);

      return Result.failure(failure);
    }
  }
}

/// Provider for deposit notifier
final depositNotifierProvider =
    AsyncNotifierProvider.autoDispose<DepositNotifier, DepositResponse?>(
      () => DepositNotifier(),
      name: 'depositNotifierProvider',
    );

/// Async provider for creating bank transfer deposits (legacy - use depositNotifierProvider instead)
final createBankTransferDepositProvider = FutureProvider.autoDispose
    .family<DepositResponse, int>((ref, amount) async {
      final depositApi = ref.watch(depositApiProvider);

      try {
        return await depositApi.deposit(amount);
      } on DioException catch (error, stackTrace) {
        final failure = _DioExceptionMapperHelper().mapDioExceptionToFailure(
          error,
          stackTrace,
        );
        throw failure;
      } catch (error) {
        throw Exception(error.toString());
      }
    }, name: 'createBankTransferDepositProvider');

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}
