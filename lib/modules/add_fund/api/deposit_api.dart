import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/modules/add_fund/models/deposit_response.dart';
import 'package:maisour/modules/add_fund/models/create_order_request.dart';

part 'deposit_api.g.dart';

@RestApi()
abstract class DepositApi {
  factory DepositApi(Dio dio, {String baseUrl}) = _DepositApi;

  @POST(ApiEndpoints.deposit)
  Future<DepositResponse> deposit(@Query('amount') int amount);

  @POST(ApiEndpoints.createOrder)
  Future<String> createOrder(@Body() CreateOrderRequest request);
}
