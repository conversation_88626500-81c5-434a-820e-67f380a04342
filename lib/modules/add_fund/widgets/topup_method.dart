import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

enum TopUpMethod { bankTransfer, debitCard }

class TopUpMethodCard extends StatefulWidget {
  final Function(TopUpMethod) onMethodSelected;
  final TopUpMethod? initialMethod;

  const TopUpMethodCard({
    super.key,
    required this.onMethodSelected,
    required this.initialMethod,
  });

  @override
  State<TopUpMethodCard> createState() => _TopUpMethodCardState();
}

class _TopUpMethodCardState extends State<TopUpMethodCard> {
  TopUpMethod? selectedMethod;

  @override
  void initState() {
    super.initState();
    selectedMethod = widget.initialMethod;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.selectTopUpMethod.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.w.heightBox,
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            children: [
              _buildMethodOption(
                TopUpMethod.bankTransfer,
                LocaleKeys.bankTransfer.tr(),
                Text(
                  LocaleKeys.take23DaysToComplete.tr(),
                  style: AppTextStyles.text12.medium.dark300,
                ),
                Assets.icons.bankAccount.svg(width: 32.w, height: 32.w),
                isSelected: selectedMethod == TopUpMethod.bankTransfer,
              ),
              Divider(color: AppColors.gray.shade100).paddingStart(60.w),
              _buildMethodOption(
                TopUpMethod.debitCard,
                LocaleKeys.debitCard.tr(),
                Row(
                  children: [
                    Assets.icons.visa.svg(width: 12.w, height: 12.w),
                    8.w.widthBox,
                    Assets.icons.mastercard.svg(width: 12.w, height: 12.w),
                    8.w.widthBox,
                    Text(
                      "& ${LocaleKeys.more.tr()}",
                      style: AppTextStyles.text12.medium.dark300,
                    ),
                  ],
                ),
                Assets.icons.card.svg(width: 32.w, height: 32.w),
                isSelected: selectedMethod == TopUpMethod.debitCard,
              ),
            ],
          ),
        ),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildMethodOption(
    TopUpMethod method,
    String title,
    Widget subtitle,
    Widget icon, {
    bool isSelected = false,
  }) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          // Icon
          icon,
          12.w.widthBox,
          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTextStyles.text14.bold.dark900),
                subtitle,
              ],
            ),
          ),
          // Selection indicator
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected ? AppColors.primary : AppColors.white,
              border: Border.all(
                color: isSelected ? AppColors.primary : AppColors.gray.shade300,
                width: 2,
              ),
            ),
            child: isSelected
                ? Icon(Icons.check, size: 12.w, color: AppColors.white)
                : null,
          ),
        ],
      ),
    ).onTap(() {
      setState(() {
        selectedMethod = method;
      });
      widget.onMethodSelected(method);
    });
  }
}
