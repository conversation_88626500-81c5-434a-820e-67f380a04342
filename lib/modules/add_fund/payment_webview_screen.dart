import 'dart:developer';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/providers/user_provider.dart';

class PaymentWebViewScreen extends ConsumerStatefulWidget {
  final String paymentLink;

  const PaymentWebViewScreen({super.key, required this.paymentLink});

  @override
  ConsumerState<PaymentWebViewScreen> createState() =>
      _PaymentWebViewScreenState();
}

class _PaymentWebViewScreenState extends ConsumerState<PaymentWebViewScreen> {
  late WebViewController _webViewController;
  bool _isLoading = true;
  String? _currentUrl;

  @override
  void initState() {
    super.initState();
    _currentUrl = widget.paymentLink;
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            log('WebView is loading (progress : $progress%)');
            if (progress == 100) {
              // Page fully loaded
            }
          },
          onNavigationRequest: (NavigationRequest request) async {
            if (request.url.contains("https://www.maisour.com")) {
              log('Payment successful: 100');
              _goBack();
              _showSuccessToast();
            } else if (request.url == _currentUrl) {
              log('Navigation: 1');
              return NavigationDecision.navigate;
            } else {
              log('Navigation: 2');
              if (Theme.of(context).platform == TargetPlatform.iOS) {
                log('Navigation: 3');
                return NavigationDecision.navigate;
              } else {
                log('Navigation: 4');
                return NavigationDecision.prevent;
              }
            }
            log('Navigation: 5');
            return NavigationDecision.prevent;
          },
          onPageStarted: (String url) {
            log('Page started loading: $url');
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            log('Page finished loading: $url');
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentLink));
  }

  void _goBack() {
    if (mounted) {
      GoRouter.of(context).pop();
    }
  }

  void _showSuccessToast() {
    if (mounted) {
      context.showSuccessToast(
        LocaleKeys.success.tr(),
        LocaleKeys.fundsAddedSuccessfully.tr(),
      );

      // Refresh user data in background without blocking UI
      _refreshUserInBackground();
    }
  }

  void _refreshUserInBackground() {
    // Get the user notifier and refresh user data in background
    final userNotifier = ref.read(userProvider.notifier);
    userNotifier.refreshUserFromServer().catchError((error) {
      // Log error but don't show to user since this is background refresh
      log('Background user refresh failed: $error');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.payment.tr()),
        centerTitle: false,
        leading: BackButton(
          onPressed: () {
            // Refresh user data when user manually closes the payment screen
            _refreshUserInBackground();
            _goBack();
          },
        ),
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _webViewController),
          if (_isLoading)
            Container(
              color: Colors.white,
              child: const Center(child: AppCircularLoader()),
            ),
        ],
      ),
    );
  }
}
