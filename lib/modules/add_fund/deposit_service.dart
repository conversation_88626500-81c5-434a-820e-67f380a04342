import 'package:maisour/modules/add_fund/api/deposit_api.dart';
import 'package:maisour/modules/add_fund/models/deposit_response.dart';
import 'package:maisour/modules/add_fund/models/create_order_request.dart';

/// Service for handling deposit operations
class DepositService {
  final DepositApi _depositApi;

  DepositService(this._depositApi);

  /// Create a bank transfer deposit
  ///
  /// [amount] - The amount to deposit in AED
  /// Returns a [DepositResponse] with id and referenceNumber
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<DepositResponse> createBankTransferDeposit(int amount) async {
    return await _depositApi.deposit(amount);
  }

  /// Create a card payment order
  ///
  /// [amount] - The amount to deposit in AED
  /// [currency] - The currency (default: AED)
  /// [language] - The user's current locale
  /// Returns a [String] with paymentLink
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<String> createCardPaymentOrder(
    String amount,
    String currency,
    String language,
  ) async {
    final request = CreateOrderRequest(
      amount: amount,
      currency: currency,
      action: 'PURCHASE',
      redirectUrl: 'https://www.maisour.com',
      language: language,
    );

    return await _depositApi.createOrder(request);
  }
}
