import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/modules/bank_transfer/models/const_account_data.dart';

part 'const_account_data_api.g.dart';

@RestApi()
abstract class ConstAccountDataApi {
  factory ConstAccountDataApi(Dio dio, {String? baseUrl}) =
      _ConstAccountDataApi;

  @GET(ApiEndpoints.getConstAccountData)
  Future<ConstAccountData> getConstAccountData();
}
