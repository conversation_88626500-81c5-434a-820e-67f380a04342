// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'const_account_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ConstAccountData {

 AccountField get accountName; Account<PERSON>ield get accountNumber; Account<PERSON>ield get iban; AccountField get bankName;
/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ConstAccountDataCopyWith<ConstAccountData> get copyWith => _$ConstAccountDataCopyWithImpl<ConstAccountData>(this as ConstAccountData, _$identity);

  /// Serializes this ConstAccountData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ConstAccountData&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.bankName, bankName) || other.bankName == bankName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountName,accountNumber,iban,bankName);

@override
String toString() {
  return 'ConstAccountData(accountName: $accountName, accountNumber: $accountNumber, iban: $iban, bankName: $bankName)';
}


}

/// @nodoc
abstract mixin class $ConstAccountDataCopyWith<$Res>  {
  factory $ConstAccountDataCopyWith(ConstAccountData value, $Res Function(ConstAccountData) _then) = _$ConstAccountDataCopyWithImpl;
@useResult
$Res call({
 AccountField accountName, AccountField accountNumber, AccountField iban, AccountField bankName
});


$AccountFieldCopyWith<$Res> get accountName;$AccountFieldCopyWith<$Res> get accountNumber;$AccountFieldCopyWith<$Res> get iban;$AccountFieldCopyWith<$Res> get bankName;

}
/// @nodoc
class _$ConstAccountDataCopyWithImpl<$Res>
    implements $ConstAccountDataCopyWith<$Res> {
  _$ConstAccountDataCopyWithImpl(this._self, this._then);

  final ConstAccountData _self;
  final $Res Function(ConstAccountData) _then;

/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountName = null,Object? accountNumber = null,Object? iban = null,Object? bankName = null,}) {
  return _then(_self.copyWith(
accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as AccountField,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as AccountField,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as AccountField,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as AccountField,
  ));
}
/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get accountName {
  
  return $AccountFieldCopyWith<$Res>(_self.accountName, (value) {
    return _then(_self.copyWith(accountName: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get accountNumber {
  
  return $AccountFieldCopyWith<$Res>(_self.accountNumber, (value) {
    return _then(_self.copyWith(accountNumber: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get iban {
  
  return $AccountFieldCopyWith<$Res>(_self.iban, (value) {
    return _then(_self.copyWith(iban: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get bankName {
  
  return $AccountFieldCopyWith<$Res>(_self.bankName, (value) {
    return _then(_self.copyWith(bankName: value));
  });
}
}


/// Adds pattern-matching-related methods to [ConstAccountData].
extension ConstAccountDataPatterns on ConstAccountData {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ConstAccountData value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ConstAccountData() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ConstAccountData value)  $default,){
final _that = this;
switch (_that) {
case _ConstAccountData():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ConstAccountData value)?  $default,){
final _that = this;
switch (_that) {
case _ConstAccountData() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( AccountField accountName,  AccountField accountNumber,  AccountField iban,  AccountField bankName)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ConstAccountData() when $default != null:
return $default(_that.accountName,_that.accountNumber,_that.iban,_that.bankName);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( AccountField accountName,  AccountField accountNumber,  AccountField iban,  AccountField bankName)  $default,) {final _that = this;
switch (_that) {
case _ConstAccountData():
return $default(_that.accountName,_that.accountNumber,_that.iban,_that.bankName);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( AccountField accountName,  AccountField accountNumber,  AccountField iban,  AccountField bankName)?  $default,) {final _that = this;
switch (_that) {
case _ConstAccountData() when $default != null:
return $default(_that.accountName,_that.accountNumber,_that.iban,_that.bankName);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ConstAccountData implements ConstAccountData {
  const _ConstAccountData({required this.accountName, required this.accountNumber, required this.iban, required this.bankName});
  factory _ConstAccountData.fromJson(Map<String, dynamic> json) => _$ConstAccountDataFromJson(json);

@override final  AccountField accountName;
@override final  AccountField accountNumber;
@override final  AccountField iban;
@override final  AccountField bankName;

/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ConstAccountDataCopyWith<_ConstAccountData> get copyWith => __$ConstAccountDataCopyWithImpl<_ConstAccountData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ConstAccountDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ConstAccountData&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.bankName, bankName) || other.bankName == bankName));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,accountName,accountNumber,iban,bankName);

@override
String toString() {
  return 'ConstAccountData(accountName: $accountName, accountNumber: $accountNumber, iban: $iban, bankName: $bankName)';
}


}

/// @nodoc
abstract mixin class _$ConstAccountDataCopyWith<$Res> implements $ConstAccountDataCopyWith<$Res> {
  factory _$ConstAccountDataCopyWith(_ConstAccountData value, $Res Function(_ConstAccountData) _then) = __$ConstAccountDataCopyWithImpl;
@override @useResult
$Res call({
 AccountField accountName, AccountField accountNumber, AccountField iban, AccountField bankName
});


@override $AccountFieldCopyWith<$Res> get accountName;@override $AccountFieldCopyWith<$Res> get accountNumber;@override $AccountFieldCopyWith<$Res> get iban;@override $AccountFieldCopyWith<$Res> get bankName;

}
/// @nodoc
class __$ConstAccountDataCopyWithImpl<$Res>
    implements _$ConstAccountDataCopyWith<$Res> {
  __$ConstAccountDataCopyWithImpl(this._self, this._then);

  final _ConstAccountData _self;
  final $Res Function(_ConstAccountData) _then;

/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountName = null,Object? accountNumber = null,Object? iban = null,Object? bankName = null,}) {
  return _then(_ConstAccountData(
accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as AccountField,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as AccountField,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as AccountField,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as AccountField,
  ));
}

/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get accountName {
  
  return $AccountFieldCopyWith<$Res>(_self.accountName, (value) {
    return _then(_self.copyWith(accountName: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get accountNumber {
  
  return $AccountFieldCopyWith<$Res>(_self.accountNumber, (value) {
    return _then(_self.copyWith(accountNumber: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get iban {
  
  return $AccountFieldCopyWith<$Res>(_self.iban, (value) {
    return _then(_self.copyWith(iban: value));
  });
}/// Create a copy of ConstAccountData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<$Res> get bankName {
  
  return $AccountFieldCopyWith<$Res>(_self.bankName, (value) {
    return _then(_self.copyWith(bankName: value));
  });
}
}


/// @nodoc
mixin _$AccountField {

 int get id; String get nameInEnglish; String get nameInArabic; String get value;
/// Create a copy of AccountField
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountFieldCopyWith<AccountField> get copyWith => _$AccountFieldCopyWithImpl<AccountField>(this as AccountField, _$identity);

  /// Serializes this AccountField to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountField&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value);

@override
String toString() {
  return 'AccountField(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value)';
}


}

/// @nodoc
abstract mixin class $AccountFieldCopyWith<$Res>  {
  factory $AccountFieldCopyWith(AccountField value, $Res Function(AccountField) _then) = _$AccountFieldCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, String value
});




}
/// @nodoc
class _$AccountFieldCopyWithImpl<$Res>
    implements $AccountFieldCopyWith<$Res> {
  _$AccountFieldCopyWithImpl(this._self, this._then);

  final AccountField _self;
  final $Res Function(AccountField) _then;

/// Create a copy of AccountField
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [AccountField].
extension AccountFieldPatterns on AccountField {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AccountField value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AccountField() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AccountField value)  $default,){
final _that = this;
switch (_that) {
case _AccountField():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AccountField value)?  $default,){
final _that = this;
switch (_that) {
case _AccountField() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  String value)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AccountField() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  String value)  $default,) {final _that = this;
switch (_that) {
case _AccountField():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  String value)?  $default,) {final _that = this;
switch (_that) {
case _AccountField() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AccountField implements AccountField {
  const _AccountField({required this.id, required this.nameInEnglish, required this.nameInArabic, required this.value});
  factory _AccountField.fromJson(Map<String, dynamic> json) => _$AccountFieldFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override final  String value;

/// Create a copy of AccountField
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountFieldCopyWith<_AccountField> get copyWith => __$AccountFieldCopyWithImpl<_AccountField>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountFieldToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountField&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value);

@override
String toString() {
  return 'AccountField(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value)';
}


}

/// @nodoc
abstract mixin class _$AccountFieldCopyWith<$Res> implements $AccountFieldCopyWith<$Res> {
  factory _$AccountFieldCopyWith(_AccountField value, $Res Function(_AccountField) _then) = __$AccountFieldCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, String value
});




}
/// @nodoc
class __$AccountFieldCopyWithImpl<$Res>
    implements _$AccountFieldCopyWith<$Res> {
  __$AccountFieldCopyWithImpl(this._self, this._then);

  final _AccountField _self;
  final $Res Function(_AccountField) _then;

/// Create a copy of AccountField
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,}) {
  return _then(_AccountField(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
