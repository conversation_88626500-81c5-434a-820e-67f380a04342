// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'const_account_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ConstAccountData _$ConstAccountDataFromJson(Map<String, dynamic> json) =>
    _ConstAccountData(
      accountName: AccountField.fromJson(
        json['accountName'] as Map<String, dynamic>,
      ),
      accountNumber: AccountField.fromJson(
        json['accountNumber'] as Map<String, dynamic>,
      ),
      iban: AccountField.fromJson(json['iban'] as Map<String, dynamic>),
      bankName: AccountField.fromJson(json['bankName'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ConstAccountDataToJson(_ConstAccountData instance) =>
    <String, dynamic>{
      'accountName': instance.accountName,
      'accountNumber': instance.accountNumber,
      'iban': instance.iban,
      'bankName': instance.bankName,
    };

_AccountField _$AccountFieldFromJson(Map<String, dynamic> json) =>
    _AccountField(
      id: (json['id'] as num).toInt(),
      nameInEnglish: json['nameInEnglish'] as String,
      nameInArabic: json['nameInArabic'] as String,
      value: json['value'] as String,
    );

Map<String, dynamic> _$AccountFieldToJson(_AccountField instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nameInEnglish': instance.nameInEnglish,
      'nameInArabic': instance.nameInArabic,
      'value': instance.value,
    };
