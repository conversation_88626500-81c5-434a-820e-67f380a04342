import 'package:freezed_annotation/freezed_annotation.dart';

part 'const_account_data.freezed.dart';
part 'const_account_data.g.dart';

@freezed
abstract class ConstAccountData with _$ConstAccountData {
  const factory ConstAccountData({
    required AccountField accountName,
    required AccountField accountNumber,
    required AccountField iban,
    required AccountField bankName,
  }) = _ConstAccountData;

  factory ConstAccountData.fromJson(Map<String, dynamic> json) =>
      _$ConstAccountDataFromJson(json);
}

@freezed
abstract class AccountField with _$AccountField {
  const factory AccountField({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required String value,
  }) = _AccountField;

  factory AccountField.fromJson(Map<String, dynamic> json) =>
      _$AccountFieldFromJson(json);
}
