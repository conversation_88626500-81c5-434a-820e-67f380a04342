import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/bank_transfer/api/const_account_data_api.dart';
import 'package:maisour/modules/bank_transfer/models/const_account_data.dart';
import 'package:maisour/modules/bank_transfer/const_account_data_service.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/models/failure.dart';

/// Provider for constant account data API
final constAccountDataApiProvider = Provider.autoDispose<ConstAccountDataApi>((
  ref,
) {
  final dio = ref.watch(networkServiceProvider);
  return ConstAccountDataApi(dio);
}, name: 'constAccountDataApiProvider');

/// Provider for constant account data service
final constAccountDataServiceProvider =
    Provider.autoDispose<ConstAccountDataService>((ref) {
      final constAccountDataApi = ref.watch(constAccountDataApiProvider);
      return ConstAccountDataService(constAccountDataApi);
    }, name: 'constAccountDataServiceProvider');

/// FutureProvider for constant account data
/// Automatically handles loading, success, and error states
final constAccountDataFutureProvider =
    FutureProvider.autoDispose<ConstAccountData>((ref) async {
      try {
        final constAccountDataService = ref.watch(
          constAccountDataServiceProvider,
        );
        return await constAccountDataService.getConstAccountData();
      } on DioException catch (error, stackTrace) {
        final failure = _DioExceptionMapperHelper().mapDioExceptionToFailure(
          error,
          stackTrace,
        );
        throw failure;
      } catch (error) {
        throw Failure(
          message: LocaleKeys.somethingWentWrong.tr(),
          exception: error as Exception?,
        );
      }
    });

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}
