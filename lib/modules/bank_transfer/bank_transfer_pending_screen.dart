import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/dotted_line_painter.dart';
import 'package:maisour/modules/bank_transfer/providers/const_account_data_provider.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BankTransferPendingScreen extends ConsumerWidget {
  final String referenceId;
  final int amount;

  const BankTransferPendingScreen({
    super.key,
    required this.referenceId,
    required this.amount,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Warning icon and title section
                    _buildHeaderSection(),

                    // Bank details card
                    _buildBankDetailsCard(ref),

                    8.w.heightBox,

                    Text(
                      LocaleKeys.copyReferenceNumber.tr(),
                      style: AppTextStyles.text12.gray600,
                    ),

                    8.w.heightBox,
                    // How to top up section
                    _buildHowToTopUpSection(),

                    16.w.heightBox,
                  ],
                ),
              ),
            ),

            // Bottom button
            _buildBottomButton(context),
            16.w.heightBox,
          ],
        ).paddingSymmetric(horizontal: 16.w),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Column(
      children: [
        16.w.heightBox,

        // Warning icon
        Assets.icons.warning.svg(width: 80.w, height: 80.w),

        16.w.heightBox,

        // Title
        Text(
          LocaleKeys.bankTransferPending.tr(),
          style: AppTextStyles.text18.bold.dark900,
          textAlign: TextAlign.center,
        ),

        6.w.heightBox,

        // Description
        Text(
          LocaleKeys.yourTransferWasInitiated.tr(),
          style: AppTextStyles.text14.dark300,
          textAlign: TextAlign.center,
        ),

        16.w.heightBox,
      ],
    );
  }

  Widget _buildBankDetailsCard(WidgetRef ref) {
    final constAccountDataAsync = ref.watch(constAccountDataFutureProvider);

    return constAccountDataAsync.when(
      data: (constAccountData) {
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: AppColors.gray.alphaPercent(5),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            children: [
              _buildBankDetailRow(
                label: LocaleKeys.depositAmount.tr(),
                value: Currency.aed.formatCurrency(amount),
                showCurrencyIcon: true,
                onCopy: () =>
                    _copyToClipboard(Currency.aed.formatCurrency(amount)),
              ),
              10.w.heightBox,
              _buildBankDetailRow(
                label: LocaleKeys.accountNumber.tr(),
                value: constAccountData.accountNumber.value,
                onCopy: () =>
                    _copyToClipboard(constAccountData.accountNumber.value),
              ),
              10.w.heightBox,
              _buildBankDetailRow(
                label: LocaleKeys.accountName.tr(),
                value: constAccountData.accountName.value,
                onCopy: () =>
                    _copyToClipboard(constAccountData.accountName.value),
              ),
              10.w.heightBox,
              _buildBankDetailRow(
                label: LocaleKeys.bankName.tr(),
                value: constAccountData.bankName.value,
                onCopy: () => _copyToClipboard(constAccountData.bankName.value),
              ),
              10.w.heightBox,
              _buildBankDetailRow(
                label: LocaleKeys.iban.tr(),
                value: constAccountData.iban.value,
                onCopy: () => _copyToClipboard(constAccountData.iban.value),
              ),
              10.w.heightBox,
              _buildBankDetailRow(
                label: LocaleKeys.referenceNumber.tr(),
                value: referenceId,
                onCopy: () => _copyToClipboard(referenceId),
              ),
            ],
          ),
        );
      },
      loading: () => Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: AppColors.gray.alphaPercent(5),
          border: Border.all(color: AppColors.gray.shade100),
        ),
        child: AppShimmerLoader.fixed(
          itemCount: 6,
          itemHeight: 24.h,
          itemSpacing: 10,
          borderRadius: 4.r,
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Text(
          error.toString(),
          style: AppTextStyles.text14.medium.gray700,
        ),
      ),
    );
  }

  Widget _buildBankDetailRow({
    required String label,
    required String value,
    bool showCurrencyIcon = false,
    required VoidCallback onCopy,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text(label, style: AppTextStyles.text12.medium.gray700),
            8.w.widthBox,
            GestureDetector(
              onTap: onCopy,
              child: Assets.icons.copy.svg(
                width: 16.w,
                height: 16.w,
                colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
              ),
            ),
          ],
        ),
        12.w.widthBox,
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (showCurrencyIcon) ...[
                Assets.images.dirham.image(width: 20.w, height: 20.w),
                4.w.widthBox,
              ],
              Flexible(
                child: Text(
                  value,
                  style: AppTextStyles.text14.semiBold.gray900,
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHowToTopUpSection() {
    return Stack(
      children: [
        // Background image
        PositionedDirectional(
          top: 0,
          end: 0,
          child: Assets.icons.halfCircleGreen.svg(width: 80.w, height: 80.w),
        ),

        // Main content container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.howToTopUpYourWallet.tr(),
                style: AppTextStyles.text12.bold.gray,
              ),
              16.w.heightBox,
              _buildTimelineSteps(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineSteps() {
    final steps = [
      _TimelineStep(
        icon: Assets.icons.bankAccount.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.success, BlendMode.srcIn),
        ),
        title: LocaleKeys.copyAccountDetails.tr(),
        description: LocaleKeys.useTheDisplayedBankInfoForYourTransfer.tr(),
      ),
      _TimelineStep(
        icon: Assets.icons.moneyIn.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.success, BlendMode.srcIn),
        ),
        title: LocaleKeys.makeThePayment.tr(),
        description: LocaleKeys.completeTheTransferThroughYourBankingApp.tr(),
      ),
      _TimelineStep(
        icon: Assets.icons.success.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.success, BlendMode.srcIn),
        ),
        title: LocaleKeys.waitForTheConfirmation.tr(),
        description: LocaleKeys.weWillVerifyYourPaymentAndUpdateYourWallet.tr(),
      ),
    ];

    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isLast = index == steps.length - 1;
        return _buildTimelineStep(step, isLast);
      }).toList(),
    );
  }

  Widget _buildTimelineStep(_TimelineStep step, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline dot + dotted line
        Column(
          children: [
            Stack(
              alignment: AlignmentDirectional.center,
              children: [
                Container(
                  height: 40.w,
                  width: 40.w,
                  margin: EdgeInsets.only(top: 3.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: AppColors.success.alphaPercent(10),
                    ),
                    color: AppColors.success.alphaPercent(5),
                  ),
                ),
                step.icon,
              ],
            ),

            if (!isLast)
              CustomPaint(
                painter: DottedLinePainter(),
                child: SizedBox(height: 30.w, width: 2.w),
              ),
          ],
        ),
        12.w.widthBox,
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(step.title, style: AppTextStyles.text14.bold.dark900),
              2.w.heightBox,
              Text(
                step.description,
                style: AppTextStyles.text10.medium.dark300,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomButton(BuildContext context) {
    return AppButton(
      onPressed: () {
        GoRouter.of(context).pop();
      },
      text: LocaleKeys.done.tr(),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    // You can add a toast notification here if needed
  }
}

class _TimelineStep {
  final Widget icon;
  final String title;
  final String description;

  const _TimelineStep({
    required this.icon,
    required this.title,
    required this.description,
  });
}
