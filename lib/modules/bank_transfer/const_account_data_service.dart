import 'package:maisour/modules/bank_transfer/api/const_account_data_api.dart';
import 'package:maisour/modules/bank_transfer/models/const_account_data.dart';

/// Service for handling constant account data operations
class ConstAccountDataService {
  final ConstAccountDataApi _constAccountDataApi;

  ConstAccountDataService(this._constAccountDataApi);

  /// Get constant account data
  ///
  /// Returns the constant account data (account name, number, IBAN, bank name)
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<ConstAccountData> getConstAccountData() async {
    return await _constAccountDataApi.getConstAccountData();
  }
}
