import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import '../models/ownership.dart';

part 'ownership_api.g.dart';

@RestApi()
abstract class OwnershipApi {
  factory OwnershipApi(Dio dio, {String? baseUrl}) = _OwnershipApi;

  @GET('${ApiEndpoints.getOwnershipData}/{order}')
  Future<OwnershipResponse> getOwnershipData(@Path('order') String order);
}
