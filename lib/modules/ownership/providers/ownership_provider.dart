import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/ownership/models/ownership.dart';
import 'package:maisour/modules/ownership/ownership_service.dart';
import 'package:maisour/modules/ownership/providers/ownership_api_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:dio/dio.dart';

/// Ownership service provider
final ownershipServiceProvider = Provider.autoDispose<OwnershipService>((ref) {
  final ownershipApi = ref.watch(ownershipApiProvider);
  return OwnershipService(ownershipApi);
}, name: 'ownershipServiceProvider');

/// Ownership state notifier provider
final ownershipNotifierProvider =
    StateNotifierProvider.autoDispose<OwnershipNotifier, OwnershipState>((ref) {
      final ownershipService = ref.watch(ownershipServiceProvider);
      return OwnershipNotifier(ownershipService);
    }, name: 'ownershipNotifierProvider');

/// Ownership state notifier
class OwnershipNotifier extends StateNotifier<OwnershipState>
    with DioExceptionMapper {
  final OwnershipService _ownershipService;

  OwnershipNotifier(this._ownershipService) : super(const OwnershipState());

  /// Get ownership data
  Future<void> getOwnershipData() async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      final response = await _ownershipService.getOwnershipData(order: 'desc');
      state = state.copyWith(
        status: ApiStatus.success,
        ownerships: response.data,
        totalCount: response.data.length,
      );
    } on DioException catch (error, stackTrace) {
      final failure = mapDioExceptionToFailure(error, stackTrace);
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: failure.message,
        errorKey: failure.errorKey,
      );
    } catch (error) {
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: error.toString(),
      );
    }
  }
}

/// Ownership state
class OwnershipState {
  final ApiStatus status;
  final List<Ownership> ownerships;
  final int totalCount;
  final String? errorMessage;
  final String? errorKey;

  const OwnershipState({
    this.status = ApiStatus.initial,
    this.ownerships = const [],
    this.totalCount = 0,
    this.errorMessage,
    this.errorKey,
  });

  OwnershipState copyWith({
    ApiStatus? status,
    List<Ownership>? ownerships,
    int? totalCount,
    String? errorMessage,
    String? errorKey,
  }) {
    return OwnershipState(
      status: status ?? this.status,
      ownerships: ownerships ?? this.ownerships,
      totalCount: totalCount ?? this.totalCount,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
    );
  }
}
