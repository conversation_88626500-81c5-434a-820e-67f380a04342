import 'package:maisour/modules/ownership/api/ownership_api.dart';
import 'package:maisour/modules/ownership/models/ownership.dart';

class OwnershipService {
  final OwnershipApi _ownershipApi;

  OwnershipService(this._ownershipApi);

  // Get ownership data - throws DioException on error
  Future<OwnershipResponse> getOwnershipData({required String order}) async {
    return await _ownershipApi.getOwnershipData(order);
  }
}
