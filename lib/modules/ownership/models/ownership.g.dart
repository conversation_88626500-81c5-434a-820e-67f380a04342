// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ownership.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Ownership _$OwnershipFromJson(Map<String, dynamic> json) => _Ownership(
  propertyId: (json['propertyId'] as num).toInt(),
  titleInEnglish: json['titleInEnglish'] as String,
  titleInArabic: json['titleInArabic'] as String,
  cityInEnglish: json['cityInEnglish'] as String,
  cityInArabic: json['cityInArabic'] as String,
  countryInEnglish: json['countryInEnglish'] as String,
  countryInArabic: json['countryInArabic'] as String,
  imageUrl: json['imageUrl'] as String,
  totalInvestmentAmount: (json['totalInvestmentAmount'] as num).toDouble(),
);

Map<String, dynamic> _$OwnershipToJson(_Ownership instance) =>
    <String, dynamic>{
      'propertyId': instance.propertyId,
      'titleInEnglish': instance.titleInEnglish,
      'titleInArabic': instance.titleInArabic,
      'cityInEnglish': instance.cityInEnglish,
      'cityInArabic': instance.cityInArabic,
      'countryInEnglish': instance.countryInEnglish,
      'countryInArabic': instance.countryInArabic,
      'imageUrl': instance.imageUrl,
      'totalInvestmentAmount': instance.totalInvestmentAmount,
    };

_OwnershipResponse _$OwnershipResponseFromJson(Map<String, dynamic> json) =>
    _OwnershipResponse(
      status: json['status'] as bool,
      statusCode: (json['statusCode'] as num).toInt(),
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => Ownership.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OwnershipResponseToJson(_OwnershipResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'statusCode': instance.statusCode,
      'message': instance.message,
      'data': instance.data,
    };
