import 'package:freezed_annotation/freezed_annotation.dart';

part 'ownership.freezed.dart';
part 'ownership.g.dart';

@freezed
abstract class Ownership with _$Ownership {
  const factory Ownership({
    required int propertyId,
    required String titleInEnglish,
    required String titleInArabic,
    required String cityInEnglish,
    required String cityInArabic,
    required String countryInEnglish,
    required String countryInArabic,
    required String imageUrl,
    required double totalInvestmentAmount,
  }) = _Ownership;

  factory Ownership.fromJson(Map<String, dynamic> json) =>
      _$OwnershipFromJson(json);
}

@freezed
abstract class OwnershipResponse with _$OwnershipResponse {
  const factory OwnershipResponse({
    required bool status,
    required int statusCode,
    required String message,
    required List<Ownership> data,
  }) = _OwnershipResponse;

  factory OwnershipResponse.fromJson(Map<String, dynamic> json) =>
      _$OwnershipResponseFromJson(json);
}

extension OwnershipExtension on Ownership {
  String title(String language) {
    return language == 'ar' ? titleInArabic : titleInEnglish;
  }

  String city(String language) {
    return language == 'ar' ? cityInArabic : cityInEnglish;
  }

  String country(String language) {
    return language == 'ar' ? countryInArabic : countryInEnglish;
  }
}
