// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ownership.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Ownership {

 int get propertyId; String get titleInEnglish; String get titleInArabic; String get cityInEnglish; String get cityInArabic; String get countryInEnglish; String get countryInArabic; String get imageUrl; double get totalInvestmentAmount;
/// Create a copy of Ownership
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OwnershipCopyWith<Ownership> get copyWith => _$OwnershipCopyWithImpl<Ownership>(this as Ownership, _$identity);

  /// Serializes this Ownership to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Ownership&&(identical(other.propertyId, propertyId) || other.propertyId == propertyId)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.totalInvestmentAmount, totalInvestmentAmount) || other.totalInvestmentAmount == totalInvestmentAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,propertyId,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,imageUrl,totalInvestmentAmount);

@override
String toString() {
  return 'Ownership(propertyId: $propertyId, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, imageUrl: $imageUrl, totalInvestmentAmount: $totalInvestmentAmount)';
}


}

/// @nodoc
abstract mixin class $OwnershipCopyWith<$Res>  {
  factory $OwnershipCopyWith(Ownership value, $Res Function(Ownership) _then) = _$OwnershipCopyWithImpl;
@useResult
$Res call({
 int propertyId, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, String imageUrl, double totalInvestmentAmount
});




}
/// @nodoc
class _$OwnershipCopyWithImpl<$Res>
    implements $OwnershipCopyWith<$Res> {
  _$OwnershipCopyWithImpl(this._self, this._then);

  final Ownership _self;
  final $Res Function(Ownership) _then;

/// Create a copy of Ownership
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? propertyId = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? imageUrl = null,Object? totalInvestmentAmount = null,}) {
  return _then(_self.copyWith(
propertyId: null == propertyId ? _self.propertyId : propertyId // ignore: cast_nullable_to_non_nullable
as int,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,totalInvestmentAmount: null == totalInvestmentAmount ? _self.totalInvestmentAmount : totalInvestmentAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [Ownership].
extension OwnershipPatterns on Ownership {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _Ownership value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Ownership() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _Ownership value)  $default,){
final _that = this;
switch (_that) {
case _Ownership():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _Ownership value)?  $default,){
final _that = this;
switch (_that) {
case _Ownership() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int propertyId,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String imageUrl,  double totalInvestmentAmount)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Ownership() when $default != null:
return $default(_that.propertyId,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.imageUrl,_that.totalInvestmentAmount);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int propertyId,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String imageUrl,  double totalInvestmentAmount)  $default,) {final _that = this;
switch (_that) {
case _Ownership():
return $default(_that.propertyId,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.imageUrl,_that.totalInvestmentAmount);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int propertyId,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String imageUrl,  double totalInvestmentAmount)?  $default,) {final _that = this;
switch (_that) {
case _Ownership() when $default != null:
return $default(_that.propertyId,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.imageUrl,_that.totalInvestmentAmount);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _Ownership implements Ownership {
  const _Ownership({required this.propertyId, required this.titleInEnglish, required this.titleInArabic, required this.cityInEnglish, required this.cityInArabic, required this.countryInEnglish, required this.countryInArabic, required this.imageUrl, required this.totalInvestmentAmount});
  factory _Ownership.fromJson(Map<String, dynamic> json) => _$OwnershipFromJson(json);

@override final  int propertyId;
@override final  String titleInEnglish;
@override final  String titleInArabic;
@override final  String cityInEnglish;
@override final  String cityInArabic;
@override final  String countryInEnglish;
@override final  String countryInArabic;
@override final  String imageUrl;
@override final  double totalInvestmentAmount;

/// Create a copy of Ownership
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OwnershipCopyWith<_Ownership> get copyWith => __$OwnershipCopyWithImpl<_Ownership>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OwnershipToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Ownership&&(identical(other.propertyId, propertyId) || other.propertyId == propertyId)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.totalInvestmentAmount, totalInvestmentAmount) || other.totalInvestmentAmount == totalInvestmentAmount));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,propertyId,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,imageUrl,totalInvestmentAmount);

@override
String toString() {
  return 'Ownership(propertyId: $propertyId, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, imageUrl: $imageUrl, totalInvestmentAmount: $totalInvestmentAmount)';
}


}

/// @nodoc
abstract mixin class _$OwnershipCopyWith<$Res> implements $OwnershipCopyWith<$Res> {
  factory _$OwnershipCopyWith(_Ownership value, $Res Function(_Ownership) _then) = __$OwnershipCopyWithImpl;
@override @useResult
$Res call({
 int propertyId, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, String imageUrl, double totalInvestmentAmount
});




}
/// @nodoc
class __$OwnershipCopyWithImpl<$Res>
    implements _$OwnershipCopyWith<$Res> {
  __$OwnershipCopyWithImpl(this._self, this._then);

  final _Ownership _self;
  final $Res Function(_Ownership) _then;

/// Create a copy of Ownership
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? propertyId = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? imageUrl = null,Object? totalInvestmentAmount = null,}) {
  return _then(_Ownership(
propertyId: null == propertyId ? _self.propertyId : propertyId // ignore: cast_nullable_to_non_nullable
as int,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,totalInvestmentAmount: null == totalInvestmentAmount ? _self.totalInvestmentAmount : totalInvestmentAmount // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$OwnershipResponse {

 bool get status; int get statusCode; String get message; List<Ownership> get data;
/// Create a copy of OwnershipResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OwnershipResponseCopyWith<OwnershipResponse> get copyWith => _$OwnershipResponseCopyWithImpl<OwnershipResponse>(this as OwnershipResponse, _$identity);

  /// Serializes this OwnershipResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OwnershipResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'OwnershipResponse(status: $status, statusCode: $statusCode, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class $OwnershipResponseCopyWith<$Res>  {
  factory $OwnershipResponseCopyWith(OwnershipResponse value, $Res Function(OwnershipResponse) _then) = _$OwnershipResponseCopyWithImpl;
@useResult
$Res call({
 bool status, int statusCode, String message, List<Ownership> data
});




}
/// @nodoc
class _$OwnershipResponseCopyWithImpl<$Res>
    implements $OwnershipResponseCopyWith<$Res> {
  _$OwnershipResponseCopyWithImpl(this._self, this._then);

  final OwnershipResponse _self;
  final $Res Function(OwnershipResponse) _then;

/// Create a copy of OwnershipResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? data = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<Ownership>,
  ));
}

}


/// Adds pattern-matching-related methods to [OwnershipResponse].
extension OwnershipResponsePatterns on OwnershipResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OwnershipResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OwnershipResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OwnershipResponse value)  $default,){
final _that = this;
switch (_that) {
case _OwnershipResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OwnershipResponse value)?  $default,){
final _that = this;
switch (_that) {
case _OwnershipResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  List<Ownership> data)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OwnershipResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  List<Ownership> data)  $default,) {final _that = this;
switch (_that) {
case _OwnershipResponse():
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool status,  int statusCode,  String message,  List<Ownership> data)?  $default,) {final _that = this;
switch (_that) {
case _OwnershipResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OwnershipResponse implements OwnershipResponse {
  const _OwnershipResponse({required this.status, required this.statusCode, required this.message, required final  List<Ownership> data}): _data = data;
  factory _OwnershipResponse.fromJson(Map<String, dynamic> json) => _$OwnershipResponseFromJson(json);

@override final  bool status;
@override final  int statusCode;
@override final  String message;
 final  List<Ownership> _data;
@override List<Ownership> get data {
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_data);
}


/// Create a copy of OwnershipResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OwnershipResponseCopyWith<_OwnershipResponse> get copyWith => __$OwnershipResponseCopyWithImpl<_OwnershipResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OwnershipResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OwnershipResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other._data, _data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,const DeepCollectionEquality().hash(_data));

@override
String toString() {
  return 'OwnershipResponse(status: $status, statusCode: $statusCode, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class _$OwnershipResponseCopyWith<$Res> implements $OwnershipResponseCopyWith<$Res> {
  factory _$OwnershipResponseCopyWith(_OwnershipResponse value, $Res Function(_OwnershipResponse) _then) = __$OwnershipResponseCopyWithImpl;
@override @useResult
$Res call({
 bool status, int statusCode, String message, List<Ownership> data
});




}
/// @nodoc
class __$OwnershipResponseCopyWithImpl<$Res>
    implements _$OwnershipResponseCopyWith<$Res> {
  __$OwnershipResponseCopyWithImpl(this._self, this._then);

  final _OwnershipResponse _self;
  final $Res Function(_OwnershipResponse) _then;

/// Create a copy of OwnershipResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? data = null,}) {
  return _then(_OwnershipResponse(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<Ownership>,
  ));
}


}

// dart format on
