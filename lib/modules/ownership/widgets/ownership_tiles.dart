import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/ownership/models/ownership.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';

class OwnershipTiles extends StatelessWidget {
  const OwnershipTiles({
    super.key,
    required this.ownership,
    required this.user,
  });
  final Ownership ownership;
  final AppUser user;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.w),
      margin: EdgeInsets.only(bottom: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Row(
        children: [
          _buildPropertyImage(),
          12.w.widthBox,
          _buildPropertyDetails(context),
        ],
      ),
    );
  }

  /// Builds the property image with error handling
  Widget _buildPropertyImage() {
    return // Property Image
    ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: CachedNetworkImage(
        imageUrl: ownership.imageUrl,
        width: 80.w,
        height: 80.w,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          width: 80.w,
          height: 80.w,
          color: AppColors.gray.shade200,
          child: AppCircularLoader.medium(),
        ),
        errorWidget: (context, url, error) => Container(
          width: 80.w,
          height: 80.w,
          color: AppColors.gray.shade200,
          child: Icon(
            Icons.broken_image,
            color: AppColors.gray.shade400,
            size: 32.w,
          ),
        ),
      ),
    );
  }

  /// Builds the property details section
  Widget _buildPropertyDetails(BuildContext context) {
    final language = context.locale.languageCode;
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            ownership.title(language),
            style: AppTextStyles.text14.bold.dark900,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          4.h.heightBox,
          Text(
            '${ownership.city(language)}, ${ownership.country(language)}',
            style: AppTextStyles.text12.regular.gray700,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          4.h.heightBox,
          _buildInvestmentAmount(),
        ],
      ),
    );
  }

  /// Builds the investment amount display
  Widget _buildInvestmentAmount() {
    return Row(
      children: [
        buildCurrencyIcon(),
        Text(
          user.getCurrencyValue(ownership.totalInvestmentAmount),
          style: AppTextStyles.text14.bold.dark900,
        ),
      ],
    );
  }

  Widget buildCurrencyIcon() {
    if (user.usesImageSymbol) {
      return Assets.images.dirham
          .image(width: 20.w, height: 20.w)
          .paddingEnd(4);
    } else {
      return Text(
        '${user.currencyCode.currencySymbol} ',
        style: AppTextStyles.text14.bold.dark900,
      );
    }
  }
}
