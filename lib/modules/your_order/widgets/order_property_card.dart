import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/modules/your_order/widgets/bottom_sheets/financial_details_bottom_sheet.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';

class OrderPropertyCard extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AddInvestmentRequest finacialDetailsData;
  final AppUser user;

  const OrderPropertyCard({
    super.key,
    required this.propertyDetails,
    required this.finacialDetailsData,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).languageCode;
    final coverImage = propertyDetails.sortedImagesWithCoverFirst.first;

    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Property Image
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: CachedNetworkImage(
              imageUrl: coverImage.imageUrl,
              width: 80.w,
              height: 80.w,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 80.w,
                height: 80.w,
                color: AppColors.gray.shade200,
                child: AppCircularLoader.medium(),
              ),
              errorWidget: (context, url, error) => Container(
                width: 80.w,
                height: 80.w,
                color: AppColors.gray.shade200,
                child: Icon(
                  Icons.broken_image,
                  color: AppColors.gray.shade400,
                  size: 32.w,
                ),
              ),
            ),
          ),
          12.w.widthBox,
          // Property Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  propertyDetails.getTitle(locale),
                  style: AppTextStyles.text14.bold.dark900,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                4.w.heightBox,
                Text(
                  '${finacialDetailsData.numberOfShares.toStringAsFixed(2)} ${LocaleKeys.shares.tr()}',
                  style: AppTextStyles.text12.medium.gray600,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                4.w.heightBox,
                Text(
                  LocaleKeys.financialDetails.tr(),
                  style: AppTextStyles.text12.semiBold.primary,
                ).onTap(() {
                  FinancialDetailsBottomSheet.show(
                    context,
                    propertyDetails,
                    finacialDetailsData,
                    user,
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
