import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/property_investment/widgets/investment_breakdown_card.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

class FinancialDetailsBottomSheet {
  static void show(
    BuildContext context,
    PropertyDetails propertyDetails,
    AddInvestmentRequest finacialDetailsData,
    AppUser user,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppColors.white,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) => _FinancialDetailsBottomSheetContent(
        finacialDetailsData: finacialDetailsData,
        user: user,
      ),
    );
  }
}

class _FinancialDetailsBottomSheetContent extends StatelessWidget {
  final AddInvestmentRequest finacialDetailsData;
  final AppUser user;

  const _FinancialDetailsBottomSheetContent({
    required this.finacialDetailsData,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          16.h.heightBox,
          const BottomSheetHandleBar(),
          12.h.heightBox,
          Text(
            LocaleKeys.financialDetails.tr(),
            style: AppTextStyles.text18.bold.dark900,
          ),
          16.h.heightBox,

          // Investment Breakdown Card
          InvestmentBreakdownCard(
            potentialRewards: user.rewardPoints,
            numberOfShares: finacialDetailsData.numberOfShares,
            pricePerShare: finacialDetailsData.perSharesPrice,
            actualInvestmentAmount: finacialDetailsData.amountFee,
            purchaseCost: finacialDetailsData.purchaseCost,
            transactionCost: finacialDetailsData.transactionCost,
            totalCost: finacialDetailsData.totalCost,
          ),

          // Bottom padding for home indicator
          24.h.heightBox,
        ],
      ),
    );
  }
}
