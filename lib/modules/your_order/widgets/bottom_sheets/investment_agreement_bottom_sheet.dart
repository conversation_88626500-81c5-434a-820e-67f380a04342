import 'package:awesome_extensions/awesome_extensions_dart.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/your_order/providers/document_content_provider.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

class InvestmentAgreementBottomSheet {
  static void show(BuildContext context, Function() onAgree) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: AppColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder: (context) =>
          _InvestmentAgreementBottomSheetContent(onAgree: onAgree),
    );
  }
}

class _InvestmentAgreementBottomSheetContent extends ConsumerWidget {
  final Function() onAgree;

  const _InvestmentAgreementBottomSheetContent({required this.onAgree});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locale = Localizations.localeOf(context).languageCode;
    final documentContentAsync = ref.watch(
      documentContentProvider(locale.capitalizeFirst),
    );

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          16.h.heightBox,
          const BottomSheetHandleBar(),
          12.h.heightBox,

          // Title
          Text(
            LocaleKeys.investmentAgreement.tr().capitalize,
            style: AppTextStyles.text18.bold.dark900,
            textAlign: TextAlign.center,
          ),
          16.h.heightBox,

          // Content
          Flexible(
            child: documentContentAsync.when(
              data: (content) => SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Html(data: content),
              ),
              loading: () => Center(child: AppCircularLoader.medium()),
              error: (error, stackTrace) =>
                  Center(child: Text(error.toString())),
            ),
          ),

          // Agree & Continue Button
          if (documentContentAsync.hasValue) ...[
            16.h.heightBox,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: AppButton(
                text: LocaleKeys.agreeAndContinue.tr(),
                onPressed: () {
                  Navigator.of(context).pop();
                  onAgree.call();
                },
              ),
            ),
          ],
          24.h.heightBox,
        ],
      ),
    );
  }
}
