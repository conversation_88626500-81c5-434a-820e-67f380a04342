import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/your_order/widgets/bottom_sheets/investment_agreement_bottom_sheet.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class AgreementCheckbox extends StatefulWidget {
  final ValueChanged<bool> onChanged;

  const AgreementCheckbox({super.key, required this.onChanged});

  @override
  State<AgreementCheckbox> createState() => _AgreementCheckboxState();
}

class _AgreementCheckboxState extends State<AgreementCheckbox> {
  bool _isChecked = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Checkbox(
            value: _isChecked,
            onChanged: (value) {
              setState(() {
                _isChecked = value ?? false;
              });
              widget.onChanged(_isChecked);
            },
            activeColor: AppColors.primary,
            visualDensity: VisualDensity.standard,
          ),
          Expanded(
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: '${LocaleKeys.youAgreeTo.tr()} ',
                    style: AppTextStyles.text14.medium.dark900,
                  ),
                  TextSpan(
                    text: LocaleKeys.investmentAgreement.tr(),
                    style: AppTextStyles.text14.medium.primary.underline(),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        InvestmentAgreementBottomSheet.show(context, () {
                          setState(() {
                            _isChecked = true;
                          });
                          widget.onChanged(true);
                        });
                      },
                  ),
                  TextSpan(
                    text: '.',
                    style: AppTextStyles.text14.medium.dark900,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
