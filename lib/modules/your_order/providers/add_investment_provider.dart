import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/modules/your_order/providers/your_order_api_provider.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:dio/dio.dart';

/// Provider for adding investment
final addInvestmentProvider =
    AutoDisposeAsyncNotifierProvider<AddInvestmentNotifier, AppUser?>(
      () => AddInvestmentNotifier(),
      name: 'addInvestmentProvider',
    );

/// Notifier for adding investment
class AddInvestmentNotifier extends AutoDisposeAsyncNotifier<AppUser?>
    with DioExceptionMapper {
  @override
  Future<AppUser?> build() async {
    // Initial state - no operation
    return null;
  }

  /// Add investment
  Future<void> addInvestment(AddInvestmentRequest request) async {
    state = const AsyncValue.loading();
    try {
      final api = ref.read(yourOrderApiProvider);
      final updatedAppUser = await api.addInvestment(request);
      state = AsyncValue.data(updatedAppUser);
    } catch (e, st) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, st);
        state = AsyncValue.error(failure, st);
      } else {
        state = AsyncValue.error(Failure(message: e.toString()), st);
      }
    }
  }
}
