import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/your_order/api/your_order_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for your_order module
final yourOrderApiProvider = Provider.autoDispose<YourOrderApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return YourOrderApi(dio);
}, name: 'yourOrderApiProvider');
