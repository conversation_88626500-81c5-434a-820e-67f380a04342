import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/your_order/providers/your_order_api_provider.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:dio/dio.dart';

/// Provider for fetching document content
final documentContentProvider = FutureProvider.autoDispose
    .family<String, String>((ref, locale) async {
      final api = ref.watch(yourOrderApiProvider);

      try {
        final response = await api.getDocumentContent(
          locale: locale,
          documentType: 'Investment Agreement',
        );

        final content = response['documentContent'];
        if (content == null || content.isEmpty) {
          throw Failure(
            message: 'No content available for the investment agreement',
          );
        }

        return content;
      } on DioException catch (e, stackTrace) {
        final mapper = _DocumentContentMapper();
        final failure = mapper.mapDioExceptionToFailure(e, stackTrace);
        throw failure;
      } catch (e, stackTrace) {
        if (e is Failure) {
          rethrow;
        }
        throw Failure(message: e.toString(), stackTrace: stackTrace);
      }
    }, name: 'documentContentProvider');

/// Helper class to use DioExceptionMapper mixin
class _DocumentContentMapper with DioExceptionMapper {}
