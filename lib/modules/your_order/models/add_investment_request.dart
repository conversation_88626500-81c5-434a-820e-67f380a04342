import 'package:json_annotation/json_annotation.dart';

part 'add_investment_request.g.dart';

@JsonSerializable()
class AddInvestmentRequest {
  final double amountFee;
  final double purchaseCost;
  final double transactionCost;
  final double totalCost;
  final int propertyId;
  final double numberOfShares;
  final double perSharesPrice;

  const AddInvestmentRequest({
    required this.amountFee,
    required this.purchaseCost,
    required this.transactionCost,
    required this.totalCost,
    required this.propertyId,
    required this.numberOfShares,
    required this.perSharesPrice,
  });

  /// Convert object to JSON map
  Map<String, dynamic> toJson() => _$AddInvestmentRequestToJson(this);

  /// Create object from JSON map
  factory AddInvestmentRequest.fromJson(Map<String, dynamic> json) =>
      _$AddInvestmentRequestFromJson(json);
}
