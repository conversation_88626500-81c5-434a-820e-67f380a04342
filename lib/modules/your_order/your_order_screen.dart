import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/modules/your_order/providers/add_investment_provider.dart';
import 'package:maisour/modules/your_order/widgets/agreement_checkbox.dart';
import 'package:maisour/modules/your_order/widgets/order_property_card.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Screen for placing investment orders
///
/// This screen displays:
/// - Property details and investment breakdown
/// - User's wallet balance
/// - Agreement checkbox for terms acceptance
/// - Investment submission button
///
/// Features:
/// - Real-time wallet balance display
/// - Agreement validation before submission
/// - Loading states during API calls
/// - Automatic user state updates on success
/// - Error handling with localized messages
class YourOrderScreen extends ConsumerStatefulWidget {
  final AddInvestmentRequest finacialDetailsData;
  final PropertyDetails propertyDetails;

  const YourOrderScreen({
    super.key,
    required this.finacialDetailsData,
    required this.propertyDetails,
  });

  @override
  ConsumerState<YourOrderScreen> createState() => _YourOrderScreenState();
}

class _YourOrderScreenState extends ConsumerState<YourOrderScreen> {
  /// Tracks whether the user has accepted the investment agreement
  bool _isAgreementChecked = false;

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider)!;

    // Set up investment state listener for success/error handling
    _setupInvestmentStateListener();

    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            // Main content area
            Expanded(child: _buildMainContent(user)),
            // Bottom payment section with wallet balance and submit button
            _buildBottomPaymentSection(user),
          ],
        ),
      ),
    );
  }

  /// Sets up listener for investment provider state changes
  ///
  /// Handles:
  /// - Success: Updates global user state and navigates to success screen
  /// - Error: Shows localized error messages
  /// - Loading: Managed by button's isLoading property
  void _setupInvestmentStateListener() {
    ref.listen<AsyncValue<AppUser?>>(addInvestmentProvider, (previous, next) {
      next.when(
        data: (appUser) => _handleInvestmentSuccess(appUser),
        error: (error, stackTrace) => _handleInvestmentError(error),
        loading: () {
          // Loading state is handled by the button's isLoading property
        },
      );
    });
  }

  /// Handles successful investment submission
  ///
  /// Updates global user state with new data and navigates to success screen
  void _handleInvestmentSuccess(AppUser? appUser) {
    if (appUser != null && mounted) {
      // Update global user state using extension
      ref.updateCurrentUser(appUser);

      // Navigate to success screen
      GoRouter.of(context).pushReplacement(
        RoutePath.orderSuccessful,
        extra: widget.propertyDetails.propertyStatus,
      );
    }
  }

  /// Handles investment submission errors
  ///
  /// Shows localized error messages based on error type
  void _handleInvestmentError(Object error) {
    if (mounted) {
      if (error is Failure) {
        // Get localized error message based on errorKey
        final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
          errorKey: error.errorKey,
          fallbackMessage: error.message,
        );
        context.showErrorToast(title, message);
      } else {
        context.showErrorToast(
          LocaleKeys.somethingWentWrong.tr(),
          error.toString(),
        );
      }
    }
  }

  /// Builds the app bar with screen title
  PreferredSizeWidget _buildAppBar() {
    return AppBar(title: Text(LocaleKeys.yourOrder.tr()), centerTitle: false);
  }

  /// Builds the main scrollable content area
  Widget _buildMainContent(AppUser user) {
    return SingleChildScrollView(
      child: Column(
        children: [
          16.w.heightBox,
          // Property card with investment details
          _buildPropertyCard(user),
          16.w.heightBox,
          // Agreement checkbox for terms acceptance
          _buildAgreementCheckbox(),
          16.w.heightBox,
        ],
      ),
    );
  }

  /// Builds the property card showing investment details
  Widget _buildPropertyCard(AppUser user) {
    return OrderPropertyCard(
      propertyDetails: widget.propertyDetails,
      finacialDetailsData: widget.finacialDetailsData,
      user: user,
    ).paddingHorizontal(16.w);
  }

  /// Builds the agreement checkbox for terms acceptance
  Widget _buildAgreementCheckbox() {
    return AgreementCheckbox(
      onChanged: (isChecked) {
        _isAgreementChecked = isChecked;
      },
    );
  }

  /// Builds the bottom payment section with wallet balance and submit button
  Widget _buildBottomPaymentSection(AppUser user) {
    return Column(
      children: [
        // Wallet balance display
        _buildWalletBalanceSection(user),
        // Total investment and submit button
        _buildInvestmentSummaryAndButton(),
      ],
    );
  }

  /// Builds the wallet balance display section
  Widget _buildWalletBalanceSection(AppUser user) {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(color: AppColors.primary.alphaPercent(10)),
      child: Row(
        children: [
          Assets.icons.wallet.svg(
            width: 20.w,
            height: 20.w,
            colorFilter: ColorFilter.mode(
              AppColors.dark.shade900,
              BlendMode.srcIn,
            ),
          ),
          8.w.widthBox,
          Text(
            LocaleKeys.walletBalance.tr(),
            style: AppTextStyles.text14.medium.dark900,
          ),
          Spacer(),
          Assets.images.dirham.image(width: 20.w, height: 20.w),
          4.w.widthBox,
          Text(
            Currency.aed.formatCurrency(user.credit),
            style: AppTextStyles.text14.bold.dark900,
          ),
        ],
      ),
    );
  }

  /// Builds the investment summary and submit button row
  Widget _buildInvestmentSummaryAndButton() {
    return Row(
      children: [
        // Total investment amount display
        _buildTotalInvestmentDisplay(),
        16.w.widthBox,
        // Submit button with loading state
        Expanded(child: _buildSubmitButton()),
      ],
    ).paddingAll(16);
  }

  /// Builds the total investment amount display
  Widget _buildTotalInvestmentDisplay() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.totalInvestment.tr(),
          style: AppTextStyles.text12.medium.dark300,
        ),
        4.w.heightBox,
        Row(
          children: [
            Assets.images.dirham
                .image(width: 24.w, height: 24.w)
                .paddingEnd(4.w),
            Text(
              Currency.aed.formatCurrency(widget.finacialDetailsData.totalCost),
              style: AppTextStyles.text16.bold.dark900,
            ),
          ],
        ),
      ],
    );
  }

  /// Builds the submit button with Consumer for reactive loading state
  Widget _buildSubmitButton() {
    return Consumer(
      builder: (context, ref, child) {
        final addInvestmentAsync = ref.watch(addInvestmentProvider);

        return AppButton(
          text: _getButtonText(),
          isLoading: addInvestmentAsync.isLoading,
          onPressed: _handleSubmitButtonPressed,
        );
      },
    );
  }

  /// Returns the appropriate button text based on property status
  String _getButtonText() {
    return widget.propertyDetails.propertyStatus == PropertyStatus.comingSoon
        ? LocaleKeys.placeOrder.tr()
        : LocaleKeys.proceedToPay.tr();
  }

  /// Handles submit button press with validation
  void _handleSubmitButtonPressed() {
    if (_isAgreementChecked) {
      _submitInvestment();
    } else {
      _showAgreementRequiredMessage();
    }
  }

  /// Shows message when agreement is not accepted
  void _showAgreementRequiredMessage() {
    context.showWarningToast(
      LocaleKeys.actionRequired.tr(),
      LocaleKeys.pleaseAcceptTheInvestmentAgreementToContinue.tr(),
    );
  }

  /// Submits the investment request
  ///
  /// Creates the investment request with proper formatting and triggers the API call
  Future<void> _submitInvestment() async {
    final request = widget.finacialDetailsData;

    // Trigger the investment - success/error handling is done in the listener
    await ref.read(addInvestmentProvider.notifier).addInvestment(request);
  }
}
