import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/shared/models/app_user.dart';

part 'your_order_api.g.dart';

@RestApi()
abstract class YourOrderApi {
  factory YourOrderApi(Dio dio, {String baseUrl}) = _YourOrderApi;

  @GET(ApiEndpoints.getOtherDocumentContent)
  Future<Map<String, String>> getDocumentContent({
    @Query('locale') required String locale,
    @Query('documentType') required String documentType,
  });

  @POST(ApiEndpoints.addInvestment)
  Future<AppUser> addInvestment(@Body() AddInvestmentRequest request);
}
