import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class OrderSuccessfulScreen extends ConsumerWidget {
  final PropertyStatus propertyStatus;

  const OrderSuccessfulScreen({super.key, required this.propertyStatus});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          // Remove all routes and redirect to portfolio screen
          _navigateToPortfolio(context);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(appbarTitle),
          centerTitle: false,
          // Override back button behavior
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () => _navigateToPortfolio(context),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    16.w.heightBox,
                    Center(
                      child: Assets.lottie.sucess.lottie(
                        width: 136.w,
                        repeat: false,
                        reverse: false,
                      ),
                    ),
                    16.w.heightBox,
                    Text(title, style: AppTextStyles.text18.bold.dark900),
                  ],
                ),
              ),
              AppButton(
                onPressed: () => _navigateToPortfolio(context),
                text: LocaleKeys.viewPortfolio.tr(),
              ),
              16.w.heightBox,
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    );
  }

  /// Navigates to portfolio screen by removing all routes
  void _navigateToPortfolio(BuildContext context) {
    // Remove all routes and navigate to portfolio
    GoRouter.of(context).goNamed(RouteName.portfolio.name);
  }

  bool get isPreOrder => propertyStatus == PropertyStatus.comingSoon;

  String get appbarTitle => isPreOrder
      ? LocaleKeys.interestConfirmation.tr()
      : LocaleKeys.investmentConfirmation.tr();

  String get title => isPreOrder
      ? LocaleKeys.interestSuccessful.tr()
      : LocaleKeys.paymentSuccessful.tr();
}
