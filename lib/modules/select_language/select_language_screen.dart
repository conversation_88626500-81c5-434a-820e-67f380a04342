import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/language_provider.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/modules/auth/auth_service.dart';
import 'package:maisour/shared/providers/user_provider.dart';

class SelectLanguageScreen extends ConsumerStatefulWidget {
  const SelectLanguageScreen({super.key});

  @override
  ConsumerState<SelectLanguageScreen> createState() =>
      _SelectLanguageScreenState();
}

class _SelectLanguageScreenState extends ConsumerState<SelectLanguageScreen> {
  String? _selectedLanguage;

  @override
  void initState() {
    super.initState();
    // Initialize with current language
    final currentLanguage = ref.read(currentLanguageProvider);
    _selectedLanguage = currentLanguage;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.selectLanguage.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Language selection container
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(color: AppColors.gray.shade200),
                      ),
                      child: Column(
                        children: [
                          // English option
                          _buildLanguageOption(
                            language: AppConstants.languageNames['en']!,
                            languageCode: 'en',
                            isSelected: _selectedLanguage == 'en',
                            onTap: () =>
                                setState(() => _selectedLanguage = 'en'),
                          ),
                          // Divider
                          Divider(
                            color: AppColors.gray.shade200,
                          ).paddingStart(16.w),
                          // Arabic option
                          _buildLanguageOption(
                            language: AppConstants.languageNames['ar']!,
                            languageCode: 'ar',
                            isSelected: _selectedLanguage == 'ar',
                            onTap: () =>
                                setState(() => _selectedLanguage = 'ar'),
                          ),
                        ],
                      ),
                    ),
                    16.h.heightBox,
                    // Description text
                    Text(
                      LocaleKeys
                          .selectYourPreferredLanguageItWillApplyThroughoutTheApp
                          .tr(),
                      style: AppTextStyles.text12.medium.dark300,
                    ),
                  ],
                ),
              ),
            ),
            // Bottom buttons
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // Save button
                  AppButton(
                    text: LocaleKeys.save.tr(),
                    onPressed: _onSave,
                    type: ButtonType.filled,
                  ),
                  8.h.heightBox,
                  // Cancel button
                  AppButton(
                    text: LocaleKeys.cancel.tr(),
                    onPressed: () => GoRouter.of(context).pop(),
                    type: ButtonType.text,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption({
    required String language,
    required String languageCode,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          children: [
            Expanded(
              child: Text(language, style: AppTextStyles.text14.semiBold.dark),
            ),
            // Selection indicator
            Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? AppColors.primary : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppColors.primary
                      : AppColors.gray.shade400,
                  width: 1.5.w,
                ),
              ),
              child: isSelected
                  ? Icon(Icons.check, color: Colors.white, size: 12.w)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onSave() async {
    if (_selectedLanguage == null) return;

    // Change the language first
    await ref
        .read(languageProvider.notifier)
        .changeLanguage(context, _selectedLanguage!);

    // Notify backend about language change only if user is authenticated
    try {
      final currentUser = ref.read(userProvider);

      if (currentUser != null) {
        // User is authenticated, notify backend
        final authService = ref.read(authServiceProvider);
        await authService.updateLanguage(languageCode: _selectedLanguage!);

        // Update user language in the app
        final updatedUser = currentUser.copyWith(
          user: currentUser.user.copyWith(langKey: _selectedLanguage!),
        );
        ref.updateCurrentUser(updatedUser);
        debugPrint('Backend notified of language change: $_selectedLanguage');
      } else {
        // User is not authenticated (welcome screen, before login, etc.)
        debugPrint('User not authenticated, skipping backend language update');
      }
    } catch (e) {
      // Don't fail the language change if backend call fails
      debugPrint('Failed to notify backend of language change: $e');
    }

    if (mounted) {
      // Pop the current screen - the main widget will handle the rebuild
      GoRouter.of(context).pop();
    }
  }
}
