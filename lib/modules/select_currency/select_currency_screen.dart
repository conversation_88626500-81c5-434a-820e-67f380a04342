import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/auth/auth_service.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/providers/user_provider.dart';

class SelectCurrencyScreen extends ConsumerStatefulWidget {
  const SelectCurrencyScreen({super.key});

  @override
  ConsumerState<SelectCurrencyScreen> createState() =>
      _SelectCurrencyScreenState();
}

class _SelectCurrencyScreenState extends ConsumerState<SelectCurrencyScreen> {
  Currency? _selectedCurrency;
  AppUser? _user;

  @override
  void initState() {
    super.initState();
    _user = ref.read(userProvider);
    _selectedCurrency = _user?.currencyCode;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.selectCurrency.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Currency selection container
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(color: AppColors.gray.shade200),
                      ),
                      child: Column(
                        children: [
                          // AED option
                          _buildCurrencyOption(
                            currency: Currency.aed,
                            isSelected: _selectedCurrency == Currency.aed,
                            onTap: () => setState(
                              () => _selectedCurrency = Currency.aed,
                            ),
                          ),
                          // Divider
                          Divider(
                            color: AppColors.gray.shade200,
                          ).paddingStart(16.w),
                          // USD option
                          _buildCurrencyOption(
                            currency: Currency.usd,
                            isSelected: _selectedCurrency == Currency.usd,
                            onTap: () => setState(
                              () => _selectedCurrency = Currency.usd,
                            ),
                          ),
                          // Divider
                          Divider(
                            color: AppColors.gray.shade200,
                          ).paddingStart(16.w),
                          // EUR option
                          _buildCurrencyOption(
                            currency: Currency.eur,
                            isSelected: _selectedCurrency == Currency.eur,
                            onTap: () => setState(
                              () => _selectedCurrency = Currency.eur,
                            ),
                          ),
                        ],
                      ),
                    ),
                    16.h.heightBox,
                    // Description text
                    Text(
                      LocaleKeys.currencyDescription.tr(),
                      style: AppTextStyles.text12.medium.dark300,
                    ),
                  ],
                ),
              ),
            ),
            // Bottom buttons
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  // Save button
                  AppButton(
                    text: LocaleKeys.save.tr(),
                    onPressed: _onSave,
                    type: ButtonType.filled,
                  ),
                  8.h.heightBox,
                  // Cancel button
                  AppButton(
                    text: LocaleKeys.cancel.tr(),
                    onPressed: () => GoRouter.of(context).pop(),
                    type: ButtonType.text,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrencyOption({
    required Currency currency,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          children: [
            // Currency details
            Expanded(
              child: Row(
                children: [
                  _getCurrencyFlag(currency),
                  4.w.widthBox,
                  Text(
                    currency.displayName,
                    style: AppTextStyles.text14.semiBold.dark,
                  ),
                  4.w.widthBox,
                  Text(
                    '${currency.fullName} ',
                    style: AppTextStyles.text10.medium.dark300,
                  ),
                  if (currency == Currency.aed)
                    Row(
                      children: [
                        Text('(', style: AppTextStyles.text10.medium.dark300),
                        Assets.images.dirham.image(width: 12.w, height: 12.h),
                        Text(')', style: AppTextStyles.text10.medium.dark300),
                      ],
                    )
                  else
                    Text(
                      '(${currency.currencySymbol})',
                      style: AppTextStyles.text10.medium.dark300,
                    ),
                ],
              ),
            ),
            // Selection indicator
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? AppColors.primary : Colors.transparent,
                border: Border.all(
                  color: isSelected
                      ? AppColors.primary
                      : AppColors.gray.shade400,
                  width: 1.5.w,
                ),
              ),
              child: isSelected
                  ? Icon(Icons.check, color: Colors.white, size: 12.w)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _getCurrencyFlag(Currency currency) {
    switch (currency) {
      case Currency.aed:
        return Assets.icons.aedFlag.svg(width: 20.w, height: 20.w);
      case Currency.usd:
        return Assets.icons.usdFlag.svg(width: 20.w, height: 20.w);
      case Currency.eur:
        return Assets.icons.eurFlag.svg(width: 20.w, height: 20.w);
    }
  }

  Future<void> _onSave() async {
    if (_selectedCurrency == null) return;

    // Notify backend about currency change only if user is authenticated
    try {
      final currentUser = ref.read(userProvider);

      if (currentUser != null) {
        // User is authenticated, notify backend
        final authService = ref.read(authServiceProvider);
        await authService.updateCurrency(
          currencyCode: _selectedCurrency!.value,
        );
        debugPrint(
          'Backend notified of currency change: ${_selectedCurrency!.value}',
        );

        // Update user currency in the app
        final user = currentUser.copyWith(currencyCode: _selectedCurrency!);
        ref.updateCurrentUser(user);
      } else {
        // User is not authenticated (welcome screen, before login, etc.)
        debugPrint('User not authenticated, skipping backend currency update');
      }
    } catch (e) {
      // Don't fail the currency change if backend call fails
      debugPrint('Failed to notify backend of currency change: $e');
    }

    if (mounted) {
      // Pop the current screen
      GoRouter.of(context).pop();
    }
  }
}
