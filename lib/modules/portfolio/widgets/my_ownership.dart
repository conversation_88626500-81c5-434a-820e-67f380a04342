import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/ownership/models/ownership.dart';
import 'package:maisour/modules/ownership/providers/ownership_provider.dart';
import 'package:maisour/modules/ownership/widgets/ownership_tiles.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/shared/providers/user_provider.dart';

class MyOwnership extends ConsumerStatefulWidget {
  const MyOwnership({super.key});

  @override
  ConsumerState<MyOwnership> createState() => _MyOwnershipWidgetState();
}

class _MyOwnershipWidgetState extends ConsumerState<MyOwnership> {
  @override
  Widget build(BuildContext context) {
    final ownershipState = ref.watch(ownershipNotifierProvider);

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(ownershipState),
          16.h.heightBox,
          _buildContent(ownershipState),
        ],
      ),
    );
  }

  /// Builds the header section with title and view all button
  Widget _buildHeader(OwnershipState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.myOwnerships.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
      ],
    );
  }

  /// Builds the main content based on state
  Widget _buildContent(OwnershipState state) {
    switch (state.status) {
      case ApiStatus.loading:
        return const SizedBox.shrink();

      case ApiStatus.success:
        if (state.ownerships.isEmpty) {
          return const SizedBox.shrink();
        }
        return _buildOwnershipList(state.ownerships);

      case ApiStatus.error:
        return _buildErrorState(
          state.errorMessage ?? LocaleKeys.somethingWentWrong.tr(),
        );

      default:
        return const SizedBox.shrink();
    }
  }

  /// Builds the list of ownership items (max 3)
  Widget _buildOwnershipList(List<Ownership> ownerships) {
    // final displayOwnerships = ownerships.take(3).toList();

    return Column(
      children: ownerships
          .map((ownership) => _buildOwnershipItem(ownership))
          .toList(),
    );
  }

  /// Builds individual ownership item
  Widget _buildOwnershipItem(Ownership ownership) {
    return OwnershipTiles(
      ownership: ownership,
      user: ref.read(userProvider)!,
    ).onTap(() {
      GoRouter.of(context)
          .pushNamed(
            RouteName.ownershipDetails.name,
            extra: ownership.propertyId,
          )
          .then((value) {
            debugPrint(
              '================ 🔄 Refreshed Ownership 🔄 ================',
            );
            ref.read(ownershipNotifierProvider.notifier).getOwnershipData();
          });
    });
  }

  /// Builds the error state
  Widget _buildErrorState(String errorMessage) {
    return Text(
      errorMessage,
      style: AppTextStyles.text12.regular.danger,
      textAlign: TextAlign.center,
    );
  }
}
