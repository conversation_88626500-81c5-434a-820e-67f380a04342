import 'dart:math';
import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class CircularGraph extends StatefulWidget {
  final double percentage; // value between 0 and 100
  final String totalInvestment;

  const CircularGraph({
    super.key,
    required this.percentage,
    required this.totalInvestment,
  });

  @override
  State<CircularGraph> createState() => _CircularGraphState();
}

class _CircularGraphState extends State<CircularGraph>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double oldPercentage = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animateTo(widget.percentage);
  }

  @override
  void didUpdateWidget(covariant CircularGraph oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.percentage != widget.percentage) {
      oldPercentage = oldWidget.percentage;
      _animateTo(widget.percentage);
    }
  }

  void _animateTo(double newPercentage) {
    _animation =
        Tween<double>(begin: oldPercentage, end: newPercentage).animate(
          CurvedAnimation(parent: _controller, curve: Curves.easeOutCubic),
        )..addListener(() => setState(() {}));
    _controller.forward(from: 0.0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive dimensions based on available space
        final availableWidth = constraints.maxWidth;
        final availableHeight = constraints.maxHeight;

        // Calculate responsive radius and stroke width
        final radius = min(availableWidth / 2.0, availableHeight * 0.95);
        final strokeWidth = min(radius * 0.40, 40.0); // Responsive stroke width

        return CustomPaint(
          painter: _CircularGraphPainter(
            percentage: _animation.value,
            radius: radius,
            strokeWidth: strokeWidth,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                LocaleKeys.totalInvestment.tr(),
                style: AppTextStyles.text16.medium.gray,
              ),
              const SizedBox(height: 4),
              Text(
                widget.totalInvestment,
                style: AppTextStyles.text24.extraBold.dark900,
              ),
            ],
          ),
        );
      },
    );
  }
}

class _CircularGraphPainter extends CustomPainter {
  final double percentage;
  final double radius;
  final double strokeWidth;

  _CircularGraphPainter({
    required this.percentage,
    required this.radius,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height);

    final backgroundPaint = Paint()
      ..color = AppColors
          .success
          .shade200 // Light green
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final foregroundPaint = Paint()
      ..color = AppColors
          .success // Darker green
      ..strokeWidth = strokeWidth + (strokeWidth / 3)
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final rect = Rect.fromCircle(
      center: center,
      radius: radius - strokeWidth / 2,
    );

    // Start at 180° (π), sweep to 180° (π) for full background arc
    canvas.drawArc(rect, pi, pi, false, backgroundPaint);

    // Foreground: Sweep proportional angle based on percentage
    final sweepAngle = pi * (percentage.clamp(0, 100) / 100);
    canvas.drawArc(rect, pi, sweepAngle, false, foregroundPaint);
  }

  @override
  bool shouldRepaint(covariant _CircularGraphPainter oldDelegate) {
    return oldDelegate.percentage != percentage ||
        oldDelegate.radius != radius ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
