import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:easy_localization/easy_localization.dart' as easy_localization;
import 'package:maisour/shared/models/app_user.dart';

class InvestmentTextField extends StatelessWidget {
  const InvestmentTextField({
    super.key,
    required this.controller,
    required this.user,
    this.helperText,
    this.prefixIcon,
    this.onChanged,
  });

  final TextEditingController controller;
  final AppUser user;
  final String? helperText;
  final Widget? prefixIcon;
  final Function(int)? onChanged;

  /// Common method to validate and adjust input value
  void _validateAndAdjustValue() {
    final value = controller.text;
    final numericValue = int.tryParse(value);
    if (numericValue != null) {
      final minValue = user.convertedValue(500).floorToDouble();
      final maxValue = user.convertedValue(200000).ceilToDouble();

      if (numericValue < minValue) {
        controller.text = minValue.toInt().toString();
        onChanged?.call(minValue.toInt());
      } else if (numericValue > maxValue) {
        controller.text = maxValue.toInt().toString();
        onChanged?.call(maxValue.toInt());
      } else {
        // Value is valid, call onChanged with the current value
        onChanged?.call(numericValue);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      cursorColor: AppColors.primary.shade200,
      style: AppTextStyles.text14.bold.dark900,
      keyboardType: TextInputType.number,
      textInputAction: TextInputAction.done,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(6), // Max 6 digits (200000)
      ],
      textDirection: context.locale.languageCode == 'ar'
          ? TextDirection.rtl
          : TextDirection.ltr,
      onEditingComplete: () {
        _validateAndAdjustValue();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      onTapOutside: (event) {
        _validateAndAdjustValue();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      decoration: InputDecoration(
        filled: true,
        fillColor: AppColors.gray.alphaPercent(10),
        labelStyle: AppTextStyles.text14.medium.dark300,
        errorStyle: AppTextStyles.text10.medium.danger,
        contentPadding: EdgeInsetsDirectional.symmetric(
          horizontal: 16.w,
          vertical: 14.w,
        ),
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.gray.alphaPercent(10)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.gray.alphaPercent(10)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.r),
          borderSide: BorderSide(color: AppColors.gray.alphaPercent(10)),
        ),
      ),
    );
  }
}
