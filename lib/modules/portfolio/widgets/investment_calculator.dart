import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/portfolio/widgets/circular_graph.dart';
import 'package:maisour/modules/portfolio/widgets/investment_text_field.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';

class InvestmentCalculator extends StatefulWidget {
  final AppUser user;
  const InvestmentCalculator({super.key, required this.user});

  @override
  State<InvestmentCalculator> createState() => _InvestmentCalculatorState();
}

class _InvestmentCalculatorState extends State<InvestmentCalculator> {
  final TextEditingController initialInvestmentController =
      TextEditingController();
  int initialInvestment = 0;
  int propertyValueGrowth = 5;
  int expectedReturnYield = 7;

  double totalRentalYield = 0;
  double totalValueAppreciation = 0;
  double totalInvestmentReturn = 0;
  double grossInvestmentGain = 0;

  @override
  void initState() {
    initialInvestment =
        int.tryParse(widget.user.convertedValue(500).toString()) ?? 500;
    initialInvestmentController.text = initialInvestment.toString();
    doCalculate();
    super.initState();
  }

  @override
  void dispose() {
    initialInvestmentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.h.heightBox,
        Text(
          LocaleKeys.investmentCalculator.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        4.w.heightBox,
        Text(
          LocaleKeys.expectedInvestmentReturnIn5Years.tr(),
          style: AppTextStyles.text14.medium.gray,
        ),
        16.w.heightBox,
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppColors.gray.shade200),
          ),
          child: Column(
            children: [
              SizedBox(
                width: double.infinity,
                height: 150.w,
                child: CircularGraph(
                  percentage: (initialInvestment / totalInvestmentReturn) * 100,
                  totalInvestment: totalInvestmentReturn.toStringAsFixed(2),
                ),
              ),
              48.heightBox,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 14.w,
                        height: 14.w,
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                        ),
                      ),
                      4.w.widthBox,
                      Text(
                        LocaleKeys.investment.tr(),
                        style: AppTextStyles.text12.medium.gray600,
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        width: 14.w,
                        height: 14.w,
                        decoration: BoxDecoration(
                          color: AppColors.success.shade200,
                          shape: BoxShape.circle,
                        ),
                      ),
                      4.w.widthBox,
                      Text(
                        LocaleKeys.returns.tr(),
                        style: AppTextStyles.text12.medium.gray600,
                      ),
                    ],
                  ),
                ],
              ),
              26.w.heightBox,
              _buildBreakdownRow(
                LocaleKeys.totalRentalYield.tr(),
                totalRentalYield.toStringAsFixed(2),
              ),
              10.w.heightBox,
              _buildBreakdownRow(
                LocaleKeys.valueAppreciation.tr(),
                totalValueAppreciation.toStringAsFixed(2),
              ),
              10.w.heightBox,
              _buildBreakdownRow(
                LocaleKeys.totalInvestmentReturn.tr(),
                totalInvestmentReturn.toStringAsFixed(2),
              ),
              10.w.heightBox,
              _buildBreakdownRow(
                LocaleKeys.grossInvestmentGain.tr(),
                grossInvestmentGain.toStringAsFixed(1),
                isPercentage: true,
              ),
            ],
          ),
        ),
        16.h.heightBox,
        _buildInvestmentCard(),
        16.h.heightBox,
        _buildPropertyValueGrowth(),
        16.h.heightBox,
        _buildExpectedReturnYield(),
        16.h.heightBox,
      ],
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

  Widget _buildBreakdownRow(
    String label,
    String value, {
    bool isPercentage = false,
  }) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [Text(label, style: AppTextStyles.text12.medium.gray600)],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (!isPercentage) _buildCurrencyIcon(),
            Text(
              value + (isPercentage ? ' %' : ''),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCurrencyIcon() {
    if (widget.user.usesImageSymbol) {
      return Assets.images.dirham
          .image(width: 20.w, height: 20.w)
          .paddingEnd(4);
    } else {
      return Text(
        '${widget.user.currencyCode.currencySymbol} ',
        style: AppTextStyles.text14.semiBold.dark900,
      );
    }
  }

  Widget _buildInvestmentCard() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.initialInvestment.tr(),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
            SizedBox(
              width: 160.w,
              child: InvestmentTextField(
                controller: initialInvestmentController,
                user: widget.user,
                onChanged: (int value) {
                  initialInvestment = value;
                  doCalculate();
                },
                prefixIcon: SizedBox(
                  width: 20.w,
                  child: Center(
                    child: widget.user.usesImageSymbol
                        ? Assets.images.dirham.image(width: 20.w, height: 20.w)
                        : Text(
                            '${widget.user.currencyCode.currencySymbol} ',
                            style: AppTextStyles.text14.semiBold.dark900,
                          ),
                  ),
                ),
              ),
            ),
          ],
        ),
        16.w.heightBox,
        Slider(
          value: double.tryParse(initialInvestment.toString()) ?? 500,
          onChanged: (value) {
            initialInvestment = value.toInt();
            initialInvestmentController.text = initialInvestment.toString();
            doCalculate();
          },
          min: widget.user.convertedValue(500).floorToDouble(),
          max: widget.user.convertedValue(200000).ceilToDouble(),
          activeColor: AppColors.success,
          inactiveColor: AppColors.gray.shade100,
          padding: EdgeInsets.symmetric(horizontal: 6.w),
        ),
      ],
    );
  }

  Widget _buildPropertyValueGrowth() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.propertyValueGrowth.tr(),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
            Text(
              '${propertyValueGrowth.toString()}%',
              style: AppTextStyles.text14.bold.dark900,
            ),
          ],
        ),
        16.w.heightBox,
        Slider(
          value: propertyValueGrowth.toDouble(),
          onChanged: (value) {
            propertyValueGrowth = value.toInt();
            doCalculate();
          },
          min: 1,
          max: 100,
          activeColor: AppColors.success,
          inactiveColor: AppColors.gray.shade100,
          padding: EdgeInsets.symmetric(horizontal: 6.w),
        ),
      ],
    );
  }

  Widget _buildExpectedReturnYield() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.expectedRentalYield.tr(),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
            Text(
              '${expectedReturnYield.toString()}%',
              style: AppTextStyles.text14.bold.dark900,
            ),
          ],
        ),
        16.w.heightBox,
        Slider(
          value: expectedReturnYield.toDouble(),
          onChanged: (value) {
            expectedReturnYield = value.toInt();
            doCalculate();
          },
          min: 1,
          max: 15,
          activeColor: AppColors.success,
          inactiveColor: AppColors.gray.shade100,
          padding: EdgeInsets.symmetric(horizontal: 6.w),
        ),
      ],
    );
  }

  void doCalculate() {
    setState(() {
      totalRentalYield = initialInvestment * (expectedReturnYield / 100) * 5;
      totalValueAppreciation = initialInvestment * (propertyValueGrowth / 100);
      totalInvestmentReturn =
          initialInvestment + totalRentalYield + totalValueAppreciation;
      grossInvestmentGain =
          ((totalRentalYield + totalValueAppreciation) / initialInvestment) *
          100;
    });
  }
}
