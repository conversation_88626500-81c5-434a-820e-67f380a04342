import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/portfolio/widgets/investment_calculator.dart';
import 'package:maisour/modules/portfolio/widgets/my_ownership.dart';
import 'package:maisour/modules/ownership/providers/ownership_provider.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/app_bars/app_sliver_app_bar.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';

class PortfolioScreen extends ConsumerStatefulWidget {
  const PortfolioScreen({super.key});

  @override
  ConsumerState<PortfolioScreen> createState() => _PortfolioScreenState();
}

class _PortfolioScreenState extends ConsumerState<PortfolioScreen> {
  @override
  void initState() {
    super.initState();
    // Load ownership data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ownershipNotifierProvider.notifier).getOwnershipData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider);
    final ownershipState = ref.watch(ownershipNotifierProvider);

    return CustomScrollView(
      slivers: [
        AppSliverAppBar(
          title: LocaleKeys.portfolio.tr(),
          pinned: true,
          floating: false,
          snap: false,
        ),
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InvestmentCalculator(user: user!).paddingHorizontal(16.w),
              16.w.heightBox,
              // Only show MyOwnership if user has ownerships
              if (ownershipState.status == ApiStatus.success &&
                  ownershipState.ownerships.isNotEmpty) ...[
                AppSpacer(),
                MyOwnership(),
              ],
              4.w.heightBox,
              RegulatedByDsfa(
                iconSize: 24.w,
                textStyle: AppTextStyles.text14.medium.dark900,
              ),
              16.w.heightBox,
            ],
          ),
        ),
      ],
    );
  }
}
