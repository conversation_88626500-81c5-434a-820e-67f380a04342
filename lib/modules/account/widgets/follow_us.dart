import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/app_version_service.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/enums/social_media_platform.dart';

class FollowUs extends StatelessWidget {
  const FollowUs({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Follow us section
        _buildFollowUsSection(),

        // App version
        _buildAppVersionSection(),

        // Regulation info
        RegulatedByDsfa(
          iconSize: 24.w,
          textStyle: AppTextStyles.text14.medium.dark300,
        ),
      ],
    ).paddingOnly(top: 16.w);
  }

  Widget _buildFollowUsSection() {
    return Column(
      children: [
        // Separator line with "Follow us" text
        Row(
          children: [
            32.w.widthBox,
            Expanded(child: Divider(color: AppColors.gray.shade200)),
            16.w.widthBox,
            Text(
              LocaleKeys.followUs.tr(),
              style: AppTextStyles.text14.medium.gray600,
            ),
            16.w.widthBox,
            Expanded(child: Divider(color: AppColors.gray.shade200)),
            32.w.widthBox,
          ],
        ),

        16.h.heightBox,

        // Social media icons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialMediaIcon(
              icon: Assets.icons.facebook.svg(width: 24.w, height: 24.w),
              onTap: () => UrlLauncherService.launchSocialMedia(
                SocialMediaPlatform.facebook,
              ),
            ),
            16.w.widthBox,
            _buildSocialMediaIcon(
              icon: Assets.icons.instagram.svg(width: 24.w, height: 24.w),
              onTap: () => UrlLauncherService.launchSocialMedia(
                SocialMediaPlatform.instagram,
              ),
            ),
            16.w.widthBox,
            _buildSocialMediaIcon(
              icon: Assets.icons.whatsapp.svg(width: 24.w, height: 24.w),
              onTap: () => UrlLauncherService.launchSocialMedia(
                SocialMediaPlatform.whatsapp,
              ),
            ),
            16.w.widthBox,
            _buildSocialMediaIcon(
              icon: Assets.icons.twitter.svg(width: 24.w, height: 24.w),
              onTap: () => UrlLauncherService.launchSocialMedia(
                SocialMediaPlatform.twitter,
              ),
            ),
            16.w.widthBox,
            _buildSocialMediaIcon(
              icon: Assets.icons.youtube.svg(width: 24.w, height: 24.w),
              onTap: () => UrlLauncherService.launchSocialMedia(
                SocialMediaPlatform.youtube,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialMediaIcon({
    required Widget icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(onTap: onTap, child: icon);
  }

  Widget _buildAppVersionSection() {
    return FutureBuilder<String>(
      future: AppVersionService.getAppVersion(),
      builder: (context, snapshot) {
        final version = snapshot.data ?? '';
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Text(
            version,
            style: AppTextStyles.text14.medium.gray600,
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }
}
