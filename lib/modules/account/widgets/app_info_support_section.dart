import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:share_plus/share_plus.dart';

class AppInfoSupportSection extends StatelessWidget {
  const AppInfoSupportSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: BoxBorder.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        children: [
          // Our Story
          // _buildMenuItem(
          //   icon: Assets.icons.ourStory.svg(
          //     width: 24.w,
          //     height: 24.w,
          //     colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
          //   ),
          //   title: LocaleKeys.ourStory.tr(),
          //   onTap: () {},
          //   showDivider: true,
          // ),

          // Get Help
          _buildMenuItem(
            icon: Assets.icons.getHelp.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.getHelp.tr(),
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.getHelp.name);
            },
            showDivider: true,
          ),

          // Share App
          _buildMenuItem(
            icon: Assets.icons.share.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.shareApp.tr(),
            onTap: () {
              SharePlus.instance.share(
                ShareParams(
                  subject: LocaleKeys.shareApp.tr(),
                  title: LocaleKeys.shareApp.tr(),
                  text: LocaleKeys.shareAppDesc.tr(),
                ),
              );
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required Widget icon,
    required String title,
    required VoidCallback? onTap,
    required bool showDivider,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Row(
            children: [
              icon,
              16.w.widthBox,
              Expanded(
                child: Text(title, style: AppTextStyles.text14.semiBold.dark),
              ),
            ],
          ),
        ),

        if (showDivider)
          Divider(
            color: AppColors.gray.shade200,
            height: 1.h,
            thickness: 1.h,
          ).paddingSTEB(40.w, 16.w, 0, 0)
        else
          SizedBox(height: 16.w),
      ],
    ).paddingSTEB(16.w, 16.w, 0, 0);
  }
}
