import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';

class ProfileSection extends StatelessWidget {
  final AppUser user;

  const ProfileSection({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: BoxBorder.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        children: [
          // My Profile
          _buildMenuItem(
            icon: Assets.icons.account.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.myProfile.tr(),
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.myProfile.name);
            },
            showDivider: true,
          ),

          // Update Password
          _buildMenuItem(
            icon: Assets.icons.lock.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.updatePassword.tr(),
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.updatePassword.name);
            },
            showDivider: true,
          ),

          // Documents
          _buildMenuItem(
            icon: Assets.icons.documents.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.documents.tr(),
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.documents.name);
            },
            showDivider: true,
            trailingText: user.totalDocument.toString(),
          ),

          // // Referrals
          // _buildMenuItem(
          //   icon: Assets.icons.gift.svg(
          //     width: 24.w,
          //     height: 24.w,
          //     colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
          //   ),
          //   title: LocaleKeys.referrals.tr(),
          //   onTap: () {},
          //   showDivider: true,
          // ),

          // Sell Properties
          _buildMenuItem(
            icon: Assets.icons.building.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.sellProperties.tr(),
            onTap: () {
              UrlLauncherService.launchURL(AppConstants.sellYourProperty);
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required Widget icon,
    required String title,
    required VoidCallback? onTap,
    required bool showDivider,
    String? trailingText,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Row(
            children: [
              icon,
              16.w.widthBox,
              Expanded(
                child: Text(title, style: AppTextStyles.text14.semiBold.dark),
              ),
              if (trailingText != null)
                Text(
                  trailingText,
                  style: AppTextStyles.text12.medium.dark300,
                ).paddingEnd(16),
            ],
          ),
        ),

        if (showDivider)
          Divider(
            color: AppColors.gray.shade200,
            height: 1.h,
            thickness: 1.h,
          ).paddingSTEB(40.w, 16.w, 0, 0)
        else
          SizedBox(height: 16.w),
      ],
    ).paddingSTEB(16.w, 16.w, 0, 0);
  }
}
