import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/config/app_router.dart';
import 'package:go_router/go_router.dart';

class LogoutOptionsSection extends ConsumerWidget {
  const LogoutOptionsSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: BoxBorder.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        children: [
          // Logout
          _buildMenuItem(
            icon: Assets.icons.logout.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.danger, BlendMode.srcIn),
            ),
            title: LocaleKeys.logout.tr(),
            onTap: () => _showLogoutConfirmationDialog(context, ref),
            showDivider: true,
          ),

          // Close Account
          _buildMenuItem(
            icon: Assets.icons.trash.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.danger, BlendMode.srcIn),
            ),
            title: LocaleKeys.closeAccount.tr(),
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.closeAccount.name);
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Future<void> _showLogoutConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
  ) async {
    return showDialog<void>(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        title: Text(LocaleKeys.logout.tr()),
        content: Text(LocaleKeys.areYouSureYouWantToLogout.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocaleKeys.cancel.tr()),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performLogout(context, ref);
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.danger),
            child: Text(LocaleKeys.yes.tr()),
          ),
        ],
      ),
    );
  }

  Future<void> _performLogout(BuildContext context, WidgetRef ref) async {
    try {
      final authNotifier = ref.read(authNotifierProvider('account').notifier);
      await authNotifier.signOut();

      // Navigate to welcome screen and clear all routes
      if (context.mounted) {
        context.goNamed(RouteName.login.name);
      }
    } catch (error) {
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }

  Widget _buildMenuItem({
    required Widget icon,
    required String title,
    required VoidCallback? onTap,
    required bool showDivider,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Row(
            children: [
              icon,
              16.w.widthBox,
              Expanded(
                child: Text(title, style: AppTextStyles.text14.semiBold.danger),
              ),
            ],
          ),
        ),

        if (showDivider)
          Divider(
            color: AppColors.gray.shade200,
            height: 1.h,
            thickness: 1.h,
          ).paddingSTEB(40.w, 16.w, 0, 0)
        else
          SizedBox(height: 16.w),
      ],
    ).paddingSTEB(16.w, 16.w, 0, 0);
  }
}
