import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/shared/models/app_user.dart';

class PreferencesSection extends ConsumerWidget {
  const PreferencesSection({super.key, required this.user});
  final AppUser user;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: BoxBorder.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        children: [
          // Language
          _buildMenuItem(
            icon: Assets.icons.language.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.language.tr(),
            value: AppConstants.languageNames[user.user.langKey],
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.selectLanguage.name);
            },
            showDivider: true,
          ),

          // Currency
          _buildMenuItem(
            icon: Assets.icons.currency.svg(
              width: 24.w,
              height: 24.w,
              colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
            ),
            title: LocaleKeys.currency.tr(),
            value: user.currencyCode.displayName,
            onTap: () {
              GoRouter.of(context).pushNamed(RouteName.selectCurrency.name);
            },
            showDivider: false,
          ),

          // Notifications
          // _buildMenuItem(
          //   icon: Assets.icons.notification.svg(
          //     width: 24.w,
          //     height: 24.w,
          //     colorFilter: ColorFilter.mode(AppColors.dark, BlendMode.srcIn),
          //   ),
          //   title: LocaleKeys.notifications.tr(),
          //   onTap: () {},
          //   showDivider: false,
          // ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required Widget icon,
    required String title,
    required VoidCallback? onTap,
    required bool showDivider,
    String? value,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Row(
            children: [
              icon,
              16.w.widthBox,
              Expanded(
                child: Text(title, style: AppTextStyles.text14.semiBold.dark),
              ),
              if (value != null)
                Text(
                  value,
                  style: AppTextStyles.text14.medium.dark300,
                ).paddingEnd(16),
            ],
          ),
        ),

        if (showDivider)
          Divider(
            color: AppColors.gray.shade200,
            height: 1.h,
            thickness: 1.h,
          ).paddingSTEB(40.w, 16.w, 0, 0)
        else
          SizedBox(height: 16.w),
      ],
    ).paddingSTEB(16.w, 16.w, 0, 0);
  }
}
