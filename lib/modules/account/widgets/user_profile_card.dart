import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/date_formatter.dart';

class UserProfileCard extends StatelessWidget {
  final AppUser user;

  const UserProfileCard({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.w),
      child: Row(
        children: [
          // User Avatar
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.gray.shade100,
              border: Border.all(color: AppColors.gray.shade200),
            ),
            child: Center(
              child: Text(
                _getInitials(user.user.fullName),
                style: AppTextStyles.text20.bold.dark900,
              ),
            ),
          ),

          12.w.widthBox,

          // User Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // User Name
                Text(
                  user.user.fullName,
                  style: AppTextStyles.text16.bold.dark900,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                4.h.heightBox,

                // Membership Date
                Text(
                  '${LocaleKeys.memberSince.tr()} ${DateFormatter.formatDate(user.user.createdDate)}',
                  style: AppTextStyles.text12.medium.dark300,
                ),
              ],
            ),
          ),

          12.w.widthBox,

          // Account Type Tag
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
            decoration: BoxDecoration(
              color: AppColors.primary.alphaPercent(5),
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(color: AppColors.primary),
            ),
            child: Text(
              user.userAccountTypeDetails.accountType,
              style: AppTextStyles.text12.bold.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// Get initials from full name
  String _getInitials(String fullName) {
    final names = fullName.trim().split('');
    return names[0].toUpperCase();
  }
}
