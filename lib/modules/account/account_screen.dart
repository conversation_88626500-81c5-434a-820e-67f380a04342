import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/account/widgets/follow_us.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/app_bars/app_sliver_app_bar.dart';
import 'package:maisour/shared/widgets/banners/onboarding_banner.dart';
import 'package:maisour/shared/widgets/banners/tax_residency_banner.dart';
import 'package:maisour/shared/widgets/banners/verification_pending_banner.dart';
import 'package:maisour/shared/widgets/banners/verification_rejected_banner.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/modules/account/widgets/user_profile_card.dart';
import 'package:maisour/modules/account/widgets/profile_section.dart';
import 'package:maisour/modules/account/widgets/preferences_section.dart';
import 'package:maisour/modules/account/widgets/app_info_support_section.dart';
import 'package:maisour/modules/account/widgets/logout_options_section.dart';
import 'package:maisour/config/app_router.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';

class AccountScreen extends ConsumerWidget {
  const AccountScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appUser = ref.watchCurrentUser;

    // If user is null (after logout), redirect to login
    if (appUser == null) {
      // Use a post-frame callback to avoid build-time navigation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          context.goNamed(RouteName.login.name);
        }
      });
      return const Scaffold(body: AppCircularLoader());
    }

    // Show onboarding banner if user hasn't completed verification
    final showOnboardingBanner =
        appUser.verifiedStatus == VerifiedStatus.started ||
        appUser.verifiedStatus == VerifiedStatus.notYet;

    final showVerificationPendingBanner =
        appUser.verifiedStatus == VerifiedStatus.pending;

    final showVerificationRejectedBanner =
        appUser.verifiedStatus == VerifiedStatus.notVerified;

    final shoAddTaxResidencyBanner =
        appUser.verifiedStatus == VerifiedStatus.verified &&
        appUser.taxResidencyInfoExists == false;

    return SafeArea(
      child: CustomScrollView(
        slivers: [
          // App bar - not pinned so it doesn't hide when scrolling
          AppSliverAppBar(
            title: LocaleKeys.account.tr(),
            pinned: false,
            floating: true,
            snap: false,
          ),

          // Onboarding banner - always visible, not hidden on scroll
          if (showOnboardingBanner) const OnboardingBanner(),

          // Verification pending banner
          if (showVerificationPendingBanner) const VerificationPendingBanner(),

          // Verification rejected banner
          if (showVerificationRejectedBanner)
            const VerificationRejectedBanner(),

          // Tax residency banner
          if (shoAddTaxResidencyBanner) const TaxResidencyBanner(),

          // Main content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                16.h.heightBox,
                UserProfileCard(user: appUser),
                _buildSectionTitle(LocaleKeys.profile.tr()),
                ProfileSection(user: appUser),
                10.h.heightBox,
                _buildSectionTitle(LocaleKeys.preferences.tr()),
                PreferencesSection(user: appUser),
                10.h.heightBox,
                _buildSectionTitle(LocaleKeys.appInfoSupport.tr()),
                const AppInfoSupportSection(),
                10.h.heightBox,
                _buildSectionTitle(LocaleKeys.logoutOptions.tr()),
                const LogoutOptionsSection(),
                10.h.heightBox,
                const FollowUs(),
                30.h.heightBox,
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(title, style: AppTextStyles.text16.bold.dark).paddingAll(16.w);
  }
}
