import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/services/connectivity/connectivity_provider.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

/// Reusable screen widget displayed when there's no internet connection
///
/// Features:
/// - Shows no internet icon and message
/// - Provides retry functionality
/// - Monitors connectivity state changes
/// - Follows existing design patterns and SafeArea guidelines
/// - Uses established UI components and styling
class NoInternetScreen extends ConsumerWidget {
  /// Optional callback when retry button is pressed
  final VoidCallback? onRetry;

  /// Optional custom message to display instead of default
  final String? customMessage;

  /// Whether to show the retry button
  final bool showRetryButton;

  const NoInternetScreen({
    super.key,
    this.onRetry,
    this.customMessage,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectivityState = ref.watch(connectivityProvider);

    return GradientBackground(
      child: Scaffold(
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.noInternet.image(height: 200.w),

            32.h.heightBox,

            // Title
            Text(
              LocaleKeys.noInternetConnection.tr(),
              style: AppTextStyles.text20.bold.dark900,
            ),

            8.h.heightBox,

            // Description
            Text(
              customMessage ?? LocaleKeys.noInternetDescription.tr(),
              style: AppTextStyles.text12.medium.dark300,
              textAlign: TextAlign.center,
            ),

            // Show connectivity status if checking
            if (connectivityState.isChecking) ...[
              16.h.heightBox,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16.w,
                    height: 16.w,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary,
                      ),
                    ),
                  ),
                  12.w.widthBox,
                  Text(
                    LocaleKeys.checkingConnection.tr(),
                    style: AppTextStyles.text14.dark300,
                  ),
                ],
              ),
            ],

            24.h.heightBox,

            // Retry button
            if (showRetryButton)
              AppButton(
                text: LocaleKeys.tryAgain.tr(),
                onPressed: connectivityState.isChecking
                    ? () {} // Disabled state
                    : () async {
                        // Refresh connectivity state
                        await ref
                            .read(connectivityProvider.notifier)
                            .refreshConnectivity();

                        // Call custom retry callback if provided
                        onRetry?.call();
                      },
                backgroundColor: connectivityState.isChecking
                    ? AppColors.gray.shade300
                    : AppColors.primary,
                textColor: connectivityState.isChecking
                    ? AppColors.gray.shade500
                    : AppColors.white,
              ),

            24.h.heightBox,

            // Connection status info
            _buildConnectionStatusInfo(connectivityState),
          ],
        ).paddingHorizontal(16.w),
      ),
    );
  }

  /// Build connection status information widget
  Widget _buildConnectionStatusInfo(ConnectivityState state) {
    if (state.hasError) {
      return Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: AppColors.danger.shade100,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: AppColors.danger.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, size: 16.w, color: AppColors.danger),
            8.w.widthBox,
            Text(
              LocaleKeys.connectionCheckFailed.tr(),
              style: AppTextStyles.text12.danger.medium,
            ),
          ],
        ),
      );
    }

    if (state.connectivityResults.isNotEmpty &&
        !state.connectivityResults.contains(ConnectivityResult.none)) {
      final connectionType = _getConnectionTypeText(state.connectivityResults);

      return Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: AppColors.warning.shade100,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: AppColors.warning.shade200),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.info_outline, size: 16.w, color: AppColors.warning),
            8.w.widthBox,
            Flexible(
              child: Text(
                LocaleKeys.connectedButNoInternet.tr(
                  namedArgs: {'connectionType': connectionType},
                ),
                style: AppTextStyles.text12.warning700.medium,
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// Get human-readable connection type text
  String _getConnectionTypeText(List<ConnectivityResult> results) {
    if (results.contains(ConnectivityResult.wifi)) {
      return 'WiFi';
    } else if (results.contains(ConnectivityResult.mobile)) {
      return 'Mobile Data';
    } else if (results.contains(ConnectivityResult.ethernet)) {
      return 'Ethernet';
    } else {
      return 'Network';
    }
  }
}
