import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for managing SliverAppBar UI state
final sliverAppBarUIProvider =
    StateNotifierProvider.autoDispose<
      SliverAppBarUINotifier,
      SliverAppBarUIState
    >((ref) {
      return SliverAppBarUINotifier();
    }, name: 'sliverAppBarUIProvider');

/// UI state for SliverAppBar
class SliverAppBarUIState {
  final bool isAppBarCollapsed;
  final int currentImage;

  const SliverAppBarUIState({
    this.isAppBarCollapsed = false,
    this.currentImage = 0,
  });

  SliverAppBarUIState copyWith({bool? isAppBarCollapsed, int? currentImage}) {
    return SliverAppBarUIState(
      isAppBarCollapsed: isAppBarCollapsed ?? this.isAppBarCollapsed,
      currentImage: currentImage ?? this.currentImage,
    );
  }
}

/// Notifier for SliverAppBar UI state
class SliverAppBarUINotifier extends StateNotifier<SliverAppBarUIState> {
  SliverAppBarUINotifier() : super(const SliverAppBarUIState());

  /// Update app bar collapse state
  void setAppBarCollapsed(bool isCollapsed) {
    state = state.copyWith(isAppBarCollapsed: isCollapsed);
  }

  /// Update current image index
  void setCurrentImage(int imageIndex) {
    state = state.copyWith(currentImage: imageIndex);
  }

  /// Reset UI state
  void reset() {
    state = const SliverAppBarUIState();
  }
}
