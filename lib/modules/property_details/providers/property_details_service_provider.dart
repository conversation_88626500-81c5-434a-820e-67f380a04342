import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/property_details/property_details_service.dart';
import 'property_details_api_provider.dart';

/// Property details service provider
final propertyDetailsServiceProvider =
    Provider.autoDispose<PropertyDetailsService>((ref) {
      final propertyDetailsApi = ref.watch(propertyDetailsApiProvider);
      return PropertyDetailsService(propertyDetailsApi);
    }, name: 'propertyDetailsServiceProvider');
