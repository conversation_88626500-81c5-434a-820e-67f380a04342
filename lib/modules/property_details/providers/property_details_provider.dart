import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/property_details/property_details_service.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'property_details_service_provider.dart';

/// Property details state
class PropertyDetailsState {
  final ApiStatus status;
  final PropertyDetails? propertyDetails;
  final String? errorMessage;
  final String? errorKey;

  const PropertyDetailsState({
    this.status = ApiStatus.initial,
    this.propertyDetails,
    this.errorMessage,
    this.errorKey,
  });

  PropertyDetailsState copyWith({
    ApiStatus? status,
    PropertyDetails? propertyDetails,
    String? errorMessage,
    String? errorKey,
  }) {
    return PropertyDetailsState(
      status: status ?? this.status,
      propertyDetails: propertyDetails ?? this.propertyDetails,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
    );
  }
}

/// Property details notifier provider
final propertyDetailsNotifierProvider =
    StateNotifierProvider.family<
      PropertyDetailsNotifier,
      PropertyDetailsState,
      int
    >((ref, propertyId) {
      final propertyDetailsService = ref.watch(propertyDetailsServiceProvider);
      return PropertyDetailsNotifier(propertyDetailsService, propertyId);
    }, name: 'propertyDetailsNotifierProvider');

/// Property details notifier
class PropertyDetailsNotifier extends StateNotifier<PropertyDetailsState>
    with DioExceptionMapper {
  final PropertyDetailsService _propertyDetailsService;
  final int _propertyId;

  PropertyDetailsNotifier(this._propertyDetailsService, this._propertyId)
    : super(const PropertyDetailsState());

  /// Load property details
  Future<void> loadPropertyDetails() async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      final propertyDetails = await _propertyDetailsService.getPropertyDetails(
        _propertyId,
      );
      state = state.copyWith(
        status: ApiStatus.success,
        propertyDetails: propertyDetails,
      );
    } on DioException catch (error, stackTrace) {
      final failure = mapDioExceptionToFailure(error, stackTrace);
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: failure.message,
        errorKey: failure.errorKey,
      );
    } catch (error) {
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: error.toString(),
      );
    }
  }

  /// Refresh property details
  Future<void> refreshPropertyDetails() async {
    await loadPropertyDetails();
  }
}
