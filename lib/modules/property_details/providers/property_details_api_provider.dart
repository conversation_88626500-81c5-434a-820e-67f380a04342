import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/property_details/api/property_details_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for property details module
final propertyDetailsApiProvider = Provider.autoDispose<PropertyDetailsApi>((
  ref,
) {
  final dio = ref.watch(networkServiceProvider);
  return PropertyDetailsApi(dio);
}, name: 'propertyDetailsApiProvider');
