import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_amenity.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class AmenityCard extends StatelessWidget {
  final PropertyAmenity amenity;
  final String locale;
  final double? cardWidth;
  final bool showBorder;

  const AmenityCard({
    super.key,
    required this.amenity,
    required this.locale,
    this.cardWidth,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    final name = locale == 'ar' ? amenity.nameInArabic : amenity.nameInEnglish;
    final screenWidth = MediaQuery.of(context).size.width;
    final width = cardWidth ?? (screenWidth - 32.w - 24.w) / 3;

    return Container(
      width: width,
      height: 100.h, // Fixed height for consistency
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: showBorder ? Border.all(color: AppColors.gray.shade100) : null,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Amenity icon/image
          _buildAmenityImage(amenity.imageUrl),
          8.h.heightBox,
          // Amenity name
          Text(
            name,
            style: AppTextStyles.text12.medium.dark900,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAmenityImage(String imageUrl) {
    final isSvg = imageUrl.toLowerCase().contains('.svg');

    return Container(
      width: 40.w,
      height: 40.w,
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(10),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Center(
        child: imageUrl.isNotEmpty
            ? (isSvg
                  ? SvgPicture.network(
                      imageUrl,
                      width: 20.w,
                      height: 20.w,
                      placeholderBuilder: (context) => Icon(
                        Icons.image,
                        size: 20.w,
                        color: AppColors.gray.shade400,
                      ),
                    )
                  : CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 20.w,
                      height: 20.w,
                      placeholder: (context, url) => Icon(
                        Icons.image,
                        size: 20.w,
                        color: AppColors.gray.shade400,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        Icons.image,
                        size: 20.w,
                        color: AppColors.gray.shade400,
                      ),
                    ))
            : Icon(Icons.image, size: 20.w, color: AppColors.gray.shade400),
      ),
    );
  }
}
