import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/modules/properties/widgets/onboard_instruction.dart';
import 'package:maisour/modules/properties/widgets/property_features.dart';
import 'package:maisour/modules/properties/widgets/suspended_instruction.dart';
import 'package:maisour/modules/property_details/widgets/financial_details_card.dart';
import 'package:maisour/modules/property_details/widgets/guaranteed_rental_banner.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/layouts/blur.dart';

class PropertyBasicDetailsContent extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;
  const PropertyBasicDetailsContent({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).languageCode;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (propertyDetails.propertyFeatures.isNotEmpty &&
            propertyDetails.limitedVisibility == false)
          // Property features/tags row
          PropertyFeatures(
            propertyFeatures: propertyDetails.propertyFeatures,
            languageCode: locale,
          ).paddingOnly(top: 16.w),

        16.h.heightBox,

        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property title
            Text(
              propertyDetails.getTitle(locale),
              style: AppTextStyles.text20.bold.dark900,
            ),
            12.h.heightBox,

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    if (user.usesImageSymbol)
                      Assets.images.dirham
                          .image(width: 28.w, height: 28.w)
                          .paddingEnd(4)
                    else
                      Text(
                        '${user.currencyCode.currencySymbol} ',
                        style: AppTextStyles.text20.bold.dark900,
                      ),
                    Text(
                      user.getCurrencyValue(propertyDetails.totalPrice),
                      style: AppTextStyles.text20.bold.dark900,
                    ),
                  ],
                ),

                Text(
                  '${propertyDetails.funded.toStringAsFixed(0)}% ${LocaleKeys.funded.tr()}',
                  style: AppTextStyles.text14.medium.dark300,
                ),
              ],
            ),
            8.w.heightBox,
            LinearProgressIndicator(
              value: (propertyDetails.funded / 100).clamp(0.0, 1.0),
              minHeight: 6,
              backgroundColor: AppColors.gray.shade100,
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            8.w.heightBox,
            Row(
              children: [
                Assets.icons.users.svg(
                  width: 16.w,
                  height: 16.w,
                  colorFilter: ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                ),
                8.w.widthBox,
                Text(
                  '${propertyDetails.numberOfInvestors} ${LocaleKeys.investors.tr()}',
                  style: AppTextStyles.text14.semiBold.primary,
                ),
              ],
            ),

            24.w.heightBox,
            // Financial details card
            FinancialDetailsCard(
              propertyDetails: propertyDetails,
              user: user,
            ).blurred(
              disable: propertyDetails.limitedVisibility == false,
              visibleChild: user.accountStatus == AccountStatus.registered
                  ? const OnboardInstruction()
                  : SuspendedInstruction(),
            ),

            16.h.heightBox,

            if (propertyDetails.limitedVisibility == false)
              // Guaranteed rental income banner
              const GuaranteedRentalBanner(),
            if (propertyDetails.limitedVisibility == false) 12.h.heightBox,
          ],
        ).paddingSymmetric(horizontal: 16.w),
      ],
    );
  }
}
