import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'bottom_sheets/documents_bottom_sheet.dart';
import 'document_card.dart';

class Documents extends StatelessWidget {
  final PropertyDetails propertyDetails;

  const Documents({super.key, required this.propertyDetails});

  @override
  Widget build(BuildContext context) {
    final documents = propertyDetails.propertyDocuments;

    if (documents.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalDocuments = documents.length;
    final displayDocuments = documents.take(3).toList();
    final remainingCount = totalDocuments - 3;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${LocaleKeys.documents.tr()} ($totalDocuments)',
              style: AppTextStyles.text18.bold.dark900,
            ),
            if (remainingCount > 0)
              Text(
                LocaleKeys.viewAll.tr(),
                style: AppTextStyles.text14.medium.primary,
              ).onTap(() {
                DocumentsBottomSheet.show(
                  context,
                  propertyDetails: propertyDetails,
                );
              }),
          ],
        ),
        12.h.heightBox,
        Column(
          children: displayDocuments
              .map(
                (document) => Padding(
                  padding: EdgeInsets.only(bottom: 8.h),
                  child: DocumentCard(document: document).onTap(
                    () => UrlLauncherService.launchURL(document.documentUrl),
                  ),
                ),
              )
              .toList(),
        ),
      ],
    ).paddingAll(16.w);
  }
}
