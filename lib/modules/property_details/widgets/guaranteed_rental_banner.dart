import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'bottom_sheets/about_rental_income_bottom_sheet.dart';

class GuaranteedRentalBanner extends StatelessWidget {
  const GuaranteedRentalBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
      decoration: BoxDecoration(
        color: AppColors.success.alphaPercent(10),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Assets.icons.successFilled.svg(width: 24.w),
          4.w.widthBox,
          Expanded(
            child: Text(
              LocaleKeys.guaranteedRentalIncome.tr(),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
          ),
          Assets.icons.info.svg(
            width: 16.w,
            height: 16.w,
            colorFilter: ColorFilter.mode(
              AppColors.dark.shade300,
              BlendMode.srcIn,
            ),
          ),
        ],
      ),
    ).onTap(() {
      AboutRentalIncomeBottomSheet.show(context);
    });
  }
}
