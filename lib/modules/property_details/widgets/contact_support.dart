import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class ContactSupport extends StatelessWidget {
  const ContactSupport({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 150.w,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.success.shade100),
        gradient: LinearGradient(
          colors: [
            AppColors.gradientGreen.alphaPercent(20),
            AppColors.gradientDarkGreen.alphaPercent(20),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.doYouHaveQuestionsAboutTheProperty.tr(),
                  style: AppTextStyles.text16.bold.dark900,
                ),
                Spacer(),
                AppButton(
                  text: LocaleKeys.contactSupport.tr(),
                  backgroundColor: AppColors.dark,
                  fontSize: 12.sp,
                  width: 140.w,
                  onPressed: () {
                    GoRouter.of(context).pushNamed(RouteName.getHelp.name);
                  },
                ),
              ],
            ),
          ),
          Assets.icons.supportCall.svg(width: 90.w),
        ],
      ),
    );
  }
}
