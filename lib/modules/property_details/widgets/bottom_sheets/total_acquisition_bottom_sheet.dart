import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

class TotalAcquisitionBottomSheet extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;

  const TotalAcquisitionBottomSheet({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  static Future<void> show(
    BuildContext context,
    PropertyDetails propertyDetails,
    AppUser user,
  ) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => TotalAcquisitionBottomSheet(
        propertyDetails: propertyDetails,
        user: user,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,

      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          LocaleKeys.totalAcquisitionCost.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        12.h.heightBox,
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: AppColors.gray.alphaPercent(5),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            children: [
              16.w.heightBox,
              _buildNumberOfShares(context),
              10.w.heightBox,
              _buildTotalPricePerShare(context),
              16.w.heightBox,
            ],
          ),
        ),

        24.h.heightBox,
      ],
    ).paddingSymmetric(horizontal: 16.w);
  }

  Widget _buildNumberOfShares(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            LocaleKeys.numberOfShares.tr(),
            style: AppTextStyles.text12.medium.gray700,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Flexible(
          child: Text(
            propertyDetails.numberOfShare.toString(),
            style: AppTextStyles.text14.semiBold.dark900,
          ),
        ),
      ],
    );
  }

  Widget _buildTotalPricePerShare(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            LocaleKeys.totalPricePerShare.tr(),
            style: AppTextStyles.text12.medium.gray700,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Flexible(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (user.usesImageSymbol)
                Assets.images.dirham
                    .image(width: 20.w, height: 20.w)
                    .paddingEnd(4)
              else
                Text(
                  '${user.currencyCode.currencySymbol} ',
                  style: AppTextStyles.text14.semiBold.dark900,
                ),
              Text(
                user.getCurrencyValue(
                  propertyDetails.propertyAcquisition /
                      propertyDetails.numberOfShare,
                ),
                style: AppTextStyles.text14.semiBold.dark900,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
