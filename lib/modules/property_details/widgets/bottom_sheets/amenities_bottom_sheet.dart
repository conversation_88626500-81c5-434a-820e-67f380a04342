import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';
import '../amenity_card.dart';

class AmenitiesBottomSheet extends StatelessWidget {
  final PropertyDetails propertyDetails;

  const AmenitiesBottomSheet({super.key, required this.propertyDetails});

  static Future<void> show(
    BuildContext context, {
    required PropertyDetails propertyDetails,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) =>
          AmenitiesBottomSheet(propertyDetails: propertyDetails),
    );
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).languageCode;
    final amenities = propertyDetails.amenities.where((a) => a.active).toList();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          '${LocaleKeys.amenities.tr()} (${amenities.length})',
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ).paddingHorizontal(16.w),
        12.h.heightBox,
        Divider(color: AppColors.gray.shade100, height: 1),
        10.h.heightBox,
        Flexible(
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 12.w,
              runSpacing: 12.h,
              children: amenities
                  .map(
                    (amenity) => AmenityCard(amenity: amenity, locale: locale),
                  )
                  .toList(),
            ).paddingAll(16.w),
          ),
        ),
      ],
    );
  }
}
