import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

class OtherCostsBottomSheet extends StatelessWidget {
  final List<PropertyRentalOtherCost> propertyRentalOtherCosts;
  final AppUser user;
  final double rentAmount;

  const OtherCostsBottomSheet({
    super.key,
    required this.propertyRentalOtherCosts,
    required this.user,
    required this.rentAmount,
  });

  static Future<void> show(
    BuildContext context,
    final List<PropertyRentalOtherCost> propertyRentalOtherCosts,
    AppUser user,
    double rentAmount,
  ) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => OtherCostsBottomSheet(
        propertyRentalOtherCosts: propertyRentalOtherCosts,
        user: user,
        rentAmount: rentAmount,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          LocaleKeys.otherCosts.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        12.h.heightBox,
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: AppColors.gray.alphaPercent(5),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            children: [
              16.w.heightBox,
              ...propertyRentalOtherCosts.map(
                (PropertyRentalOtherCost propertyRentalOtherCost) =>
                    _buildCostCard(context, propertyRentalOtherCost),
              ),
              6.w.heightBox,
            ],
          ),
        ),

        24.h.heightBox,
      ],
    ).paddingSymmetric(horizontal: 16.w);
  }

  Widget _buildCostCard(
    BuildContext context,
    PropertyRentalOtherCost propertyRentalOtherCost,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            '${propertyRentalOtherCost.rentalOtherCostConstant.getName(context.locale.languageCode)}${propertyRentalOtherCost.rentalOtherCostConstant.isPercentage ? ' (${propertyRentalOtherCost.actualValue} %)' : ''}',
            style: AppTextStyles.text12.medium.gray700,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        6.w.widthBox,
        Flexible(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (user.usesImageSymbol)
                Assets.images.dirham
                    .image(width: 20.w, height: 20.w)
                    .paddingEnd(4)
              else
                Text(
                  '${user.currencyCode.currencySymbol} ',
                  style: AppTextStyles.text14.semiBold.dark900,
                ),
              Text(
                propertyRentalOtherCost.rentalOtherCostConstant.isPercentage
                    ? user.getCurrencyValue(
                        (propertyRentalOtherCost.actualValue * rentAmount) /
                            100,
                      )
                    : user.getCurrencyValue(
                        propertyRentalOtherCost.actualValue,
                      ),
                style: AppTextStyles.text14.semiBold.dark900,
              ),
            ],
          ),
        ),
      ],
    ).paddingOnly(bottom: 10.w);
  }
}
