import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'bottom_sheets/purchase_cost_bottom_sheet.dart';
import 'bottom_sheets/other_costs_bottom_sheet.dart';
import 'bottom_sheets/transaction_cost_bottom_sheet.dart';
import 'bottom_sheets/total_acquisition_bottom_sheet.dart';

enum InvestmentBreakdownTab { purchase, rental }

class InvestmentBreakdown extends StatefulWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;

  const InvestmentBreakdown({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  @override
  State<InvestmentBreakdown> createState() => _InvestmentBreakdownState();
}

class _InvestmentBreakdownState extends State<InvestmentBreakdown> {
  InvestmentBreakdownTab selectedTab = InvestmentBreakdownTab.purchase;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          LocaleKeys.investmentBreakdown.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.w.heightBox,

        // Tab Chips
        Row(
          children: [
            _buildTabChip(
              InvestmentBreakdownTab.purchase,
              LocaleKeys.purchase.tr(),
            ),
            8.w.widthBox,
            _buildTabChip(
              InvestmentBreakdownTab.rental,
              LocaleKeys.rental.tr(),
            ),
          ],
        ),
        16.w.heightBox,

        // Content based on selected tab
        _buildContent(),
      ],
    ).paddingSymmetric(horizontal: 16.w, vertical: 16.w);
  }

  Widget _buildTabChip(InvestmentBreakdownTab tab, String label) {
    final isSelected = selectedTab == tab;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 12.w),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.alphaPercent(10)
            : AppColors.white,
        borderRadius: BorderRadius.circular(24.r),
      ),
      child: Text(
        label,
        style: isSelected
            ? AppTextStyles.text14.semiBold.primary
            : AppTextStyles.text14.semiBold.gray600,
      ),
    ).onTap(() {
      setState(() {
        selectedTab = tab;
      });
    });
  }

  Widget _buildContent() {
    switch (selectedTab) {
      case InvestmentBreakdownTab.purchase:
        return _buildPurchaseContent();
      case InvestmentBreakdownTab.rental:
        return _buildRentalContent();
    }
  }

  Widget _buildPurchaseContent() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(5),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Column(
        children: [
          _buildBreakdownRow(
            LocaleKeys.purchaseCost.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.totalPurchaseCost,
            ),
            onTap: () => _showPurchaseCostBreakdown(context),
          ),
          6.w.heightBox,
          _buildBreakdownRow(
            LocaleKeys.transactionCost.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.totalTransactionCost,
            ),
            onTap: () => _showTransactionCostBreakdown(context),
          ),
          6.w.heightBox,
          _buildBreakdownRow(
            LocaleKeys.totalAcquisitionCost.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.propertyAcquisition,
            ),
            onTap: () => _showTotalAcquisitionBreakdown(context),
          ),
        ],
      ),
    );
  }

  Widget _buildRentalContent() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray.shade200),
      ),
      child: Column(
        children: [
          _buildBreakdownRow(
            LocaleKeys.grossRentPerYear.tr(),
            widget.user.getCurrencyValue(widget.propertyDetails.rentAmount),
          ),
          6.w.heightBox,
          _buildBreakdownRow(
            LocaleKeys.annualGrossRent.tr(),
            '${widget.propertyDetails.annualGrossRent.toStringAsFixed(2)}%',
            isPercentage: true,
          ),
          6.w.heightBox,
          _buildBreakdownRow(
            LocaleKeys.expectedAnnualNetRent.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.expectedAnnualNetRent,
            ),
          ),
          6.h.heightBox,
          _buildBreakdownRow(
            '${LocaleKeys.expectedAnnualNetRent.tr()} %',
            '${widget.propertyDetails.expectedAnnualNetRentPercentage}%',
            isPercentage: true,
          ),
          6.h.heightBox,
          _buildBreakdownRow(
            LocaleKeys.serviceCharges.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.propertyManagementFee,
            ),
          ),
          6.h.heightBox,
          _buildBreakdownRow(
            LocaleKeys.otherCosts.tr(),
            widget.user.getCurrencyValue(
              widget.propertyDetails.totalRentalOtherCost,
            ),
            onTap: () => _showOtherCostsBreakdown(context),
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownRow(
    String label,
    String value, {
    bool isPercentage = false,
    VoidCallback? onTap,
  }) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Text(label, style: AppTextStyles.text12.medium.gray700),
              if (onTap != null) ...[
                4.w.widthBox,
                Icon(Icons.info, size: 16.w, color: AppColors.gray.shade300),
              ],
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (!isPercentage) _buildCurrencyIcon(),
            Text(value, style: AppTextStyles.text14.semiBold.dark900),
          ],
        ),
      ],
    ).onTap(() {
      if (onTap != null) {
        onTap();
      }
    });
  }

  Widget _buildCurrencyIcon() {
    if (widget.user.usesImageSymbol) {
      return Assets.images.dirham
          .image(width: 20.w, height: 20.w)
          .paddingEnd(4);
    } else {
      return Text(
        '${widget.user.currencyCode.currencySymbol} ',
        style: AppTextStyles.text14.semiBold.dark900,
      );
    }
  }

  void _showPurchaseCostBreakdown(BuildContext context) {
    PurchaseCostBottomSheet.show(
      context,
      widget.propertyDetails.propertyPurchaseCosts,
      widget.user,
      widget.propertyDetails.totalPrice,
    );
  }

  void _showTransactionCostBreakdown(BuildContext context) {
    TransactionCostBottomSheet.show(
      context,
      widget.propertyDetails.propertyTransactionCosts,
      widget.user,
      widget.propertyDetails.totalPrice,
    );
  }

  void _showTotalAcquisitionBreakdown(BuildContext context) {
    TotalAcquisitionBottomSheet.show(
      context,
      widget.propertyDetails,
      widget.user,
    );
  }

  void _showOtherCostsBreakdown(BuildContext context) {
    OtherCostsBottomSheet.show(
      context,
      widget.propertyDetails.propertyRentalOtherCosts,
      widget.user,
      widget.propertyDetails.rentAmount,
    );
  }
}
