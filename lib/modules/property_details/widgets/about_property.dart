import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'bottom_sheets/about_property_bottom_sheet.dart';

class AboutProperty extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;

  const AboutProperty({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).languageCode;
    final aboutText = propertyDetails.aboutProperty(locale);

    // Split text into lines and limit to 5 lines
    final lines = aboutText.split('\n');
    final isTextLong = lines.length > 5 || aboutText.length > 300;

    String displayText;
    if (isTextLong) {
      // Take first 5 lines or first 300 characters, whichever comes first
      final limitedLines = lines.take(5).join('\n');
      if (limitedLines.length > 300) {
        displayText = aboutText.substring(0, 300).trim();
        // Find the last complete word
        final lastSpaceIndex = displayText.lastIndexOf(' ');
        if (lastSpaceIndex > 0) {
          displayText = displayText.substring(0, lastSpaceIndex);
        }
      } else {
        displayText = limitedLines;
      }
    } else {
      displayText = aboutText;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.aboutProperty.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        8.h.heightBox,
        Text(
          isTextLong ? '$displayText...' : displayText,
          style: AppTextStyles.text14.dark900,
        ),
        if (isTextLong) ...[
          8.h.heightBox,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                LocaleKeys.readMore.tr(),
                style: AppTextStyles.text16.semiBold.primary,
              ),
              4.w.widthBox,
              Icon(Icons.arrow_forward, size: 18.w, color: AppColors.primary),
            ],
          ).onTap(() {
            AboutPropertyBottomSheet.show(
              context,
              propertyDetails: propertyDetails,
            );
          }),
        ],
      ],
    ).paddingAll(16.w);
  }
}
