import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_manager.dart';
import 'package:maisour/modules/property_details/models/seller_information.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class DeveloperAndManager extends StatelessWidget {
  final SellerInformation? sellerInformation;
  final PropertyManager? propertyManager;
  const DeveloperAndManager({
    super.key,
    this.sellerInformation,
    this.propertyManager,
  });

  @override
  Widget build(BuildContext context) {
    final locale = context.locale.languageCode;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.developerAndManager.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.w.heightBox,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (sellerInformation != null)
              _buildCard(
                context,
                sellerInformation!.imageUrl,
                sellerInformation!.getName(locale),
                sellerInformation!.getAbout(locale),
              ),
            if (propertyManager != null)
              _buildCard(
                context,
                propertyManager!.imageUrl,
                propertyManager!.getName(locale),
                propertyManager!.getAbout(locale),
              ).paddingOnly(top: 16.w),
          ],
        ),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildCard(
    BuildContext context,
    String imageUrl,
    String name,
    String about,
  ) {
    final isSvg = imageUrl.toLowerCase().contains('.svg');

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 16.w),
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(5),
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(40.r),
              border: Border.all(color: AppColors.gray.shade200),
            ),
            child: ClipOval(
              child: isSvg
                  ? SvgPicture.network(
                      imageUrl,
                      width: 48.w,
                      height: 48.w,
                      fit: BoxFit.cover,
                    )
                  : CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 48.w,
                      height: 48.w,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Icon(
                        Icons.image,
                        size: 20.w,
                        color: AppColors.gray.shade400,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        Icons.image,
                        size: 20.w,
                        color: AppColors.gray.shade400,
                      ),
                    ),
            ),
          ),
          12.w.widthBox,
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: AppTextStyles.text14.bold.dark900),
                4.w.heightBox,
                Text(about, style: AppTextStyles.text12.regular.dark900),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
