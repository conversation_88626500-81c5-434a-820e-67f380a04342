import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/map_launcher_service.dart';

class MapView extends StatelessWidget {
  final PropertyDetails propertyDetails;

  const MapView({super.key, required this.propertyDetails});

  @override
  Widget build(BuildContext context) {
    final coordinates = _parseLocation(propertyDetails.mapLocation);
    final locale = context.locale.languageCode;

    if (coordinates == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.exploreOnMap.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        12.h.heightBox,
        SizedBox(
          height: 250.h,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: coordinates,
                    zoom: 16.0,
                  ),
                  markers: {
                    Marker(
                      markerId: MarkerId('property_${propertyDetails.id}'),
                      position: coordinates,
                      icon: BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueRed,
                      ),
                      infoWindow: InfoWindow(
                        title: propertyDetails.getTitle(locale),
                        snippet: propertyDetails.getAddress(locale),
                      ),
                    ),
                  },
                  myLocationEnabled: false,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                  mapToolbarEnabled: false,
                  compassEnabled: true,
                  mapType: MapType.normal,
                ),
                // Custom navigation button in top right corner
                Positioned(
                  top: 16.h,
                  right: 16.w,
                  child: Stack(
                    alignment: AlignmentDirectional.center,
                    children: [
                      Container(
                        width: 40.w,
                        height: 40.w,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                      ),
                      Assets.icons.navigation.svg(
                        width: 20.w,
                        height: 20.w,
                        colorFilter: ColorFilter.mode(
                          AppColors.dark.shade900,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ).onTap(() => _launchExternalMap(context, coordinates)),
                ),
              ],
            ),
          ),
        ),
      ],
    ).paddingAll(16.w);
  }

  LatLng? _parseLocation(String mapLocation) {
    try {
      // Handle different location formats
      if (mapLocation.isEmpty) return null;

      // Format: "latitude,longitude" (e.g., "25.2048,55.2708")
      if (mapLocation.contains(',')) {
        final parts = mapLocation.split(',');
        if (parts.length == 2) {
          final lat = double.tryParse(parts[0].trim());
          final lng = double.tryParse(parts[1].trim());
          if (lat != null && lng != null) {
            return LatLng(lat, lng);
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error parsing location: $e');
      return null;
    }
  }

  void _launchExternalMap(BuildContext context, LatLng coordinates) {
    final locale = context.locale.languageCode;
    MapLauncherService.launchMap(
      context: context,
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
      title: propertyDetails.getTitle(locale),
      address: propertyDetails.getAddress(locale),
    );
  }
}
