import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';

class PropertyDetailsShimmerLoading extends StatelessWidget {
  const PropertyDetailsShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property title shimmer
          Container(
            height: 24.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ).applyShimmer(),
          16.h.heightBox,
          // Property description shimmer
          Container(
            height: 16.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ).applyShimmer(),
          8.h.heightBox,
          Container(
            height: 16.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ).applyShimmer(),
          24.h.heightBox,
          // Property details shimmer
          Container(
            height: 120.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ).applyShimmer(),
          16.h.heightBox,
          Container(
            height: 120.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ).applyShimmer(),
        ],
      ),
    );
  }
}
