import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_document.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class DocumentCard extends StatelessWidget {
  final PropertyDocument document;

  const DocumentCard({super.key, required this.document});

  @override
  Widget build(BuildContext context) {
    final name = document.name;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.gray.shade200),
      ),
      child: Row(
        children: [
          // Document icon
          _buildDocumentIcon(document.documentUrl),
          12.w.widthBox,
          // Document name
          Expanded(
            child: Text(
              name,
              style: AppTextStyles.text14.semiBold.dark900,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Chevron icon
          Icon(Icons.chevron_right, size: 24.w, color: AppColors.gray),
        ],
      ),
    );
  }

  Widget _buildDocumentIcon(String url) {
    final fileExtension = _getFileExtension(url);

    switch (fileExtension) {
      case 'pdf':
        return Assets.icons.pdf.svg();
      case 'doc':
        return Assets.icons.doc.svg();
      case 'xls':
        return Assets.icons.excel.svg();
      default:
        return Assets.icons.doc.svg();
    }
  }

  String _getFileExtension(String url) {
    final uri = Uri.parse(url);
    final path = uri.path;
    final extension = path.split('.').last.toLowerCase();

    // Handle common file extensions
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'xls':
      case 'xlsx':
        return 'xls';
      default:
        return 'doc';
    }
  }
}
