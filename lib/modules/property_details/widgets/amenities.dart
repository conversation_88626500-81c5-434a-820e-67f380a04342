import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'bottom_sheets/amenities_bottom_sheet.dart';
import 'amenity_card.dart';

class Amenities extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;

  const Amenities({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context).languageCode;
    final amenities = propertyDetails.amenities.where((a) => a.active).toList();

    if (amenities.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalAmenities = amenities.length;
    final displayAmenities = amenities.take(5).toList();
    final remainingCount = totalAmenities - 5;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${LocaleKeys.amenities.tr()} ($totalAmenities)',
          style: AppTextStyles.text18.bold.dark900,
        ),
        12.h.heightBox,
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: [
            ...displayAmenities.map(
              (amenity) => AmenityCard(amenity: amenity, locale: locale),
            ),
            if (remainingCount > 0) _buildViewAllCard(context, remainingCount),
          ],
        ),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildViewAllCard(BuildContext context, int remainingCount) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth =
        (screenWidth - 32.w - 24.w) / 3; // Account for padding and spacing

    return Container(
      width: cardWidth,
      height: 100.h, // Fixed height for consistency
      decoration: BoxDecoration(
        color: AppColors.primary.alphaPercent(5),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.primary.shade100),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('+$remainingCount', style: AppTextStyles.text20.bold.primary),
          4.h.heightBox,
          Text(
            LocaleKeys.viewAll.tr(),
            style: AppTextStyles.text12.bold.primary,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).onTap(() {
      AmenitiesBottomSheet.show(context, propertyDetails: propertyDetails);
    });
  }
}
