import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/divided_frequency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/models/app_user.dart';

class FinancialDetailsCard extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;

  const FinancialDetailsCard({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(5),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _annualGrossRent(),
          6.w.heightBox,
          _minInvestment(),
          6.w.heightBox,
          _rentDistribution(),
          6.w.heightBox,
          _suggestedHoldingPeriod(),
        ],
      ),
    );
  }

  Row _annualGrossRent() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.annualGrossRent.tr(),
          style: AppTextStyles.text12.medium.gray700,
        ),
        Row(
          children: [
            if (user.usesImageSymbol)
              Assets.images.dirham
                  .image(width: 20.w, height: 20.w)
                  .paddingEnd(4)
            else
              Text(
                '${user.currencyCode.currencySymbol} ',
                style: AppTextStyles.text14.bold.gray900,
              ),
            Text(
              user.getCurrencyValue(propertyDetails.rentAmount),
              style: AppTextStyles.text14.semiBold.gray900,
            ),
          ],
        ),
      ],
    );
  }

  Row _minInvestment() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.minInvestment.tr(),
          style: AppTextStyles.text12.medium.gray700,
        ),
        Row(
          children: [
            if (user.usesImageSymbol)
              Assets.images.dirham
                  .image(width: 20.w, height: 20.w)
                  .paddingEnd(4)
            else
              Text(
                '${user.currencyCode.currencySymbol} ',
                style: AppTextStyles.text14.bold.gray900,
              ),
            Text(
              user.getCurrencyValue(propertyDetails.minInvestmentAmount),
              style: AppTextStyles.text14.semiBold.gray900,
            ),
          ],
        ),
      ],
    );
  }

  Row _rentDistribution() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.rentDistribution.tr(),
          style: AppTextStyles.text12.medium.gray700,
        ),
        Text(
          propertyDetails.dividedFrequency.displayName,
          style: AppTextStyles.text14.semiBold.gray900,
        ),
      ],
    );
  }

  Row _suggestedHoldingPeriod() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.suggestedHoldingPeriod.tr(),
          style: AppTextStyles.text12.medium.gray700,
        ),
        Text(
          '${propertyDetails.suggestedHoldingPeroid} ${LocaleKeys.years.tr()}',
          style: AppTextStyles.text14.semiBold.gray900,
        ),
      ],
    );
  }
}
