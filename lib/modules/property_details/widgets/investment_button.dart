import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class InvestmentButton extends StatelessWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;
  final int propertyId;
  const InvestmentButton({
    super.key,
    required this.propertyDetails,
    required this.user,
    required this.propertyId,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AppButton(
          text: propertyDetails.propertyStatus == PropertyStatus.live
              ? LocaleKeys.investNow.tr()
              : LocaleKeys.preOrderNow.tr(),
          onPressed: () {
            if (user.phoneNumber == null) {
              GoRouter.of(context).pushNamed(RouteName.phoneVerification.name);
            } else {
              GoRouter.of(context).pushNamed(
                RouteName.propertyInvestment.name,
                extra: propertyDetails,
              );
            }
          },
        ),
        if (propertyDetails.propertyStatus == PropertyStatus.comingSoon)
          Text(
            LocaleKeys.noPaymentYet.tr(),
            style: AppTextStyles.text12.medium.dark300,
          ).paddingOnly(top: 8.w),
      ],
    );
  }
}
