import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import '../models/property_details.dart';

part 'property_details_api.g.dart';

@RestApi()
abstract class PropertyDetailsApi {
  factory PropertyDetailsApi(Dio dio, {String? baseUrl}) = _PropertyDetailsApi;

  @GET('${ApiEndpoints.propertyDetails}/{propertyId}')
  Future<PropertyDetails> getPropertyDetails(
    @Path('propertyId') int propertyId,
  );
}
