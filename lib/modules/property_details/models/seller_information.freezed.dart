// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_information.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SellerInformation {

 int get id; String get nameInEnglish; String get nameInArabic; String get imageUrl; String get aboutInEnglish; String get aboutInArabic; bool get active;
/// Create a copy of SellerInformation
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SellerInformationCopyWith<SellerInformation> get copyWith => _$SellerInformationCopyWithImpl<SellerInformation>(this as SellerInformation, _$identity);

  /// Serializes this SellerInformation to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SellerInformation&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.aboutInEnglish, aboutInEnglish) || other.aboutInEnglish == aboutInEnglish)&&(identical(other.aboutInArabic, aboutInArabic) || other.aboutInArabic == aboutInArabic)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,imageUrl,aboutInEnglish,aboutInArabic,active);

@override
String toString() {
  return 'SellerInformation(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, imageUrl: $imageUrl, aboutInEnglish: $aboutInEnglish, aboutInArabic: $aboutInArabic, active: $active)';
}


}

/// @nodoc
abstract mixin class $SellerInformationCopyWith<$Res>  {
  factory $SellerInformationCopyWith(SellerInformation value, $Res Function(SellerInformation) _then) = _$SellerInformationCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, String imageUrl, String aboutInEnglish, String aboutInArabic, bool active
});




}
/// @nodoc
class _$SellerInformationCopyWithImpl<$Res>
    implements $SellerInformationCopyWith<$Res> {
  _$SellerInformationCopyWithImpl(this._self, this._then);

  final SellerInformation _self;
  final $Res Function(SellerInformation) _then;

/// Create a copy of SellerInformation
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? imageUrl = null,Object? aboutInEnglish = null,Object? aboutInArabic = null,Object? active = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,aboutInEnglish: null == aboutInEnglish ? _self.aboutInEnglish : aboutInEnglish // ignore: cast_nullable_to_non_nullable
as String,aboutInArabic: null == aboutInArabic ? _self.aboutInArabic : aboutInArabic // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [SellerInformation].
extension SellerInformationPatterns on SellerInformation {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SellerInformation value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SellerInformation() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SellerInformation value)  $default,){
final _that = this;
switch (_that) {
case _SellerInformation():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SellerInformation value)?  $default,){
final _that = this;
switch (_that) {
case _SellerInformation() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  String imageUrl,  String aboutInEnglish,  String aboutInArabic,  bool active)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SellerInformation() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.imageUrl,_that.aboutInEnglish,_that.aboutInArabic,_that.active);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  String imageUrl,  String aboutInEnglish,  String aboutInArabic,  bool active)  $default,) {final _that = this;
switch (_that) {
case _SellerInformation():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.imageUrl,_that.aboutInEnglish,_that.aboutInArabic,_that.active);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  String imageUrl,  String aboutInEnglish,  String aboutInArabic,  bool active)?  $default,) {final _that = this;
switch (_that) {
case _SellerInformation() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.imageUrl,_that.aboutInEnglish,_that.aboutInArabic,_that.active);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SellerInformation implements SellerInformation {
  const _SellerInformation({required this.id, required this.nameInEnglish, required this.nameInArabic, required this.imageUrl, required this.aboutInEnglish, required this.aboutInArabic, required this.active});
  factory _SellerInformation.fromJson(Map<String, dynamic> json) => _$SellerInformationFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override final  String imageUrl;
@override final  String aboutInEnglish;
@override final  String aboutInArabic;
@override final  bool active;

/// Create a copy of SellerInformation
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SellerInformationCopyWith<_SellerInformation> get copyWith => __$SellerInformationCopyWithImpl<_SellerInformation>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SellerInformationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SellerInformation&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.aboutInEnglish, aboutInEnglish) || other.aboutInEnglish == aboutInEnglish)&&(identical(other.aboutInArabic, aboutInArabic) || other.aboutInArabic == aboutInArabic)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,imageUrl,aboutInEnglish,aboutInArabic,active);

@override
String toString() {
  return 'SellerInformation(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, imageUrl: $imageUrl, aboutInEnglish: $aboutInEnglish, aboutInArabic: $aboutInArabic, active: $active)';
}


}

/// @nodoc
abstract mixin class _$SellerInformationCopyWith<$Res> implements $SellerInformationCopyWith<$Res> {
  factory _$SellerInformationCopyWith(_SellerInformation value, $Res Function(_SellerInformation) _then) = __$SellerInformationCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, String imageUrl, String aboutInEnglish, String aboutInArabic, bool active
});




}
/// @nodoc
class __$SellerInformationCopyWithImpl<$Res>
    implements _$SellerInformationCopyWith<$Res> {
  __$SellerInformationCopyWithImpl(this._self, this._then);

  final _SellerInformation _self;
  final $Res Function(_SellerInformation) _then;

/// Create a copy of SellerInformation
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? imageUrl = null,Object? aboutInEnglish = null,Object? aboutInArabic = null,Object? active = null,}) {
  return _then(_SellerInformation(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,aboutInEnglish: null == aboutInEnglish ? _self.aboutInEnglish : aboutInEnglish // ignore: cast_nullable_to_non_nullable
as String,aboutInArabic: null == aboutInArabic ? _self.aboutInArabic : aboutInArabic // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
