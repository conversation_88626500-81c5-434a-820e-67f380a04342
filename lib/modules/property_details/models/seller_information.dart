import 'package:freezed_annotation/freezed_annotation.dart';

part 'seller_information.freezed.dart';
part 'seller_information.g.dart';

@freezed
abstract class SellerInformation with _$SellerInformation {
  const factory SellerInformation({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required String imageUrl,
    required String aboutInEnglish,
    required String aboutInArabic,
    required bool active,
  }) = _SellerInformation;

  factory SellerInformation.fromJson(Map<String, dynamic> json) =>
      _$SellerInformationFromJson(json);
}
