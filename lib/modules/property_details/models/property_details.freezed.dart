// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'property_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PropertyDetails {

 int get id; String get code; String get titleInEnglish; String get titleInArabic; String get cityInEnglish; String get cityInArabic; String get countryInEnglish; String get countryInArabic; String get addressInEnglish; String get addressInArabic; String get aboutInEnglish; String get aboutInArabic; String get investTextInEnglish; String get investTextInArabic; String get suggestedHoldingPeroid; double get minInvestmentAmount; double get rentAmount; double get fiveYearExpectedReturn; DividedFrequency get dividedFrequency; int get numberOfShare; double get totalPrice; String get endDate; String get purchaseDate; String get mapLocation; double get propertyManagementFee; PropertyStatus get propertyStatus; bool get limitedVisibility; List<PropertyTransactionCost> get propertyTransactionCosts; List<PropertyPurchaseCost> get propertyPurchaseCosts; List<PropertyRentalOtherCost> get propertyRentalOtherCosts; List<PropertyImage> get propertyImages; List<PropertyFeature> get propertyFeatures; List<PropertyDocument> get propertyDocuments; List<PropertyAmenity> get amenities; SellerInformation? get sellerInformation; PropertyManager? get propertyManager; int get remainingPeriod; int get numberOfInvestors; double get propertyPricePerShare; double get propertyAcquisition; double get totalPurchaseCost; double get totalTransactionCost; double get totalRentalOtherCost; double get expectedAnnualAppreciation; bool get featured; double get totalSuccessfulInvestment;
/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyDetailsCopyWith<PropertyDetails> get copyWith => _$PropertyDetailsCopyWithImpl<PropertyDetails>(this as PropertyDetails, _$identity);

  /// Serializes this PropertyDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.addressInEnglish, addressInEnglish) || other.addressInEnglish == addressInEnglish)&&(identical(other.addressInArabic, addressInArabic) || other.addressInArabic == addressInArabic)&&(identical(other.aboutInEnglish, aboutInEnglish) || other.aboutInEnglish == aboutInEnglish)&&(identical(other.aboutInArabic, aboutInArabic) || other.aboutInArabic == aboutInArabic)&&(identical(other.investTextInEnglish, investTextInEnglish) || other.investTextInEnglish == investTextInEnglish)&&(identical(other.investTextInArabic, investTextInArabic) || other.investTextInArabic == investTextInArabic)&&(identical(other.suggestedHoldingPeroid, suggestedHoldingPeroid) || other.suggestedHoldingPeroid == suggestedHoldingPeroid)&&(identical(other.minInvestmentAmount, minInvestmentAmount) || other.minInvestmentAmount == minInvestmentAmount)&&(identical(other.rentAmount, rentAmount) || other.rentAmount == rentAmount)&&(identical(other.fiveYearExpectedReturn, fiveYearExpectedReturn) || other.fiveYearExpectedReturn == fiveYearExpectedReturn)&&(identical(other.dividedFrequency, dividedFrequency) || other.dividedFrequency == dividedFrequency)&&(identical(other.numberOfShare, numberOfShare) || other.numberOfShare == numberOfShare)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.mapLocation, mapLocation) || other.mapLocation == mapLocation)&&(identical(other.propertyManagementFee, propertyManagementFee) || other.propertyManagementFee == propertyManagementFee)&&(identical(other.propertyStatus, propertyStatus) || other.propertyStatus == propertyStatus)&&(identical(other.limitedVisibility, limitedVisibility) || other.limitedVisibility == limitedVisibility)&&const DeepCollectionEquality().equals(other.propertyTransactionCosts, propertyTransactionCosts)&&const DeepCollectionEquality().equals(other.propertyPurchaseCosts, propertyPurchaseCosts)&&const DeepCollectionEquality().equals(other.propertyRentalOtherCosts, propertyRentalOtherCosts)&&const DeepCollectionEquality().equals(other.propertyImages, propertyImages)&&const DeepCollectionEquality().equals(other.propertyFeatures, propertyFeatures)&&const DeepCollectionEquality().equals(other.propertyDocuments, propertyDocuments)&&const DeepCollectionEquality().equals(other.amenities, amenities)&&(identical(other.sellerInformation, sellerInformation) || other.sellerInformation == sellerInformation)&&(identical(other.propertyManager, propertyManager) || other.propertyManager == propertyManager)&&(identical(other.remainingPeriod, remainingPeriod) || other.remainingPeriod == remainingPeriod)&&(identical(other.numberOfInvestors, numberOfInvestors) || other.numberOfInvestors == numberOfInvestors)&&(identical(other.propertyPricePerShare, propertyPricePerShare) || other.propertyPricePerShare == propertyPricePerShare)&&(identical(other.propertyAcquisition, propertyAcquisition) || other.propertyAcquisition == propertyAcquisition)&&(identical(other.totalPurchaseCost, totalPurchaseCost) || other.totalPurchaseCost == totalPurchaseCost)&&(identical(other.totalTransactionCost, totalTransactionCost) || other.totalTransactionCost == totalTransactionCost)&&(identical(other.totalRentalOtherCost, totalRentalOtherCost) || other.totalRentalOtherCost == totalRentalOtherCost)&&(identical(other.expectedAnnualAppreciation, expectedAnnualAppreciation) || other.expectedAnnualAppreciation == expectedAnnualAppreciation)&&(identical(other.featured, featured) || other.featured == featured)&&(identical(other.totalSuccessfulInvestment, totalSuccessfulInvestment) || other.totalSuccessfulInvestment == totalSuccessfulInvestment));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,code,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,addressInEnglish,addressInArabic,aboutInEnglish,aboutInArabic,investTextInEnglish,investTextInArabic,suggestedHoldingPeroid,minInvestmentAmount,rentAmount,fiveYearExpectedReturn,dividedFrequency,numberOfShare,totalPrice,endDate,purchaseDate,mapLocation,propertyManagementFee,propertyStatus,limitedVisibility,const DeepCollectionEquality().hash(propertyTransactionCosts),const DeepCollectionEquality().hash(propertyPurchaseCosts),const DeepCollectionEquality().hash(propertyRentalOtherCosts),const DeepCollectionEquality().hash(propertyImages),const DeepCollectionEquality().hash(propertyFeatures),const DeepCollectionEquality().hash(propertyDocuments),const DeepCollectionEquality().hash(amenities),sellerInformation,propertyManager,remainingPeriod,numberOfInvestors,propertyPricePerShare,propertyAcquisition,totalPurchaseCost,totalTransactionCost,totalRentalOtherCost,expectedAnnualAppreciation,featured,totalSuccessfulInvestment]);

@override
String toString() {
  return 'PropertyDetails(id: $id, code: $code, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, addressInEnglish: $addressInEnglish, addressInArabic: $addressInArabic, aboutInEnglish: $aboutInEnglish, aboutInArabic: $aboutInArabic, investTextInEnglish: $investTextInEnglish, investTextInArabic: $investTextInArabic, suggestedHoldingPeroid: $suggestedHoldingPeroid, minInvestmentAmount: $minInvestmentAmount, rentAmount: $rentAmount, fiveYearExpectedReturn: $fiveYearExpectedReturn, dividedFrequency: $dividedFrequency, numberOfShare: $numberOfShare, totalPrice: $totalPrice, endDate: $endDate, purchaseDate: $purchaseDate, mapLocation: $mapLocation, propertyManagementFee: $propertyManagementFee, propertyStatus: $propertyStatus, limitedVisibility: $limitedVisibility, propertyTransactionCosts: $propertyTransactionCosts, propertyPurchaseCosts: $propertyPurchaseCosts, propertyRentalOtherCosts: $propertyRentalOtherCosts, propertyImages: $propertyImages, propertyFeatures: $propertyFeatures, propertyDocuments: $propertyDocuments, amenities: $amenities, sellerInformation: $sellerInformation, propertyManager: $propertyManager, remainingPeriod: $remainingPeriod, numberOfInvestors: $numberOfInvestors, propertyPricePerShare: $propertyPricePerShare, propertyAcquisition: $propertyAcquisition, totalPurchaseCost: $totalPurchaseCost, totalTransactionCost: $totalTransactionCost, totalRentalOtherCost: $totalRentalOtherCost, expectedAnnualAppreciation: $expectedAnnualAppreciation, featured: $featured, totalSuccessfulInvestment: $totalSuccessfulInvestment)';
}


}

/// @nodoc
abstract mixin class $PropertyDetailsCopyWith<$Res>  {
  factory $PropertyDetailsCopyWith(PropertyDetails value, $Res Function(PropertyDetails) _then) = _$PropertyDetailsCopyWithImpl;
@useResult
$Res call({
 int id, String code, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, String addressInEnglish, String addressInArabic, String aboutInEnglish, String aboutInArabic, String investTextInEnglish, String investTextInArabic, String suggestedHoldingPeroid, double minInvestmentAmount, double rentAmount, double fiveYearExpectedReturn, DividedFrequency dividedFrequency, int numberOfShare, double totalPrice, String endDate, String purchaseDate, String mapLocation, double propertyManagementFee, PropertyStatus propertyStatus, bool limitedVisibility, List<PropertyTransactionCost> propertyTransactionCosts, List<PropertyPurchaseCost> propertyPurchaseCosts, List<PropertyRentalOtherCost> propertyRentalOtherCosts, List<PropertyImage> propertyImages, List<PropertyFeature> propertyFeatures, List<PropertyDocument> propertyDocuments, List<PropertyAmenity> amenities, SellerInformation? sellerInformation, PropertyManager? propertyManager, int remainingPeriod, int numberOfInvestors, double propertyPricePerShare, double propertyAcquisition, double totalPurchaseCost, double totalTransactionCost, double totalRentalOtherCost, double expectedAnnualAppreciation, bool featured, double totalSuccessfulInvestment
});


$SellerInformationCopyWith<$Res>? get sellerInformation;$PropertyManagerCopyWith<$Res>? get propertyManager;

}
/// @nodoc
class _$PropertyDetailsCopyWithImpl<$Res>
    implements $PropertyDetailsCopyWith<$Res> {
  _$PropertyDetailsCopyWithImpl(this._self, this._then);

  final PropertyDetails _self;
  final $Res Function(PropertyDetails) _then;

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? code = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? addressInEnglish = null,Object? addressInArabic = null,Object? aboutInEnglish = null,Object? aboutInArabic = null,Object? investTextInEnglish = null,Object? investTextInArabic = null,Object? suggestedHoldingPeroid = null,Object? minInvestmentAmount = null,Object? rentAmount = null,Object? fiveYearExpectedReturn = null,Object? dividedFrequency = null,Object? numberOfShare = null,Object? totalPrice = null,Object? endDate = null,Object? purchaseDate = null,Object? mapLocation = null,Object? propertyManagementFee = null,Object? propertyStatus = null,Object? limitedVisibility = null,Object? propertyTransactionCosts = null,Object? propertyPurchaseCosts = null,Object? propertyRentalOtherCosts = null,Object? propertyImages = null,Object? propertyFeatures = null,Object? propertyDocuments = null,Object? amenities = null,Object? sellerInformation = freezed,Object? propertyManager = freezed,Object? remainingPeriod = null,Object? numberOfInvestors = null,Object? propertyPricePerShare = null,Object? propertyAcquisition = null,Object? totalPurchaseCost = null,Object? totalTransactionCost = null,Object? totalRentalOtherCost = null,Object? expectedAnnualAppreciation = null,Object? featured = null,Object? totalSuccessfulInvestment = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,addressInEnglish: null == addressInEnglish ? _self.addressInEnglish : addressInEnglish // ignore: cast_nullable_to_non_nullable
as String,addressInArabic: null == addressInArabic ? _self.addressInArabic : addressInArabic // ignore: cast_nullable_to_non_nullable
as String,aboutInEnglish: null == aboutInEnglish ? _self.aboutInEnglish : aboutInEnglish // ignore: cast_nullable_to_non_nullable
as String,aboutInArabic: null == aboutInArabic ? _self.aboutInArabic : aboutInArabic // ignore: cast_nullable_to_non_nullable
as String,investTextInEnglish: null == investTextInEnglish ? _self.investTextInEnglish : investTextInEnglish // ignore: cast_nullable_to_non_nullable
as String,investTextInArabic: null == investTextInArabic ? _self.investTextInArabic : investTextInArabic // ignore: cast_nullable_to_non_nullable
as String,suggestedHoldingPeroid: null == suggestedHoldingPeroid ? _self.suggestedHoldingPeroid : suggestedHoldingPeroid // ignore: cast_nullable_to_non_nullable
as String,minInvestmentAmount: null == minInvestmentAmount ? _self.minInvestmentAmount : minInvestmentAmount // ignore: cast_nullable_to_non_nullable
as double,rentAmount: null == rentAmount ? _self.rentAmount : rentAmount // ignore: cast_nullable_to_non_nullable
as double,fiveYearExpectedReturn: null == fiveYearExpectedReturn ? _self.fiveYearExpectedReturn : fiveYearExpectedReturn // ignore: cast_nullable_to_non_nullable
as double,dividedFrequency: null == dividedFrequency ? _self.dividedFrequency : dividedFrequency // ignore: cast_nullable_to_non_nullable
as DividedFrequency,numberOfShare: null == numberOfShare ? _self.numberOfShare : numberOfShare // ignore: cast_nullable_to_non_nullable
as int,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: null == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as String,mapLocation: null == mapLocation ? _self.mapLocation : mapLocation // ignore: cast_nullable_to_non_nullable
as String,propertyManagementFee: null == propertyManagementFee ? _self.propertyManagementFee : propertyManagementFee // ignore: cast_nullable_to_non_nullable
as double,propertyStatus: null == propertyStatus ? _self.propertyStatus : propertyStatus // ignore: cast_nullable_to_non_nullable
as PropertyStatus,limitedVisibility: null == limitedVisibility ? _self.limitedVisibility : limitedVisibility // ignore: cast_nullable_to_non_nullable
as bool,propertyTransactionCosts: null == propertyTransactionCosts ? _self.propertyTransactionCosts : propertyTransactionCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyTransactionCost>,propertyPurchaseCosts: null == propertyPurchaseCosts ? _self.propertyPurchaseCosts : propertyPurchaseCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyPurchaseCost>,propertyRentalOtherCosts: null == propertyRentalOtherCosts ? _self.propertyRentalOtherCosts : propertyRentalOtherCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyRentalOtherCost>,propertyImages: null == propertyImages ? _self.propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,propertyFeatures: null == propertyFeatures ? _self.propertyFeatures : propertyFeatures // ignore: cast_nullable_to_non_nullable
as List<PropertyFeature>,propertyDocuments: null == propertyDocuments ? _self.propertyDocuments : propertyDocuments // ignore: cast_nullable_to_non_nullable
as List<PropertyDocument>,amenities: null == amenities ? _self.amenities : amenities // ignore: cast_nullable_to_non_nullable
as List<PropertyAmenity>,sellerInformation: freezed == sellerInformation ? _self.sellerInformation : sellerInformation // ignore: cast_nullable_to_non_nullable
as SellerInformation?,propertyManager: freezed == propertyManager ? _self.propertyManager : propertyManager // ignore: cast_nullable_to_non_nullable
as PropertyManager?,remainingPeriod: null == remainingPeriod ? _self.remainingPeriod : remainingPeriod // ignore: cast_nullable_to_non_nullable
as int,numberOfInvestors: null == numberOfInvestors ? _self.numberOfInvestors : numberOfInvestors // ignore: cast_nullable_to_non_nullable
as int,propertyPricePerShare: null == propertyPricePerShare ? _self.propertyPricePerShare : propertyPricePerShare // ignore: cast_nullable_to_non_nullable
as double,propertyAcquisition: null == propertyAcquisition ? _self.propertyAcquisition : propertyAcquisition // ignore: cast_nullable_to_non_nullable
as double,totalPurchaseCost: null == totalPurchaseCost ? _self.totalPurchaseCost : totalPurchaseCost // ignore: cast_nullable_to_non_nullable
as double,totalTransactionCost: null == totalTransactionCost ? _self.totalTransactionCost : totalTransactionCost // ignore: cast_nullable_to_non_nullable
as double,totalRentalOtherCost: null == totalRentalOtherCost ? _self.totalRentalOtherCost : totalRentalOtherCost // ignore: cast_nullable_to_non_nullable
as double,expectedAnnualAppreciation: null == expectedAnnualAppreciation ? _self.expectedAnnualAppreciation : expectedAnnualAppreciation // ignore: cast_nullable_to_non_nullable
as double,featured: null == featured ? _self.featured : featured // ignore: cast_nullable_to_non_nullable
as bool,totalSuccessfulInvestment: null == totalSuccessfulInvestment ? _self.totalSuccessfulInvestment : totalSuccessfulInvestment // ignore: cast_nullable_to_non_nullable
as double,
  ));
}
/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerInformationCopyWith<$Res>? get sellerInformation {
    if (_self.sellerInformation == null) {
    return null;
  }

  return $SellerInformationCopyWith<$Res>(_self.sellerInformation!, (value) {
    return _then(_self.copyWith(sellerInformation: value));
  });
}/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PropertyManagerCopyWith<$Res>? get propertyManager {
    if (_self.propertyManager == null) {
    return null;
  }

  return $PropertyManagerCopyWith<$Res>(_self.propertyManager!, (value) {
    return _then(_self.copyWith(propertyManager: value));
  });
}
}


/// Adds pattern-matching-related methods to [PropertyDetails].
extension PropertyDetailsPatterns on PropertyDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyDetails value)  $default,){
final _that = this;
switch (_that) {
case _PropertyDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyDetails value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String addressInEnglish,  String addressInArabic,  String aboutInEnglish,  String aboutInArabic,  String investTextInEnglish,  String investTextInArabic,  String suggestedHoldingPeroid,  double minInvestmentAmount,  double rentAmount,  double fiveYearExpectedReturn,  DividedFrequency dividedFrequency,  int numberOfShare,  double totalPrice,  String endDate,  String purchaseDate,  String mapLocation,  double propertyManagementFee,  PropertyStatus propertyStatus,  bool limitedVisibility,  List<PropertyTransactionCost> propertyTransactionCosts,  List<PropertyPurchaseCost> propertyPurchaseCosts,  List<PropertyRentalOtherCost> propertyRentalOtherCosts,  List<PropertyImage> propertyImages,  List<PropertyFeature> propertyFeatures,  List<PropertyDocument> propertyDocuments,  List<PropertyAmenity> amenities,  SellerInformation? sellerInformation,  PropertyManager? propertyManager,  int remainingPeriod,  int numberOfInvestors,  double propertyPricePerShare,  double propertyAcquisition,  double totalPurchaseCost,  double totalTransactionCost,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool featured,  double totalSuccessfulInvestment)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.addressInEnglish,_that.addressInArabic,_that.aboutInEnglish,_that.aboutInArabic,_that.investTextInEnglish,_that.investTextInArabic,_that.suggestedHoldingPeroid,_that.minInvestmentAmount,_that.rentAmount,_that.fiveYearExpectedReturn,_that.dividedFrequency,_that.numberOfShare,_that.totalPrice,_that.endDate,_that.purchaseDate,_that.mapLocation,_that.propertyManagementFee,_that.propertyStatus,_that.limitedVisibility,_that.propertyTransactionCosts,_that.propertyPurchaseCosts,_that.propertyRentalOtherCosts,_that.propertyImages,_that.propertyFeatures,_that.propertyDocuments,_that.amenities,_that.sellerInformation,_that.propertyManager,_that.remainingPeriod,_that.numberOfInvestors,_that.propertyPricePerShare,_that.propertyAcquisition,_that.totalPurchaseCost,_that.totalTransactionCost,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.featured,_that.totalSuccessfulInvestment);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String addressInEnglish,  String addressInArabic,  String aboutInEnglish,  String aboutInArabic,  String investTextInEnglish,  String investTextInArabic,  String suggestedHoldingPeroid,  double minInvestmentAmount,  double rentAmount,  double fiveYearExpectedReturn,  DividedFrequency dividedFrequency,  int numberOfShare,  double totalPrice,  String endDate,  String purchaseDate,  String mapLocation,  double propertyManagementFee,  PropertyStatus propertyStatus,  bool limitedVisibility,  List<PropertyTransactionCost> propertyTransactionCosts,  List<PropertyPurchaseCost> propertyPurchaseCosts,  List<PropertyRentalOtherCost> propertyRentalOtherCosts,  List<PropertyImage> propertyImages,  List<PropertyFeature> propertyFeatures,  List<PropertyDocument> propertyDocuments,  List<PropertyAmenity> amenities,  SellerInformation? sellerInformation,  PropertyManager? propertyManager,  int remainingPeriod,  int numberOfInvestors,  double propertyPricePerShare,  double propertyAcquisition,  double totalPurchaseCost,  double totalTransactionCost,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool featured,  double totalSuccessfulInvestment)  $default,) {final _that = this;
switch (_that) {
case _PropertyDetails():
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.addressInEnglish,_that.addressInArabic,_that.aboutInEnglish,_that.aboutInArabic,_that.investTextInEnglish,_that.investTextInArabic,_that.suggestedHoldingPeroid,_that.minInvestmentAmount,_that.rentAmount,_that.fiveYearExpectedReturn,_that.dividedFrequency,_that.numberOfShare,_that.totalPrice,_that.endDate,_that.purchaseDate,_that.mapLocation,_that.propertyManagementFee,_that.propertyStatus,_that.limitedVisibility,_that.propertyTransactionCosts,_that.propertyPurchaseCosts,_that.propertyRentalOtherCosts,_that.propertyImages,_that.propertyFeatures,_that.propertyDocuments,_that.amenities,_that.sellerInformation,_that.propertyManager,_that.remainingPeriod,_that.numberOfInvestors,_that.propertyPricePerShare,_that.propertyAcquisition,_that.totalPurchaseCost,_that.totalTransactionCost,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.featured,_that.totalSuccessfulInvestment);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  String addressInEnglish,  String addressInArabic,  String aboutInEnglish,  String aboutInArabic,  String investTextInEnglish,  String investTextInArabic,  String suggestedHoldingPeroid,  double minInvestmentAmount,  double rentAmount,  double fiveYearExpectedReturn,  DividedFrequency dividedFrequency,  int numberOfShare,  double totalPrice,  String endDate,  String purchaseDate,  String mapLocation,  double propertyManagementFee,  PropertyStatus propertyStatus,  bool limitedVisibility,  List<PropertyTransactionCost> propertyTransactionCosts,  List<PropertyPurchaseCost> propertyPurchaseCosts,  List<PropertyRentalOtherCost> propertyRentalOtherCosts,  List<PropertyImage> propertyImages,  List<PropertyFeature> propertyFeatures,  List<PropertyDocument> propertyDocuments,  List<PropertyAmenity> amenities,  SellerInformation? sellerInformation,  PropertyManager? propertyManager,  int remainingPeriod,  int numberOfInvestors,  double propertyPricePerShare,  double propertyAcquisition,  double totalPurchaseCost,  double totalTransactionCost,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool featured,  double totalSuccessfulInvestment)?  $default,) {final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.addressInEnglish,_that.addressInArabic,_that.aboutInEnglish,_that.aboutInArabic,_that.investTextInEnglish,_that.investTextInArabic,_that.suggestedHoldingPeroid,_that.minInvestmentAmount,_that.rentAmount,_that.fiveYearExpectedReturn,_that.dividedFrequency,_that.numberOfShare,_that.totalPrice,_that.endDate,_that.purchaseDate,_that.mapLocation,_that.propertyManagementFee,_that.propertyStatus,_that.limitedVisibility,_that.propertyTransactionCosts,_that.propertyPurchaseCosts,_that.propertyRentalOtherCosts,_that.propertyImages,_that.propertyFeatures,_that.propertyDocuments,_that.amenities,_that.sellerInformation,_that.propertyManager,_that.remainingPeriod,_that.numberOfInvestors,_that.propertyPricePerShare,_that.propertyAcquisition,_that.totalPurchaseCost,_that.totalTransactionCost,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.featured,_that.totalSuccessfulInvestment);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyDetails implements PropertyDetails {
  const _PropertyDetails({required this.id, required this.code, required this.titleInEnglish, required this.titleInArabic, required this.cityInEnglish, required this.cityInArabic, required this.countryInEnglish, required this.countryInArabic, required this.addressInEnglish, required this.addressInArabic, required this.aboutInEnglish, required this.aboutInArabic, required this.investTextInEnglish, required this.investTextInArabic, required this.suggestedHoldingPeroid, required this.minInvestmentAmount, required this.rentAmount, required this.fiveYearExpectedReturn, required this.dividedFrequency, required this.numberOfShare, required this.totalPrice, required this.endDate, required this.purchaseDate, required this.mapLocation, required this.propertyManagementFee, required this.propertyStatus, required this.limitedVisibility, required final  List<PropertyTransactionCost> propertyTransactionCosts, required final  List<PropertyPurchaseCost> propertyPurchaseCosts, required final  List<PropertyRentalOtherCost> propertyRentalOtherCosts, required final  List<PropertyImage> propertyImages, required final  List<PropertyFeature> propertyFeatures, required final  List<PropertyDocument> propertyDocuments, required final  List<PropertyAmenity> amenities, this.sellerInformation, this.propertyManager, required this.remainingPeriod, required this.numberOfInvestors, required this.propertyPricePerShare, required this.propertyAcquisition, required this.totalPurchaseCost, required this.totalTransactionCost, required this.totalRentalOtherCost, required this.expectedAnnualAppreciation, required this.featured, required this.totalSuccessfulInvestment}): _propertyTransactionCosts = propertyTransactionCosts,_propertyPurchaseCosts = propertyPurchaseCosts,_propertyRentalOtherCosts = propertyRentalOtherCosts,_propertyImages = propertyImages,_propertyFeatures = propertyFeatures,_propertyDocuments = propertyDocuments,_amenities = amenities;
  factory _PropertyDetails.fromJson(Map<String, dynamic> json) => _$PropertyDetailsFromJson(json);

@override final  int id;
@override final  String code;
@override final  String titleInEnglish;
@override final  String titleInArabic;
@override final  String cityInEnglish;
@override final  String cityInArabic;
@override final  String countryInEnglish;
@override final  String countryInArabic;
@override final  String addressInEnglish;
@override final  String addressInArabic;
@override final  String aboutInEnglish;
@override final  String aboutInArabic;
@override final  String investTextInEnglish;
@override final  String investTextInArabic;
@override final  String suggestedHoldingPeroid;
@override final  double minInvestmentAmount;
@override final  double rentAmount;
@override final  double fiveYearExpectedReturn;
@override final  DividedFrequency dividedFrequency;
@override final  int numberOfShare;
@override final  double totalPrice;
@override final  String endDate;
@override final  String purchaseDate;
@override final  String mapLocation;
@override final  double propertyManagementFee;
@override final  PropertyStatus propertyStatus;
@override final  bool limitedVisibility;
 final  List<PropertyTransactionCost> _propertyTransactionCosts;
@override List<PropertyTransactionCost> get propertyTransactionCosts {
  if (_propertyTransactionCosts is EqualUnmodifiableListView) return _propertyTransactionCosts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyTransactionCosts);
}

 final  List<PropertyPurchaseCost> _propertyPurchaseCosts;
@override List<PropertyPurchaseCost> get propertyPurchaseCosts {
  if (_propertyPurchaseCosts is EqualUnmodifiableListView) return _propertyPurchaseCosts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyPurchaseCosts);
}

 final  List<PropertyRentalOtherCost> _propertyRentalOtherCosts;
@override List<PropertyRentalOtherCost> get propertyRentalOtherCosts {
  if (_propertyRentalOtherCosts is EqualUnmodifiableListView) return _propertyRentalOtherCosts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyRentalOtherCosts);
}

 final  List<PropertyImage> _propertyImages;
@override List<PropertyImage> get propertyImages {
  if (_propertyImages is EqualUnmodifiableListView) return _propertyImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyImages);
}

 final  List<PropertyFeature> _propertyFeatures;
@override List<PropertyFeature> get propertyFeatures {
  if (_propertyFeatures is EqualUnmodifiableListView) return _propertyFeatures;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyFeatures);
}

 final  List<PropertyDocument> _propertyDocuments;
@override List<PropertyDocument> get propertyDocuments {
  if (_propertyDocuments is EqualUnmodifiableListView) return _propertyDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyDocuments);
}

 final  List<PropertyAmenity> _amenities;
@override List<PropertyAmenity> get amenities {
  if (_amenities is EqualUnmodifiableListView) return _amenities;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_amenities);
}

@override final  SellerInformation? sellerInformation;
@override final  PropertyManager? propertyManager;
@override final  int remainingPeriod;
@override final  int numberOfInvestors;
@override final  double propertyPricePerShare;
@override final  double propertyAcquisition;
@override final  double totalPurchaseCost;
@override final  double totalTransactionCost;
@override final  double totalRentalOtherCost;
@override final  double expectedAnnualAppreciation;
@override final  bool featured;
@override final  double totalSuccessfulInvestment;

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyDetailsCopyWith<_PropertyDetails> get copyWith => __$PropertyDetailsCopyWithImpl<_PropertyDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.addressInEnglish, addressInEnglish) || other.addressInEnglish == addressInEnglish)&&(identical(other.addressInArabic, addressInArabic) || other.addressInArabic == addressInArabic)&&(identical(other.aboutInEnglish, aboutInEnglish) || other.aboutInEnglish == aboutInEnglish)&&(identical(other.aboutInArabic, aboutInArabic) || other.aboutInArabic == aboutInArabic)&&(identical(other.investTextInEnglish, investTextInEnglish) || other.investTextInEnglish == investTextInEnglish)&&(identical(other.investTextInArabic, investTextInArabic) || other.investTextInArabic == investTextInArabic)&&(identical(other.suggestedHoldingPeroid, suggestedHoldingPeroid) || other.suggestedHoldingPeroid == suggestedHoldingPeroid)&&(identical(other.minInvestmentAmount, minInvestmentAmount) || other.minInvestmentAmount == minInvestmentAmount)&&(identical(other.rentAmount, rentAmount) || other.rentAmount == rentAmount)&&(identical(other.fiveYearExpectedReturn, fiveYearExpectedReturn) || other.fiveYearExpectedReturn == fiveYearExpectedReturn)&&(identical(other.dividedFrequency, dividedFrequency) || other.dividedFrequency == dividedFrequency)&&(identical(other.numberOfShare, numberOfShare) || other.numberOfShare == numberOfShare)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.endDate, endDate) || other.endDate == endDate)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate)&&(identical(other.mapLocation, mapLocation) || other.mapLocation == mapLocation)&&(identical(other.propertyManagementFee, propertyManagementFee) || other.propertyManagementFee == propertyManagementFee)&&(identical(other.propertyStatus, propertyStatus) || other.propertyStatus == propertyStatus)&&(identical(other.limitedVisibility, limitedVisibility) || other.limitedVisibility == limitedVisibility)&&const DeepCollectionEquality().equals(other._propertyTransactionCosts, _propertyTransactionCosts)&&const DeepCollectionEquality().equals(other._propertyPurchaseCosts, _propertyPurchaseCosts)&&const DeepCollectionEquality().equals(other._propertyRentalOtherCosts, _propertyRentalOtherCosts)&&const DeepCollectionEquality().equals(other._propertyImages, _propertyImages)&&const DeepCollectionEquality().equals(other._propertyFeatures, _propertyFeatures)&&const DeepCollectionEquality().equals(other._propertyDocuments, _propertyDocuments)&&const DeepCollectionEquality().equals(other._amenities, _amenities)&&(identical(other.sellerInformation, sellerInformation) || other.sellerInformation == sellerInformation)&&(identical(other.propertyManager, propertyManager) || other.propertyManager == propertyManager)&&(identical(other.remainingPeriod, remainingPeriod) || other.remainingPeriod == remainingPeriod)&&(identical(other.numberOfInvestors, numberOfInvestors) || other.numberOfInvestors == numberOfInvestors)&&(identical(other.propertyPricePerShare, propertyPricePerShare) || other.propertyPricePerShare == propertyPricePerShare)&&(identical(other.propertyAcquisition, propertyAcquisition) || other.propertyAcquisition == propertyAcquisition)&&(identical(other.totalPurchaseCost, totalPurchaseCost) || other.totalPurchaseCost == totalPurchaseCost)&&(identical(other.totalTransactionCost, totalTransactionCost) || other.totalTransactionCost == totalTransactionCost)&&(identical(other.totalRentalOtherCost, totalRentalOtherCost) || other.totalRentalOtherCost == totalRentalOtherCost)&&(identical(other.expectedAnnualAppreciation, expectedAnnualAppreciation) || other.expectedAnnualAppreciation == expectedAnnualAppreciation)&&(identical(other.featured, featured) || other.featured == featured)&&(identical(other.totalSuccessfulInvestment, totalSuccessfulInvestment) || other.totalSuccessfulInvestment == totalSuccessfulInvestment));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,code,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,addressInEnglish,addressInArabic,aboutInEnglish,aboutInArabic,investTextInEnglish,investTextInArabic,suggestedHoldingPeroid,minInvestmentAmount,rentAmount,fiveYearExpectedReturn,dividedFrequency,numberOfShare,totalPrice,endDate,purchaseDate,mapLocation,propertyManagementFee,propertyStatus,limitedVisibility,const DeepCollectionEquality().hash(_propertyTransactionCosts),const DeepCollectionEquality().hash(_propertyPurchaseCosts),const DeepCollectionEquality().hash(_propertyRentalOtherCosts),const DeepCollectionEquality().hash(_propertyImages),const DeepCollectionEquality().hash(_propertyFeatures),const DeepCollectionEquality().hash(_propertyDocuments),const DeepCollectionEquality().hash(_amenities),sellerInformation,propertyManager,remainingPeriod,numberOfInvestors,propertyPricePerShare,propertyAcquisition,totalPurchaseCost,totalTransactionCost,totalRentalOtherCost,expectedAnnualAppreciation,featured,totalSuccessfulInvestment]);

@override
String toString() {
  return 'PropertyDetails(id: $id, code: $code, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, addressInEnglish: $addressInEnglish, addressInArabic: $addressInArabic, aboutInEnglish: $aboutInEnglish, aboutInArabic: $aboutInArabic, investTextInEnglish: $investTextInEnglish, investTextInArabic: $investTextInArabic, suggestedHoldingPeroid: $suggestedHoldingPeroid, minInvestmentAmount: $minInvestmentAmount, rentAmount: $rentAmount, fiveYearExpectedReturn: $fiveYearExpectedReturn, dividedFrequency: $dividedFrequency, numberOfShare: $numberOfShare, totalPrice: $totalPrice, endDate: $endDate, purchaseDate: $purchaseDate, mapLocation: $mapLocation, propertyManagementFee: $propertyManagementFee, propertyStatus: $propertyStatus, limitedVisibility: $limitedVisibility, propertyTransactionCosts: $propertyTransactionCosts, propertyPurchaseCosts: $propertyPurchaseCosts, propertyRentalOtherCosts: $propertyRentalOtherCosts, propertyImages: $propertyImages, propertyFeatures: $propertyFeatures, propertyDocuments: $propertyDocuments, amenities: $amenities, sellerInformation: $sellerInformation, propertyManager: $propertyManager, remainingPeriod: $remainingPeriod, numberOfInvestors: $numberOfInvestors, propertyPricePerShare: $propertyPricePerShare, propertyAcquisition: $propertyAcquisition, totalPurchaseCost: $totalPurchaseCost, totalTransactionCost: $totalTransactionCost, totalRentalOtherCost: $totalRentalOtherCost, expectedAnnualAppreciation: $expectedAnnualAppreciation, featured: $featured, totalSuccessfulInvestment: $totalSuccessfulInvestment)';
}


}

/// @nodoc
abstract mixin class _$PropertyDetailsCopyWith<$Res> implements $PropertyDetailsCopyWith<$Res> {
  factory _$PropertyDetailsCopyWith(_PropertyDetails value, $Res Function(_PropertyDetails) _then) = __$PropertyDetailsCopyWithImpl;
@override @useResult
$Res call({
 int id, String code, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, String addressInEnglish, String addressInArabic, String aboutInEnglish, String aboutInArabic, String investTextInEnglish, String investTextInArabic, String suggestedHoldingPeroid, double minInvestmentAmount, double rentAmount, double fiveYearExpectedReturn, DividedFrequency dividedFrequency, int numberOfShare, double totalPrice, String endDate, String purchaseDate, String mapLocation, double propertyManagementFee, PropertyStatus propertyStatus, bool limitedVisibility, List<PropertyTransactionCost> propertyTransactionCosts, List<PropertyPurchaseCost> propertyPurchaseCosts, List<PropertyRentalOtherCost> propertyRentalOtherCosts, List<PropertyImage> propertyImages, List<PropertyFeature> propertyFeatures, List<PropertyDocument> propertyDocuments, List<PropertyAmenity> amenities, SellerInformation? sellerInformation, PropertyManager? propertyManager, int remainingPeriod, int numberOfInvestors, double propertyPricePerShare, double propertyAcquisition, double totalPurchaseCost, double totalTransactionCost, double totalRentalOtherCost, double expectedAnnualAppreciation, bool featured, double totalSuccessfulInvestment
});


@override $SellerInformationCopyWith<$Res>? get sellerInformation;@override $PropertyManagerCopyWith<$Res>? get propertyManager;

}
/// @nodoc
class __$PropertyDetailsCopyWithImpl<$Res>
    implements _$PropertyDetailsCopyWith<$Res> {
  __$PropertyDetailsCopyWithImpl(this._self, this._then);

  final _PropertyDetails _self;
  final $Res Function(_PropertyDetails) _then;

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? code = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? addressInEnglish = null,Object? addressInArabic = null,Object? aboutInEnglish = null,Object? aboutInArabic = null,Object? investTextInEnglish = null,Object? investTextInArabic = null,Object? suggestedHoldingPeroid = null,Object? minInvestmentAmount = null,Object? rentAmount = null,Object? fiveYearExpectedReturn = null,Object? dividedFrequency = null,Object? numberOfShare = null,Object? totalPrice = null,Object? endDate = null,Object? purchaseDate = null,Object? mapLocation = null,Object? propertyManagementFee = null,Object? propertyStatus = null,Object? limitedVisibility = null,Object? propertyTransactionCosts = null,Object? propertyPurchaseCosts = null,Object? propertyRentalOtherCosts = null,Object? propertyImages = null,Object? propertyFeatures = null,Object? propertyDocuments = null,Object? amenities = null,Object? sellerInformation = freezed,Object? propertyManager = freezed,Object? remainingPeriod = null,Object? numberOfInvestors = null,Object? propertyPricePerShare = null,Object? propertyAcquisition = null,Object? totalPurchaseCost = null,Object? totalTransactionCost = null,Object? totalRentalOtherCost = null,Object? expectedAnnualAppreciation = null,Object? featured = null,Object? totalSuccessfulInvestment = null,}) {
  return _then(_PropertyDetails(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,addressInEnglish: null == addressInEnglish ? _self.addressInEnglish : addressInEnglish // ignore: cast_nullable_to_non_nullable
as String,addressInArabic: null == addressInArabic ? _self.addressInArabic : addressInArabic // ignore: cast_nullable_to_non_nullable
as String,aboutInEnglish: null == aboutInEnglish ? _self.aboutInEnglish : aboutInEnglish // ignore: cast_nullable_to_non_nullable
as String,aboutInArabic: null == aboutInArabic ? _self.aboutInArabic : aboutInArabic // ignore: cast_nullable_to_non_nullable
as String,investTextInEnglish: null == investTextInEnglish ? _self.investTextInEnglish : investTextInEnglish // ignore: cast_nullable_to_non_nullable
as String,investTextInArabic: null == investTextInArabic ? _self.investTextInArabic : investTextInArabic // ignore: cast_nullable_to_non_nullable
as String,suggestedHoldingPeroid: null == suggestedHoldingPeroid ? _self.suggestedHoldingPeroid : suggestedHoldingPeroid // ignore: cast_nullable_to_non_nullable
as String,minInvestmentAmount: null == minInvestmentAmount ? _self.minInvestmentAmount : minInvestmentAmount // ignore: cast_nullable_to_non_nullable
as double,rentAmount: null == rentAmount ? _self.rentAmount : rentAmount // ignore: cast_nullable_to_non_nullable
as double,fiveYearExpectedReturn: null == fiveYearExpectedReturn ? _self.fiveYearExpectedReturn : fiveYearExpectedReturn // ignore: cast_nullable_to_non_nullable
as double,dividedFrequency: null == dividedFrequency ? _self.dividedFrequency : dividedFrequency // ignore: cast_nullable_to_non_nullable
as DividedFrequency,numberOfShare: null == numberOfShare ? _self.numberOfShare : numberOfShare // ignore: cast_nullable_to_non_nullable
as int,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,endDate: null == endDate ? _self.endDate : endDate // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: null == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as String,mapLocation: null == mapLocation ? _self.mapLocation : mapLocation // ignore: cast_nullable_to_non_nullable
as String,propertyManagementFee: null == propertyManagementFee ? _self.propertyManagementFee : propertyManagementFee // ignore: cast_nullable_to_non_nullable
as double,propertyStatus: null == propertyStatus ? _self.propertyStatus : propertyStatus // ignore: cast_nullable_to_non_nullable
as PropertyStatus,limitedVisibility: null == limitedVisibility ? _self.limitedVisibility : limitedVisibility // ignore: cast_nullable_to_non_nullable
as bool,propertyTransactionCosts: null == propertyTransactionCosts ? _self._propertyTransactionCosts : propertyTransactionCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyTransactionCost>,propertyPurchaseCosts: null == propertyPurchaseCosts ? _self._propertyPurchaseCosts : propertyPurchaseCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyPurchaseCost>,propertyRentalOtherCosts: null == propertyRentalOtherCosts ? _self._propertyRentalOtherCosts : propertyRentalOtherCosts // ignore: cast_nullable_to_non_nullable
as List<PropertyRentalOtherCost>,propertyImages: null == propertyImages ? _self._propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,propertyFeatures: null == propertyFeatures ? _self._propertyFeatures : propertyFeatures // ignore: cast_nullable_to_non_nullable
as List<PropertyFeature>,propertyDocuments: null == propertyDocuments ? _self._propertyDocuments : propertyDocuments // ignore: cast_nullable_to_non_nullable
as List<PropertyDocument>,amenities: null == amenities ? _self._amenities : amenities // ignore: cast_nullable_to_non_nullable
as List<PropertyAmenity>,sellerInformation: freezed == sellerInformation ? _self.sellerInformation : sellerInformation // ignore: cast_nullable_to_non_nullable
as SellerInformation?,propertyManager: freezed == propertyManager ? _self.propertyManager : propertyManager // ignore: cast_nullable_to_non_nullable
as PropertyManager?,remainingPeriod: null == remainingPeriod ? _self.remainingPeriod : remainingPeriod // ignore: cast_nullable_to_non_nullable
as int,numberOfInvestors: null == numberOfInvestors ? _self.numberOfInvestors : numberOfInvestors // ignore: cast_nullable_to_non_nullable
as int,propertyPricePerShare: null == propertyPricePerShare ? _self.propertyPricePerShare : propertyPricePerShare // ignore: cast_nullable_to_non_nullable
as double,propertyAcquisition: null == propertyAcquisition ? _self.propertyAcquisition : propertyAcquisition // ignore: cast_nullable_to_non_nullable
as double,totalPurchaseCost: null == totalPurchaseCost ? _self.totalPurchaseCost : totalPurchaseCost // ignore: cast_nullable_to_non_nullable
as double,totalTransactionCost: null == totalTransactionCost ? _self.totalTransactionCost : totalTransactionCost // ignore: cast_nullable_to_non_nullable
as double,totalRentalOtherCost: null == totalRentalOtherCost ? _self.totalRentalOtherCost : totalRentalOtherCost // ignore: cast_nullable_to_non_nullable
as double,expectedAnnualAppreciation: null == expectedAnnualAppreciation ? _self.expectedAnnualAppreciation : expectedAnnualAppreciation // ignore: cast_nullable_to_non_nullable
as double,featured: null == featured ? _self.featured : featured // ignore: cast_nullable_to_non_nullable
as bool,totalSuccessfulInvestment: null == totalSuccessfulInvestment ? _self.totalSuccessfulInvestment : totalSuccessfulInvestment // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SellerInformationCopyWith<$Res>? get sellerInformation {
    if (_self.sellerInformation == null) {
    return null;
  }

  return $SellerInformationCopyWith<$Res>(_self.sellerInformation!, (value) {
    return _then(_self.copyWith(sellerInformation: value));
  });
}/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PropertyManagerCopyWith<$Res>? get propertyManager {
    if (_self.propertyManager == null) {
    return null;
  }

  return $PropertyManagerCopyWith<$Res>(_self.propertyManager!, (value) {
    return _then(_self.copyWith(propertyManager: value));
  });
}
}


/// @nodoc
mixin _$PropertyTransactionCost {

 int get id; double get actualValue; TransactionCostConstant get transactionCostConstant;
/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyTransactionCostCopyWith<PropertyTransactionCost> get copyWith => _$PropertyTransactionCostCopyWithImpl<PropertyTransactionCost>(this as PropertyTransactionCost, _$identity);

  /// Serializes this PropertyTransactionCost to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyTransactionCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.transactionCostConstant, transactionCostConstant) || other.transactionCostConstant == transactionCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,transactionCostConstant);

@override
String toString() {
  return 'PropertyTransactionCost(id: $id, actualValue: $actualValue, transactionCostConstant: $transactionCostConstant)';
}


}

/// @nodoc
abstract mixin class $PropertyTransactionCostCopyWith<$Res>  {
  factory $PropertyTransactionCostCopyWith(PropertyTransactionCost value, $Res Function(PropertyTransactionCost) _then) = _$PropertyTransactionCostCopyWithImpl;
@useResult
$Res call({
 int id, double actualValue, TransactionCostConstant transactionCostConstant
});


$TransactionCostConstantCopyWith<$Res> get transactionCostConstant;

}
/// @nodoc
class _$PropertyTransactionCostCopyWithImpl<$Res>
    implements $PropertyTransactionCostCopyWith<$Res> {
  _$PropertyTransactionCostCopyWithImpl(this._self, this._then);

  final PropertyTransactionCost _self;
  final $Res Function(PropertyTransactionCost) _then;

/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? actualValue = null,Object? transactionCostConstant = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,transactionCostConstant: null == transactionCostConstant ? _self.transactionCostConstant : transactionCostConstant // ignore: cast_nullable_to_non_nullable
as TransactionCostConstant,
  ));
}
/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransactionCostConstantCopyWith<$Res> get transactionCostConstant {
  
  return $TransactionCostConstantCopyWith<$Res>(_self.transactionCostConstant, (value) {
    return _then(_self.copyWith(transactionCostConstant: value));
  });
}
}


/// Adds pattern-matching-related methods to [PropertyTransactionCost].
extension PropertyTransactionCostPatterns on PropertyTransactionCost {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyTransactionCost value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyTransactionCost() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyTransactionCost value)  $default,){
final _that = this;
switch (_that) {
case _PropertyTransactionCost():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyTransactionCost value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyTransactionCost() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  double actualValue,  TransactionCostConstant transactionCostConstant)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyTransactionCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.transactionCostConstant);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  double actualValue,  TransactionCostConstant transactionCostConstant)  $default,) {final _that = this;
switch (_that) {
case _PropertyTransactionCost():
return $default(_that.id,_that.actualValue,_that.transactionCostConstant);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  double actualValue,  TransactionCostConstant transactionCostConstant)?  $default,) {final _that = this;
switch (_that) {
case _PropertyTransactionCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.transactionCostConstant);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyTransactionCost implements PropertyTransactionCost {
  const _PropertyTransactionCost({required this.id, required this.actualValue, required this.transactionCostConstant});
  factory _PropertyTransactionCost.fromJson(Map<String, dynamic> json) => _$PropertyTransactionCostFromJson(json);

@override final  int id;
@override final  double actualValue;
@override final  TransactionCostConstant transactionCostConstant;

/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyTransactionCostCopyWith<_PropertyTransactionCost> get copyWith => __$PropertyTransactionCostCopyWithImpl<_PropertyTransactionCost>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyTransactionCostToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyTransactionCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.transactionCostConstant, transactionCostConstant) || other.transactionCostConstant == transactionCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,transactionCostConstant);

@override
String toString() {
  return 'PropertyTransactionCost(id: $id, actualValue: $actualValue, transactionCostConstant: $transactionCostConstant)';
}


}

/// @nodoc
abstract mixin class _$PropertyTransactionCostCopyWith<$Res> implements $PropertyTransactionCostCopyWith<$Res> {
  factory _$PropertyTransactionCostCopyWith(_PropertyTransactionCost value, $Res Function(_PropertyTransactionCost) _then) = __$PropertyTransactionCostCopyWithImpl;
@override @useResult
$Res call({
 int id, double actualValue, TransactionCostConstant transactionCostConstant
});


@override $TransactionCostConstantCopyWith<$Res> get transactionCostConstant;

}
/// @nodoc
class __$PropertyTransactionCostCopyWithImpl<$Res>
    implements _$PropertyTransactionCostCopyWith<$Res> {
  __$PropertyTransactionCostCopyWithImpl(this._self, this._then);

  final _PropertyTransactionCost _self;
  final $Res Function(_PropertyTransactionCost) _then;

/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? actualValue = null,Object? transactionCostConstant = null,}) {
  return _then(_PropertyTransactionCost(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,transactionCostConstant: null == transactionCostConstant ? _self.transactionCostConstant : transactionCostConstant // ignore: cast_nullable_to_non_nullable
as TransactionCostConstant,
  ));
}

/// Create a copy of PropertyTransactionCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TransactionCostConstantCopyWith<$Res> get transactionCostConstant {
  
  return $TransactionCostConstantCopyWith<$Res>(_self.transactionCostConstant, (value) {
    return _then(_self.copyWith(transactionCostConstant: value));
  });
}
}


/// @nodoc
mixin _$TransactionCostConstant {

 int get id; String get nameInEnglish; String get nameInArabic; double get value; String get type; bool get active;
/// Create a copy of TransactionCostConstant
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TransactionCostConstantCopyWith<TransactionCostConstant> get copyWith => _$TransactionCostConstantCopyWithImpl<TransactionCostConstant>(this as TransactionCostConstant, _$identity);

  /// Serializes this TransactionCostConstant to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TransactionCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'TransactionCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class $TransactionCostConstantCopyWith<$Res>  {
  factory $TransactionCostConstantCopyWith(TransactionCostConstant value, $Res Function(TransactionCostConstant) _then) = _$TransactionCostConstantCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class _$TransactionCostConstantCopyWithImpl<$Res>
    implements $TransactionCostConstantCopyWith<$Res> {
  _$TransactionCostConstantCopyWithImpl(this._self, this._then);

  final TransactionCostConstant _self;
  final $Res Function(TransactionCostConstant) _then;

/// Create a copy of TransactionCostConstant
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [TransactionCostConstant].
extension TransactionCostConstantPatterns on TransactionCostConstant {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TransactionCostConstant value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TransactionCostConstant() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TransactionCostConstant value)  $default,){
final _that = this;
switch (_that) {
case _TransactionCostConstant():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TransactionCostConstant value)?  $default,){
final _that = this;
switch (_that) {
case _TransactionCostConstant() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TransactionCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)  $default,) {final _that = this;
switch (_that) {
case _TransactionCostConstant():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,) {final _that = this;
switch (_that) {
case _TransactionCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TransactionCostConstant implements TransactionCostConstant {
  const _TransactionCostConstant({required this.id, required this.nameInEnglish, required this.nameInArabic, required this.value, required this.type, required this.active});
  factory _TransactionCostConstant.fromJson(Map<String, dynamic> json) => _$TransactionCostConstantFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override final  double value;
@override final  String type;
@override final  bool active;

/// Create a copy of TransactionCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TransactionCostConstantCopyWith<_TransactionCostConstant> get copyWith => __$TransactionCostConstantCopyWithImpl<_TransactionCostConstant>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TransactionCostConstantToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TransactionCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'TransactionCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class _$TransactionCostConstantCopyWith<$Res> implements $TransactionCostConstantCopyWith<$Res> {
  factory _$TransactionCostConstantCopyWith(_TransactionCostConstant value, $Res Function(_TransactionCostConstant) _then) = __$TransactionCostConstantCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class __$TransactionCostConstantCopyWithImpl<$Res>
    implements _$TransactionCostConstantCopyWith<$Res> {
  __$TransactionCostConstantCopyWithImpl(this._self, this._then);

  final _TransactionCostConstant _self;
  final $Res Function(_TransactionCostConstant) _then;

/// Create a copy of TransactionCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_TransactionCostConstant(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$PropertyPurchaseCost {

 int get id; double get actualValue; PurchaseCostConstant get purchaseCostConstant;
/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyPurchaseCostCopyWith<PropertyPurchaseCost> get copyWith => _$PropertyPurchaseCostCopyWithImpl<PropertyPurchaseCost>(this as PropertyPurchaseCost, _$identity);

  /// Serializes this PropertyPurchaseCost to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyPurchaseCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.purchaseCostConstant, purchaseCostConstant) || other.purchaseCostConstant == purchaseCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,purchaseCostConstant);

@override
String toString() {
  return 'PropertyPurchaseCost(id: $id, actualValue: $actualValue, purchaseCostConstant: $purchaseCostConstant)';
}


}

/// @nodoc
abstract mixin class $PropertyPurchaseCostCopyWith<$Res>  {
  factory $PropertyPurchaseCostCopyWith(PropertyPurchaseCost value, $Res Function(PropertyPurchaseCost) _then) = _$PropertyPurchaseCostCopyWithImpl;
@useResult
$Res call({
 int id, double actualValue, PurchaseCostConstant purchaseCostConstant
});


$PurchaseCostConstantCopyWith<$Res> get purchaseCostConstant;

}
/// @nodoc
class _$PropertyPurchaseCostCopyWithImpl<$Res>
    implements $PropertyPurchaseCostCopyWith<$Res> {
  _$PropertyPurchaseCostCopyWithImpl(this._self, this._then);

  final PropertyPurchaseCost _self;
  final $Res Function(PropertyPurchaseCost) _then;

/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? actualValue = null,Object? purchaseCostConstant = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,purchaseCostConstant: null == purchaseCostConstant ? _self.purchaseCostConstant : purchaseCostConstant // ignore: cast_nullable_to_non_nullable
as PurchaseCostConstant,
  ));
}
/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PurchaseCostConstantCopyWith<$Res> get purchaseCostConstant {
  
  return $PurchaseCostConstantCopyWith<$Res>(_self.purchaseCostConstant, (value) {
    return _then(_self.copyWith(purchaseCostConstant: value));
  });
}
}


/// Adds pattern-matching-related methods to [PropertyPurchaseCost].
extension PropertyPurchaseCostPatterns on PropertyPurchaseCost {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyPurchaseCost value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyPurchaseCost() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyPurchaseCost value)  $default,){
final _that = this;
switch (_that) {
case _PropertyPurchaseCost():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyPurchaseCost value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyPurchaseCost() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  double actualValue,  PurchaseCostConstant purchaseCostConstant)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyPurchaseCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.purchaseCostConstant);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  double actualValue,  PurchaseCostConstant purchaseCostConstant)  $default,) {final _that = this;
switch (_that) {
case _PropertyPurchaseCost():
return $default(_that.id,_that.actualValue,_that.purchaseCostConstant);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  double actualValue,  PurchaseCostConstant purchaseCostConstant)?  $default,) {final _that = this;
switch (_that) {
case _PropertyPurchaseCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.purchaseCostConstant);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyPurchaseCost implements PropertyPurchaseCost {
  const _PropertyPurchaseCost({required this.id, required this.actualValue, required this.purchaseCostConstant});
  factory _PropertyPurchaseCost.fromJson(Map<String, dynamic> json) => _$PropertyPurchaseCostFromJson(json);

@override final  int id;
@override final  double actualValue;
@override final  PurchaseCostConstant purchaseCostConstant;

/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyPurchaseCostCopyWith<_PropertyPurchaseCost> get copyWith => __$PropertyPurchaseCostCopyWithImpl<_PropertyPurchaseCost>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyPurchaseCostToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyPurchaseCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.purchaseCostConstant, purchaseCostConstant) || other.purchaseCostConstant == purchaseCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,purchaseCostConstant);

@override
String toString() {
  return 'PropertyPurchaseCost(id: $id, actualValue: $actualValue, purchaseCostConstant: $purchaseCostConstant)';
}


}

/// @nodoc
abstract mixin class _$PropertyPurchaseCostCopyWith<$Res> implements $PropertyPurchaseCostCopyWith<$Res> {
  factory _$PropertyPurchaseCostCopyWith(_PropertyPurchaseCost value, $Res Function(_PropertyPurchaseCost) _then) = __$PropertyPurchaseCostCopyWithImpl;
@override @useResult
$Res call({
 int id, double actualValue, PurchaseCostConstant purchaseCostConstant
});


@override $PurchaseCostConstantCopyWith<$Res> get purchaseCostConstant;

}
/// @nodoc
class __$PropertyPurchaseCostCopyWithImpl<$Res>
    implements _$PropertyPurchaseCostCopyWith<$Res> {
  __$PropertyPurchaseCostCopyWithImpl(this._self, this._then);

  final _PropertyPurchaseCost _self;
  final $Res Function(_PropertyPurchaseCost) _then;

/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? actualValue = null,Object? purchaseCostConstant = null,}) {
  return _then(_PropertyPurchaseCost(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,purchaseCostConstant: null == purchaseCostConstant ? _self.purchaseCostConstant : purchaseCostConstant // ignore: cast_nullable_to_non_nullable
as PurchaseCostConstant,
  ));
}

/// Create a copy of PropertyPurchaseCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PurchaseCostConstantCopyWith<$Res> get purchaseCostConstant {
  
  return $PurchaseCostConstantCopyWith<$Res>(_self.purchaseCostConstant, (value) {
    return _then(_self.copyWith(purchaseCostConstant: value));
  });
}
}


/// @nodoc
mixin _$PurchaseCostConstant {

 int get id; String get nameInEnglish; String get nameInArabic; double get value; String get type; bool get active;
/// Create a copy of PurchaseCostConstant
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PurchaseCostConstantCopyWith<PurchaseCostConstant> get copyWith => _$PurchaseCostConstantCopyWithImpl<PurchaseCostConstant>(this as PurchaseCostConstant, _$identity);

  /// Serializes this PurchaseCostConstant to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PurchaseCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'PurchaseCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class $PurchaseCostConstantCopyWith<$Res>  {
  factory $PurchaseCostConstantCopyWith(PurchaseCostConstant value, $Res Function(PurchaseCostConstant) _then) = _$PurchaseCostConstantCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class _$PurchaseCostConstantCopyWithImpl<$Res>
    implements $PurchaseCostConstantCopyWith<$Res> {
  _$PurchaseCostConstantCopyWithImpl(this._self, this._then);

  final PurchaseCostConstant _self;
  final $Res Function(PurchaseCostConstant) _then;

/// Create a copy of PurchaseCostConstant
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PurchaseCostConstant].
extension PurchaseCostConstantPatterns on PurchaseCostConstant {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PurchaseCostConstant value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PurchaseCostConstant() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PurchaseCostConstant value)  $default,){
final _that = this;
switch (_that) {
case _PurchaseCostConstant():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PurchaseCostConstant value)?  $default,){
final _that = this;
switch (_that) {
case _PurchaseCostConstant() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PurchaseCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)  $default,) {final _that = this;
switch (_that) {
case _PurchaseCostConstant():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,) {final _that = this;
switch (_that) {
case _PurchaseCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PurchaseCostConstant implements PurchaseCostConstant {
  const _PurchaseCostConstant({required this.id, required this.nameInEnglish, required this.nameInArabic, required this.value, required this.type, required this.active});
  factory _PurchaseCostConstant.fromJson(Map<String, dynamic> json) => _$PurchaseCostConstantFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override final  double value;
@override final  String type;
@override final  bool active;

/// Create a copy of PurchaseCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PurchaseCostConstantCopyWith<_PurchaseCostConstant> get copyWith => __$PurchaseCostConstantCopyWithImpl<_PurchaseCostConstant>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PurchaseCostConstantToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PurchaseCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'PurchaseCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class _$PurchaseCostConstantCopyWith<$Res> implements $PurchaseCostConstantCopyWith<$Res> {
  factory _$PurchaseCostConstantCopyWith(_PurchaseCostConstant value, $Res Function(_PurchaseCostConstant) _then) = __$PurchaseCostConstantCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class __$PurchaseCostConstantCopyWithImpl<$Res>
    implements _$PurchaseCostConstantCopyWith<$Res> {
  __$PurchaseCostConstantCopyWithImpl(this._self, this._then);

  final _PurchaseCostConstant _self;
  final $Res Function(_PurchaseCostConstant) _then;

/// Create a copy of PurchaseCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_PurchaseCostConstant(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$PropertyRentalOtherCost {

 int get id; double get actualValue; RentalOtherCostConstant get rentalOtherCostConstant;
/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyRentalOtherCostCopyWith<PropertyRentalOtherCost> get copyWith => _$PropertyRentalOtherCostCopyWithImpl<PropertyRentalOtherCost>(this as PropertyRentalOtherCost, _$identity);

  /// Serializes this PropertyRentalOtherCost to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyRentalOtherCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.rentalOtherCostConstant, rentalOtherCostConstant) || other.rentalOtherCostConstant == rentalOtherCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,rentalOtherCostConstant);

@override
String toString() {
  return 'PropertyRentalOtherCost(id: $id, actualValue: $actualValue, rentalOtherCostConstant: $rentalOtherCostConstant)';
}


}

/// @nodoc
abstract mixin class $PropertyRentalOtherCostCopyWith<$Res>  {
  factory $PropertyRentalOtherCostCopyWith(PropertyRentalOtherCost value, $Res Function(PropertyRentalOtherCost) _then) = _$PropertyRentalOtherCostCopyWithImpl;
@useResult
$Res call({
 int id, double actualValue, RentalOtherCostConstant rentalOtherCostConstant
});


$RentalOtherCostConstantCopyWith<$Res> get rentalOtherCostConstant;

}
/// @nodoc
class _$PropertyRentalOtherCostCopyWithImpl<$Res>
    implements $PropertyRentalOtherCostCopyWith<$Res> {
  _$PropertyRentalOtherCostCopyWithImpl(this._self, this._then);

  final PropertyRentalOtherCost _self;
  final $Res Function(PropertyRentalOtherCost) _then;

/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? actualValue = null,Object? rentalOtherCostConstant = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,rentalOtherCostConstant: null == rentalOtherCostConstant ? _self.rentalOtherCostConstant : rentalOtherCostConstant // ignore: cast_nullable_to_non_nullable
as RentalOtherCostConstant,
  ));
}
/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RentalOtherCostConstantCopyWith<$Res> get rentalOtherCostConstant {
  
  return $RentalOtherCostConstantCopyWith<$Res>(_self.rentalOtherCostConstant, (value) {
    return _then(_self.copyWith(rentalOtherCostConstant: value));
  });
}
}


/// Adds pattern-matching-related methods to [PropertyRentalOtherCost].
extension PropertyRentalOtherCostPatterns on PropertyRentalOtherCost {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyRentalOtherCost value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyRentalOtherCost() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyRentalOtherCost value)  $default,){
final _that = this;
switch (_that) {
case _PropertyRentalOtherCost():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyRentalOtherCost value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyRentalOtherCost() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  double actualValue,  RentalOtherCostConstant rentalOtherCostConstant)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyRentalOtherCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.rentalOtherCostConstant);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  double actualValue,  RentalOtherCostConstant rentalOtherCostConstant)  $default,) {final _that = this;
switch (_that) {
case _PropertyRentalOtherCost():
return $default(_that.id,_that.actualValue,_that.rentalOtherCostConstant);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  double actualValue,  RentalOtherCostConstant rentalOtherCostConstant)?  $default,) {final _that = this;
switch (_that) {
case _PropertyRentalOtherCost() when $default != null:
return $default(_that.id,_that.actualValue,_that.rentalOtherCostConstant);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyRentalOtherCost implements PropertyRentalOtherCost {
  const _PropertyRentalOtherCost({required this.id, required this.actualValue, required this.rentalOtherCostConstant});
  factory _PropertyRentalOtherCost.fromJson(Map<String, dynamic> json) => _$PropertyRentalOtherCostFromJson(json);

@override final  int id;
@override final  double actualValue;
@override final  RentalOtherCostConstant rentalOtherCostConstant;

/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyRentalOtherCostCopyWith<_PropertyRentalOtherCost> get copyWith => __$PropertyRentalOtherCostCopyWithImpl<_PropertyRentalOtherCost>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyRentalOtherCostToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyRentalOtherCost&&(identical(other.id, id) || other.id == id)&&(identical(other.actualValue, actualValue) || other.actualValue == actualValue)&&(identical(other.rentalOtherCostConstant, rentalOtherCostConstant) || other.rentalOtherCostConstant == rentalOtherCostConstant));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,actualValue,rentalOtherCostConstant);

@override
String toString() {
  return 'PropertyRentalOtherCost(id: $id, actualValue: $actualValue, rentalOtherCostConstant: $rentalOtherCostConstant)';
}


}

/// @nodoc
abstract mixin class _$PropertyRentalOtherCostCopyWith<$Res> implements $PropertyRentalOtherCostCopyWith<$Res> {
  factory _$PropertyRentalOtherCostCopyWith(_PropertyRentalOtherCost value, $Res Function(_PropertyRentalOtherCost) _then) = __$PropertyRentalOtherCostCopyWithImpl;
@override @useResult
$Res call({
 int id, double actualValue, RentalOtherCostConstant rentalOtherCostConstant
});


@override $RentalOtherCostConstantCopyWith<$Res> get rentalOtherCostConstant;

}
/// @nodoc
class __$PropertyRentalOtherCostCopyWithImpl<$Res>
    implements _$PropertyRentalOtherCostCopyWith<$Res> {
  __$PropertyRentalOtherCostCopyWithImpl(this._self, this._then);

  final _PropertyRentalOtherCost _self;
  final $Res Function(_PropertyRentalOtherCost) _then;

/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? actualValue = null,Object? rentalOtherCostConstant = null,}) {
  return _then(_PropertyRentalOtherCost(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,actualValue: null == actualValue ? _self.actualValue : actualValue // ignore: cast_nullable_to_non_nullable
as double,rentalOtherCostConstant: null == rentalOtherCostConstant ? _self.rentalOtherCostConstant : rentalOtherCostConstant // ignore: cast_nullable_to_non_nullable
as RentalOtherCostConstant,
  ));
}

/// Create a copy of PropertyRentalOtherCost
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RentalOtherCostConstantCopyWith<$Res> get rentalOtherCostConstant {
  
  return $RentalOtherCostConstantCopyWith<$Res>(_self.rentalOtherCostConstant, (value) {
    return _then(_self.copyWith(rentalOtherCostConstant: value));
  });
}
}


/// @nodoc
mixin _$RentalOtherCostConstant {

 int get id; String get nameInEnglish; String get nameInArabic; double get value; String get type; bool get active;
/// Create a copy of RentalOtherCostConstant
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RentalOtherCostConstantCopyWith<RentalOtherCostConstant> get copyWith => _$RentalOtherCostConstantCopyWithImpl<RentalOtherCostConstant>(this as RentalOtherCostConstant, _$identity);

  /// Serializes this RentalOtherCostConstant to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RentalOtherCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'RentalOtherCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class $RentalOtherCostConstantCopyWith<$Res>  {
  factory $RentalOtherCostConstantCopyWith(RentalOtherCostConstant value, $Res Function(RentalOtherCostConstant) _then) = _$RentalOtherCostConstantCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class _$RentalOtherCostConstantCopyWithImpl<$Res>
    implements $RentalOtherCostConstantCopyWith<$Res> {
  _$RentalOtherCostConstantCopyWithImpl(this._self, this._then);

  final RentalOtherCostConstant _self;
  final $Res Function(RentalOtherCostConstant) _then;

/// Create a copy of RentalOtherCostConstant
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [RentalOtherCostConstant].
extension RentalOtherCostConstantPatterns on RentalOtherCostConstant {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RentalOtherCostConstant value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RentalOtherCostConstant() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RentalOtherCostConstant value)  $default,){
final _that = this;
switch (_that) {
case _RentalOtherCostConstant():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RentalOtherCostConstant value)?  $default,){
final _that = this;
switch (_that) {
case _RentalOtherCostConstant() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RentalOtherCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)  $default,) {final _that = this;
switch (_that) {
case _RentalOtherCostConstant():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  double value,  String type,  bool active)?  $default,) {final _that = this;
switch (_that) {
case _RentalOtherCostConstant() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.value,_that.type,_that.active);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RentalOtherCostConstant implements RentalOtherCostConstant {
  const _RentalOtherCostConstant({required this.id, required this.nameInEnglish, required this.nameInArabic, required this.value, required this.type, required this.active});
  factory _RentalOtherCostConstant.fromJson(Map<String, dynamic> json) => _$RentalOtherCostConstantFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override final  double value;
@override final  String type;
@override final  bool active;

/// Create a copy of RentalOtherCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RentalOtherCostConstantCopyWith<_RentalOtherCostConstant> get copyWith => __$RentalOtherCostConstantCopyWithImpl<_RentalOtherCostConstant>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RentalOtherCostConstantToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RentalOtherCostConstant&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.value, value) || other.value == value)&&(identical(other.type, type) || other.type == type)&&(identical(other.active, active) || other.active == active));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,value,type,active);

@override
String toString() {
  return 'RentalOtherCostConstant(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, value: $value, type: $type, active: $active)';
}


}

/// @nodoc
abstract mixin class _$RentalOtherCostConstantCopyWith<$Res> implements $RentalOtherCostConstantCopyWith<$Res> {
  factory _$RentalOtherCostConstantCopyWith(_RentalOtherCostConstant value, $Res Function(_RentalOtherCostConstant) _then) = __$RentalOtherCostConstantCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, double value, String type, bool active
});




}
/// @nodoc
class __$RentalOtherCostConstantCopyWithImpl<$Res>
    implements _$RentalOtherCostConstantCopyWith<$Res> {
  __$RentalOtherCostConstantCopyWithImpl(this._self, this._then);

  final _RentalOtherCostConstant _self;
  final $Res Function(_RentalOtherCostConstant) _then;

/// Create a copy of RentalOtherCostConstant
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? value = null,Object? type = null,Object? active = null,}) {
  return _then(_RentalOtherCostConstant(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
