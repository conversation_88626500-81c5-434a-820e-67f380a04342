import 'package:freezed_annotation/freezed_annotation.dart';

part 'property_manager.freezed.dart';
part 'property_manager.g.dart';

@freezed
abstract class PropertyManager with _$PropertyManager {
  const factory PropertyManager({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required String imageUrl,
    required String aboutInEnglish,
    required String aboutInArabic,
    required bool active,
  }) = _PropertyManager;

  factory PropertyManager.fromJson(Map<String, dynamic> json) =>
      _$PropertyManagerFromJson(json);
}
