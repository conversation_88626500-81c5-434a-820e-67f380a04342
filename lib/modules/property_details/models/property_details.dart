import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/modules/properties/models/property_feature.dart';
import 'package:maisour/modules/properties/models/property_image.dart';
import 'package:maisour/modules/property_details/models/property_amenity.dart';
import 'package:maisour/modules/property_details/models/property_document.dart';
import 'package:maisour/modules/property_details/models/seller_information.dart';
import 'package:maisour/modules/property_details/models/property_manager.dart';
import 'package:maisour/shared/enums/divided_frequency.dart';
import 'package:maisour/shared/enums/property_status.dart';

part 'property_details.freezed.dart';
part 'property_details.g.dart';

@freezed
abstract class PropertyDetails with _$PropertyDetails {
  const factory PropertyDetails({
    required int id,
    required String code,
    required String titleInEnglish,
    required String titleInArabic,
    required String cityInEnglish,
    required String cityInArabic,
    required String countryInEnglish,
    required String countryInArabic,
    required String addressInEnglish,
    required String addressInArabic,
    required String aboutInEnglish,
    required String aboutInArabic,
    required String investTextInEnglish,
    required String investTextInArabic,
    required String suggestedHoldingPeroid,
    required double minInvestmentAmount,
    required double rentAmount,
    required double fiveYearExpectedReturn,
    required DividedFrequency dividedFrequency,
    required int numberOfShare,
    required double totalPrice,
    required String endDate,
    required String purchaseDate,
    required String mapLocation,
    required double propertyManagementFee,
    required PropertyStatus propertyStatus,
    required bool limitedVisibility,
    required List<PropertyTransactionCost> propertyTransactionCosts,
    required List<PropertyPurchaseCost> propertyPurchaseCosts,
    required List<PropertyRentalOtherCost> propertyRentalOtherCosts,
    required List<PropertyImage> propertyImages,
    required List<PropertyFeature> propertyFeatures,
    required List<PropertyDocument> propertyDocuments,
    required List<PropertyAmenity> amenities,
    SellerInformation? sellerInformation,
    PropertyManager? propertyManager,
    required int remainingPeriod,
    required int numberOfInvestors,
    required double propertyPricePerShare,
    required double propertyAcquisition,
    required double totalPurchaseCost,
    required double totalTransactionCost,
    required double totalRentalOtherCost,
    required double expectedAnnualAppreciation,
    required bool featured,
    required double totalSuccessfulInvestment,
  }) = _PropertyDetails;

  factory PropertyDetails.fromJson(Map<String, dynamic> json) =>
      _$PropertyDetailsFromJson(json);
}

@freezed
abstract class PropertyTransactionCost with _$PropertyTransactionCost {
  const factory PropertyTransactionCost({
    required int id,
    required double actualValue,
    required TransactionCostConstant transactionCostConstant,
  }) = _PropertyTransactionCost;

  factory PropertyTransactionCost.fromJson(Map<String, dynamic> json) =>
      _$PropertyTransactionCostFromJson(json);
}

@freezed
abstract class TransactionCostConstant with _$TransactionCostConstant {
  const factory TransactionCostConstant({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required double value,
    required String type,
    required bool active,
  }) = _TransactionCostConstant;

  factory TransactionCostConstant.fromJson(Map<String, dynamic> json) =>
      _$TransactionCostConstantFromJson(json);
}

@freezed
abstract class PropertyPurchaseCost with _$PropertyPurchaseCost {
  const factory PropertyPurchaseCost({
    required int id,
    required double actualValue,
    required PurchaseCostConstant purchaseCostConstant,
  }) = _PropertyPurchaseCost;

  factory PropertyPurchaseCost.fromJson(Map<String, dynamic> json) =>
      _$PropertyPurchaseCostFromJson(json);
}

@freezed
abstract class PurchaseCostConstant with _$PurchaseCostConstant {
  const factory PurchaseCostConstant({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required double value,
    required String type,
    required bool active,
  }) = _PurchaseCostConstant;

  factory PurchaseCostConstant.fromJson(Map<String, dynamic> json) =>
      _$PurchaseCostConstantFromJson(json);
}

@freezed
abstract class PropertyRentalOtherCost with _$PropertyRentalOtherCost {
  const factory PropertyRentalOtherCost({
    required int id,
    required double actualValue,
    required RentalOtherCostConstant rentalOtherCostConstant,
  }) = _PropertyRentalOtherCost;

  factory PropertyRentalOtherCost.fromJson(Map<String, dynamic> json) =>
      _$PropertyRentalOtherCostFromJson(json);
}

@freezed
abstract class RentalOtherCostConstant with _$RentalOtherCostConstant {
  const factory RentalOtherCostConstant({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required double value,
    required String type,
    required bool active,
  }) = _RentalOtherCostConstant;

  factory RentalOtherCostConstant.fromJson(Map<String, dynamic> json) =>
      _$RentalOtherCostConstantFromJson(json);
}
