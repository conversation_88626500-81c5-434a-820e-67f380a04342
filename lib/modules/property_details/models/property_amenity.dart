import 'package:freezed_annotation/freezed_annotation.dart';

part 'property_amenity.freezed.dart';
part 'property_amenity.g.dart';

@freezed
abstract class PropertyAmenity with _$PropertyAmenity {
  const factory PropertyAmenity({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    required String imageUrl,
    required bool active,
  }) = _PropertyAmenity;

  factory PropertyAmenity.fromJson(Map<String, dynamic> json) =>
      _$PropertyAmenityFromJson(json);
}
