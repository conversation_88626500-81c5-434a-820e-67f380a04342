// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'property_document.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PropertyDocument {

 int get id; String get name; String get documentUrl;
/// Create a copy of PropertyDocument
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyDocumentCopyWith<PropertyDocument> get copyWith => _$PropertyDocumentCopyWithImpl<PropertyDocument>(this as PropertyDocument, _$identity);

  /// Serializes this PropertyDocument to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyDocument&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,documentUrl);

@override
String toString() {
  return 'PropertyDocument(id: $id, name: $name, documentUrl: $documentUrl)';
}


}

/// @nodoc
abstract mixin class $PropertyDocumentCopyWith<$Res>  {
  factory $PropertyDocumentCopyWith(PropertyDocument value, $Res Function(PropertyDocument) _then) = _$PropertyDocumentCopyWithImpl;
@useResult
$Res call({
 int id, String name, String documentUrl
});




}
/// @nodoc
class _$PropertyDocumentCopyWithImpl<$Res>
    implements $PropertyDocumentCopyWith<$Res> {
  _$PropertyDocumentCopyWithImpl(this._self, this._then);

  final PropertyDocument _self;
  final $Res Function(PropertyDocument) _then;

/// Create a copy of PropertyDocument
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? documentUrl = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PropertyDocument].
extension PropertyDocumentPatterns on PropertyDocument {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyDocument value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyDocument() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyDocument value)  $default,){
final _that = this;
switch (_that) {
case _PropertyDocument():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyDocument value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyDocument() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String name,  String documentUrl)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyDocument() when $default != null:
return $default(_that.id,_that.name,_that.documentUrl);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String name,  String documentUrl)  $default,) {final _that = this;
switch (_that) {
case _PropertyDocument():
return $default(_that.id,_that.name,_that.documentUrl);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String name,  String documentUrl)?  $default,) {final _that = this;
switch (_that) {
case _PropertyDocument() when $default != null:
return $default(_that.id,_that.name,_that.documentUrl);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyDocument implements PropertyDocument {
  const _PropertyDocument({required this.id, required this.name, required this.documentUrl});
  factory _PropertyDocument.fromJson(Map<String, dynamic> json) => _$PropertyDocumentFromJson(json);

@override final  int id;
@override final  String name;
@override final  String documentUrl;

/// Create a copy of PropertyDocument
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyDocumentCopyWith<_PropertyDocument> get copyWith => __$PropertyDocumentCopyWithImpl<_PropertyDocument>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyDocumentToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyDocument&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.documentUrl, documentUrl) || other.documentUrl == documentUrl));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,documentUrl);

@override
String toString() {
  return 'PropertyDocument(id: $id, name: $name, documentUrl: $documentUrl)';
}


}

/// @nodoc
abstract mixin class _$PropertyDocumentCopyWith<$Res> implements $PropertyDocumentCopyWith<$Res> {
  factory _$PropertyDocumentCopyWith(_PropertyDocument value, $Res Function(_PropertyDocument) _then) = __$PropertyDocumentCopyWithImpl;
@override @useResult
$Res call({
 int id, String name, String documentUrl
});




}
/// @nodoc
class __$PropertyDocumentCopyWithImpl<$Res>
    implements _$PropertyDocumentCopyWith<$Res> {
  __$PropertyDocumentCopyWithImpl(this._self, this._then);

  final _PropertyDocument _self;
  final $Res Function(_PropertyDocument) _then;

/// Create a copy of PropertyDocument
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? documentUrl = null,}) {
  return _then(_PropertyDocument(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,documentUrl: null == documentUrl ? _self.documentUrl : documentUrl // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
