import 'package:freezed_annotation/freezed_annotation.dart';

part 'property_document.freezed.dart';
part 'property_document.g.dart';

@freezed
abstract class PropertyDocument with _$PropertyDocument {
  const factory PropertyDocument({
    required int id,
    required String name,
    required String documentUrl,
  }) = _PropertyDocument;

  factory PropertyDocument.fromJson(Map<String, dynamic> json) =>
      _$PropertyDocumentFromJson(json);
}
