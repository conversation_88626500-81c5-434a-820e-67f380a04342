// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PropertyDetails _$PropertyDetailsFromJson(
  Map<String, dynamic> json,
) => _PropertyDetails(
  id: (json['id'] as num).toInt(),
  code: json['code'] as String,
  titleInEnglish: json['titleInEnglish'] as String,
  titleInArabic: json['titleInArabic'] as String,
  cityInEnglish: json['cityInEnglish'] as String,
  cityInArabic: json['cityInArabic'] as String,
  countryInEnglish: json['countryInEnglish'] as String,
  countryInArabic: json['countryInArabic'] as String,
  addressInEnglish: json['addressInEnglish'] as String,
  addressInArabic: json['addressInArabic'] as String,
  aboutInEnglish: json['aboutInEnglish'] as String,
  aboutInArabic: json['aboutInArabic'] as String,
  investTextInEnglish: json['investTextInEnglish'] as String,
  investTextInArabic: json['investTextInArabic'] as String,
  suggestedHoldingPeroid: json['suggestedHoldingPeroid'] as String,
  minInvestmentAmount: (json['minInvestmentAmount'] as num).toDouble(),
  rentAmount: (json['rentAmount'] as num).toDouble(),
  fiveYearExpectedReturn: (json['fiveYearExpectedReturn'] as num).toDouble(),
  dividedFrequency: $enumDecode(
    _$DividedFrequencyEnumMap,
    json['dividedFrequency'],
  ),
  numberOfShare: (json['numberOfShare'] as num).toInt(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  endDate: json['endDate'] as String,
  purchaseDate: json['purchaseDate'] as String,
  mapLocation: json['mapLocation'] as String,
  propertyManagementFee: (json['propertyManagementFee'] as num).toDouble(),
  propertyStatus: $enumDecode(_$PropertyStatusEnumMap, json['propertyStatus']),
  limitedVisibility: json['limitedVisibility'] as bool,
  propertyTransactionCosts: (json['propertyTransactionCosts'] as List<dynamic>)
      .map((e) => PropertyTransactionCost.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyPurchaseCosts: (json['propertyPurchaseCosts'] as List<dynamic>)
      .map((e) => PropertyPurchaseCost.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyRentalOtherCosts: (json['propertyRentalOtherCosts'] as List<dynamic>)
      .map((e) => PropertyRentalOtherCost.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyImages: (json['propertyImages'] as List<dynamic>)
      .map((e) => PropertyImage.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyFeatures: (json['propertyFeatures'] as List<dynamic>)
      .map((e) => PropertyFeature.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyDocuments: (json['propertyDocuments'] as List<dynamic>)
      .map((e) => PropertyDocument.fromJson(e as Map<String, dynamic>))
      .toList(),
  amenities: (json['amenities'] as List<dynamic>)
      .map((e) => PropertyAmenity.fromJson(e as Map<String, dynamic>))
      .toList(),
  sellerInformation: json['sellerInformation'] == null
      ? null
      : SellerInformation.fromJson(
          json['sellerInformation'] as Map<String, dynamic>,
        ),
  propertyManager: json['propertyManager'] == null
      ? null
      : PropertyManager.fromJson(
          json['propertyManager'] as Map<String, dynamic>,
        ),
  remainingPeriod: (json['remainingPeriod'] as num).toInt(),
  numberOfInvestors: (json['numberOfInvestors'] as num).toInt(),
  propertyPricePerShare: (json['propertyPricePerShare'] as num).toDouble(),
  propertyAcquisition: (json['propertyAcquisition'] as num).toDouble(),
  totalPurchaseCost: (json['totalPurchaseCost'] as num).toDouble(),
  totalTransactionCost: (json['totalTransactionCost'] as num).toDouble(),
  totalRentalOtherCost: (json['totalRentalOtherCost'] as num).toDouble(),
  expectedAnnualAppreciation: (json['expectedAnnualAppreciation'] as num)
      .toDouble(),
  featured: json['featured'] as bool,
  totalSuccessfulInvestment: (json['totalSuccessfulInvestment'] as num)
      .toDouble(),
);

Map<String, dynamic> _$PropertyDetailsToJson(_PropertyDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'titleInEnglish': instance.titleInEnglish,
      'titleInArabic': instance.titleInArabic,
      'cityInEnglish': instance.cityInEnglish,
      'cityInArabic': instance.cityInArabic,
      'countryInEnglish': instance.countryInEnglish,
      'countryInArabic': instance.countryInArabic,
      'addressInEnglish': instance.addressInEnglish,
      'addressInArabic': instance.addressInArabic,
      'aboutInEnglish': instance.aboutInEnglish,
      'aboutInArabic': instance.aboutInArabic,
      'investTextInEnglish': instance.investTextInEnglish,
      'investTextInArabic': instance.investTextInArabic,
      'suggestedHoldingPeroid': instance.suggestedHoldingPeroid,
      'minInvestmentAmount': instance.minInvestmentAmount,
      'rentAmount': instance.rentAmount,
      'fiveYearExpectedReturn': instance.fiveYearExpectedReturn,
      'dividedFrequency': _$DividedFrequencyEnumMap[instance.dividedFrequency]!,
      'numberOfShare': instance.numberOfShare,
      'totalPrice': instance.totalPrice,
      'endDate': instance.endDate,
      'purchaseDate': instance.purchaseDate,
      'mapLocation': instance.mapLocation,
      'propertyManagementFee': instance.propertyManagementFee,
      'propertyStatus': _$PropertyStatusEnumMap[instance.propertyStatus]!,
      'limitedVisibility': instance.limitedVisibility,
      'propertyTransactionCosts': instance.propertyTransactionCosts,
      'propertyPurchaseCosts': instance.propertyPurchaseCosts,
      'propertyRentalOtherCosts': instance.propertyRentalOtherCosts,
      'propertyImages': instance.propertyImages,
      'propertyFeatures': instance.propertyFeatures,
      'propertyDocuments': instance.propertyDocuments,
      'amenities': instance.amenities,
      'sellerInformation': instance.sellerInformation,
      'propertyManager': instance.propertyManager,
      'remainingPeriod': instance.remainingPeriod,
      'numberOfInvestors': instance.numberOfInvestors,
      'propertyPricePerShare': instance.propertyPricePerShare,
      'propertyAcquisition': instance.propertyAcquisition,
      'totalPurchaseCost': instance.totalPurchaseCost,
      'totalTransactionCost': instance.totalTransactionCost,
      'totalRentalOtherCost': instance.totalRentalOtherCost,
      'expectedAnnualAppreciation': instance.expectedAnnualAppreciation,
      'featured': instance.featured,
      'totalSuccessfulInvestment': instance.totalSuccessfulInvestment,
    };

const _$DividedFrequencyEnumMap = {
  DividedFrequency.weekly: 'Weekly',
  DividedFrequency.monthly: 'Monthly',
  DividedFrequency.quarterly: 'Quarterly',
  DividedFrequency.biannually: 'Biannually',
  DividedFrequency.annually: 'Annually',
};

const _$PropertyStatusEnumMap = {
  PropertyStatus.comingSoon: 'ComingSoon',
  PropertyStatus.cancelled: 'Cancelled',
  PropertyStatus.sold: 'Sold',
  PropertyStatus.funded: 'Funded',
  PropertyStatus.request: 'Request',
  PropertyStatus.live: 'Live',
};

_PropertyTransactionCost _$PropertyTransactionCostFromJson(
  Map<String, dynamic> json,
) => _PropertyTransactionCost(
  id: (json['id'] as num).toInt(),
  actualValue: (json['actualValue'] as num).toDouble(),
  transactionCostConstant: TransactionCostConstant.fromJson(
    json['transactionCostConstant'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PropertyTransactionCostToJson(
  _PropertyTransactionCost instance,
) => <String, dynamic>{
  'id': instance.id,
  'actualValue': instance.actualValue,
  'transactionCostConstant': instance.transactionCostConstant,
};

_TransactionCostConstant _$TransactionCostConstantFromJson(
  Map<String, dynamic> json,
) => _TransactionCostConstant(
  id: (json['id'] as num).toInt(),
  nameInEnglish: json['nameInEnglish'] as String,
  nameInArabic: json['nameInArabic'] as String,
  value: (json['value'] as num).toDouble(),
  type: json['type'] as String,
  active: json['active'] as bool,
);

Map<String, dynamic> _$TransactionCostConstantToJson(
  _TransactionCostConstant instance,
) => <String, dynamic>{
  'id': instance.id,
  'nameInEnglish': instance.nameInEnglish,
  'nameInArabic': instance.nameInArabic,
  'value': instance.value,
  'type': instance.type,
  'active': instance.active,
};

_PropertyPurchaseCost _$PropertyPurchaseCostFromJson(
  Map<String, dynamic> json,
) => _PropertyPurchaseCost(
  id: (json['id'] as num).toInt(),
  actualValue: (json['actualValue'] as num).toDouble(),
  purchaseCostConstant: PurchaseCostConstant.fromJson(
    json['purchaseCostConstant'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PropertyPurchaseCostToJson(
  _PropertyPurchaseCost instance,
) => <String, dynamic>{
  'id': instance.id,
  'actualValue': instance.actualValue,
  'purchaseCostConstant': instance.purchaseCostConstant,
};

_PurchaseCostConstant _$PurchaseCostConstantFromJson(
  Map<String, dynamic> json,
) => _PurchaseCostConstant(
  id: (json['id'] as num).toInt(),
  nameInEnglish: json['nameInEnglish'] as String,
  nameInArabic: json['nameInArabic'] as String,
  value: (json['value'] as num).toDouble(),
  type: json['type'] as String,
  active: json['active'] as bool,
);

Map<String, dynamic> _$PurchaseCostConstantToJson(
  _PurchaseCostConstant instance,
) => <String, dynamic>{
  'id': instance.id,
  'nameInEnglish': instance.nameInEnglish,
  'nameInArabic': instance.nameInArabic,
  'value': instance.value,
  'type': instance.type,
  'active': instance.active,
};

_PropertyRentalOtherCost _$PropertyRentalOtherCostFromJson(
  Map<String, dynamic> json,
) => _PropertyRentalOtherCost(
  id: (json['id'] as num).toInt(),
  actualValue: (json['actualValue'] as num).toDouble(),
  rentalOtherCostConstant: RentalOtherCostConstant.fromJson(
    json['rentalOtherCostConstant'] as Map<String, dynamic>,
  ),
);

Map<String, dynamic> _$PropertyRentalOtherCostToJson(
  _PropertyRentalOtherCost instance,
) => <String, dynamic>{
  'id': instance.id,
  'actualValue': instance.actualValue,
  'rentalOtherCostConstant': instance.rentalOtherCostConstant,
};

_RentalOtherCostConstant _$RentalOtherCostConstantFromJson(
  Map<String, dynamic> json,
) => _RentalOtherCostConstant(
  id: (json['id'] as num).toInt(),
  nameInEnglish: json['nameInEnglish'] as String,
  nameInArabic: json['nameInArabic'] as String,
  value: (json['value'] as num).toDouble(),
  type: json['type'] as String,
  active: json['active'] as bool,
);

Map<String, dynamic> _$RentalOtherCostConstantToJson(
  _RentalOtherCostConstant instance,
) => <String, dynamic>{
  'id': instance.id,
  'nameInEnglish': instance.nameInEnglish,
  'nameInArabic': instance.nameInArabic,
  'value': instance.value,
  'type': instance.type,
  'active': instance.active,
};
