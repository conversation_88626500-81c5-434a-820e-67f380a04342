// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'property_amenity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PropertyAmenity _$PropertyAmenityFromJson(Map<String, dynamic> json) =>
    _PropertyAmenity(
      id: (json['id'] as num).toInt(),
      nameInEnglish: json['nameInEnglish'] as String,
      nameInArabic: json['nameInArabic'] as String,
      imageUrl: json['imageUrl'] as String,
      active: json['active'] as bool,
    );

Map<String, dynamic> _$PropertyAmenityToJson(_PropertyAmenity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nameInEnglish': instance.nameInEnglish,
      'nameInArabic': instance.nameInArabic,
      'imageUrl': instance.imageUrl,
      'active': instance.active,
    };
