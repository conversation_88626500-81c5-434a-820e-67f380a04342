import 'api/property_details_api.dart';
import 'models/property_details.dart';

class PropertyDetailsService {
  final PropertyDetailsApi _propertyDetailsApi;

  PropertyDetailsService(this._propertyDetailsApi);

  // Get property details by ID
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<PropertyDetails> getPropertyDetails(int propertyId) async {
    return await _propertyDetailsApi.getPropertyDetails(propertyId);
  }
}
