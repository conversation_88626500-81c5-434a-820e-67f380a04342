import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/properties/widgets/property_image_indicator.dart';
import 'package:maisour/modules/property_details/widgets/contact_support.dart';
import 'package:maisour/modules/property_details/widgets/developer_and_manager.dart';
import 'package:maisour/modules/property_details/widgets/documents.dart';
import 'package:maisour/modules/property_details/widgets/investment_button.dart';
import 'package:maisour/modules/property_details/widgets/map_view.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/widgets/layouts/blur.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'providers/property_details_provider.dart';
import 'providers/property_details_ui_provider.dart';
import 'package:maisour/modules/property_details/widgets/property_details_shimmer_loading.dart';
import 'package:maisour/modules/property_details/widgets/property_basic_details_content.dart';
import 'package:maisour/modules/property_details/widgets/investment_breakdown.dart';
import 'package:maisour/modules/property_details/widgets/about_property.dart';
import 'package:maisour/modules/property_details/widgets/amenities.dart';
import 'package:maisour/shared/enums/property_status.dart';

class PropertyDetailsScreen extends ConsumerStatefulWidget {
  final int propertyId;

  const PropertyDetailsScreen({super.key, required this.propertyId});

  @override
  ConsumerState<PropertyDetailsScreen> createState() =>
      _PropertyDetailsScreenState();
}

class _PropertyDetailsScreenState extends ConsumerState<PropertyDetailsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load property details when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(propertyDetailsNotifierProvider(widget.propertyId).notifier)
          .loadPropertyDetails();
    });

    // Add scroll listener to detect app bar collapse
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final scrollOffset = _scrollController.offset;
    final isCollapsed = scrollOffset > 200; // Threshold for collapse detection

    // Only update if the state actually changed
    final currentState = ref.read(sliverAppBarUIProvider);
    if (currentState.isAppBarCollapsed != isCollapsed) {
      ref.read(sliverAppBarUIProvider.notifier).setAppBarCollapsed(isCollapsed);
    }
  }

  @override
  Widget build(BuildContext context) {
    final propertyDetailsState = ref.watch(
      propertyDetailsNotifierProvider(widget.propertyId),
    );
    final user = ref.watch(userProvider)!;

    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                Consumer(
                  builder: (context, ref, child) {
                    final uiState = ref.watch(sliverAppBarUIProvider);
                    return _buildSliverAppBar(propertyDetailsState, uiState);
                  },
                ),
                SliverToBoxAdapter(
                  child: _buildContent(propertyDetailsState, user),
                ),
              ],
            ),
          ),
          // Persistent bottom button
          if (propertyDetailsState.status == ApiStatus.success &&
              propertyDetailsState.propertyDetails != null &&
              propertyDetailsState.propertyDetails!.canInvest &&
              user.canInvest)
            InvestmentButton(
              propertyDetails: propertyDetailsState.propertyDetails!,
              user: user,
              propertyId: widget.propertyId,
            ).paddingSymmetric(horizontal: 16.w, vertical: 6.h),

          if (propertyDetailsState.status == ApiStatus.success &&
              propertyDetailsState.propertyDetails != null &&
              user.accountStatus == AccountStatus.suspended)
            _buildSuspendedContent().paddingSymmetric(
              horizontal: 16.w,
              vertical: 6.h,
            ),
          16.h.heightBox,
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(
    PropertyDetailsState state,
    SliverAppBarUIState uiState,
  ) {
    return SliverAppBar(
      expandedHeight: 300.w,
      pinned: true,
      backgroundColor: AppColors.white,
      title: AnimatedOpacity(
        opacity: uiState.isAppBarCollapsed ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child:
            state.status == ApiStatus.success && state.propertyDetails != null
            ? Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  state.propertyDetails!.titleInEnglish,
                  style: AppTextStyles.text16.bold.dark900,
                ),
              )
            : const SizedBox.shrink(),
      ),
      leading: Container(
        margin: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: AppColors.white,
          shape: BoxShape.circle,
        ),
        child: BackButton(onPressed: () => GoRouter.of(context).pop()),
      ),
      actions: [
        AnimatedOpacity(
          opacity: uiState.isAppBarCollapsed ? 0.0 : 1.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            margin: EdgeInsets.all(8.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (state.propertyDetails?.propertyStatus ==
                    PropertyStatus.live)
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                if (state.propertyDetails?.propertyStatus ==
                    PropertyStatus.live)
                  6.w.widthBox,
                Text(
                  state.propertyDetails?.propertyStatus.displayName ?? '',
                  style: AppTextStyles.text12.medium.dark900,
                ),
              ],
            ),
          ),
        ),
        16.w.widthBox,
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(state, uiState),
        collapseMode: CollapseMode.parallax,
      ),
    );
  }

  Widget _buildImageCarousel(
    PropertyDetailsState state,
    SliverAppBarUIState uiState,
  ) {
    if (state.status == ApiStatus.loading) {
      return Container(
        height: 300.w,
        color: AppColors.gray.shade200,
        child: Center(child: AppCircularLoader.medium()),
      );
    }

    if (state.status == ApiStatus.success && state.propertyDetails != null) {
      final images = state.propertyDetails!.sortedImagesWithCoverFirst;

      return Stack(
        children: [
          PageView.builder(
            itemCount: images.length,
            onPageChanged: (index) => ref
                .read(sliverAppBarUIProvider.notifier)
                .setCurrentImage(index),
            itemBuilder: (context, index) {
              return CachedNetworkImage(
                imageUrl: images[index].imageUrl,
                height: 300.w,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.gray.shade200,
                  child: Center(child: AppCircularLoader.medium()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.gray.shade200,
                  child: Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 48.w,
                      color: AppColors.gray.shade400,
                    ),
                  ),
                ),
              );
            },
          ),
          // Image counter
          if (images.length > 1)
            PositionedDirectional(
              bottom: 16.h,
              end: 16.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
                decoration: BoxDecoration(
                  color: AppColors.dark.alphaPercent(60),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Assets.icons.images.svg(
                      width: 16.w,
                      height: 16.w,
                      colorFilter: ColorFilter.mode(
                        AppColors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                    6.w.widthBox,
                    Text(
                      '${uiState.currentImage + 1}/${images.length}',
                      style: AppTextStyles.text12.medium.white,
                    ),
                  ],
                ),
              ),
            ),
          // Image dots indicator
          if (images.length > 1)
            PositionedDirectional(
              bottom: 16.h,
              start: 0,
              end: 0,
              child: PropertyImageIndicator(
                currentPage: uiState.currentImage,
                totalPages: images.length,
              ),
            ),
        ],
      ).blurred(disable: state.propertyDetails?.limitedVisibility == false);
    }

    return Container(
      height: 300.h,
      color: AppColors.gray.shade200,
      child: Center(
        child: Icon(Icons.image, size: 48.w, color: AppColors.gray.shade400),
      ),
    );
  }

  Widget _buildContent(PropertyDetailsState state, AppUser user) {
    switch (state.status) {
      case ApiStatus.initial:
      case ApiStatus.loading:
        return const PropertyDetailsShimmerLoading();
      case ApiStatus.success:
        return Column(
          children: [
            PropertyBasicDetailsContent(
              propertyDetails: state.propertyDetails!,
              user: user,
            ),

            if (state.propertyDetails!.limitedVisibility == false) ...[
              const AppSpacer(),
              InvestmentBreakdown(
                propertyDetails: state.propertyDetails!,
                user: user,
              ),
              const AppSpacer(),
              AboutProperty(
                propertyDetails: state.propertyDetails!,
                user: user,
              ),
              if (state.propertyDetails!.amenities.isNotEmpty) ...[
                const AppSpacer(),
                Amenities(propertyDetails: state.propertyDetails!, user: user),
                const AppSpacer(),
                MapView(propertyDetails: state.propertyDetails!),
                if (state.propertyDetails!.propertyDocuments.isNotEmpty) ...[
                  const AppSpacer(),
                  Documents(propertyDetails: state.propertyDetails!),
                ],
                if (state.propertyDetails!.sellerInformation != null ||
                    state.propertyDetails!.propertyManager != null) ...[
                  const AppSpacer(),
                  DeveloperAndManager(
                    sellerInformation: state.propertyDetails!.sellerInformation,
                    propertyManager: state.propertyDetails!.propertyManager,
                  ),
                ],
                const AppSpacer(),
              ],
            ],

            const ContactSupport(),
            RegulatedByDsfa(
              iconSize: 24.w,
              textStyle: AppTextStyles.text14.medium.dark300,
            ),
            16.h.heightBox,
          ],
        );
      case ApiStatus.error:
        return _buildError(state.errorMessage ?? LocaleKeys.tryAgain.tr());
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildError(String errorMessage) {
    return Center(child: Text(errorMessage));
  }

  Widget _buildSuspendedContent() {
    return Card(
      color: AppColors.white,
      elevation: 0.5,
      child: Row(
        children: [
          Assets.icons.warning.svg(width: 32.w, height: 32.w),
          8.w.widthBox,
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.yourAccountHasBeenSuspended.tr(),
                style: AppTextStyles.text14.bold.dark900,
              ),
              Row(
                children: [
                  Text(
                    LocaleKeys.toViewMoreDetailsPlease.tr(),
                    style: AppTextStyles.text12.medium.dark300,
                  ),
                  4.w.widthBox,
                  Text(
                    LocaleKeys.contactUs.tr().toLowerCase(),
                    style: AppTextStyles.text12.medium.primary.underline(),
                  ).onTap(() {
                    GoRouter.of(context).pushNamed(RouteName.getHelp.name);
                  }),
                ],
              ),
            ],
          ),
        ],
      ).paddingAll(14.w),
    );
  }
}
