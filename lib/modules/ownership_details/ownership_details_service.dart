import 'package:maisour/modules/ownership_details/api/ownership_details_api.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import 'package:maisour/shared/models/app_user.dart';

class OwnershipDetailsService {
  final OwnershipDetailsApi _ownershipDetailsApi;

  OwnershipDetailsService(this._ownershipDetailsApi);

  /// Get ownership details for a property
  Future<OwnershipDetailsResponse> getOwnershipDetails({
    required int propertyId,
  }) async {
    return await _ownershipDetailsApi.getOwnershipDetails(propertyId);
  }

  /// Generate OTP for investment operations (cancel/update)
  Future<OtpResponse> generateOTP() async {
    // Get public IP address
    final ipAddress = await _ownershipDetailsApi.getPublicIp();

    // Call API with IP address as query parameter
    return await _ownershipDetailsApi.generateOTP(ipAddress);
  }

  /// Cancel investment
  Future<AppUser> cancelInvestment({
    required int otpRecordId,
    required int investmentId,
  }) async {
    return await _ownershipDetailsApi.cancelInvestment(
      otpRecordId,
      investmentId,
    );
  }
}
