import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/modules/ownership_details/providers/ownership_details_provider.dart';
import 'package:maisour/modules/ownership_details/providers/ownership_details_ui_provider.dart';
import 'package:maisour/modules/ownership_details/widgets/dividend_history.dart';
import 'package:maisour/modules/ownership_details/widgets/investment_summary.dart';
import 'package:maisour/modules/ownership_details/widgets/ownership_details_shimmer_loading.dart';
import 'package:maisour/modules/ownership_details/widgets/proprty_price_section.dart';
import 'package:maisour/modules/ownership_details/widgets/timelines.dart';
import 'package:maisour/modules/properties/widgets/property_image_indicator.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';

class OwnershipDetailsScreen extends ConsumerStatefulWidget {
  final int propertyId;

  const OwnershipDetailsScreen({super.key, required this.propertyId});

  @override
  ConsumerState<OwnershipDetailsScreen> createState() =>
      _OwnershipDetailsScreenState();
}

class _OwnershipDetailsScreenState
    extends ConsumerState<OwnershipDetailsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Load ownership details when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(ownershipDetailsNotifierProvider(widget.propertyId).notifier)
          .loadOwnershipDetails(widget.propertyId);
    });

    // Add scroll listener to detect app bar collapse
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final scrollOffset = _scrollController.offset;
    final isCollapsed = scrollOffset > 200; // Threshold for collapse detection

    // Only update if the state actually changed
    final currentState = ref.read(ownershipDetailsSliverAppBarUIProvider);
    if (currentState.isAppBarCollapsed != isCollapsed) {
      ref
          .read(ownershipDetailsSliverAppBarUIProvider.notifier)
          .setAppBarCollapsed(isCollapsed);
    }
  }

  @override
  Widget build(BuildContext context) {
    final ownershipDetailsState = ref.watch(
      ownershipDetailsNotifierProvider(widget.propertyId),
    );

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          Consumer(
            builder: (context, ref, child) {
              final uiState = ref.watch(ownershipDetailsSliverAppBarUIProvider);
              return _buildSliverAppBar(ownershipDetailsState, uiState);
            },
          ),
          SliverToBoxAdapter(child: _buildContent(ownershipDetailsState)),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(
    OwnershipDetailsState state,
    OwnershipDetailsSliverAppBarUIState uiState,
  ) {
    return SliverAppBar(
      expandedHeight: 300.w,
      pinned: true,
      backgroundColor: AppColors.white,
      title: AnimatedOpacity(
        opacity: uiState.isAppBarCollapsed ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child:
            state.status == ApiStatus.success && state.ownershipDetails != null
            ? Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  state.ownershipDetails!.propertyDetails.titleInEnglish,
                  style: AppTextStyles.text16.bold.dark900,
                ),
              )
            : const SizedBox.shrink(),
      ),
      leading: Container(
        margin: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: AppColors.white,
          shape: BoxShape.circle,
        ),
        child: BackButton(onPressed: () => GoRouter.of(context).pop()),
      ),
      actions: [
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              height: 32.w,
              width: 32.w,
              decoration: BoxDecoration(
                color: AppColors.white,
                shape: BoxShape.circle,
              ),
            ),
            Assets.icons.info.svg(
              width: 20.w,
              height: 20.w,
              colorFilter: ColorFilter.mode(
                AppColors.dark.shade900,
                BlendMode.srcIn,
              ),
            ),
          ],
        ).onTap(() {
          GoRouter.of(context)
              .pushNamed(
                RouteName.propertyDetails.name,
                extra: widget.propertyId,
              )
              .then((value) {
                debugPrint(
                  '================ 🔄 Refreshed Ownership Details 🔄 ================',
                );
                ref
                    .read(
                      ownershipDetailsNotifierProvider(
                        widget.propertyId,
                      ).notifier,
                    )
                    .loadOwnershipDetails(widget.propertyId);
              });
        }),
        12.w.widthBox,
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              height: 32.w,
              width: 32.w,
              decoration: BoxDecoration(
                color: AppColors.white,
                shape: BoxShape.circle,
              ),
            ),
            Assets.icons.chat.svg(
              width: 20.w,
              height: 20.w,
              colorFilter: ColorFilter.mode(
                AppColors.dark.shade900,
                BlendMode.srcIn,
              ),
            ),
          ],
        ).onTap(() {
          GoRouter.of(context).pushNamed(RouteName.getHelp.name);
        }),

        // Status container - conditionally shown/hidden
        if (!uiState.isAppBarCollapsed)
          Container(
            margin: EdgeInsets.all(8.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (state.ownershipDetails?.propertyDetails.status ==
                    PropertyStatus.live)
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                if (state.ownershipDetails?.propertyDetails.status ==
                    PropertyStatus.live)
                  6.w.widthBox,
                Text(
                  state.ownershipDetails?.propertyDetails.status.displayName ??
                      '',
                  style: AppTextStyles.text12.medium.dark900,
                ),
              ],
            ),
          ),
        16.w.widthBox,
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: _buildImageCarousel(state, uiState),
        collapseMode: CollapseMode.parallax,
      ),
    );
  }

  Widget _buildImageCarousel(
    OwnershipDetailsState state,
    OwnershipDetailsSliverAppBarUIState uiState,
  ) {
    if (state.status == ApiStatus.loading) {
      return Container(
        height: 300.w,
        color: AppColors.gray.shade200,
        child: Center(child: AppCircularLoader.medium()),
      );
    }

    if (state.status == ApiStatus.success && state.ownershipDetails != null) {
      final images =
          state.ownershipDetails!.propertyDetails.sortedImagesWithCoverFirst;

      return Stack(
        children: [
          PageView.builder(
            itemCount: images.length,
            onPageChanged: (index) => ref
                .read(ownershipDetailsSliverAppBarUIProvider.notifier)
                .setCurrentImage(index),
            itemBuilder: (context, index) {
              return CachedNetworkImage(
                imageUrl: images[index].imageUrl,
                height: 300.w,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.gray.shade200,
                  child: Center(child: AppCircularLoader.medium()),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.gray.shade200,
                  child: Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 48.w,
                      color: AppColors.gray.shade400,
                    ),
                  ),
                ),
              );
            },
          ),
          // Image counter
          if (images.length > 1)
            PositionedDirectional(
              bottom: 16.h,
              end: 16.w,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
                decoration: BoxDecoration(
                  color: AppColors.dark.alphaPercent(60),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Assets.icons.images.svg(
                      width: 16.w,
                      height: 16.w,
                      colorFilter: ColorFilter.mode(
                        AppColors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                    6.w.widthBox,
                    Text(
                      '${uiState.currentImage + 1}/${images.length}',
                      style: AppTextStyles.text12.medium.white,
                    ),
                  ],
                ),
              ),
            ),
          // Image dots indicator
          if (images.length > 1)
            PositionedDirectional(
              bottom: 16.h,
              start: 0,
              end: 0,
              child: PropertyImageIndicator(
                currentPage: uiState.currentImage,
                totalPages: images.length,
              ),
            ),
        ],
      );
    }

    return Container(
      height: 300.h,
      color: AppColors.gray.shade200,
      child: Center(
        child: Icon(Icons.image, size: 48.w, color: AppColors.gray.shade400),
      ),
    );
  }

  Widget _buildError(String errorMessage) {
    return Center(child: Text(errorMessage));
  }

  Widget _buildContent(OwnershipDetailsState state) {
    final user = ref.watch(userProvider)!;
    switch (state.status) {
      case ApiStatus.loading:
        return const OwnershipDetailsShimmerLoading();

      case ApiStatus.error:
        return _buildError(state.errorMessage ?? LocaleKeys.tryAgain.tr());

      case ApiStatus.success:
        final ownershipDetails = state.ownershipDetails!;

        return Column(
          children: [
            // Property Information Section
            ProprtyPriceSection(
              propertyDetails: ownershipDetails.propertyDetails,
              user: user,
            ),

            const AppSpacer(),
            // Investment Summary
            InvestmentSummaryCard(
              investmentSummary: ownershipDetails.investmentSummary,
            ),

            // Timeline
            if (ownershipDetails.timeline.isNotEmpty) ...[
              const AppSpacer(),
              Timelines(
                timeline: ownershipDetails.timeline,
                user: user,
                propertyDetails: ownershipDetails.propertyDetails,
              ),
            ],

            if (ownershipDetails.dividendHistory.isNotEmpty) ...[
              const AppSpacer(),
              DividendHistoryCard(
                dividendHistory: ownershipDetails.dividendHistory,
                user: user,
              ),
            ],

            24.h.heightBox,
          ],
        );

      default:
        return const SizedBox.shrink();
    }
  }
}
