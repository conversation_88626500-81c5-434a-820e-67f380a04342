import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for managing Ownership Details SliverAppBar UI state
final ownershipDetailsSliverAppBarUIProvider =
    StateNotifierProvider.autoDispose<
      OwnershipDetailsSliverAppBarUINotifier,
      OwnershipDetailsSliverAppBarUIState
    >((ref) {
      return OwnershipDetailsSliverAppBarUINotifier();
    }, name: 'ownershipDetailsSliverAppBarUIProvider');

/// UI state for Ownership Details SliverAppBar
class OwnershipDetailsSliverAppBarUIState {
  final bool isAppBarCollapsed;
  final int currentImage;

  const OwnershipDetailsSliverAppBarUIState({
    this.isAppBarCollapsed = false,
    this.currentImage = 0,
  });

  OwnershipDetailsSliverAppBarUIState copyWith({
    bool? isAppBarCollapsed,
    int? currentImage,
  }) {
    return OwnershipDetailsSliverAppBarUIState(
      isAppBarCollapsed: isAppBarCollapsed ?? this.isAppBarCollapsed,
      currentImage: currentImage ?? this.currentImage,
    );
  }
}

/// Notifier for Ownership Details SliverAppBar UI state
class OwnershipDetailsSliverAppBarUINotifier
    extends StateNotifier<OwnershipDetailsSliverAppBarUIState> {
  OwnershipDetailsSliverAppBarUINotifier()
    : super(const OwnershipDetailsSliverAppBarUIState());

  /// Update app bar collapse state
  void setAppBarCollapsed(bool isCollapsed) {
    state = state.copyWith(isAppBarCollapsed: isCollapsed);
  }

  /// Update current image index
  void setCurrentImage(int imageIndex) {
    state = state.copyWith(currentImage: imageIndex);
  }

  /// Reset UI state
  void reset() {
    state = const OwnershipDetailsSliverAppBarUIState();
  }
}
