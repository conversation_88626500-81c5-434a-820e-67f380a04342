import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/ownership_details/api/ownership_details_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Ownership Details API provider
final ownershipDetailsApiProvider = Provider.autoDispose<OwnershipDetailsApi>((
  ref,
) {
  final dio = ref.watch(networkServiceProvider);
  return OwnershipDetailsApi(dio);
}, name: 'ownershipDetailsApiProvider');
