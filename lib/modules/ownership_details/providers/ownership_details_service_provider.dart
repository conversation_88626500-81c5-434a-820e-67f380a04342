import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/ownership_details/ownership_details_service.dart';
import 'ownership_details_api_provider.dart';

/// Ownership Details Service provider
final ownershipDetailsServiceProvider =
    Provider.autoDispose<OwnershipDetailsService>((ref) {
      final api = ref.watch(ownershipDetailsApiProvider);
      return OwnershipDetailsService(api);
    }, name: 'ownershipDetailsServiceProvider');
