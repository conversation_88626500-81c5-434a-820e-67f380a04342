import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/ownership_details/ownership_details_service.dart';
import 'package:maisour/modules/ownership_details/providers/ownership_details_service_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Provider for InvestmentOperationsNotifier
final investmentOperationsNotifierProvider =
    StateNotifierProvider.autoDispose<
      InvestmentOperationsNotifier,
      InvestmentOperationsState
    >((ref) {
      final service = ref.watch(ownershipDetailsServiceProvider);
      return InvestmentOperationsNotifier(service);
    }, name: 'investmentOperationsNotifierProvider');

class InvestmentOperationsNotifier
    extends StateNotifier<InvestmentOperationsState>
    with DioExceptionMapper {
  final OwnershipDetailsService _service;

  InvestmentOperationsNotifier(this._service)
    : super(const InvestmentOperationsState());

  /// Generate OTP for investment operations
  Future<void> generateOTP() async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: true,
      isCancellingInvestment: false,
    );

    try {
      final otpResponse = await _service.generateOTP();
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        otpId: otpResponse.id,
        isGeneratingOTP: true,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isGeneratingOTP: false,
      );
    }
  }

  /// Cancel investment
  Future<void> cancelInvestment({
    required int otpRecordId,
    required int investmentId,
  }) async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: false,
      isCancellingInvestment: true,
    );

    try {
      final updatedUser = await _service.cancelInvestment(
        otpRecordId: otpRecordId,
        investmentId: investmentId,
      );
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        isCancellingInvestment: true,
        updatedUser: updatedUser,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isCancellingInvestment: false,
      );
    }
  }

  /// Reset state
  void reset() {
    state = const InvestmentOperationsState();
  }
}

class InvestmentOperationsState {
  final ApiStatus status;
  final String? errorMessage;
  final String? errorKey;
  final int? otpId;
  final bool isGeneratingOTP;
  final bool isCancellingInvestment;
  final AppUser? updatedUser;

  const InvestmentOperationsState({
    this.status = ApiStatus.initial,
    this.errorMessage,
    this.errorKey,
    this.otpId,
    this.isGeneratingOTP = false,
    this.isCancellingInvestment = false,
    this.updatedUser,
  });

  InvestmentOperationsState copyWith({
    ApiStatus? status,
    String? errorMessage,
    String? errorKey,
    int? otpId,
    bool? isGeneratingOTP,
    bool? isCancellingInvestment,
    AppUser? updatedUser,
  }) {
    return InvestmentOperationsState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
      otpId: otpId ?? this.otpId,
      isGeneratingOTP: isGeneratingOTP ?? this.isGeneratingOTP,
      isCancellingInvestment:
          isCancellingInvestment ?? this.isCancellingInvestment,
      updatedUser: updatedUser ?? this.updatedUser,
    );
  }
}
