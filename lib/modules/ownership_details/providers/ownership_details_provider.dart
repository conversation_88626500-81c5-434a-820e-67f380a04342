import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/modules/ownership_details/ownership_details_service.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:dio/dio.dart';
import 'ownership_details_service_provider.dart';

/// Ownership Details State
class OwnershipDetailsState {
  final ApiStatus status;
  final OwnershipDetailsResponse? ownershipDetails;
  final String? errorMessage;

  const OwnershipDetailsState({
    this.status = ApiStatus.initial,
    this.ownershipDetails,
    this.errorMessage,
  });

  OwnershipDetailsState copyWith({
    ApiStatus? status,
    OwnershipDetailsResponse? ownershipDetails,
    String? errorMessage,
  }) {
    return OwnershipDetailsState(
      status: status ?? this.status,
      ownershipDetails: ownershipDetails ?? this.ownershipDetails,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Ownership Details Notifier
class OwnershipDetailsNotifier extends StateNotifier<OwnershipDetailsState>
    with DioExceptionMapper {
  final OwnershipDetailsService _service;

  OwnershipDetailsNotifier(this._service)
    : super(const OwnershipDetailsState());

  /// Load ownership details
  Future<void> loadOwnershipDetails(int propertyId) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      final response = await _service.getOwnershipDetails(
        propertyId: propertyId,
      );
      state = state.copyWith(
        status: ApiStatus.success,
        ownershipDetails: response,
      );
    } on DioException catch (error, stackTrace) {
      final failure = mapDioExceptionToFailure(error, stackTrace);
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: failure.message,
      );
    } catch (error) {
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: error.toString(),
      );
    }
  }

  /// Reset state
  void reset() {
    state = const OwnershipDetailsState();
  }
}

/// Ownership Details Provider
final ownershipDetailsNotifierProvider = StateNotifierProvider.autoDispose
    .family<OwnershipDetailsNotifier, OwnershipDetailsState, int>((
      ref,
      propertyId,
    ) {
      final service = ref.watch(ownershipDetailsServiceProvider);
      return OwnershipDetailsNotifier(service);
    }, name: 'ownershipDetailsNotifierProvider');
