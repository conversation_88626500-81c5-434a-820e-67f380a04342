import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/modules/ownership_details/widgets/bottom_sheets/timeline_details_bottom_sheet.dart';
import 'package:maisour/shared/enums/investment_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/date_formatter.dart';
import 'package:maisour/shared/widgets/indicators/dotted_line_painter.dart';

class Timelines extends StatelessWidget {
  final List<TimelineEvent> timeline;
  final AppUser user;
  final PropertyDetails propertyDetails;

  const Timelines({
    super.key,
    required this.timeline,
    required this.user,
    required this.propertyDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Text(
          LocaleKeys.timeline.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),

        16.w.heightBox,

        // Timeline Content
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.gray.shade100),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Column(
            children: timeline.asMap().entries.map((entry) {
              final index = entry.key;
              final event = entry.value;
              final isLast = index == timeline.length - 1;
              return _buildTimelineEvent(event, isLast, context);
            }).toList(),
          ),
        ),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildTimelineEvent(
    TimelineEvent event,
    bool isLast,
    BuildContext context,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline dot + dotted line
        Column(
          children: [
            Stack(
              alignment: AlignmentDirectional.center,
              children: [
                Container(
                  height: 32.w,
                  width: 32.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.success,
                  ),
                ),
                _getIconForEventType(event.status),
              ],
            ),

            if (!isLast)
              CustomPaint(
                painter: DottedLinePainter(),
                child: SizedBox(height: 50.h, width: 2.w),
              ),
          ],
        ),
        12.w.widthBox,
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getTitleForEventType(event.status),
                style: AppTextStyles.text14.bold.dark900,
              ),
              4.h.heightBox,
              Text(
                DateFormatter.formatDate(event.transactionDate),
                style: AppTextStyles.text14.medium.gray300,
              ),
              4.h.heightBox,
              Text(
                LocaleKeys.viewDetails.tr(),
                style: AppTextStyles.text14.semiBold.primary,
              ).onTap(() {
                TimelineDetailsBottomSheet.show(
                  context,
                  event,
                  user,
                  propertyDetails,
                );
              }),
            ],
          ),
        ),
      ],
    );
  }

  String _getTitleForEventType(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.successful:
        return LocaleKeys.investedInProperty.tr();
      case InvestmentStatus.pending:
        return LocaleKeys.interestShownInProperty.tr();

      default:
        return '';
    }
  }

  Widget _getIconForEventType(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.successful:
        return Assets.icons.hand.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
        );

      case InvestmentStatus.pending:
        return Assets.icons.star.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
        );

      default:
        return Assets.icons.coin.svg(
          width: 20.w,
          height: 20.w,
          colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
        );
    }
  }
}
