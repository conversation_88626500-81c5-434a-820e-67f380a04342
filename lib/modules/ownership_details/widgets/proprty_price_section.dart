import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';

class ProprtyPriceSection extends StatelessWidget {
  const ProprtyPriceSection({
    super.key,
    required this.propertyDetails,
    required this.user,
  });

  final PropertyDetails propertyDetails;
  final AppUser user;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 16.w),
      child: Column(
        children: [
          // Property Title
          Text(
            propertyDetails.title(context.locale.languageCode),
            style: AppTextStyles.text20.bold.dark900,
          ),
          12.w.heightBox,
          // Price Information
          Row(
            children: [
              Expanded(
                child: _buildPriceInfo(
                  LocaleKeys.purchasePrice.tr(),
                  propertyDetails.purchasePrice,
                ),
              ),
              12.w.widthBox,
              Expanded(
                child: _buildPriceInfo(
                  LocaleKeys.currentValuation.tr(),
                  propertyDetails.currentValuation,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriceInfo(String label, double amount) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: AppTextStyles.text12.medium.gray700),
        8.h.heightBox,
        Row(
          children: [
            if (user.usesImageSymbol)
              Assets.images.dirham.image(width: 20.w, height: 20.w)
            else
              Text(
                user.currencyCode.currencySymbol,
                style: AppTextStyles.text14.bold.dark900,
              ),
            4.w.widthBox,
            Text(
              user.getCurrencyValue(amount),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
          ],
        ),
      ],
    );
  }
}
