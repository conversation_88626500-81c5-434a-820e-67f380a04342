import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class InvestmentSummaryCard extends StatelessWidget {
  final InvestmentSummary investmentSummary;

  const InvestmentSummaryCard({super.key, required this.investmentSummary});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Text(
            LocaleKeys.investmentSummary.tr(),
            style: AppTextStyles.text18.bold.dark900,
          ),

          // Investment Details Card
          Container(
            margin: EdgeInsets.only(top: 8.h),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: AppColors.gray.alphaPercent(5),
              border: Border.all(color: AppColors.gray.shade100),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                // Shares Row
                _buildInfoRow(
                  LocaleKeys.shares.tr(),
                  investmentSummary.shares.toStringAsFixed(2),
                ),
                8.h.heightBox,
                // Ownership Percentage Row
                _buildInfoRow(
                  LocaleKeys.ownershipPercentage.tr(),
                  '${investmentSummary.ownershipPercentage.toStringAsFixed(2)}%',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: AppTextStyles.text12.medium.gray700),
        Text(value, style: AppTextStyles.text14.semiBold.gray900),
      ],
    );
  }
}
