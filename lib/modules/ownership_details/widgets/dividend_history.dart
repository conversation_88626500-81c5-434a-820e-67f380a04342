import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/date_formatter.dart';

class DividendHistoryCard extends StatelessWidget {
  final List<DividendHistory> dividendHistory;
  final AppUser user;

  const DividendHistoryCard({
    super.key,
    required this.dividendHistory,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.w.heightBox,
        // Section Header
        Text(
          LocaleKeys.dividendHistory.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ).paddingHorizontal(16.w),
        12.h.heightBox,
        // Dividend History Content
        Column(
          children: dividendHistory.asMap().entries.map((entry) {
            final index = entry.key;
            final event = entry.value;
            final isLast = index == dividendHistory.length - 1;
            return _buildDividendItem(event, isLast);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDividendItem(DividendHistory dividend, bool isLast) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          16.w.widthBox,
          //Icon Container
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: AppColors.gray.shade100,
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              Assets.icons.arrowBottomLeft.svg(
                width: 32.w,
                height: 32.w,
                colorFilter: ColorFilter.mode(
                  AppColors.gray.shade600,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
          10.w.widthBox,

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      LocaleKeys.dividendCredited.tr(),
                      style: AppTextStyles.text14.bold.dark900,
                    ),
                    // Amount
                    Row(
                      children: [
                        if (user.usesImageSymbol)
                          Assets.images.dirham.image(width: 20.w, height: 20.w)
                        else
                          Text(
                            user.currencyCode.currencySymbol,
                            style: AppTextStyles.text14.bold.dark900,
                          ),
                        4.w.widthBox,
                        Text(
                          user.getCurrencyValue(dividend.amount),
                          style: AppTextStyles.text14.bold.dark900,
                        ),
                      ],
                    ),
                  ],
                ).paddingEnd(16.w),
                6.h.heightBox,
                Text(
                  DateFormatter.formatDate(dividend.creditedDate),
                  style: AppTextStyles.text14.medium.dark300,
                ),

                8.h.heightBox,
                if (!isLast)
                  Divider(color: AppColors.gray.shade200, thickness: 1.h),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
