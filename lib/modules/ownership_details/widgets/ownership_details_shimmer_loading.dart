import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';

class OwnershipDetailsShimmerLoading extends StatelessWidget {
  const OwnershipDetailsShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property title shimmer
          Container(
            height: 28.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ).applyShimmer(),
          12.h.heightBox,

          // Price information shimmer
          Row(
            children: [
              Expanded(child: _buildPriceInfoShimmer()),
              12.w.widthBox,
              Expanded(child: _buildPriceInfoShimmer()),
            ],
          ),
          24.h.heightBox,

          // Investment summary shimmer
          Container(
            height: 100.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(12.r),
            ),
          ).applyShimmer(),
          16.h.heightBox,

          // Timeline shimmer
          Container(
            height: 200.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(12.r),
            ),
          ).applyShimmer(),
          16.h.heightBox,

          // Dividend history shimmer
          Container(
            height: 150.h,
            decoration: BoxDecoration(
              color: AppColors.gray.shade200,
              borderRadius: BorderRadius.circular(12.r),
            ),
          ).applyShimmer(),
        ],
      ),
    );
  }

  Widget _buildPriceInfoShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label shimmer
        Container(
          height: 14.h,
          width: 80.w,
          decoration: BoxDecoration(
            color: AppColors.gray.shade200,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ).applyShimmer(),
        8.h.heightBox,
        // Amount shimmer
        Container(
          height: 20.h,
          width: 100.w,
          decoration: BoxDecoration(
            color: AppColors.gray.shade200,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ).applyShimmer(),
      ],
    );
  }
}
