import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/ownership_details/models/ownership_details.dart';
import 'package:maisour/modules/ownership_details/providers/ownership_details_provider.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/investment_status.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';
import 'package:maisour/modules/ownership_details/providers/investment_operations_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TimelineDetailsBottomSheet extends StatelessWidget {
  final TimelineEvent event;
  final AppUser user;
  final PropertyDetails propertyDetails;

  const TimelineDetailsBottomSheet({
    super.key,
    required this.event,
    required this.user,
    required this.propertyDetails,
  });

  static void show(
    BuildContext context,
    TimelineEvent event,
    AppUser user,
    PropertyDetails propertyDetails,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      builder: (context) => TimelineDetailsBottomSheet(
        event: event,
        user: user,
        propertyDetails: propertyDetails,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // Listen to investment operations state
        ref.listen<
          InvestmentOperationsState
        >(investmentOperationsNotifierProvider, (previous, next) {
          if (next.status == ApiStatus.success &&
              next.isGeneratingOTP == true) {
            // OTP generation successful, navigate to OTP verification
            GoRouter.of(
              context,
            ).pushNamed(RouteName.otpVerification.name).then((value) {
              if (context.mounted) {
                Navigator.of(context).pop();
              }
              if (value is int) {
                // Call cancel investment API with OTP record ID and investment ID
                ref
                    .read(investmentOperationsNotifierProvider.notifier)
                    .cancelInvestment(
                      otpRecordId: value,
                      investmentId: event.investmentId,
                    );
              }
            });
          } else if (next.status == ApiStatus.success &&
              next.isCancellingInvestment == true) {
            // Cancel investment successful, update user and refresh ownership details
            ref.updateCurrentUser(next.updatedUser!);
            ref
                .read(
                  ownershipDetailsNotifierProvider(propertyDetails.id).notifier,
                )
                .loadOwnershipDetails(propertyDetails.id);
          } else if (next.status == ApiStatus.error) {
            final (
              title,
              message,
            ) = ErrorMessageHelper.getLocalizedErrorMessage(
              errorKey: next.errorKey,
              fallbackMessage: next.errorMessage ?? 'Failed to send OTP',
            );
            context.showErrorToast(title, message);
          }
        });

        final investmentOperationsState = ref.watch(
          investmentOperationsNotifierProvider,
        );

        final isGeneratingOTP =
            investmentOperationsState.status == ApiStatus.loading &&
            investmentOperationsState.isGeneratingOTP == true;

        final isCancellingInvestment =
            investmentOperationsState.status == ApiStatus.loading &&
            investmentOperationsState.isCancellingInvestment == true;

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            16.h.heightBox,
            const BottomSheetHandleBar(),
            12.h.heightBox,
            // Header
            Text(
              LocaleKeys.purchaseDetails.tr(),
              style: AppTextStyles.text18.bold.dark900,
            ),
            16.h.heightBox,

            // Content
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.gray.shade100),
                color: AppColors.gray.alphaPercent(5),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                children: [
                  _buildDetailRow(
                    LocaleKeys.shares.tr(),
                    event.numberOfShares.toStringAsFixed(2),
                  ),
                  10.w.heightBox,
                  _buildDetailRow(
                    LocaleKeys.amountInvested.tr(),
                    event.amountFee.toStringAsFixed(2),
                    showCurrencyIcon: true,
                  ),
                  10.w.heightBox,
                  _buildDetailRow(
                    LocaleKeys.transactionCost.tr(),
                    event.transactionCost.toStringAsFixed(2),
                    showCurrencyIcon: true,
                  ),
                  10.w.heightBox,
                  _buildDetailRow(
                    LocaleKeys.purchaseCost.tr(),
                    event.purchaseCost.toStringAsFixed(2),
                    showCurrencyIcon: true,
                  ),
                  10.w.heightBox,
                  _buildDetailRow(
                    LocaleKeys.status.tr(),
                    event.status.displayName,
                    status: event.status,
                  ),
                ],
              ),
            ),

            // Action buttons
            if (propertyDetails.canInvest &&
                user.canInvest &&
                (event.status == InvestmentStatus.pending ||
                    event.status == InvestmentStatus.successful) &&
                dayDifference <= 2)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.w),
                child: Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        type: ButtonType.outlined,
                        textColor: AppColors.danger,
                        borderColor: AppColors.gray.shade200,
                        fontSize: 12.sp,
                        isLoading: isGeneratingOTP || isCancellingInvestment,
                        onPressed: () => _handleCancelInvestment(ref),
                        text:
                            (propertyDetails.status == PropertyStatus.live) &&
                                event.status == InvestmentStatus.pending
                            ? LocaleKeys.cancelInvestment.tr()
                            : LocaleKeys.deleteInvestment.tr(),
                      ),
                    ),

                    // 12.w.widthBox,
                    // Expanded(
                    //   child: AppButton(
                    //     fontSize: 12.sp,
                    //     onPressed: () =>
                    //         (propertyDetails.status == PropertyStatus.live) &&
                    //             event.status == InvestmentStatus.pending
                    //         ? _handleConfirmInvestment(context, ref)
                    //         : _handleUpdateInvestment(ref),
                    //     text:
                    //         (propertyDetails.status == PropertyStatus.live) &&
                    //             event.status == InvestmentStatus.pending
                    //         ? LocaleKeys.confirm.tr()
                    //         : LocaleKeys.update.tr(),
                    //   ),
                    // ),
                  ],
                ),
              ),
            24.h.heightBox,
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool showCurrencyIcon = false,
    InvestmentStatus? status,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: AppTextStyles.text12.medium.dark900),
        Row(
          children: [
            if (showCurrencyIcon) ...[
              if (user.usesImageSymbol)
                Assets.images.dirham.image(width: 20.w, height: 20.w)
              else
                Text(
                  user.currencyCode.currencySymbol,
                  style: AppTextStyles.text14.semiBold.dark900,
                ),
              4.w.widthBox,
            ],
            if (status != null) ...[_getStatusIcon(status), 4.w.widthBox],
            Text(
              value,
              style: status != null
                  ? AppTextStyles.text14.semiBold.dark900.copyWith(
                      color: _getStatusColor(status),
                    )
                  : AppTextStyles.text14.semiBold.dark900,
            ),
          ],
        ),
      ],
    );
  }

  Color _getStatusColor(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.successful:
        return AppColors.success;
      case InvestmentStatus.pending:
        return AppColors.warning;

      default:
        return AppColors.gray;
    }
  }

  Widget _getStatusIcon(InvestmentStatus status) {
    switch (status) {
      case InvestmentStatus.successful:
        return Assets.icons.successFilled.svg(width: 20.w, height: 20.w);
      case InvestmentStatus.pending:
        return Assets.icons.warning.svg(width: 20.w, height: 20.w);
      default:
        return Assets.icons.warning.svg(width: 20.w, height: 20.w);
    }
  }

  int get dayDifference =>
      DateTime.now().difference(event.transactionDate).inDays;

  void _handleCancelInvestment(WidgetRef ref) {
    ref.read(investmentOperationsNotifierProvider.notifier).generateOTP();
  }

  // void _handleUpdateInvestment(WidgetRef ref) {}

  // void _handleConfirmInvestment(BuildContext context, WidgetRef ref) {
  //   if (user.balance < event.amount) {
  //     context.showWarningToast(
  //       LocaleKeys.oops.tr(),
  //       LocaleKeys.notEnoughBalance.tr(),
  //     );
  //     GoRouter.of(context).pushNamed(RouteName.addFund.name);
  //   }
  // }
}
