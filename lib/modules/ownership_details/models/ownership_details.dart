import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/modules/properties/models/property_image.dart';
import 'package:maisour/shared/enums/investment_status.dart';
import 'package:maisour/shared/enums/payment_status.dart';
import 'package:maisour/shared/enums/property_status.dart';

part 'ownership_details.freezed.dart';
part 'ownership_details.g.dart';

@freezed
abstract class PropertyDetails with _$PropertyDetails {
  const factory PropertyDetails({
    required int id,
    required String titleInEnglish,
    required String titleInArabic,
    required double purchasePrice,
    required double currentValuation,
    required PropertyStatus status,
    required List<PropertyImage> propertyImages,
  }) = _PropertyDetails;

  factory PropertyDetails.fromJson(Map<String, dynamic> json) =>
      _$PropertyDetailsFromJson(json);
}

@freezed
abstract class InvestmentSummary with _$InvestmentSummary {
  const factory InvestmentSummary({
    required double shares,
    required double ownershipPercentage,
  }) = _InvestmentSummary;

  factory InvestmentSummary.fromJson(Map<String, dynamic> json) =>
      _$InvestmentSummaryFromJson(json);
}

@freezed
abstract class TimelineEvent with _$TimelineEvent {
  const factory TimelineEvent({
    required int investmentId,
    required DateTime transactionDate,
    required InvestmentStatus status,
    required double amount,
    required double amountFee,
    required double purchaseCost,
    required double transactionCost,
    required double numberOfShares,
    required double perSharesPrice,
  }) = _TimelineEvent;

  factory TimelineEvent.fromJson(Map<String, dynamic> json) =>
      _$TimelineEventFromJson(json);
}

@freezed
abstract class DividendHistory with _$DividendHistory {
  const factory DividendHistory({
    required double amount,
    required DateTime creditedDate,
    required PaymentStatus status,
  }) = _DividendHistory;

  factory DividendHistory.fromJson(Map<String, dynamic> json) =>
      _$DividendHistoryFromJson(json);
}

@freezed
abstract class OwnershipDetailsResponse with _$OwnershipDetailsResponse {
  const factory OwnershipDetailsResponse({
    required bool status,
    required int statusCode,
    required String message,
    required PropertyDetails propertyDetails,
    required InvestmentSummary investmentSummary,
    required List<TimelineEvent> timeline,
    required List<DividendHistory> dividendHistory,
  }) = _OwnershipDetailsResponse;

  factory OwnershipDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$OwnershipDetailsResponseFromJson(json);
}

extension PropertyDetailsExtension on PropertyDetails {
  String title(String language) {
    return language == 'ar' ? titleInArabic : titleInEnglish;
  }

  /// Returns a sorted list of property images with cover image first
  List<PropertyImage> get sortedImagesWithCoverFirst {
    // Find cover image
    final coverImage = propertyImages.firstWhere(
      (image) => image.coverImage,
      orElse: () => propertyImages.first,
    );

    // Create new list with cover image first, then add all other images
    final sortedImages = <PropertyImage>[coverImage];
    sortedImages.addAll(propertyImages.where((image) => image != coverImage));

    return sortedImages;
  }
}

extension ProprtyDetailsExtension on PropertyDetails {
  bool get canInvest =>
      status == PropertyStatus.live || status == PropertyStatus.comingSoon;
}
