// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ownership_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PropertyDetails {

 int get id; String get titleInEnglish; String get titleInArabic; double get purchasePrice; double get currentValuation; PropertyStatus get status; List<PropertyImage> get propertyImages;
/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyDetailsCopyWith<PropertyDetails> get copyWith => _$PropertyDetailsCopyWithImpl<PropertyDetails>(this as PropertyDetails, _$identity);

  /// Serializes this PropertyDetails to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.currentValuation, currentValuation) || other.currentValuation == currentValuation)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.propertyImages, propertyImages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,titleInEnglish,titleInArabic,purchasePrice,currentValuation,status,const DeepCollectionEquality().hash(propertyImages));

@override
String toString() {
  return 'PropertyDetails(id: $id, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, purchasePrice: $purchasePrice, currentValuation: $currentValuation, status: $status, propertyImages: $propertyImages)';
}


}

/// @nodoc
abstract mixin class $PropertyDetailsCopyWith<$Res>  {
  factory $PropertyDetailsCopyWith(PropertyDetails value, $Res Function(PropertyDetails) _then) = _$PropertyDetailsCopyWithImpl;
@useResult
$Res call({
 int id, String titleInEnglish, String titleInArabic, double purchasePrice, double currentValuation, PropertyStatus status, List<PropertyImage> propertyImages
});




}
/// @nodoc
class _$PropertyDetailsCopyWithImpl<$Res>
    implements $PropertyDetailsCopyWith<$Res> {
  _$PropertyDetailsCopyWithImpl(this._self, this._then);

  final PropertyDetails _self;
  final $Res Function(PropertyDetails) _then;

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? purchasePrice = null,Object? currentValuation = null,Object? status = null,Object? propertyImages = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,purchasePrice: null == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double,currentValuation: null == currentValuation ? _self.currentValuation : currentValuation // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PropertyStatus,propertyImages: null == propertyImages ? _self.propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,
  ));
}

}


/// Adds pattern-matching-related methods to [PropertyDetails].
extension PropertyDetailsPatterns on PropertyDetails {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyDetails value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyDetails value)  $default,){
final _that = this;
switch (_that) {
case _PropertyDetails():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyDetails value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String titleInEnglish,  String titleInArabic,  double purchasePrice,  double currentValuation,  PropertyStatus status,  List<PropertyImage> propertyImages)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that.id,_that.titleInEnglish,_that.titleInArabic,_that.purchasePrice,_that.currentValuation,_that.status,_that.propertyImages);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String titleInEnglish,  String titleInArabic,  double purchasePrice,  double currentValuation,  PropertyStatus status,  List<PropertyImage> propertyImages)  $default,) {final _that = this;
switch (_that) {
case _PropertyDetails():
return $default(_that.id,_that.titleInEnglish,_that.titleInArabic,_that.purchasePrice,_that.currentValuation,_that.status,_that.propertyImages);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String titleInEnglish,  String titleInArabic,  double purchasePrice,  double currentValuation,  PropertyStatus status,  List<PropertyImage> propertyImages)?  $default,) {final _that = this;
switch (_that) {
case _PropertyDetails() when $default != null:
return $default(_that.id,_that.titleInEnglish,_that.titleInArabic,_that.purchasePrice,_that.currentValuation,_that.status,_that.propertyImages);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyDetails implements PropertyDetails {
  const _PropertyDetails({required this.id, required this.titleInEnglish, required this.titleInArabic, required this.purchasePrice, required this.currentValuation, required this.status, required final  List<PropertyImage> propertyImages}): _propertyImages = propertyImages;
  factory _PropertyDetails.fromJson(Map<String, dynamic> json) => _$PropertyDetailsFromJson(json);

@override final  int id;
@override final  String titleInEnglish;
@override final  String titleInArabic;
@override final  double purchasePrice;
@override final  double currentValuation;
@override final  PropertyStatus status;
 final  List<PropertyImage> _propertyImages;
@override List<PropertyImage> get propertyImages {
  if (_propertyImages is EqualUnmodifiableListView) return _propertyImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyImages);
}


/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyDetailsCopyWith<_PropertyDetails> get copyWith => __$PropertyDetailsCopyWithImpl<_PropertyDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyDetailsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyDetails&&(identical(other.id, id) || other.id == id)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.purchasePrice, purchasePrice) || other.purchasePrice == purchasePrice)&&(identical(other.currentValuation, currentValuation) || other.currentValuation == currentValuation)&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._propertyImages, _propertyImages));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,titleInEnglish,titleInArabic,purchasePrice,currentValuation,status,const DeepCollectionEquality().hash(_propertyImages));

@override
String toString() {
  return 'PropertyDetails(id: $id, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, purchasePrice: $purchasePrice, currentValuation: $currentValuation, status: $status, propertyImages: $propertyImages)';
}


}

/// @nodoc
abstract mixin class _$PropertyDetailsCopyWith<$Res> implements $PropertyDetailsCopyWith<$Res> {
  factory _$PropertyDetailsCopyWith(_PropertyDetails value, $Res Function(_PropertyDetails) _then) = __$PropertyDetailsCopyWithImpl;
@override @useResult
$Res call({
 int id, String titleInEnglish, String titleInArabic, double purchasePrice, double currentValuation, PropertyStatus status, List<PropertyImage> propertyImages
});




}
/// @nodoc
class __$PropertyDetailsCopyWithImpl<$Res>
    implements _$PropertyDetailsCopyWith<$Res> {
  __$PropertyDetailsCopyWithImpl(this._self, this._then);

  final _PropertyDetails _self;
  final $Res Function(_PropertyDetails) _then;

/// Create a copy of PropertyDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? purchasePrice = null,Object? currentValuation = null,Object? status = null,Object? propertyImages = null,}) {
  return _then(_PropertyDetails(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,purchasePrice: null == purchasePrice ? _self.purchasePrice : purchasePrice // ignore: cast_nullable_to_non_nullable
as double,currentValuation: null == currentValuation ? _self.currentValuation : currentValuation // ignore: cast_nullable_to_non_nullable
as double,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PropertyStatus,propertyImages: null == propertyImages ? _self._propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,
  ));
}


}


/// @nodoc
mixin _$InvestmentSummary {

 double get shares; double get ownershipPercentage;
/// Create a copy of InvestmentSummary
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$InvestmentSummaryCopyWith<InvestmentSummary> get copyWith => _$InvestmentSummaryCopyWithImpl<InvestmentSummary>(this as InvestmentSummary, _$identity);

  /// Serializes this InvestmentSummary to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is InvestmentSummary&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.ownershipPercentage, ownershipPercentage) || other.ownershipPercentage == ownershipPercentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,shares,ownershipPercentage);

@override
String toString() {
  return 'InvestmentSummary(shares: $shares, ownershipPercentage: $ownershipPercentage)';
}


}

/// @nodoc
abstract mixin class $InvestmentSummaryCopyWith<$Res>  {
  factory $InvestmentSummaryCopyWith(InvestmentSummary value, $Res Function(InvestmentSummary) _then) = _$InvestmentSummaryCopyWithImpl;
@useResult
$Res call({
 double shares, double ownershipPercentage
});




}
/// @nodoc
class _$InvestmentSummaryCopyWithImpl<$Res>
    implements $InvestmentSummaryCopyWith<$Res> {
  _$InvestmentSummaryCopyWithImpl(this._self, this._then);

  final InvestmentSummary _self;
  final $Res Function(InvestmentSummary) _then;

/// Create a copy of InvestmentSummary
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? shares = null,Object? ownershipPercentage = null,}) {
  return _then(_self.copyWith(
shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as double,ownershipPercentage: null == ownershipPercentage ? _self.ownershipPercentage : ownershipPercentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [InvestmentSummary].
extension InvestmentSummaryPatterns on InvestmentSummary {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _InvestmentSummary value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _InvestmentSummary() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _InvestmentSummary value)  $default,){
final _that = this;
switch (_that) {
case _InvestmentSummary():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _InvestmentSummary value)?  $default,){
final _that = this;
switch (_that) {
case _InvestmentSummary() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double shares,  double ownershipPercentage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _InvestmentSummary() when $default != null:
return $default(_that.shares,_that.ownershipPercentage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double shares,  double ownershipPercentage)  $default,) {final _that = this;
switch (_that) {
case _InvestmentSummary():
return $default(_that.shares,_that.ownershipPercentage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double shares,  double ownershipPercentage)?  $default,) {final _that = this;
switch (_that) {
case _InvestmentSummary() when $default != null:
return $default(_that.shares,_that.ownershipPercentage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _InvestmentSummary implements InvestmentSummary {
  const _InvestmentSummary({required this.shares, required this.ownershipPercentage});
  factory _InvestmentSummary.fromJson(Map<String, dynamic> json) => _$InvestmentSummaryFromJson(json);

@override final  double shares;
@override final  double ownershipPercentage;

/// Create a copy of InvestmentSummary
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InvestmentSummaryCopyWith<_InvestmentSummary> get copyWith => __$InvestmentSummaryCopyWithImpl<_InvestmentSummary>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$InvestmentSummaryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InvestmentSummary&&(identical(other.shares, shares) || other.shares == shares)&&(identical(other.ownershipPercentage, ownershipPercentage) || other.ownershipPercentage == ownershipPercentage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,shares,ownershipPercentage);

@override
String toString() {
  return 'InvestmentSummary(shares: $shares, ownershipPercentage: $ownershipPercentage)';
}


}

/// @nodoc
abstract mixin class _$InvestmentSummaryCopyWith<$Res> implements $InvestmentSummaryCopyWith<$Res> {
  factory _$InvestmentSummaryCopyWith(_InvestmentSummary value, $Res Function(_InvestmentSummary) _then) = __$InvestmentSummaryCopyWithImpl;
@override @useResult
$Res call({
 double shares, double ownershipPercentage
});




}
/// @nodoc
class __$InvestmentSummaryCopyWithImpl<$Res>
    implements _$InvestmentSummaryCopyWith<$Res> {
  __$InvestmentSummaryCopyWithImpl(this._self, this._then);

  final _InvestmentSummary _self;
  final $Res Function(_InvestmentSummary) _then;

/// Create a copy of InvestmentSummary
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? shares = null,Object? ownershipPercentage = null,}) {
  return _then(_InvestmentSummary(
shares: null == shares ? _self.shares : shares // ignore: cast_nullable_to_non_nullable
as double,ownershipPercentage: null == ownershipPercentage ? _self.ownershipPercentage : ownershipPercentage // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$TimelineEvent {

 int get investmentId; DateTime get transactionDate; InvestmentStatus get status; double get amount; double get amountFee; double get purchaseCost; double get transactionCost; double get numberOfShares; double get perSharesPrice;
/// Create a copy of TimelineEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TimelineEventCopyWith<TimelineEvent> get copyWith => _$TimelineEventCopyWithImpl<TimelineEvent>(this as TimelineEvent, _$identity);

  /// Serializes this TimelineEvent to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TimelineEvent&&(identical(other.investmentId, investmentId) || other.investmentId == investmentId)&&(identical(other.transactionDate, transactionDate) || other.transactionDate == transactionDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.amountFee, amountFee) || other.amountFee == amountFee)&&(identical(other.purchaseCost, purchaseCost) || other.purchaseCost == purchaseCost)&&(identical(other.transactionCost, transactionCost) || other.transactionCost == transactionCost)&&(identical(other.numberOfShares, numberOfShares) || other.numberOfShares == numberOfShares)&&(identical(other.perSharesPrice, perSharesPrice) || other.perSharesPrice == perSharesPrice));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,investmentId,transactionDate,status,amount,amountFee,purchaseCost,transactionCost,numberOfShares,perSharesPrice);

@override
String toString() {
  return 'TimelineEvent(investmentId: $investmentId, transactionDate: $transactionDate, status: $status, amount: $amount, amountFee: $amountFee, purchaseCost: $purchaseCost, transactionCost: $transactionCost, numberOfShares: $numberOfShares, perSharesPrice: $perSharesPrice)';
}


}

/// @nodoc
abstract mixin class $TimelineEventCopyWith<$Res>  {
  factory $TimelineEventCopyWith(TimelineEvent value, $Res Function(TimelineEvent) _then) = _$TimelineEventCopyWithImpl;
@useResult
$Res call({
 int investmentId, DateTime transactionDate, InvestmentStatus status, double amount, double amountFee, double purchaseCost, double transactionCost, double numberOfShares, double perSharesPrice
});




}
/// @nodoc
class _$TimelineEventCopyWithImpl<$Res>
    implements $TimelineEventCopyWith<$Res> {
  _$TimelineEventCopyWithImpl(this._self, this._then);

  final TimelineEvent _self;
  final $Res Function(TimelineEvent) _then;

/// Create a copy of TimelineEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? investmentId = null,Object? transactionDate = null,Object? status = null,Object? amount = null,Object? amountFee = null,Object? purchaseCost = null,Object? transactionCost = null,Object? numberOfShares = null,Object? perSharesPrice = null,}) {
  return _then(_self.copyWith(
investmentId: null == investmentId ? _self.investmentId : investmentId // ignore: cast_nullable_to_non_nullable
as int,transactionDate: null == transactionDate ? _self.transactionDate : transactionDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvestmentStatus,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,amountFee: null == amountFee ? _self.amountFee : amountFee // ignore: cast_nullable_to_non_nullable
as double,purchaseCost: null == purchaseCost ? _self.purchaseCost : purchaseCost // ignore: cast_nullable_to_non_nullable
as double,transactionCost: null == transactionCost ? _self.transactionCost : transactionCost // ignore: cast_nullable_to_non_nullable
as double,numberOfShares: null == numberOfShares ? _self.numberOfShares : numberOfShares // ignore: cast_nullable_to_non_nullable
as double,perSharesPrice: null == perSharesPrice ? _self.perSharesPrice : perSharesPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [TimelineEvent].
extension TimelineEventPatterns on TimelineEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TimelineEvent value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TimelineEvent() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TimelineEvent value)  $default,){
final _that = this;
switch (_that) {
case _TimelineEvent():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TimelineEvent value)?  $default,){
final _that = this;
switch (_that) {
case _TimelineEvent() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int investmentId,  DateTime transactionDate,  InvestmentStatus status,  double amount,  double amountFee,  double purchaseCost,  double transactionCost,  double numberOfShares,  double perSharesPrice)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TimelineEvent() when $default != null:
return $default(_that.investmentId,_that.transactionDate,_that.status,_that.amount,_that.amountFee,_that.purchaseCost,_that.transactionCost,_that.numberOfShares,_that.perSharesPrice);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int investmentId,  DateTime transactionDate,  InvestmentStatus status,  double amount,  double amountFee,  double purchaseCost,  double transactionCost,  double numberOfShares,  double perSharesPrice)  $default,) {final _that = this;
switch (_that) {
case _TimelineEvent():
return $default(_that.investmentId,_that.transactionDate,_that.status,_that.amount,_that.amountFee,_that.purchaseCost,_that.transactionCost,_that.numberOfShares,_that.perSharesPrice);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int investmentId,  DateTime transactionDate,  InvestmentStatus status,  double amount,  double amountFee,  double purchaseCost,  double transactionCost,  double numberOfShares,  double perSharesPrice)?  $default,) {final _that = this;
switch (_that) {
case _TimelineEvent() when $default != null:
return $default(_that.investmentId,_that.transactionDate,_that.status,_that.amount,_that.amountFee,_that.purchaseCost,_that.transactionCost,_that.numberOfShares,_that.perSharesPrice);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TimelineEvent implements TimelineEvent {
  const _TimelineEvent({required this.investmentId, required this.transactionDate, required this.status, required this.amount, required this.amountFee, required this.purchaseCost, required this.transactionCost, required this.numberOfShares, required this.perSharesPrice});
  factory _TimelineEvent.fromJson(Map<String, dynamic> json) => _$TimelineEventFromJson(json);

@override final  int investmentId;
@override final  DateTime transactionDate;
@override final  InvestmentStatus status;
@override final  double amount;
@override final  double amountFee;
@override final  double purchaseCost;
@override final  double transactionCost;
@override final  double numberOfShares;
@override final  double perSharesPrice;

/// Create a copy of TimelineEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TimelineEventCopyWith<_TimelineEvent> get copyWith => __$TimelineEventCopyWithImpl<_TimelineEvent>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TimelineEventToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TimelineEvent&&(identical(other.investmentId, investmentId) || other.investmentId == investmentId)&&(identical(other.transactionDate, transactionDate) || other.transactionDate == transactionDate)&&(identical(other.status, status) || other.status == status)&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.amountFee, amountFee) || other.amountFee == amountFee)&&(identical(other.purchaseCost, purchaseCost) || other.purchaseCost == purchaseCost)&&(identical(other.transactionCost, transactionCost) || other.transactionCost == transactionCost)&&(identical(other.numberOfShares, numberOfShares) || other.numberOfShares == numberOfShares)&&(identical(other.perSharesPrice, perSharesPrice) || other.perSharesPrice == perSharesPrice));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,investmentId,transactionDate,status,amount,amountFee,purchaseCost,transactionCost,numberOfShares,perSharesPrice);

@override
String toString() {
  return 'TimelineEvent(investmentId: $investmentId, transactionDate: $transactionDate, status: $status, amount: $amount, amountFee: $amountFee, purchaseCost: $purchaseCost, transactionCost: $transactionCost, numberOfShares: $numberOfShares, perSharesPrice: $perSharesPrice)';
}


}

/// @nodoc
abstract mixin class _$TimelineEventCopyWith<$Res> implements $TimelineEventCopyWith<$Res> {
  factory _$TimelineEventCopyWith(_TimelineEvent value, $Res Function(_TimelineEvent) _then) = __$TimelineEventCopyWithImpl;
@override @useResult
$Res call({
 int investmentId, DateTime transactionDate, InvestmentStatus status, double amount, double amountFee, double purchaseCost, double transactionCost, double numberOfShares, double perSharesPrice
});




}
/// @nodoc
class __$TimelineEventCopyWithImpl<$Res>
    implements _$TimelineEventCopyWith<$Res> {
  __$TimelineEventCopyWithImpl(this._self, this._then);

  final _TimelineEvent _self;
  final $Res Function(_TimelineEvent) _then;

/// Create a copy of TimelineEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? investmentId = null,Object? transactionDate = null,Object? status = null,Object? amount = null,Object? amountFee = null,Object? purchaseCost = null,Object? transactionCost = null,Object? numberOfShares = null,Object? perSharesPrice = null,}) {
  return _then(_TimelineEvent(
investmentId: null == investmentId ? _self.investmentId : investmentId // ignore: cast_nullable_to_non_nullable
as int,transactionDate: null == transactionDate ? _self.transactionDate : transactionDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as InvestmentStatus,amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,amountFee: null == amountFee ? _self.amountFee : amountFee // ignore: cast_nullable_to_non_nullable
as double,purchaseCost: null == purchaseCost ? _self.purchaseCost : purchaseCost // ignore: cast_nullable_to_non_nullable
as double,transactionCost: null == transactionCost ? _self.transactionCost : transactionCost // ignore: cast_nullable_to_non_nullable
as double,numberOfShares: null == numberOfShares ? _self.numberOfShares : numberOfShares // ignore: cast_nullable_to_non_nullable
as double,perSharesPrice: null == perSharesPrice ? _self.perSharesPrice : perSharesPrice // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$DividendHistory {

 double get amount; DateTime get creditedDate; PaymentStatus get status;
/// Create a copy of DividendHistory
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DividendHistoryCopyWith<DividendHistory> get copyWith => _$DividendHistoryCopyWithImpl<DividendHistory>(this as DividendHistory, _$identity);

  /// Serializes this DividendHistory to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DividendHistory&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.creditedDate, creditedDate) || other.creditedDate == creditedDate)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,creditedDate,status);

@override
String toString() {
  return 'DividendHistory(amount: $amount, creditedDate: $creditedDate, status: $status)';
}


}

/// @nodoc
abstract mixin class $DividendHistoryCopyWith<$Res>  {
  factory $DividendHistoryCopyWith(DividendHistory value, $Res Function(DividendHistory) _then) = _$DividendHistoryCopyWithImpl;
@useResult
$Res call({
 double amount, DateTime creditedDate, PaymentStatus status
});




}
/// @nodoc
class _$DividendHistoryCopyWithImpl<$Res>
    implements $DividendHistoryCopyWith<$Res> {
  _$DividendHistoryCopyWithImpl(this._self, this._then);

  final DividendHistory _self;
  final $Res Function(DividendHistory) _then;

/// Create a copy of DividendHistory
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? amount = null,Object? creditedDate = null,Object? status = null,}) {
  return _then(_self.copyWith(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,creditedDate: null == creditedDate ? _self.creditedDate : creditedDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,
  ));
}

}


/// Adds pattern-matching-related methods to [DividendHistory].
extension DividendHistoryPatterns on DividendHistory {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DividendHistory value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DividendHistory() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DividendHistory value)  $default,){
final _that = this;
switch (_that) {
case _DividendHistory():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DividendHistory value)?  $default,){
final _that = this;
switch (_that) {
case _DividendHistory() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( double amount,  DateTime creditedDate,  PaymentStatus status)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DividendHistory() when $default != null:
return $default(_that.amount,_that.creditedDate,_that.status);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( double amount,  DateTime creditedDate,  PaymentStatus status)  $default,) {final _that = this;
switch (_that) {
case _DividendHistory():
return $default(_that.amount,_that.creditedDate,_that.status);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( double amount,  DateTime creditedDate,  PaymentStatus status)?  $default,) {final _that = this;
switch (_that) {
case _DividendHistory() when $default != null:
return $default(_that.amount,_that.creditedDate,_that.status);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _DividendHistory implements DividendHistory {
  const _DividendHistory({required this.amount, required this.creditedDate, required this.status});
  factory _DividendHistory.fromJson(Map<String, dynamic> json) => _$DividendHistoryFromJson(json);

@override final  double amount;
@override final  DateTime creditedDate;
@override final  PaymentStatus status;

/// Create a copy of DividendHistory
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DividendHistoryCopyWith<_DividendHistory> get copyWith => __$DividendHistoryCopyWithImpl<_DividendHistory>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DividendHistoryToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DividendHistory&&(identical(other.amount, amount) || other.amount == amount)&&(identical(other.creditedDate, creditedDate) || other.creditedDate == creditedDate)&&(identical(other.status, status) || other.status == status));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,amount,creditedDate,status);

@override
String toString() {
  return 'DividendHistory(amount: $amount, creditedDate: $creditedDate, status: $status)';
}


}

/// @nodoc
abstract mixin class _$DividendHistoryCopyWith<$Res> implements $DividendHistoryCopyWith<$Res> {
  factory _$DividendHistoryCopyWith(_DividendHistory value, $Res Function(_DividendHistory) _then) = __$DividendHistoryCopyWithImpl;
@override @useResult
$Res call({
 double amount, DateTime creditedDate, PaymentStatus status
});




}
/// @nodoc
class __$DividendHistoryCopyWithImpl<$Res>
    implements _$DividendHistoryCopyWith<$Res> {
  __$DividendHistoryCopyWithImpl(this._self, this._then);

  final _DividendHistory _self;
  final $Res Function(_DividendHistory) _then;

/// Create a copy of DividendHistory
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? amount = null,Object? creditedDate = null,Object? status = null,}) {
  return _then(_DividendHistory(
amount: null == amount ? _self.amount : amount // ignore: cast_nullable_to_non_nullable
as double,creditedDate: null == creditedDate ? _self.creditedDate : creditedDate // ignore: cast_nullable_to_non_nullable
as DateTime,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as PaymentStatus,
  ));
}


}


/// @nodoc
mixin _$OwnershipDetailsResponse {

 bool get status; int get statusCode; String get message; PropertyDetails get propertyDetails; InvestmentSummary get investmentSummary; List<TimelineEvent> get timeline; List<DividendHistory> get dividendHistory;
/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OwnershipDetailsResponseCopyWith<OwnershipDetailsResponse> get copyWith => _$OwnershipDetailsResponseCopyWithImpl<OwnershipDetailsResponse>(this as OwnershipDetailsResponse, _$identity);

  /// Serializes this OwnershipDetailsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OwnershipDetailsResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&(identical(other.propertyDetails, propertyDetails) || other.propertyDetails == propertyDetails)&&(identical(other.investmentSummary, investmentSummary) || other.investmentSummary == investmentSummary)&&const DeepCollectionEquality().equals(other.timeline, timeline)&&const DeepCollectionEquality().equals(other.dividendHistory, dividendHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,propertyDetails,investmentSummary,const DeepCollectionEquality().hash(timeline),const DeepCollectionEquality().hash(dividendHistory));

@override
String toString() {
  return 'OwnershipDetailsResponse(status: $status, statusCode: $statusCode, message: $message, propertyDetails: $propertyDetails, investmentSummary: $investmentSummary, timeline: $timeline, dividendHistory: $dividendHistory)';
}


}

/// @nodoc
abstract mixin class $OwnershipDetailsResponseCopyWith<$Res>  {
  factory $OwnershipDetailsResponseCopyWith(OwnershipDetailsResponse value, $Res Function(OwnershipDetailsResponse) _then) = _$OwnershipDetailsResponseCopyWithImpl;
@useResult
$Res call({
 bool status, int statusCode, String message, PropertyDetails propertyDetails, InvestmentSummary investmentSummary, List<TimelineEvent> timeline, List<DividendHistory> dividendHistory
});


$PropertyDetailsCopyWith<$Res> get propertyDetails;$InvestmentSummaryCopyWith<$Res> get investmentSummary;

}
/// @nodoc
class _$OwnershipDetailsResponseCopyWithImpl<$Res>
    implements $OwnershipDetailsResponseCopyWith<$Res> {
  _$OwnershipDetailsResponseCopyWithImpl(this._self, this._then);

  final OwnershipDetailsResponse _self;
  final $Res Function(OwnershipDetailsResponse) _then;

/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? propertyDetails = null,Object? investmentSummary = null,Object? timeline = null,Object? dividendHistory = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,propertyDetails: null == propertyDetails ? _self.propertyDetails : propertyDetails // ignore: cast_nullable_to_non_nullable
as PropertyDetails,investmentSummary: null == investmentSummary ? _self.investmentSummary : investmentSummary // ignore: cast_nullable_to_non_nullable
as InvestmentSummary,timeline: null == timeline ? _self.timeline : timeline // ignore: cast_nullable_to_non_nullable
as List<TimelineEvent>,dividendHistory: null == dividendHistory ? _self.dividendHistory : dividendHistory // ignore: cast_nullable_to_non_nullable
as List<DividendHistory>,
  ));
}
/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PropertyDetailsCopyWith<$Res> get propertyDetails {
  
  return $PropertyDetailsCopyWith<$Res>(_self.propertyDetails, (value) {
    return _then(_self.copyWith(propertyDetails: value));
  });
}/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$InvestmentSummaryCopyWith<$Res> get investmentSummary {
  
  return $InvestmentSummaryCopyWith<$Res>(_self.investmentSummary, (value) {
    return _then(_self.copyWith(investmentSummary: value));
  });
}
}


/// Adds pattern-matching-related methods to [OwnershipDetailsResponse].
extension OwnershipDetailsResponsePatterns on OwnershipDetailsResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OwnershipDetailsResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OwnershipDetailsResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OwnershipDetailsResponse value)  $default,){
final _that = this;
switch (_that) {
case _OwnershipDetailsResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OwnershipDetailsResponse value)?  $default,){
final _that = this;
switch (_that) {
case _OwnershipDetailsResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  PropertyDetails propertyDetails,  InvestmentSummary investmentSummary,  List<TimelineEvent> timeline,  List<DividendHistory> dividendHistory)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OwnershipDetailsResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.propertyDetails,_that.investmentSummary,_that.timeline,_that.dividendHistory);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  PropertyDetails propertyDetails,  InvestmentSummary investmentSummary,  List<TimelineEvent> timeline,  List<DividendHistory> dividendHistory)  $default,) {final _that = this;
switch (_that) {
case _OwnershipDetailsResponse():
return $default(_that.status,_that.statusCode,_that.message,_that.propertyDetails,_that.investmentSummary,_that.timeline,_that.dividendHistory);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool status,  int statusCode,  String message,  PropertyDetails propertyDetails,  InvestmentSummary investmentSummary,  List<TimelineEvent> timeline,  List<DividendHistory> dividendHistory)?  $default,) {final _that = this;
switch (_that) {
case _OwnershipDetailsResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.propertyDetails,_that.investmentSummary,_that.timeline,_that.dividendHistory);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OwnershipDetailsResponse implements OwnershipDetailsResponse {
  const _OwnershipDetailsResponse({required this.status, required this.statusCode, required this.message, required this.propertyDetails, required this.investmentSummary, required final  List<TimelineEvent> timeline, required final  List<DividendHistory> dividendHistory}): _timeline = timeline,_dividendHistory = dividendHistory;
  factory _OwnershipDetailsResponse.fromJson(Map<String, dynamic> json) => _$OwnershipDetailsResponseFromJson(json);

@override final  bool status;
@override final  int statusCode;
@override final  String message;
@override final  PropertyDetails propertyDetails;
@override final  InvestmentSummary investmentSummary;
 final  List<TimelineEvent> _timeline;
@override List<TimelineEvent> get timeline {
  if (_timeline is EqualUnmodifiableListView) return _timeline;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_timeline);
}

 final  List<DividendHistory> _dividendHistory;
@override List<DividendHistory> get dividendHistory {
  if (_dividendHistory is EqualUnmodifiableListView) return _dividendHistory;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_dividendHistory);
}


/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OwnershipDetailsResponseCopyWith<_OwnershipDetailsResponse> get copyWith => __$OwnershipDetailsResponseCopyWithImpl<_OwnershipDetailsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OwnershipDetailsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OwnershipDetailsResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&(identical(other.propertyDetails, propertyDetails) || other.propertyDetails == propertyDetails)&&(identical(other.investmentSummary, investmentSummary) || other.investmentSummary == investmentSummary)&&const DeepCollectionEquality().equals(other._timeline, _timeline)&&const DeepCollectionEquality().equals(other._dividendHistory, _dividendHistory));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,propertyDetails,investmentSummary,const DeepCollectionEquality().hash(_timeline),const DeepCollectionEquality().hash(_dividendHistory));

@override
String toString() {
  return 'OwnershipDetailsResponse(status: $status, statusCode: $statusCode, message: $message, propertyDetails: $propertyDetails, investmentSummary: $investmentSummary, timeline: $timeline, dividendHistory: $dividendHistory)';
}


}

/// @nodoc
abstract mixin class _$OwnershipDetailsResponseCopyWith<$Res> implements $OwnershipDetailsResponseCopyWith<$Res> {
  factory _$OwnershipDetailsResponseCopyWith(_OwnershipDetailsResponse value, $Res Function(_OwnershipDetailsResponse) _then) = __$OwnershipDetailsResponseCopyWithImpl;
@override @useResult
$Res call({
 bool status, int statusCode, String message, PropertyDetails propertyDetails, InvestmentSummary investmentSummary, List<TimelineEvent> timeline, List<DividendHistory> dividendHistory
});


@override $PropertyDetailsCopyWith<$Res> get propertyDetails;@override $InvestmentSummaryCopyWith<$Res> get investmentSummary;

}
/// @nodoc
class __$OwnershipDetailsResponseCopyWithImpl<$Res>
    implements _$OwnershipDetailsResponseCopyWith<$Res> {
  __$OwnershipDetailsResponseCopyWithImpl(this._self, this._then);

  final _OwnershipDetailsResponse _self;
  final $Res Function(_OwnershipDetailsResponse) _then;

/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? propertyDetails = null,Object? investmentSummary = null,Object? timeline = null,Object? dividendHistory = null,}) {
  return _then(_OwnershipDetailsResponse(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,propertyDetails: null == propertyDetails ? _self.propertyDetails : propertyDetails // ignore: cast_nullable_to_non_nullable
as PropertyDetails,investmentSummary: null == investmentSummary ? _self.investmentSummary : investmentSummary // ignore: cast_nullable_to_non_nullable
as InvestmentSummary,timeline: null == timeline ? _self._timeline : timeline // ignore: cast_nullable_to_non_nullable
as List<TimelineEvent>,dividendHistory: null == dividendHistory ? _self._dividendHistory : dividendHistory // ignore: cast_nullable_to_non_nullable
as List<DividendHistory>,
  ));
}

/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$PropertyDetailsCopyWith<$Res> get propertyDetails {
  
  return $PropertyDetailsCopyWith<$Res>(_self.propertyDetails, (value) {
    return _then(_self.copyWith(propertyDetails: value));
  });
}/// Create a copy of OwnershipDetailsResponse
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$InvestmentSummaryCopyWith<$Res> get investmentSummary {
  
  return $InvestmentSummaryCopyWith<$Res>(_self.investmentSummary, (value) {
    return _then(_self.copyWith(investmentSummary: value));
  });
}
}

// dart format on
