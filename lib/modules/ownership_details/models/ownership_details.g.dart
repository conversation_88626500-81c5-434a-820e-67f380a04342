// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ownership_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_PropertyDetails _$PropertyDetailsFromJson(Map<String, dynamic> json) =>
    _PropertyDetails(
      id: (json['id'] as num).toInt(),
      titleInEnglish: json['titleInEnglish'] as String,
      titleInArabic: json['titleInArabic'] as String,
      purchasePrice: (json['purchasePrice'] as num).toDouble(),
      currentValuation: (json['currentValuation'] as num).toDouble(),
      status: $enumDecode(_$PropertyStatusEnumMap, json['status']),
      propertyImages: (json['propertyImages'] as List<dynamic>)
          .map((e) => PropertyImage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PropertyDetailsToJson(_PropertyDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'titleInEnglish': instance.titleInEnglish,
      'titleInArabic': instance.titleInArabic,
      'purchasePrice': instance.purchasePrice,
      'currentValuation': instance.currentValuation,
      'status': _$PropertyStatusEnumMap[instance.status]!,
      'propertyImages': instance.propertyImages,
    };

const _$PropertyStatusEnumMap = {
  PropertyStatus.comingSoon: 'ComingSoon',
  PropertyStatus.cancelled: 'Cancelled',
  PropertyStatus.sold: 'Sold',
  PropertyStatus.funded: 'Funded',
  PropertyStatus.request: 'Request',
  PropertyStatus.live: 'Live',
};

_InvestmentSummary _$InvestmentSummaryFromJson(Map<String, dynamic> json) =>
    _InvestmentSummary(
      shares: (json['shares'] as num).toDouble(),
      ownershipPercentage: (json['ownershipPercentage'] as num).toDouble(),
    );

Map<String, dynamic> _$InvestmentSummaryToJson(_InvestmentSummary instance) =>
    <String, dynamic>{
      'shares': instance.shares,
      'ownershipPercentage': instance.ownershipPercentage,
    };

_TimelineEvent _$TimelineEventFromJson(Map<String, dynamic> json) =>
    _TimelineEvent(
      investmentId: (json['investmentId'] as num).toInt(),
      transactionDate: DateTime.parse(json['transactionDate'] as String),
      status: $enumDecode(_$InvestmentStatusEnumMap, json['status']),
      amount: (json['amount'] as num).toDouble(),
      amountFee: (json['amountFee'] as num).toDouble(),
      purchaseCost: (json['purchaseCost'] as num).toDouble(),
      transactionCost: (json['transactionCost'] as num).toDouble(),
      numberOfShares: (json['numberOfShares'] as num).toDouble(),
      perSharesPrice: (json['perSharesPrice'] as num).toDouble(),
    );

Map<String, dynamic> _$TimelineEventToJson(_TimelineEvent instance) =>
    <String, dynamic>{
      'investmentId': instance.investmentId,
      'transactionDate': instance.transactionDate.toIso8601String(),
      'status': _$InvestmentStatusEnumMap[instance.status]!,
      'amount': instance.amount,
      'amountFee': instance.amountFee,
      'purchaseCost': instance.purchaseCost,
      'transactionCost': instance.transactionCost,
      'numberOfShares': instance.numberOfShares,
      'perSharesPrice': instance.perSharesPrice,
    };

const _$InvestmentStatusEnumMap = {
  InvestmentStatus.successful: 'Successful',
  InvestmentStatus.rejected: 'Rejected',
  InvestmentStatus.cancelled: 'Cancelled',
  InvestmentStatus.pending: 'Pending',
  InvestmentStatus.notFunded: 'NotFunded',
  InvestmentStatus.removed: 'Removed',
  InvestmentStatus.sold: 'Sold',
};

_DividendHistory _$DividendHistoryFromJson(Map<String, dynamic> json) =>
    _DividendHistory(
      amount: (json['amount'] as num).toDouble(),
      creditedDate: DateTime.parse(json['creditedDate'] as String),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$DividendHistoryToJson(_DividendHistory instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'creditedDate': instance.creditedDate.toIso8601String(),
      'status': _$PaymentStatusEnumMap[instance.status]!,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'Pending',
  PaymentStatus.success: 'Success',
  PaymentStatus.failed: 'Failed',
  PaymentStatus.cancelled: 'Cancelled',
  PaymentStatus.reversed: 'Reversed',
  PaymentStatus.refunded: 'Refunded',
};

_OwnershipDetailsResponse _$OwnershipDetailsResponseFromJson(
  Map<String, dynamic> json,
) => _OwnershipDetailsResponse(
  status: json['status'] as bool,
  statusCode: (json['statusCode'] as num).toInt(),
  message: json['message'] as String,
  propertyDetails: PropertyDetails.fromJson(
    json['propertyDetails'] as Map<String, dynamic>,
  ),
  investmentSummary: InvestmentSummary.fromJson(
    json['investmentSummary'] as Map<String, dynamic>,
  ),
  timeline: (json['timeline'] as List<dynamic>)
      .map((e) => TimelineEvent.fromJson(e as Map<String, dynamic>))
      .toList(),
  dividendHistory: (json['dividendHistory'] as List<dynamic>)
      .map((e) => DividendHistory.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$OwnershipDetailsResponseToJson(
  _OwnershipDetailsResponse instance,
) => <String, dynamic>{
  'status': instance.status,
  'statusCode': instance.statusCode,
  'message': instance.message,
  'propertyDetails': instance.propertyDetails,
  'investmentSummary': instance.investmentSummary,
  'timeline': instance.timeline,
  'dividendHistory': instance.dividendHistory,
};
