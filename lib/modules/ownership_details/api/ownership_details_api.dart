import 'package:dio/dio.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import '../models/ownership_details.dart';

part 'ownership_details_api.g.dart';

@RestApi()
abstract class OwnershipDetailsApi {
  factory OwnershipDetailsApi(Dio dio, {String? baseUrl}) =
      _OwnershipDetailsApi;

  @GET('${ApiEndpoints.getOwnershipDetails}/{propertyId}')
  Future<OwnershipDetailsResponse> getOwnershipDetails(
    @Path('propertyId') int propertyId,
  );

  /// Get public IP
  @GET(ApiEndpoints.getPublicIp)
  Future<String> getPublicIp();

  @POST(ApiEndpoints.generateOTP)
  Future<OtpResponse> generateOTP(@Query('ipAddress') String ipAddress);

  @POST(ApiEndpoints.cancelInvestment)
  Future<AppUser> cancelInvestment(
    @Query('otpRecordId') int otpRecordId,
    @Query('id') int investmentId,
  );
}
