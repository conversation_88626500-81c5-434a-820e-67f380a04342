import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/phone_verification/providers/phone_verification_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/resend_timer_provider.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:pinput/pinput.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';

class OtpVerificationScreen extends ConsumerStatefulWidget {
  const OtpVerificationScreen({super.key, this.phoneNumber});
  // Required only when first time verification, after that it will be null
  // its also required when update mobile number
  final String? phoneNumber;

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  final TextEditingController _pinController = TextEditingController();
  late final ProviderSubscription _phoneVerificationSubscription;
  final _formKey = GlobalKey<FormState>();
  bool isUpdateNumber = false;

  @override
  void initState() {
    super.initState();

    // Start the timer when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(otpResendTimerProvider.notifier).startTimer();
      isUpdateNumber =
          widget.phoneNumber != null &&
          ref.readCurrentUser?.phoneNumber != null;
    });

    // Listen to phone verification state changes
    _phoneVerificationSubscription = ref.listenManual<PhoneVerificationState>(
      phoneVerificationNotifierProvider('otp_verification'),
      (previous, next) {
        if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

        if (next.status == ApiStatus.success) {
          // if the user resend the code
          if (next.isResend) {
            reSendOtpSuccess();
          } else {
            // OTP verification successful
            if (next.isUpdateNumber) {
              GoRouter.of(context).pop();
              GoRouter.of(context).pop();
            } else if (widget.phoneNumber != null &&
                widget.phoneNumber!.isNotEmpty) {
              phoneOtpSuccess();
            } else {
              GoRouter.of(context).pop(next.otpId!);
            }
          }
        } else if (next.status == ApiStatus.error) {
          // Show error message
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: next.errorKey,
            fallbackMessage: next.errorMessage ?? 'Failed',
          );
          context.showErrorToast(title, message);
        }
      },
    );
  }

  void reSendOtpSuccess() {
    // resend verification code
    context.showSuccessToast(
      LocaleKeys.success.tr(),
      LocaleKeys.verificationCodeSent.tr(),
    );

    // Start the timer again
    ref.read(otpResendTimerProvider.notifier).startTimer();
  }

  void phoneOtpSuccess() {
    if (isUpdateNumber) {
      ref
          .read(phoneVerificationNotifierProvider('otp_verification').notifier)
          .updatePhoneNumber(widget.phoneNumber!)
          .then((appUser) {
            if (appUser != null) {
              // Update user in user provider
              ref.updateCurrentUser(appUser);
            }
          });
    } else {
      // Add phone first time
      GoRouter.of(context).pop();
      // Navigate to success screen
      GoRouter.of(
        context,
      ).pushReplacementNamed(RouteName.phoneVerificationSuccess.name);
    }
  }

  @override
  void dispose() {
    _pinController.dispose();
    _phoneVerificationSubscription.close();
    _formKey.currentState?.dispose();
    super.dispose();
  }

  /// Handles the verify OTP action
  void _onVerifyPressed() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      // Check if phoneNumber is provided (first time verification)
      if (widget.phoneNumber != null && widget.phoneNumber!.isNotEmpty) {
        // First time verification - send with phone number
        final currentAppUser = ref.readCurrentUser;
        final otp = _pinController.text;
        final email = currentAppUser!.user.email;

        // if user have number means user tryb to update phone number
        if (isUpdateNumber) {
          ref
              .read(
                phoneVerificationNotifierProvider('otp_verification').notifier,
              )
              .updatePhoneNumber(widget.phoneNumber!)
              .then((appUser) {
                if (appUser != null) {
                  // Update user in user provider
                  ref.updateCurrentUser(appUser);
                }
              });
        } else {
          ref
              .read(
                phoneVerificationNotifierProvider('otp_verification').notifier,
              )
              .verifyRegisterOTP(widget.phoneNumber!, otp, email)
              .then((appUser) {
                if (appUser != null) {
                  // Update user in user provider
                  ref.updateCurrentUser(appUser);
                }
              });
        }
      } else {
        // Subsequent verification - send without phone number (different API)
        final currentAppUser = ref.readCurrentUser;
        final otp = _pinController.text;
        final phone = currentAppUser!.phoneNumber!;

        ref
            .read(
              phoneVerificationNotifierProvider('otp_verification').notifier,
            )
            .verifyOTP(phone, otp);
      }
    }
  }

  /// Handles the resend OTP action
  void _onResendPressed() {
    // Check if phoneNumber is provided (first time verification)
    if (widget.phoneNumber != null && widget.phoneNumber!.isNotEmpty) {
      // First time verification - send with phone number
      ref
          .read(phoneVerificationNotifierProvider('otp_verification').notifier)
          .sendVerificationCode(widget.phoneNumber!);
    } else {
      // Subsequent verification - send without phone number (different API)
      ref
          .read(phoneVerificationNotifierProvider('otp_verification').notifier)
          .generateOTP();
    }
  }

  @override
  Widget build(BuildContext context) {
    final phoneVerificationState = ref.watch(
      phoneVerificationNotifierProvider('otp_verification'),
    );
    final timerState = ref.watch(otpResendTimerProvider);
    final isLoading = phoneVerificationState.status == ApiStatus.loading;
    final isResendEnabled = timerState.canResend && !isLoading;

    // Pinput theme
    final defaultPinTheme = PinTheme(
      width: 48.w,
      height: 48.w,
      textStyle: AppTextStyles.text18.bold.dark900,
      margin: EdgeInsetsDirectional.only(end: 8.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(8.r),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: AppColors.primary),
      borderRadius: BorderRadius.circular(8.r),
    );

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.enterVerificationCode.tr(),
                    style: AppTextStyles.text20.bold.dark900,
                  ),
                  8.h.heightBox,
                  Text(
                    LocaleKeys.enterThe5DigitCode.tr(),
                    style: AppTextStyles.text14.medium.dark300,
                  ),

                  24.h.heightBox,

                  // OTP Input Fields
                  Form(
                    key: _formKey,
                    child: Center(
                      child: Pinput(
                        controller: _pinController,
                        length: 5,
                        defaultPinTheme: defaultPinTheme,
                        focusedPinTheme: focusedPinTheme,
                        pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                        showCursor: true,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        onCompleted: (pin) {
                          // Auto verify when all 6 digits are entered
                          _onVerifyPressed();
                        },
                        validator: AppValidators.otp,
                      ),
                    ),
                  ),

                  16.h.heightBox,

                  Row(
                    children: [
                      Text(
                        '${LocaleKeys.didntReceiveCode.tr()} ',
                        style: AppTextStyles.text14.medium.dark300,
                      ),
                      (!timerState.canResend && timerState.countdown > 0)
                          ? Text(
                              '${LocaleKeys.resendIn.tr()} ${timerState.formattedTime}',
                              style: AppTextStyles.text14.medium.dark300
                                  .underline(
                                    underlineColor: AppColors.dark.shade300,
                                  ),
                            )
                          : GestureDetector(
                              onTap: isResendEnabled
                                  ? () {
                                      _pinController.clear();
                                      _onResendPressed();
                                    }
                                  : null,
                              child: Text(
                                LocaleKeys.resend.tr(),
                                style: AppTextStyles.text14.medium.primary500
                                    .underline(
                                      underlineColor: AppColors.primary,
                                    ),
                              ),
                            ),
                    ],
                  ),
                ],
              ),
            ),

            // Verify Button
            AppButton(
              text: LocaleKeys.verify.tr(),
              onPressed: _onVerifyPressed,
              isLoading: isLoading,
              showLoader: _pinController.text.length == 6,
            ),

            16.h.heightBox,
          ],
        ).paddingHorizontal(16.w),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }
}
