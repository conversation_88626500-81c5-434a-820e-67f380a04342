import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/phone_verification/phone_verification_service.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Provider for PhoneVerificationNotifier
final phoneVerificationNotifierProvider = StateNotifierProvider.family
    .autoDispose<PhoneVerificationNotifier, PhoneVerificationState, String>((
      ref,
      screenName,
    ) {
      final phoneVerificationService = ref.watch(
        phoneVerificationServiceProvider,
      );
      return PhoneVerificationNotifier(phoneVerificationService);
    }, name: 'phoneVerificationNotifierProvider');

class PhoneVerificationNotifier extends StateNotifier<PhoneVerificationState>
    with DioExceptionMapper {
  final PhoneVerificationService _phoneVerificationService;

  PhoneVerificationNotifier(this._phoneVerificationService)
    : super(const PhoneVerificationState());

  /// Send verification code to phone number
  Future<void> sendVerificationCode(String phoneNumber) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      await _phoneVerificationService.addPhoneNumber(phoneNumber);

      state = state.copyWith(
        status: ApiStatus.success,
        isResend: true,
        errorMessage: null,
        errorKey: null,
        isUpdateNumber: false,
      );

      debugPrint('Phone verification code sent successfully to: $phoneNumber');
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint(
        'Phone Verification State Updated - Error: $errorMessage, Key: $errorKey',
      );
    }
  }

  /// Verify OTP with phone number and email
  Future<AppUser?> verifyRegisterOTP(
    String phoneNumber,
    String otp,
    String email,
  ) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      final appUser = await _phoneVerificationService.verifyRegisterOTP(
        phoneNumber,
        otp,
        email,
      );

      state = state.copyWith(
        status: ApiStatus.success,
        isResend: false,
        errorMessage: null,
        errorKey: null,
        isUpdateNumber: false,
      );

      debugPrint('OTP verification successful for: $phoneNumber');
      return appUser;
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint(
        'OTP Verification State Updated - Error: $errorMessage, Key: $errorKey',
      );
      return null;
    }
  }

  /// Generate OTP for investment operations (resend)
  Future<void> generateOTP() async {
    state = state.copyWith(status: ApiStatus.loading, isResend: true);

    try {
      final otpResponse = await _phoneVerificationService.generateOTP();
      state = state.copyWith(
        status: ApiStatus.success,
        isResend: true,
        errorMessage: null,
        errorKey: null,
        otpId: otpResponse.id,
        isUpdateNumber: false,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
    }
  }

  /// Verify OTP for investment operations
  Future<void> verifyOTP(String phone, String otp) async {
    state = state.copyWith(status: ApiStatus.loading, isResend: false);

    try {
      final otpResponse = await _phoneVerificationService.verifyOTP(phone, otp);
      state = state.copyWith(
        status: ApiStatus.success,
        isResend: false,
        errorMessage: null,
        errorKey: null,
        otpId: otpResponse.id,
        isUpdateNumber: false,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
    }
  }

  /// update phone number
  Future<AppUser?> updatePhoneNumber(String phoneNumber) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      final appUser = await _phoneVerificationService.updatePhoneNumber(
        phoneNumber,
      );

      state = state.copyWith(
        status: ApiStatus.success,
        isResend: false,
        errorMessage: null,
        errorKey: null,
        isUpdateNumber: true,
      );

      debugPrint('Phone number updated successfully: $phoneNumber');
      return appUser;
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isUpdateNumber: false,
      );
      debugPrint(
        'Phone Verification State Updated - Error: $errorMessage, Key: $errorKey',
      );
      return null;
    }
  }
}

class PhoneVerificationState {
  final ApiStatus status;
  final bool isResend;
  final String? errorMessage;
  final String? errorKey;
  final int? otpId;
  final bool isUpdateNumber;

  const PhoneVerificationState({
    this.status = ApiStatus.initial,
    this.isResend = true,
    this.errorMessage,
    this.errorKey,
    this.otpId,
    this.isUpdateNumber = false,
  });

  PhoneVerificationState copyWith({
    ApiStatus? status,
    bool? isResend,
    String? errorMessage,
    String? errorKey,
    int? otpId,
    bool? isUpdateNumber,
  }) {
    return PhoneVerificationState(
      status: status ?? this.status,
      isResend: isResend ?? this.isResend,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
      otpId: otpId ?? this.otpId,
      isUpdateNumber: isUpdateNumber ?? this.isUpdateNumber,
    );
  }
}
