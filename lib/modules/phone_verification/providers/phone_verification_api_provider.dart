import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/phone_verification/api/phone_verification_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Provider for PhoneVerificationApi
final phoneVerificationApiProvider = Provider.autoDispose<PhoneVerificationApi>(
  (ref) {
    final dio = ref.watch(networkServiceProvider);
    return PhoneVerificationApi(dio);
  },
  name: 'phoneVerificationApiProvider',
);
