import 'package:dio/dio.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import 'package:retrofit/retrofit.dart';

part 'phone_verification_api.g.dart';

@RestApi()
abstract class PhoneVerificationApi {
  factory PhoneVerificationApi(Dio dio, {String? baseUrl}) =
      _PhoneVerificationApi;

  /// Add phone number endpoint
  @POST(ApiEndpoints.addPhoneNumber)
  Future<void> addPhoneNumber(@Body() Map<String, String> request);

  /// Verify OTP endpoint
  @POST(ApiEndpoints.registerVerifyOTP)
  Future<AppUser> registerVerifyOTP(@Body() Map<String, String> request);

  /// Get public IP
  @GET(ApiEndpoints.getPublicIp)
  Future<String> getPublicIp();

  /// Generate OTP for investment operations
  @POST(ApiEndpoints.generateOTP)
  Future<OtpResponse> generateOTP(@Query('ipAddress') String ipAddress);

  /// Verify OTP for investment operations
  @POST(ApiEndpoints.verifyOTP)
  Future<OtpResponse> verifyOTP(
    @Query('Phone') String phone,
    @Query('OTP') String otp,
  );

  /// Update user phoneNumber
  @POST(ApiEndpoints.updateUserData)
  Future<AppUser> updatePhoneNumber(@Query('phoneNumber') String phoneNumber);
}
