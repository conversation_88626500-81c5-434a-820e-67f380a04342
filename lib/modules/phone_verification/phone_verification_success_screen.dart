import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class PhoneVerificationSuccessScreen extends ConsumerWidget {
  const PhoneVerificationSuccessScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.successFilled.svg(width: 100.w, height: 100.w),
                  16.heightBox,
                  Text(
                    LocaleKeys.codeVerifiedSuccessfully.tr(),
                    style: AppTextStyles.text18.bold.dark900,
                  ),
                  8.heightBox,
                  Text(
                    LocaleKeys.phoneNumberVerifiedSuccessfully.tr(),
                    style: AppTextStyles.text14.dark300,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            AppButton(
              text: LocaleKeys.continueText.tr(),
              onPressed: () {
                GoRouter.of(context).pop();
              },
            ),
            16.heightBox,
          ],
        ).paddingHorizontal(16.w),
      ),
    );
  }
}
