import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_response.freezed.dart';
part 'otp_response.g.dart';

@freezed
abstract class OtpResponse with _$OtpResponse {
  const factory OtpResponse({
    required int id,
    required DateTime date,
    required String phoneNumber,
    required bool activated,
  }) = _OtpResponse;

  factory OtpResponse.fromJson(Map<String, dynamic> json) =>
      _$OtpResponseFromJson(json);
}
