import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/phone_verification/api/phone_verification_api.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import 'package:maisour/modules/phone_verification/providers/phone_verification_api_provider.dart';
import 'package:maisour/shared/models/app_user.dart';

/// Provider for PhoneVerificationService
final phoneVerificationServiceProvider =
    Provider.autoDispose<PhoneVerificationService>((ref) {
      final phoneVerificationApi = ref.watch(phoneVerificationApiProvider);
      return PhoneVerificationService(phoneVerificationApi);
    }, name: 'phoneVerificationServiceProvider');

class PhoneVerificationService {
  final PhoneVerificationApi _phoneVerificationApi;

  PhoneVerificationService(this._phoneVerificationApi);

  /// Add phone number with IP address
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<void> addPhoneNumber(String phoneNumber) async {
    // Get public IP address
    final ipAddress = await _phoneVerificationApi.getPublicIp();

    // Create request body
    final request = {'phoneNumber': phoneNumber, 'ipAddress': ipAddress};

    await _phoneVerificationApi.addPhoneNumber(request);
  }

  /// Verify OTP with phone number and email
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<AppUser> verifyRegisterOTP(
    String phoneNumber,
    String otp,
    String email,
  ) async {
    // Create request body
    final request = {'phoneNumber': phoneNumber, 'otp': otp, 'email': email};

    return await _phoneVerificationApi.registerVerifyOTP(request);
  }

  /// Generate OTP for investment operations (resend)
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<OtpResponse> generateOTP() async {
    // Get public IP address
    final ipAddress = await _phoneVerificationApi.getPublicIp();

    // Call API with IP address as query parameter
    return await _phoneVerificationApi.generateOTP(ipAddress);
  }

  /// Verify OTP for investment operations
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<OtpResponse> verifyOTP(String phone, String otp) async {
    // Call API with phone and OTP as query parameters
    return await _phoneVerificationApi.verifyOTP(phone, otp);
  }

  /// Update user phoneNumber
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<AppUser> updatePhoneNumber(String phone) async {
    return await _phoneVerificationApi.updatePhoneNumber(phone);
  }
}
