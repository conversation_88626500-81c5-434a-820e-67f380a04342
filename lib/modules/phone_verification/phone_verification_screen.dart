import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/phone_verification/providers/phone_verification_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/phone_form_field.dart';
import 'package:phone_form_field/phone_form_field.dart';

class PhoneVerificationScreen extends ConsumerStatefulWidget {
  const PhoneVerificationScreen({super.key, this.phoneNumber});
  final String? phoneNumber;

  @override
  ConsumerState<PhoneVerificationScreen> createState() =>
      _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState
    extends ConsumerState<PhoneVerificationScreen> {
  late final PhoneController phoneController;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    phoneController = PhoneController(
      initialValue: widget.phoneNumber != null
          ? PhoneNumber.parse(widget.phoneNumber!)
          : PhoneNumber(nsn: '', isoCode: IsoCode.AE),
    );
    super.initState();
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  /// Handles the send verification code action
  void _onSendVerificationCode() {
    if (_formKey.currentState?.validate() ?? false) {
      final phoneNumber = phoneController.value.international;
      if (phoneNumber.isNotEmpty) {
        ref
            .read(
              phoneVerificationNotifierProvider('phone_verification').notifier,
            )
            .sendVerificationCode(phoneNumber);
      }
    }
  }

  /// Handles the Do It Later action: pop first, then refresh user in background
  void _onDoItLater() {
    GoRouter.of(context).pop();
    // Fire and forget: refresh user in background
    ref.notifierCurrentUser.refreshUserFromServer();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<PhoneVerificationState>(
      phoneVerificationNotifierProvider('phone_verification'),
      (previous, next) {
        if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;
        if (next.status == ApiStatus.success) {
          context.showSuccessToast(
            LocaleKeys.success.tr(),
            LocaleKeys.verificationCodeSent.tr(),
          );
          // Navigate to OTP verification screen with phone number
          final phoneNumber = phoneController.value.international;
          GoRouter.of(
            context,
          ).pushNamed(RouteName.otpVerification.name, extra: phoneNumber);
        } else if (next.status == ApiStatus.error) {
          // Get localized error message based on errorKey
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: next.errorKey,
            fallbackMessage:
                next.errorMessage ?? 'Failed to send verification code',
          );

          // Use new modern toast system
          context.showErrorToast(title, message);
        }
      },
    );

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) {
        if (!didPop) {
          _onDoItLater();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(leading: BackButton(onPressed: _onDoItLater)),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        LocaleKeys.verifyPhoneNumber.tr(),
                        style: AppTextStyles.text20.bold.dark900,
                      ),
                      8.h.heightBox,
                      Text(
                        LocaleKeys.confirmYourNumberToSecureYourAccount.tr(),
                        style: AppTextStyles.text14.medium.dark300,
                      ),
                      24.h.heightBox,
                      AppPhoneFormField(controller: phoneController),
                    ],
                  ),
                ),
                Consumer(
                  builder: (context, ref, child) {
                    final phoneVerificationState = ref.watch(
                      phoneVerificationNotifierProvider('phone_verification'),
                    );
                    final isLoading =
                        phoneVerificationState.status == ApiStatus.loading;

                    return AppButton(
                      text: LocaleKeys.sendVerificationCode.tr(),
                      onPressed: _onSendVerificationCode,
                      isLoading: isLoading,
                    );
                  },
                ),
                8.h.heightBox,
                Center(
                  child: AppButton(
                    type: ButtonType.text,
                    text: LocaleKeys.doItLater.tr(),
                    onPressed: _onDoItLater,
                  ),
                ),
                2.h.heightBox,
              ],
            ).paddingHorizontal(16.w),
          ),
        ),
      ),
    );
  }
}
