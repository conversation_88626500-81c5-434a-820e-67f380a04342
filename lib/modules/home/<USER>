import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key, required this.child});

  final Widget child;

  /// Get the current index based on the current route
  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    switch (location) {
      case RoutePath.properties:
        return 0;
      case RoutePath.portfolio:
        return 1;
      case RoutePath.wallet:
        return 2;
      case RoutePath.account:
        return 3;
      default:
        return 0; // Default to properties
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch user provider to trigger rebuilds when user changes
    ref.watchCurrentUser;

    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _getCurrentIndex(context),
        backgroundColor: AppColors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.gray.shade600,
        selectedLabelStyle: AppTextStyles.text10.bold.primary,
        unselectedLabelStyle: AppTextStyles.text10.bold.gray,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          switch (index) {
            case 0:
              context.goNamed(RouteName.properties.name);
              break;
            case 1:
              context.goNamed(RouteName.portfolio.name);
              break;
            case 2:
              context.goNamed(RouteName.wallet.name);
              break;
            case 3:
              context.goNamed(RouteName.account.name);
              break;
          }
        },
        items: [
          BottomNavigationBarItem(
            icon: Assets.icons.building.svg(
              width: 22.w,
              height: 22.w,
              colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
            ),
            activeIcon: Assets.icons.buildingFilled.svg(
              width: 22.w,
              height: 22.w,
            ),
            label: LocaleKeys.properties.tr(),
          ),
          BottomNavigationBarItem(
            icon: Assets.icons.portfolio.svg(
              width: 22.w,
              height: 22.w,
              colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
            ),
            activeIcon: Assets.icons.portfolioFilled.svg(
              width: 22.w,
              height: 22.w,
            ),
            label: LocaleKeys.portfolio.tr(),
          ),
          BottomNavigationBarItem(
            icon: Assets.icons.wallet.svg(
              width: 22.w,
              height: 22.w,
              colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
            ),
            activeIcon: Assets.icons.walletFilled.svg(
              width: 22.w,
              height: 22.w,
            ),
            label: LocaleKeys.wallet.tr(),
          ),
          BottomNavigationBarItem(
            icon: Assets.icons.account.svg(
              width: 22.w,
              height: 22.w,
              colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
            ),
            activeIcon: Assets.icons.accountFilled.svg(
              width: 22.w,
              height: 22.w,
              colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
            ),
            label: LocaleKeys.account.tr(),
          ),
        ],
      ),
    );
  }
}
