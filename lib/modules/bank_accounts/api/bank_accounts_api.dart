import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/shared/constants/api_constants.dart';

part 'bank_accounts_api.g.dart';

@RestApi()
abstract class BankAccountsApi {
  factory BankAccountsApi(Dio dio, {String baseUrl}) = _BankAccountsApi;

  @GET(ApiEndpoints.getUserBankAccounts)
  Future<BankAccountsResponse> getUserBankAccounts();

  @POST(ApiEndpoints.saveBankAccount)
  Future<void> saveBankAccount(@Body() Map<String, dynamic> body);

  @POST('${ApiEndpoints.setPrimaryBankAccount}/{id}')
  Future<void> setPrimaryBankAccount(@Path('id') int id);

  @POST('${ApiEndpoints.updateBankAccountStatus}/{id}')
  Future<void> updateBankAccountStatus(
    @Path('id') int id,
    @Query('active') bool active,
  );

  @DELETE('${ApiEndpoints.deleteBankAccount}/{id}')
  Future<void> deleteBankAccount(@Path('id') int id);
}
