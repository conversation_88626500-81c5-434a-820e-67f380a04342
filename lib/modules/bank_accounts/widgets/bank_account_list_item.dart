import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class BankAccountListItem extends StatelessWidget {
  final BankAccount bankAccount;
  final bool isPrimary;
  final bool isLast;
  final Function() onTap;

  const BankAccountListItem({
    super.key,
    required this.bankAccount,
    this.isPrimary = false,
    this.isLast = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 16.w, horizontal: 16.w),
          child: Row(
            children: [
              // Bank icon
              Assets.icons.bankAccount.svg(width: 32.w, height: 32.w),
              12.w.widthBox,

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bankAccount.bankName,
                      style: AppTextStyles.text14.bold.dark900,
                    ),
                    4.h.heightBox,
                    Text(
                      _formatAccountNumber(bankAccount.accountNumber),
                      style: AppTextStyles.text12.medium.gray600,
                    ),
                  ],
                ),
              ),

              if (isPrimary)
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 4.w,
                  ),
                  margin: EdgeInsetsDirectional.only(end: 4.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary.alphaPercent(10),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    LocaleKeys.primary.tr(),
                    style: AppTextStyles.text10.bold.primary,
                  ),
                ),

              // Arrow icon
              Icon(
                Icons.chevron_right,
                size: 24.w,
                color: AppColors.gray.shade400,
              ),
            ],
          ),
        ).onTap(onTap),

        // Divider
        if (!isLast)
          Divider(
            color: AppColors.gray.shade200,
            thickness: 1.h,
            height: 1.h,
          ).paddingStart(60.w),
      ],
    );
  }

  String _formatAccountNumber(String accountNumber) {
    if (accountNumber.length <= 4) {
      return accountNumber;
    }

    final lastFour = accountNumber.substring(accountNumber.length - 4);
    final maskedPart = '*' * (accountNumber.length - 4);

    // Format as **** **** **** 1234
    final chunks = <String>[];
    for (int i = 0; i < maskedPart.length; i += 4) {
      final end = (i + 4 < maskedPart.length) ? i + 4 : maskedPart.length;
      chunks.add(maskedPart.substring(i, end));
    }

    return '${chunks.join(' ')} $lastFour';
  }
}
