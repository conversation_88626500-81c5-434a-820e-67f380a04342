import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/modules/bank_accounts/providers/bank_account_provider.dart';

class ViewBankAccountScreen extends ConsumerStatefulWidget {
  final BankAccount bankAccount;

  const ViewBankAccountScreen({super.key, required this.bankAccount});

  @override
  ConsumerState<ViewBankAccountScreen> createState() =>
      _ViewBankAccountScreenState();
}

class _ViewBankAccountScreenState extends ConsumerState<ViewBankAccountScreen> {
  BankAccount? bankAccount;
  String action = '';

  @override
  void initState() {
    super.initState();
    bankAccount = widget.bankAccount;
  }

  @override
  Widget build(BuildContext context) {
    // Watch the save bank account provider for state changes
    final saveState = ref.watch(bankAccountProvider);

    // Handle success state
    if (saveState.isSuccess && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Show success message
        if (action == 'setPrimary') {
          context.showSuccessToast(
            LocaleKeys.success.tr(),
            LocaleKeys.bankAccountSetAsPrimarySuccessfully.tr(),
          );
        } else if (action == 'setActive') {
          context.showSuccessToast(
            LocaleKeys.success.tr(),
            LocaleKeys.bankAccountStatusUpdatedSuccessfully.tr(),
          );
        } else if (action == 'delete') {
          context.showSuccessToast(
            LocaleKeys.success.tr(),
            LocaleKeys.bankAccountDeletedSuccessfully.tr(),
          );
          // Navigate back after successful deletion
          GoRouter.of(context).pop();
        }
        // Reset the state
        ref.read(bankAccountProvider.notifier).reset();
      });
    }

    // Handle error state
    if (saveState.errorMessage != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Show error message
        context.showErrorToast(LocaleKeys.oops.tr(), saveState.errorMessage!);

        // Clear the error
        ref.read(bankAccountProvider.notifier).reset();
      });
    }

    return Scaffold(
      appBar: AppBar(title: Text(bankAccount!.bankName), centerTitle: false),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    // Bank Account Details Card
                    _buildDetailsCard(),
                    16.h.heightBox,
                    // // Primary Account Card
                    _buildPrimaryAccountCard(),
                    16.h.heightBox,

                    // Status Card
                    _buildStatusCard(),
                    32.h.heightBox,
                  ],
                ),
              ),
            ),

            // Delete Button
            _buildDeleteButton(context).paddingAll(16.w),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(5),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Column(
        children: [
          _buildDetailRow(LocaleKeys.bankName.tr(), bankAccount!.bankName),
          16.h.heightBox,
          _buildDetailRow(
            LocaleKeys.accountNumber.tr(),
            bankAccount!.accountNumber,
          ),
          16.h.heightBox,
          if (bankAccount!.swiftCode != null) ...[
            _buildDetailRow(LocaleKeys.swiftCode.tr(), bankAccount!.swiftCode!),
            16.h.heightBox,
          ],
          _buildDetailRow(LocaleKeys.iban.tr(), bankAccount!.iban),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: AppTextStyles.text12.medium.gray700),
        8.w.widthBox,
        Flexible(
          child: Text(
            value,
            style: AppTextStyles.text14.semiBold.dark900,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildPrimaryAccountCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.primaryAccount.tr(),
              style: AppTextStyles.text14.semiBold.dark,
            ),
          ),
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: bankAccount!.isPrimary
                    ? AppColors.primary
                    : AppColors.gray.shade300,
                width: 2,
              ),
              color: bankAccount!.isPrimary
                  ? AppColors.primary
                  : AppColors.white,
            ),
            child: bankAccount!.isPrimary
                ? Icon(Icons.check, size: 14.w, color: AppColors.white)
                : null,
          ),
        ],
      ),
    ).onTap(() {
      if (!widget.bankAccount.isPrimary) {
        action = 'setPrimary';
        bankAccount = bankAccount!.copyWith(isPrimary: true);
        ref
            .read(bankAccountProvider.notifier)
            .setPrimaryBankAccount(widget.bankAccount.id);
      }
    });
  }

  Widget _buildStatusCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            LocaleKeys.status.tr(),
            style: AppTextStyles.text14.semiBold.dark,
          ),
          SizedBox(
            height: 16.w,
            child: Switch(
              value: bankAccount!.active,
              onChanged: _setActiveBankAccount,
              activeThumbColor: AppColors.white,
              activeTrackColor: AppColors.success,
              inactiveThumbColor: AppColors.white,
              inactiveTrackColor: AppColors.gray.shade300,
              trackOutlineColor: WidgetStateProperty.all(Colors.transparent),
            ),
          ),
        ],
      ),
    );
  }

  void _setActiveBankAccount(bool active) {
    if (bankAccount!.isPrimary == false) {
      action = 'setActive';
      bankAccount = bankAccount!.copyWith(active: active);
      ref
          .read(bankAccountProvider.notifier)
          .updateBankAccountStatus(bankAccount!.id, active);
    } else {
      context.showErrorToast(
        LocaleKeys.oops.tr(),
        LocaleKeys.cannotSetPrimaryBankAccountAsInactive.tr(),
      );
    }
  }

  Widget _buildDeleteButton(BuildContext context) {
    return bankAccount!.isPrimary == false &&
            bankAccount!.totalWithdrawalTransaction == 0
        ? AppButton(
            text: LocaleKeys.deleteAccount.tr(),
            type: ButtonType.outlined,
            textColor: AppColors.danger,
            borderColor: AppColors.danger,
            onPressed: () => _showDeleteConfirmation(context),
          )
        : const SizedBox.shrink();
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        title: Text(LocaleKeys.deleteAccount.tr()),
        content: Text(LocaleKeys.areYouSureYouWantToDeleteThisBankAccount.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(LocaleKeys.cancel.tr()),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog first
              action = 'delete';
              ref
                  .read(bankAccountProvider.notifier)
                  .deleteBankAccount(bankAccount!.id);
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.danger),
            child: Text(LocaleKeys.delete.tr()),
          ),
        ],
      ),
    );
  }
}
