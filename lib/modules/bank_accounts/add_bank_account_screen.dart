import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/country.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/widgets/form_fields/country_dropdown.dart';
import 'package:maisour/modules/bank_accounts/providers/bank_account_provider.dart';

class AddBankAccountScreen extends ConsumerStatefulWidget {
  const AddBankAccountScreen({super.key});

  @override
  ConsumerState<AddBankAccountScreen> createState() =>
      _AddBankAccountScreenState();
}

class _AddBankAccountScreenState extends ConsumerState<AddBankAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountNameController = TextEditingController();
  final _bankNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _ibanController = TextEditingController();
  final _swiftCodeController = TextEditingController();

  Country? _selectedCountry;
  bool _isPrimaryAccount = false;

  @override
  void dispose() {
    _accountNameController.dispose();
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _ibanController.dispose();
    _swiftCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        title: Text(LocaleKeys.addBankAccount.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        16.w.heightBox,
                        // Account Name (non-editable)
                        _buildAccountNameField(),
                        20.h.heightBox,

                        // Bank Name
                        _buildBankNameField(),
                        20.h.heightBox,

                        // Account Number
                        _buildAccountNumberField(),
                        20.h.heightBox,

                        // Country
                        _buildCountryField(),
                        20.h.heightBox,

                        // IBAN
                        _buildIbanField(),
                        20.h.heightBox,

                        // Swift Code
                        _buildSwiftCodeField(),
                        20.h.heightBox,

                        // Primary Account Checkbox
                        _buildPrimaryAccountCheckbox(),
                        32.h.heightBox,
                      ],
                    ),
                  ),
                ),

                // Add Account Button
                _buildAddAccountButton(),
              ],
            ),
          ),
        ),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

  // Form field builders
  Widget _buildAccountNameField() {
    return Consumer(
      builder: (context, ref, child) {
        final user = ref.watch(userProvider)!;
        _accountNameController.text = user.user.fullName;
        return AppTextField(
          controller: _accountNameController,
          labelText: '${LocaleKeys.accountName.tr()}*',
          readOnly: true,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.next,
        );
      },
    );
  }

  Widget _buildBankNameField() {
    return AppTextField(
      labelText: '${LocaleKeys.bankName.tr()}*',
      controller: _bankNameController,
      validator: AppValidators.bankName,
      inputFormatters: InputFormatters.bankNameFormatters(),
      textInputAction: TextInputAction.next,
      keyboardType: TextInputType.text,
      textCapitalization: TextCapitalization.words,
    );
  }

  Widget _buildAccountNumberField() {
    return AppTextField(
      labelText: '${LocaleKeys.accountNumber.tr()}*',
      controller: _accountNumberController,
      keyboardType: TextInputType.number,
      validator: AppValidators.accountNumber,
      inputFormatters: InputFormatters.accountNumberFormatters(),
      textInputAction: TextInputAction.next,
    );
  }

  Widget _buildCountryField() {
    return CountryDropdown(
      value: null,
      onChanged: (value) {
        _selectedCountry = value;
      },
      labelText: LocaleKeys.country.tr(),
      validator: (value) =>
          AppValidators.required(LocaleKeys.country.tr(), value?.country),
    );
  }

  Widget _buildIbanField() {
    return AppTextField(
      labelText: '${LocaleKeys.iban.tr()}*',
      controller: _ibanController,
      textCapitalization: TextCapitalization.characters,
      validator: AppValidators.iban,
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.next,
      inputFormatters: InputFormatters.ibanFormatters(),
    );
  }

  Widget _buildSwiftCodeField() {
    return AppTextField(
      labelText: LocaleKeys.swiftCode.tr(),
      controller: _swiftCodeController,
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.done,
      textCapitalization: TextCapitalization.characters,
      validator: AppValidators.swiftCode,
      inputFormatters: InputFormatters.swiftCodeFormatters(),
    );
  }

  Widget _buildPrimaryAccountCheckbox() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              LocaleKeys.primaryAccount.tr(),
              style: AppTextStyles.text14.semiBold.dark,
            ),
          ),
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _isPrimaryAccount
                    ? AppColors.primary
                    : AppColors.gray.shade300,
                width: 2,
              ),
              color: _isPrimaryAccount ? AppColors.primary : AppColors.white,
            ),
            child: _isPrimaryAccount
                ? Icon(Icons.check, size: 14.w, color: AppColors.white)
                : null,
          ),
        ],
      ).paddingAll(16.w),
    ).onTap(() {
      setState(() {
        _isPrimaryAccount = !_isPrimaryAccount;
      });
    });
  }

  Widget _buildAddAccountButton() {
    return Consumer(
      builder: (context, ref, child) {
        final saveState = ref.watch(bankAccountProvider);

        // Handle success state
        if (saveState.isSuccess && mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // Show success message
            context.showSuccessToast(
              LocaleKeys.success.tr(),
              LocaleKeys.bankAccountAddedSuccessfully.tr(),
            );
            GoRouter.of(context).pop(true);
          });
        }

        // Handle error state
        if (saveState.errorMessage != null && mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.showErrorToast(
              LocaleKeys.oops.tr(),
              saveState.errorMessage!,
            );

            // Clear the error
            ref.read(bankAccountProvider.notifier).reset();
          });
        }

        return AppButton(
          text: LocaleKeys.addAccount.tr(),
          type: ButtonType.filled,
          isLoading: saveState.isLoading,
          onPressed: saveState.isLoading ? () {} : _handleAddAccount,
        );
      },
    );
  }

  void _handleAddAccount() async {
    if (_formKey.currentState!.validate()) {
      // Prepare the request body
      final requestBody = {
        'accountName': _accountNameController.text,
        'accountNumber': _accountNumberController.text,
        'bankName': _bankNameController.text,
        'country': _selectedCountry?.country ?? '',
        'iban': _ibanController.text,
        'swiftCode': _swiftCodeController.text.isNotEmpty
            ? _swiftCodeController.text
            : null,
        'isPrimary': _isPrimaryAccount,
      };

      // Call the save API
      await ref.read(bankAccountProvider.notifier).saveBankAccount(requestBody);
    }
  }
}
