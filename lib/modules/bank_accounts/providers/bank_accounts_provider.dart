import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/bank_accounts/api/bank_accounts_api.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}

/// AsyncNotifier for bank accounts
class BankAccountsNotifier extends AutoDisposeAsyncNotifier<List<BankAccount>> {
  late final BankAccountsApi _bankAccountsApi;
  final _dioExceptionMapper = _DioExceptionMapperHelper();

  @override
  Future<List<BankAccount>> build() async {
    _bankAccountsApi = ref.watch(bankAccountsApiProvider);
    return _loadBankAccounts();
  }

  /// Load bank accounts
  Future<List<BankAccount>> _loadBankAccounts() async {
    try {
      final response = await _bankAccountsApi.getUserBankAccounts();
      return response.data;
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      throw failure;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  /// Refresh bank accounts
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadBankAccounts());
  }
}

/// Provider for bank accounts API
final bankAccountsApiProvider = Provider<BankAccountsApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return BankAccountsApi(dio);
});

/// Provider for bank accounts
final bankAccountsProvider =
    AsyncNotifierProvider.autoDispose<BankAccountsNotifier, List<BankAccount>>(
      () => BankAccountsNotifier(),
      name: 'bankAccountsProvider',
    );
