import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/bank_accounts/api/bank_accounts_api.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/modules/bank_accounts/providers/bank_accounts_provider.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// State for save bank account operation
class BankAccountState {
  final bool isLoading;
  final bool isSuccess;
  final String? errorMessage;

  const BankAccountState({
    this.isLoading = false,
    this.isSuccess = false,
    this.errorMessage,
  });

  BankAccountState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? errorMessage,
    BankAccount? savedBankAccount,
  }) {
    return BankAccountState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}

/// Notifier for save bank account operations
class BankAccountNotifier extends StateNotifier<BankAccountState> {
  final BankAccountsApi _bankAccountsApi;
  final _dioExceptionMapper = _DioExceptionMapperHelper();

  BankAccountNotifier(this._bankAccountsApi) : super(const BankAccountState());

  /// Save bank account
  Future<void> saveBankAccount(Map<String, dynamic> bankAccountData) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      isSuccess: false,
    );

    try {
      await _bankAccountsApi.saveBankAccount(bankAccountData);

      // If no exception is thrown, consider it successful
      state = state.copyWith(isLoading: false, isSuccess: true);
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      state = state.copyWith(isLoading: false, errorMessage: failure.message);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  /// Set primary bank account
  Future<void> setPrimaryBankAccount(int bankAccountId) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      isSuccess: false,
    );

    try {
      await _bankAccountsApi.setPrimaryBankAccount(bankAccountId);

      // If no exception is thrown, consider it successful
      state = state.copyWith(isLoading: false, isSuccess: true);
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      state = state.copyWith(isLoading: false, errorMessage: failure.message);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  /// Update bank account status
  Future<void> updateBankAccountStatus(int bankAccountId, bool active) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      isSuccess: false,
    );

    try {
      await _bankAccountsApi.updateBankAccountStatus(bankAccountId, active);

      // If no exception is thrown, consider it successful
      state = state.copyWith(isLoading: false, isSuccess: true);
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      state = state.copyWith(isLoading: false, errorMessage: failure.message);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  /// Delete bank account
  Future<void> deleteBankAccount(int bankAccountId) async {
    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      isSuccess: false,
    );

    try {
      await _bankAccountsApi.deleteBankAccount(bankAccountId);

      // If no exception is thrown, consider it successful
      state = state.copyWith(isLoading: false, isSuccess: true);
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      state = state.copyWith(isLoading: false, errorMessage: failure.message);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  /// Reset state
  void reset() {
    state = const BankAccountState();
  }
}

/// Provider for save bank account operations
final bankAccountProvider =
    StateNotifierProvider.autoDispose<BankAccountNotifier, BankAccountState>((
      ref,
    ) {
      final bankAccountsApi = ref.watch(bankAccountsApiProvider);
      return BankAccountNotifier(bankAccountsApi);
    });
