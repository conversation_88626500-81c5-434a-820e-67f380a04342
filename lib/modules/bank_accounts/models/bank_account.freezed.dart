// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BankAccount {

 int get id; String get accountName; String get accountNumber; String get bankName; String get country; String get iban; String? get swiftCode; bool get isPrimary; bool get active; int get totalWithdrawalTransaction;
/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BankAccountCopyWith<BankAccount> get copyWith => _$BankAccountCopyWithImpl<BankAccount>(this as BankAccount, _$identity);

  /// Serializes this BankAccount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BankAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.country, country) || other.country == country)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.swiftCode, swiftCode) || other.swiftCode == swiftCode)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.active, active) || other.active == active)&&(identical(other.totalWithdrawalTransaction, totalWithdrawalTransaction) || other.totalWithdrawalTransaction == totalWithdrawalTransaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,accountName,accountNumber,bankName,country,iban,swiftCode,isPrimary,active,totalWithdrawalTransaction);

@override
String toString() {
  return 'BankAccount(id: $id, accountName: $accountName, accountNumber: $accountNumber, bankName: $bankName, country: $country, iban: $iban, swiftCode: $swiftCode, isPrimary: $isPrimary, active: $active, totalWithdrawalTransaction: $totalWithdrawalTransaction)';
}


}

/// @nodoc
abstract mixin class $BankAccountCopyWith<$Res>  {
  factory $BankAccountCopyWith(BankAccount value, $Res Function(BankAccount) _then) = _$BankAccountCopyWithImpl;
@useResult
$Res call({
 int id, String accountName, String accountNumber, String bankName, String country, String iban, String? swiftCode, bool isPrimary, bool active, int totalWithdrawalTransaction
});




}
/// @nodoc
class _$BankAccountCopyWithImpl<$Res>
    implements $BankAccountCopyWith<$Res> {
  _$BankAccountCopyWithImpl(this._self, this._then);

  final BankAccount _self;
  final $Res Function(BankAccount) _then;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? accountName = null,Object? accountNumber = null,Object? bankName = null,Object? country = null,Object? iban = null,Object? swiftCode = freezed,Object? isPrimary = null,Object? active = null,Object? totalWithdrawalTransaction = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,swiftCode: freezed == swiftCode ? _self.swiftCode : swiftCode // ignore: cast_nullable_to_non_nullable
as String?,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,totalWithdrawalTransaction: null == totalWithdrawalTransaction ? _self.totalWithdrawalTransaction : totalWithdrawalTransaction // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [BankAccount].
extension BankAccountPatterns on BankAccount {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BankAccount value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BankAccount() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BankAccount value)  $default,){
final _that = this;
switch (_that) {
case _BankAccount():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BankAccount value)?  $default,){
final _that = this;
switch (_that) {
case _BankAccount() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String accountName,  String accountNumber,  String bankName,  String country,  String iban,  String? swiftCode,  bool isPrimary,  bool active,  int totalWithdrawalTransaction)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BankAccount() when $default != null:
return $default(_that.id,_that.accountName,_that.accountNumber,_that.bankName,_that.country,_that.iban,_that.swiftCode,_that.isPrimary,_that.active,_that.totalWithdrawalTransaction);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String accountName,  String accountNumber,  String bankName,  String country,  String iban,  String? swiftCode,  bool isPrimary,  bool active,  int totalWithdrawalTransaction)  $default,) {final _that = this;
switch (_that) {
case _BankAccount():
return $default(_that.id,_that.accountName,_that.accountNumber,_that.bankName,_that.country,_that.iban,_that.swiftCode,_that.isPrimary,_that.active,_that.totalWithdrawalTransaction);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String accountName,  String accountNumber,  String bankName,  String country,  String iban,  String? swiftCode,  bool isPrimary,  bool active,  int totalWithdrawalTransaction)?  $default,) {final _that = this;
switch (_that) {
case _BankAccount() when $default != null:
return $default(_that.id,_that.accountName,_that.accountNumber,_that.bankName,_that.country,_that.iban,_that.swiftCode,_that.isPrimary,_that.active,_that.totalWithdrawalTransaction);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BankAccount implements BankAccount {
  const _BankAccount({required this.id, required this.accountName, required this.accountNumber, required this.bankName, required this.country, required this.iban, this.swiftCode, required this.isPrimary, required this.active, required this.totalWithdrawalTransaction});
  factory _BankAccount.fromJson(Map<String, dynamic> json) => _$BankAccountFromJson(json);

@override final  int id;
@override final  String accountName;
@override final  String accountNumber;
@override final  String bankName;
@override final  String country;
@override final  String iban;
@override final  String? swiftCode;
@override final  bool isPrimary;
@override final  bool active;
@override final  int totalWithdrawalTransaction;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BankAccountCopyWith<_BankAccount> get copyWith => __$BankAccountCopyWithImpl<_BankAccount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BankAccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BankAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.accountName, accountName) || other.accountName == accountName)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.bankName, bankName) || other.bankName == bankName)&&(identical(other.country, country) || other.country == country)&&(identical(other.iban, iban) || other.iban == iban)&&(identical(other.swiftCode, swiftCode) || other.swiftCode == swiftCode)&&(identical(other.isPrimary, isPrimary) || other.isPrimary == isPrimary)&&(identical(other.active, active) || other.active == active)&&(identical(other.totalWithdrawalTransaction, totalWithdrawalTransaction) || other.totalWithdrawalTransaction == totalWithdrawalTransaction));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,accountName,accountNumber,bankName,country,iban,swiftCode,isPrimary,active,totalWithdrawalTransaction);

@override
String toString() {
  return 'BankAccount(id: $id, accountName: $accountName, accountNumber: $accountNumber, bankName: $bankName, country: $country, iban: $iban, swiftCode: $swiftCode, isPrimary: $isPrimary, active: $active, totalWithdrawalTransaction: $totalWithdrawalTransaction)';
}


}

/// @nodoc
abstract mixin class _$BankAccountCopyWith<$Res> implements $BankAccountCopyWith<$Res> {
  factory _$BankAccountCopyWith(_BankAccount value, $Res Function(_BankAccount) _then) = __$BankAccountCopyWithImpl;
@override @useResult
$Res call({
 int id, String accountName, String accountNumber, String bankName, String country, String iban, String? swiftCode, bool isPrimary, bool active, int totalWithdrawalTransaction
});




}
/// @nodoc
class __$BankAccountCopyWithImpl<$Res>
    implements _$BankAccountCopyWith<$Res> {
  __$BankAccountCopyWithImpl(this._self, this._then);

  final _BankAccount _self;
  final $Res Function(_BankAccount) _then;

/// Create a copy of BankAccount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? accountName = null,Object? accountNumber = null,Object? bankName = null,Object? country = null,Object? iban = null,Object? swiftCode = freezed,Object? isPrimary = null,Object? active = null,Object? totalWithdrawalTransaction = null,}) {
  return _then(_BankAccount(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,accountName: null == accountName ? _self.accountName : accountName // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,bankName: null == bankName ? _self.bankName : bankName // ignore: cast_nullable_to_non_nullable
as String,country: null == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String,iban: null == iban ? _self.iban : iban // ignore: cast_nullable_to_non_nullable
as String,swiftCode: freezed == swiftCode ? _self.swiftCode : swiftCode // ignore: cast_nullable_to_non_nullable
as String?,isPrimary: null == isPrimary ? _self.isPrimary : isPrimary // ignore: cast_nullable_to_non_nullable
as bool,active: null == active ? _self.active : active // ignore: cast_nullable_to_non_nullable
as bool,totalWithdrawalTransaction: null == totalWithdrawalTransaction ? _self.totalWithdrawalTransaction : totalWithdrawalTransaction // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$BankAccountsResponse {

 bool get status; int get statusCode; String get message; List<BankAccount> get data;
/// Create a copy of BankAccountsResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BankAccountsResponseCopyWith<BankAccountsResponse> get copyWith => _$BankAccountsResponseCopyWithImpl<BankAccountsResponse>(this as BankAccountsResponse, _$identity);

  /// Serializes this BankAccountsResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BankAccountsResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other.data, data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,const DeepCollectionEquality().hash(data));

@override
String toString() {
  return 'BankAccountsResponse(status: $status, statusCode: $statusCode, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class $BankAccountsResponseCopyWith<$Res>  {
  factory $BankAccountsResponseCopyWith(BankAccountsResponse value, $Res Function(BankAccountsResponse) _then) = _$BankAccountsResponseCopyWithImpl;
@useResult
$Res call({
 bool status, int statusCode, String message, List<BankAccount> data
});




}
/// @nodoc
class _$BankAccountsResponseCopyWithImpl<$Res>
    implements $BankAccountsResponseCopyWith<$Res> {
  _$BankAccountsResponseCopyWithImpl(this._self, this._then);

  final BankAccountsResponse _self;
  final $Res Function(BankAccountsResponse) _then;

/// Create a copy of BankAccountsResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? data = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<BankAccount>,
  ));
}

}


/// Adds pattern-matching-related methods to [BankAccountsResponse].
extension BankAccountsResponsePatterns on BankAccountsResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BankAccountsResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BankAccountsResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BankAccountsResponse value)  $default,){
final _that = this;
switch (_that) {
case _BankAccountsResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BankAccountsResponse value)?  $default,){
final _that = this;
switch (_that) {
case _BankAccountsResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  List<BankAccount> data)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BankAccountsResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool status,  int statusCode,  String message,  List<BankAccount> data)  $default,) {final _that = this;
switch (_that) {
case _BankAccountsResponse():
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool status,  int statusCode,  String message,  List<BankAccount> data)?  $default,) {final _that = this;
switch (_that) {
case _BankAccountsResponse() when $default != null:
return $default(_that.status,_that.statusCode,_that.message,_that.data);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _BankAccountsResponse implements BankAccountsResponse {
  const _BankAccountsResponse({required this.status, required this.statusCode, required this.message, required final  List<BankAccount> data}): _data = data;
  factory _BankAccountsResponse.fromJson(Map<String, dynamic> json) => _$BankAccountsResponseFromJson(json);

@override final  bool status;
@override final  int statusCode;
@override final  String message;
 final  List<BankAccount> _data;
@override List<BankAccount> get data {
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_data);
}


/// Create a copy of BankAccountsResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BankAccountsResponseCopyWith<_BankAccountsResponse> get copyWith => __$BankAccountsResponseCopyWithImpl<_BankAccountsResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$BankAccountsResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BankAccountsResponse&&(identical(other.status, status) || other.status == status)&&(identical(other.statusCode, statusCode) || other.statusCode == statusCode)&&(identical(other.message, message) || other.message == message)&&const DeepCollectionEquality().equals(other._data, _data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,status,statusCode,message,const DeepCollectionEquality().hash(_data));

@override
String toString() {
  return 'BankAccountsResponse(status: $status, statusCode: $statusCode, message: $message, data: $data)';
}


}

/// @nodoc
abstract mixin class _$BankAccountsResponseCopyWith<$Res> implements $BankAccountsResponseCopyWith<$Res> {
  factory _$BankAccountsResponseCopyWith(_BankAccountsResponse value, $Res Function(_BankAccountsResponse) _then) = __$BankAccountsResponseCopyWithImpl;
@override @useResult
$Res call({
 bool status, int statusCode, String message, List<BankAccount> data
});




}
/// @nodoc
class __$BankAccountsResponseCopyWithImpl<$Res>
    implements _$BankAccountsResponseCopyWith<$Res> {
  __$BankAccountsResponseCopyWithImpl(this._self, this._then);

  final _BankAccountsResponse _self;
  final $Res Function(_BankAccountsResponse) _then;

/// Create a copy of BankAccountsResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? statusCode = null,Object? message = null,Object? data = null,}) {
  return _then(_BankAccountsResponse(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as bool,statusCode: null == statusCode ? _self.statusCode : statusCode // ignore: cast_nullable_to_non_nullable
as int,message: null == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<BankAccount>,
  ));
}


}

// dart format on
