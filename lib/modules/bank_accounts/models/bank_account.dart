import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account.freezed.dart';
part 'bank_account.g.dart';

@freezed
abstract class BankAccount with _$BankAccount {
  const factory BankAccount({
    required int id,
    required String accountName,
    required String accountNumber,
    required String bankName,
    required String country,
    required String iban,
    String? swiftCode,
    required bool isPrimary,
    required bool active,
    required int totalWithdrawalTransaction,
  }) = _BankAccount;

  factory BankAccount.fromJson(Map<String, dynamic> json) =>
      _$BankAccountFromJson(json);
}

@freezed
abstract class BankAccountsResponse with _$BankAccountsResponse {
  const factory BankAccountsResponse({
    required bool status,
    required int statusCode,
    required String message,
    required List<BankAccount> data,
  }) = _BankAccountsResponse;

  factory BankAccountsResponse.fromJson(Map<String, dynamic> json) =>
      _$BankAccountsResponseFromJson(json);
}
