// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_account.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_BankAccount _$BankAccountFromJson(Map<String, dynamic> json) => _BankAccount(
  id: (json['id'] as num).toInt(),
  accountName: json['accountName'] as String,
  accountNumber: json['accountNumber'] as String,
  bankName: json['bankName'] as String,
  country: json['country'] as String,
  iban: json['iban'] as String,
  swiftCode: json['swiftCode'] as String?,
  isPrimary: json['isPrimary'] as bool,
  active: json['active'] as bool,
  totalWithdrawalTransaction: (json['totalWithdrawalTransaction'] as num)
      .toInt(),
);

Map<String, dynamic> _$BankAccountToJson(_BankAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accountName': instance.accountName,
      'accountNumber': instance.accountNumber,
      'bankName': instance.bankName,
      'country': instance.country,
      'iban': instance.iban,
      'swiftCode': instance.swiftCode,
      'isPrimary': instance.isPrimary,
      'active': instance.active,
      'totalWithdrawalTransaction': instance.totalWithdrawalTransaction,
    };

_BankAccountsResponse _$BankAccountsResponseFromJson(
  Map<String, dynamic> json,
) => _BankAccountsResponse(
  status: json['status'] as bool,
  statusCode: (json['statusCode'] as num).toInt(),
  message: json['message'] as String,
  data: (json['data'] as List<dynamic>)
      .map((e) => BankAccount.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$BankAccountsResponseToJson(
  _BankAccountsResponse instance,
) => <String, dynamic>{
  'status': instance.status,
  'statusCode': instance.statusCode,
  'message': instance.message,
  'data': instance.data,
};
