import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/form_fields/phone_form_field.dart';
import 'package:phone_form_field/phone_form_field.dart';
import 'package:maisour/modules/get_help/providers/get_help_providers.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';

class RaiseTicketScreen extends ConsumerStatefulWidget {
  const RaiseTicketScreen({super.key});

  @override
  ConsumerState<RaiseTicketScreen> createState() => _RaiseTicketScreenState();
}

class _RaiseTicketScreenState extends ConsumerState<RaiseTicketScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _messageController = TextEditingController();
  late final PhoneController _phoneController;

  @override
  void initState() {
    super.initState();
    _phoneController = PhoneController(
      initialValue: PhoneNumber(nsn: '', isoCode: IsoCode.AE),
    );
    _initializeFormData();
  }

  void _initializeFormData() {
    final user = ref.readCurrentUser;
    if (user != null) {
      _fullNameController.text = user.user.fullName;
      _emailController.text = user.user.email;
      if (user.phoneNumber != null && user.phoneNumber!.isNotEmpty) {
        // Parse phone number and set it in the controller
        try {
          final phoneNumber = PhoneNumber.parse(user.phoneNumber!);
          _phoneController.value = phoneNumber;
        } catch (e) {
          // If parsing fails, keep the default value
        }
      }
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _messageController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _onSubmit() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(contactUsNotifierProvider.notifier)
          .submitContactUs(
            name: _fullNameController.text.trim(),
            email: _emailController.text.trim(),
            phoneNumber: _phoneController.value.international,
            comment: _messageController.text.trim(),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<void>>(contactUsNotifierProvider, (previous, next) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

      // Only handle state changes from loading to data/error, not initial state
      if (previous?.isLoading == true && !next.isLoading) {
        next.when(
          data: (_) {
            context.showSuccessToast(
              LocaleKeys.success.tr(),
              LocaleKeys.supportRequestSubmittedSuccessfully.tr(),
            );
            GoRouter.of(context).pop();
          },
          error: (error, stackTrace) {
            context.showErrorToast(LocaleKeys.oops.tr(), error.toString());
          },
          loading: () {},
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.raiseTicket.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child:
            Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Full Name Field
                          AppTextField(
                            labelText: LocaleKeys.fullName.tr(),
                            controller: _fullNameController,
                            keyboardType: TextInputType.name,
                            textInputAction: TextInputAction.next,
                            textCapitalization: TextCapitalization.words,
                            validator: (value) => AppValidators.required(
                              LocaleKeys.fullName.tr(),
                              value,
                            ),
                            inputFormatters:
                                InputFormatters.fullNameFormatters(),
                          ),

                          16.h.heightBox,

                          // Email Field
                          AppTextField(
                            labelText: LocaleKeys.email.tr(),
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            textInputAction: TextInputAction.next,
                            validator: (value) => AppValidators.email(value),
                          ),

                          16.h.heightBox,

                          // Phone Number Field
                          AppPhoneFormField(controller: _phoneController),

                          16.h.heightBox,

                          // Message Field
                          AppTextField(
                            labelText: LocaleKeys.message.tr(),
                            controller: _messageController,
                            keyboardType: TextInputType.multiline,
                            textInputAction: TextInputAction.newline,
                            minLines: 4,
                            maxLines: 8,
                            validator: (value) => AppValidators.required(
                              LocaleKeys.message.tr(),
                              value,
                            ),
                          ),
                        ],
                      ).paddingAll(16.w),
                    ),
                  ),

                  // Bottom Buttons
                  Container(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      children: [
                        Consumer(
                          builder: (context, ref, child) {
                            final contactUsState = ref.watch(
                              contactUsNotifierProvider,
                            );
                            final isLoading = contactUsState.isLoading;

                            return AppButton(
                              text: LocaleKeys.submit.tr(),
                              onPressed: _onSubmit,
                              isLoading: isLoading,
                            );
                          },
                        ),
                        8.h.heightBox,
                        AppButton(
                          type: ButtonType.text,
                          text: LocaleKeys.cancel.tr(),
                          onPressed: () => GoRouter.of(context).pop(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ).onTap(() {
              FocusManager.instance.primaryFocus?.unfocus();
            }),
      ),
    );
  }
}
