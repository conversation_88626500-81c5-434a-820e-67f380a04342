import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/get_help/widgets/faq.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/enums/social_media_platform.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';
import 'package:maisour/modules/get_help/widgets/help_center.dart';
import 'package:maisour/config/app_router.dart';

class GetHelpScreen extends StatelessWidget {
  const GetHelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(LocaleKeys.getHelp.tr()), centerTitle: false),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(),
            AppSpacer(),
            HelpCenterSection(
              onRaiseTicket: () {
                GoRouter.of(context).pushNamed(RouteName.raiseTicket.name);
              },
              onGetInTouch: () {
                UrlLauncherService.launchEmail(
                  AppConstants.contactEmail,
                  subject: 'Contact Us',
                );
              },
              onChatWithUs: () {
                UrlLauncherService.launchSocialMedia(
                  SocialMediaPlatform.whatsapp,
                );
              },
            ),
            AppSpacer(),
            FAQSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              LocaleKeys.hiHowCanWeHelp.tr(),
              style: AppTextStyles.text22.bold.copyWith(
                color: AppColors.dark[900],
              ),
            ),
          ),
          Assets.icons.help.svg(width: 60.w, height: 60.w),
        ],
      ),
    );
  }
}
