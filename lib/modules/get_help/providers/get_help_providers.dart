import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/get_help/models/faq.dart';
import 'package:maisour/modules/get_help/providers/get_help_api_provider.dart';
import 'package:maisour/modules/get_help/get_help_service.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/providers/language_provider.dart';

/// Provider for get help service
final getHelpServiceProvider = Provider.autoDispose<GetHelpService>((ref) {
  final getHelpApi = ref.watch(getHelpApiProvider);
  return GetHelpService(getHelpApi);
}, name: 'getHelpServiceProvider');

/// Notifier for FAQ state management
class FaqNotifier extends AutoDisposeAsyncNotifier<List<Faq>> {
  @override
  Future<List<Faq>> build() async {
    return _fetchFaqs();
  }

  Future<List<Faq>> _fetchFaqs() async {
    try {
      final service = ref.read(getHelpServiceProvider);
      final locale = ref.read(languageProvider).languageCode;
      return await service.getFaqsByLocale(locale);
    } catch (error, stackTrace) {
      if (error is Failure) {
        rethrow;
      }
      throw Failure(
        message: 'Failed to fetch FAQs',
        exception: error as Exception?,
        stackTrace: stackTrace,
      );
    }
  }

  /// Refresh FAQs
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchFaqs());
  }
}

/// Provider for FAQ notifier
final faqNotifierProvider =
    AutoDisposeAsyncNotifierProvider<FaqNotifier, List<Faq>>(
      () => FaqNotifier(),
      name: 'faqNotifierProvider',
    );

/// Notifier for contact us state management
class ContactUsNotifier extends StateNotifier<AsyncValue<void>> {
  final GetHelpService _service;

  ContactUsNotifier(this._service) : super(const AsyncValue.data(null));

  /// Submit contact us form
  Future<void> submitContactUs({
    required String name,
    required String email,
    required String phoneNumber,
    required String comment,
  }) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return await _service.submitContactUs(
        name: name,
        email: email,
        phoneNumber: phoneNumber,
        comment: comment,
      );
    });
  }
}

/// Provider for contact us notifier
final contactUsNotifierProvider =
    StateNotifierProvider.autoDispose<ContactUsNotifier, AsyncValue<void>>(
      (ref) => ContactUsNotifier(ref.read(getHelpServiceProvider)),
      name: 'contactUsNotifierProvider',
    );
