import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/get_help/api/get_help_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for get help module
final getHelpApiProvider = Provider.autoDispose<GetHelpApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return GetHelpApi(dio);
}, name: 'getHelpApiProvider');
