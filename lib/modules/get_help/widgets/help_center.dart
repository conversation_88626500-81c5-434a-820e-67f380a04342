import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class HelpCenterSection extends StatelessWidget {
  final VoidCallback? onRaiseTicket;
  final VoidCallback? onGetInTouch;
  final VoidCallback? onChatWithUs;

  const HelpCenterSection({
    super.key,
    this.onRaiseTicket,
    this.onGetInTouch,
    this.onChatWithUs,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          LocaleKeys.helpCenter.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.w.heightBox,
        // Action Cards
        Row(
          children: [
            Expanded(
              child: _HelpCard(
                icon: Assets.icons.ticket.svg(
                  width: 24.w,
                  height: 24.w,
                  colorFilter: ColorFilter.mode(
                    AppColors.dark,
                    BlendMode.srcIn,
                  ),
                ),
                title: LocaleKeys.raiseTicket.tr(),
                onTap: onRaiseTicket,
              ),
            ),
            8.w.widthBox,
            Expanded(
              child: _HelpCard(
                icon: Assets.icons.mail.svg(
                  width: 24.w,
                  height: 24.w,
                  colorFilter: ColorFilter.mode(
                    AppColors.dark,
                    BlendMode.srcIn,
                  ),
                ),
                title: LocaleKeys.getInTouch.tr(),
                onTap: onGetInTouch,
              ),
            ),
            8.w.widthBox,
            Expanded(
              child: _HelpCard(
                icon: Assets.icons.whatsappChat.svg(
                  width: 24.w,
                  height: 24.w,
                  colorFilter: ColorFilter.mode(
                    AppColors.dark,
                    BlendMode.srcIn,
                  ),
                ),
                title: LocaleKeys.chatWithUs.tr(),
                onTap: onChatWithUs,
              ),
            ),
          ],
        ),
      ],
    ).paddingAll(16.w);
  }
}

class _HelpCard extends StatelessWidget {
  final Widget icon;
  final String title;
  final VoidCallback? onTap;

  const _HelpCard({required this.icon, required this.title, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.gray.shade200),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon,
            10.h.heightBox,
            Text(
              title,
              style: AppTextStyles.text14.semiBold.dark900,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
