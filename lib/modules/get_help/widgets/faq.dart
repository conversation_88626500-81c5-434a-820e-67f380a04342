import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/get_help/providers/get_help_providers.dart';
import 'package:maisour/modules/get_help/models/faq.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';

class FAQSection extends ConsumerStatefulWidget {
  const FAQSection({super.key});

  @override
  ConsumerState<FAQSection> createState() => _FAQSectionState();
}

class _FAQSectionState extends ConsumerState<FAQSection> {
  int? _expandedIndex;

  @override
  Widget build(BuildContext context) {
    final faqAsync = ref.watch(faqNotifierProvider);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            LocaleKeys.frequentlyAskedQuestions.tr(),
            style: AppTextStyles.text18.bold.dark900,
          ),
          16.w.heightBox,
          faqAsync.when(
            data: (faqs) => _buildFaqContent(faqs),
            loading: () => _buildLoadingState(),
            error: (error, stackTrace) => _buildErrorState(error),
          ),
          20.w.heightBox,
        ],
      ).paddingAll(16.w),
    );
  }

  Widget _buildFaqContent(List<Faq> faqs) {
    if (faqs.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: List.generate(faqs.length, (index) {
          final item = faqs[index];
          final isExpanded = _expandedIndex == index;

          return Column(
            children: [
              // FAQ Item
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.question,
                        style: AppTextStyles.text14.semiBold.dark,
                      ),
                    ),
                    8.w.widthBox,
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _expandedIndex = isExpanded ? null : index;
                        });
                      },
                      child: Icon(
                        isExpanded ? Icons.remove : Icons.add,
                        size: 20.w,
                        color: AppColors.dark.shade300,
                      ),
                    ),
                  ],
                ),
              ),

              // Answer (if expanded)
              if (isExpanded)
                Text(
                  item.answer,
                  style: AppTextStyles.text12.medium.dark300,
                ).paddingOnly(left: 16.w, right: 16.w, bottom: 16.w),

              // Separator (except for last item)
              if (index < faqs.length - 1)
                Divider(color: AppColors.gray.shade200).paddingStart(16.w),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildLoadingState() {
    return AppShimmerLoader.fixed(itemCount: 3, itemHeight: 30.w);
  }

  Widget _buildErrorState(Object error) {
    return Center(child: Text(error.toString()));
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray.shade200),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Assets.icons.getHelp.svg(
              width: 48.w,
              height: 48.w,
              colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
            ),
            16.h.heightBox,
            Text(
              LocaleKeys.noFaqsAvailable.tr(),
              style: AppTextStyles.text14.semiBold.dark900,
            ),
          ],
        ),
      ),
    );
  }
}
