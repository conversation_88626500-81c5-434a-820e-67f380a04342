import 'package:dio/dio.dart';
import 'package:maisour/modules/get_help/api/get_help_api.dart';
import 'package:maisour/modules/get_help/models/faq.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Service class for handling get help operations
class GetHelpService with DioExceptionMapper {
  final GetHelpApi _getHelpApi;

  GetHelpService(this._getHelpApi);

  /// Get FAQs by locale
  Future<List<Faq>> getFaqsByLocale(String locale) async {
    try {
      final faqs = await _getHelpApi.getFaqsByLocale(locale);
      return faqs;
    } catch (e) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        throw failure;
      }
      throw Exception('Failed to fetch FAQs: $e');
    }
  }

  /// Submit contact us form
  Future<void> submitContactUs({
    required String name,
    required String email,
    required String phoneNumber,
    required String comment,
  }) async {
    try {
      final request = {
        'name': name,
        'email': email,
        'phoneNumber': phoneNumber,
        'comment': comment,
        'timeToContact': '',
      };

      await _getHelpApi.submitContactUs(request);
    } catch (e) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        throw failure;
      }
      throw Exception('Failed to submit contact us: $e');
    }
  }
}
