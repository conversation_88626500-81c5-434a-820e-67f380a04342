import 'package:dio/dio.dart';
import 'package:maisour/modules/get_help/models/faq.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:retrofit/retrofit.dart';

part 'get_help_api.g.dart';

@RestApi()
abstract class GetHelpApi {
  factory GetHelpApi(Dio dio, {String? baseUrl}) = _GetHelpApi;

  /// Get FAQs by locale
  @GET(ApiEndpoints.faqsByLocale)
  Future<List<Faq>> getFaqsByLocale(@Query('locale') String locale);

  /// Submit contact us form
  @POST(ApiEndpoints.contactUs)
  Future<void> submitContactUs(@Body() Map<String, dynamic> request);
}
