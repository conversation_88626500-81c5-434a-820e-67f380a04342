import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/shared/enums/transaction_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/utils/date_formatter.dart';

class TransactionItem extends StatelessWidget {
  final Transaction transaction;
  final AppUser user;
  final bool isLast;
  const TransactionItem({
    super.key,
    required this.transaction,
    required this.user,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          16.w.widthBox,
          // Transaction icon
          _buildTransactionIcon(),
          10.w.widthBox,
          // Transaction details
          Expanded(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      transaction.transactionType,
                      style: AppTextStyles.text14.bold.dark900,
                    ),

                    Row(
                      children: [
                        if (user.usesImageSymbol)
                          Assets.images.dirham.image(width: 20.w, height: 20.w)
                        else
                          Text(
                            user.currencyCode.currencySymbol,
                            style: AppTextStyles.text14.bold.dark900,
                          ),
                        4.w.widthBox,
                        Text(
                          user.getCurrencyValue(transaction.amount),
                          style: AppTextStyles.text14.bold.dark900,
                        ),
                      ],
                    ),
                  ],
                ).paddingEnd(16.w),
                6.h.heightBox,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      DateFormatter.formatDate(transaction.date),
                      style: AppTextStyles.text14.medium.dark300,
                    ),
                    _buildStatusChip(),
                  ],
                ).paddingEnd(16.w),
                8.h.heightBox,
                if (!isLast)
                  Divider(color: AppColors.gray.shade200, thickness: 1.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionIcon() {
    Widget icon;

    switch (transaction.transactionType.toLowerCase()) {
      case 'investment':
      case 'sale proceed':
      case 'other adjustment':
      case 'deposit':
      case 'dividend':
      case 'reward':
      case 'company reward':
        icon = Assets.icons.arrowBottomLeft.svg(
          width: 32.w,
          height: 32.w,
          colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
        );
        break;

      case 'withdrawal':
        icon = Assets.icons.arrowTopRight.svg(
          width: 32.w,
          height: 32.w,
          colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
        );
        break;

      default:
        icon = Assets.icons.arrowBottomLeft.svg(
          width: 32.w,
          height: 32.w,
          colorFilter: ColorFilter.mode(AppColors.gray, BlendMode.srcIn),
        );
    }

    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        Container(
          width: 48.w,
          height: 48.w,
          decoration: BoxDecoration(
            color: AppColors.gray.shade100,
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        icon,
      ],
    );
  }

  Widget _buildStatusChip() {
    Color textColor;
    String statusText;

    if (transaction.status.isPositive) {
      textColor = AppColors.success;
      statusText = transaction.status.displayName;
    } else if (transaction.status.isNegative) {
      textColor = AppColors.danger;
      statusText = transaction.status.displayName;
    } else if (transaction.status.isNeutral) {
      textColor = AppColors.warning;
      statusText = transaction.status.displayName;
    } else {
      textColor = AppColors.gray.shade600;
      statusText = transaction.status.displayName;
    }

    return Text(
      statusText,
      style: AppTextStyles.text14.medium.copyWith(color: textColor),
    );
  }
}
