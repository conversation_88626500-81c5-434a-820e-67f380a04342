import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

enum TransactionFilter { last30Days, last3Months, last6Months }

class TransactionFilterModal extends StatefulWidget {
  final TransactionFilter? currentFilter;
  final Function(TransactionFilter? filter) onFilterSelected;

  const TransactionFilterModal({
    super.key,
    this.currentFilter,
    required this.onFilterSelected,
  });

  static Future<void> show(
    BuildContext context, {
    TransactionFilter? currentFilter,
    required Function(TransactionFilter? filter) onFilterSelected,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => TransactionFilterModal(
        currentFilter: currentFilter,
        onFilterSelected: onFilterSelected,
      ),
    );
  }

  @override
  State<TransactionFilterModal> createState() => _TransactionFilterModalState();
}

class _TransactionFilterModalState extends State<TransactionFilterModal> {
  TransactionFilter? selectedFilter;

  @override
  void initState() {
    super.initState();
    selectedFilter = widget.currentFilter;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        16.h.heightBox,

        // Title
        Text(
          LocaleKeys.filters.tr(),
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ),
        20.h.heightBox,

        // Filter options
        _buildFilterOption(
          TransactionFilter.last30Days,
          LocaleKeys.last30Days.tr(),
        ),
        12.h.heightBox,
        _buildFilterOption(
          TransactionFilter.last3Months,
          LocaleKeys.last3Months.tr(),
        ),
        12.h.heightBox,
        _buildFilterOption(
          TransactionFilter.last6Months,
          LocaleKeys.last6Months.tr(),
        ),

        24.h.heightBox,
      ],
    ).paddingHorizontal(16.w);
  }

  Widget _buildFilterOption(TransactionFilter filter, String title) {
    final isSelected = selectedFilter == filter;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: AppTextStyles.text14.medium.dark900),
        if (isSelected)
          Assets.icons.check.svg(
            width: 20.w,
            height: 20.w,
            colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
          ),
      ],
    ).onTap(() {
      if (isSelected) {
        // If same filter is selected, unselect it (set to null)
        Navigator.of(context).pop();
        widget.onFilterSelected(null);
      } else {
        // If different filter is selected, apply the new filter
        Navigator.of(context).pop();
        widget.onFilterSelected(filter);
      }
    });
  }
}
