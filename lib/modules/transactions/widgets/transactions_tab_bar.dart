import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'transaction_tab.dart';

class TransactionsTabBar extends StatefulWidget {
  final void Function(TransactionTab selectedTab, int index) onTabChanged;
  final int selectedIndex;
  final bool isFilterApplied;

  const TransactionsTabBar({
    super.key,
    required this.onTabChanged,
    required this.selectedIndex,
    this.isFilterApplied = false,
  });

  @override
  State<TransactionsTabBar> createState() => _TransactionsTabBarState();
}

class _TransactionsTabBarState extends State<TransactionsTabBar> {
  final List<TransactionTab> tabs = [
    TransactionTab(key: 'All', title: LocaleKeys.all.tr()),
    TransactionTab(key: 'Investment', title: LocaleKeys.investment.tr()),
    TransactionTab(key: 'Withdrawal', title: LocaleKeys.withdrawal.tr()),
    TransactionTab(key: 'Deposit', title: LocaleKeys.deposit.tr()),
    TransactionTab(key: 'Dividend', title: LocaleKeys.dividend.tr()),
    TransactionTab(key: 'Reward', title: LocaleKeys.reward.tr()),
    TransactionTab(key: 'Sale Proceed', title: LocaleKeys.saleProceed.tr()),
    TransactionTab(
      key: 'Other Adjustment',
      title: LocaleKeys.otherAdjustment.tr(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 56.w,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: tabs.length,
        itemBuilder: (_, index) {
          final isSelected = index == widget.selectedIndex;
          return Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.primary.alphaPercent(10)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(20.r),
            ),
            padding: EdgeInsetsGeometry.symmetric(
              horizontal: 14.w,
              vertical: 8.w,
            ),
            margin: EdgeInsetsGeometry.directional(
              start: 8.w,
              top: 10.w,
              bottom: 10.w,
              end: 4.w,
            ),
            child: Center(
              child: Text(
                tabs[index].title,
                style: isSelected
                    ? AppTextStyles.text12.bold.primary
                    : AppTextStyles.text12.medium.gray600,
              ),
            ),
          ).onTap(() {
            widget.onTabChanged(tabs[index], index);
          });
        },
      ),
    );
  }
}
