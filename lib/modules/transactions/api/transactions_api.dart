import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/shared/constants/api_constants.dart';

part 'transactions_api.g.dart';

@RestApi()
abstract class TransactionsApi {
  factory TransactionsApi(Dio dio, {String baseUrl}) = _TransactionsApi;

  @POST(ApiEndpoints.getBankTransactions)
  Future<TransactionsResponse> getBankTransactions({
    @Query('transactionType') String? transactionType,
    @Query('filter') String? filter,
    @Body() required Map<String, dynamic> body,
  });
}
