import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/transactions/api/transactions_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for transactions module
final transactionsApiProvider = Provider.autoDispose<TransactionsApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return TransactionsApi(dio);
}, name: 'transactionsApiProvider');
