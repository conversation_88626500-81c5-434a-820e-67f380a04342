import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/transactions/api/transactions_api.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/modules/transactions/providers/transactions_api_provider.dart';
import 'package:maisour/modules/transactions/widgets/transaction_filter_modal.dart';
import 'package:maisour/modules/transactions/widgets/transaction_tab.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// State class for transactions
class TransactionsState {
  final ApiStatus status;
  final List<Transaction> transactions;
  final String? errorMessage;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final bool hasMoreData;
  final TransactionTab? currentTab;
  final TransactionFilter? currentFilter;

  const TransactionsState({
    this.status = ApiStatus.initial,
    this.transactions = const [],
    this.errorMessage,
    this.currentPage = 1,
    this.totalPages = 0,
    this.totalItems = 0,
    this.hasMoreData = false,
    this.currentTab,
    this.currentFilter,
  });

  TransactionsState copyWith({
    ApiStatus? status,
    List<Transaction>? transactions,
    String? errorMessage,
    int? currentPage,
    int? totalPages,
    int? totalItems,
    bool? hasMoreData,
    TransactionTab? currentTab,
    TransactionFilter? currentFilter,
  }) {
    return TransactionsState(
      status: status ?? this.status,
      transactions: transactions ?? this.transactions,
      errorMessage: errorMessage ?? this.errorMessage,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalItems: totalItems ?? this.totalItems,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      currentTab: currentTab ?? this.currentTab,
      currentFilter: currentFilter ?? this.currentFilter,
    );
  }
}

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}

/// Notifier for transactions state management
class TransactionsNotifier extends StateNotifier<TransactionsState> {
  final TransactionsApi _transactionsApi;
  final _dioExceptionMapper = _DioExceptionMapperHelper();

  // Cache management
  final Map<String, List<Transaction>> _cachedTransactions = {};
  final Map<String, int> _currentPages = {};
  final Map<String, int> _totalPages = {};
  final Map<String, bool> _hasMoreData = {};

  TransactionsNotifier(this._transactionsApi)
    : super(const TransactionsState());

  /// Check if data exists in cache for a tab
  bool _hasCachedData(TransactionTab tab, TransactionFilter? filter) {
    final cacheKey = _getCacheKey(tab, filter);
    return _cachedTransactions.containsKey(cacheKey) &&
        _cachedTransactions[cacheKey]!.isNotEmpty;
  }

  /// Generate cache key for tab and filter combination
  String _getCacheKey(TransactionTab tab, TransactionFilter? filter) {
    return '${tab.key}_${filter?.name ?? 'no_filter'}';
  }

  /// Load transactions for a specific tab
  Future<void> loadTransactions(
    TransactionTab tab, {
    TransactionFilter? filter,
    bool forceRefresh = false,
  }) async {
    if (state.status == ApiStatus.loading) return;

    final cacheKey = _getCacheKey(tab, filter);
    debugPrint(
      'loadTransactions called: tab=${tab.key}, filter=$filter, forceRefresh=$forceRefresh',
    );
    debugPrint('Has cached data: ${_hasCachedData(tab, filter)}');

    // Check if we can use cached data (only if not force refresh)
    if (!forceRefresh && _hasCachedData(tab, filter)) {
      debugPrint('Using cached data for $cacheKey');
      final cachedTransactions = _cachedTransactions[cacheKey]!;
      state = state.copyWith(
        status: ApiStatus.success,
        currentTab: tab,
        currentFilter: filter,
        transactions: cachedTransactions,
        hasMoreData: _hasMoreData[cacheKey] ?? false,
        currentPage: _currentPages[cacheKey] ?? 1,
        totalPages: _totalPages[cacheKey] ?? 0,
        errorMessage: null,
      );
      return;
    }

    // If filter changed, clear the specific cache key to reload data
    if (state.currentFilter != filter) {
      debugPrint(
        'Filter changed from ${state.currentFilter} to $filter, clearing cache for $cacheKey',
      );
      _clearTabCache(cacheKey);
    }

    // Clear cache if force refresh or filter changed
    if (forceRefresh) {
      debugPrint('Clearing cache for $cacheKey due to force refresh');
      _clearTabCache(cacheKey);
    }

    // Load from API
    await _loadFromApi(tab, filter);
  }

  /// Load data from API
  Future<void> _loadFromApi(
    TransactionTab tab,
    TransactionFilter? filter,
  ) async {
    final cacheKey = _getCacheKey(tab, filter);

    try {
      state = state.copyWith(
        status: ApiStatus.loading,
        currentTab: tab,
        currentFilter: filter,
        currentPage: 1,
        transactions: [],
        errorMessage: null,
      );

      // Build request body
      final Map<String, dynamic> requestBody = {'page': 1, 'pageSize': 10};

      // Convert filter to query parameter
      String? filterQuery;
      if (filter != null) {
        switch (filter) {
          case TransactionFilter.last30Days:
            filterQuery = '30days';
            break;
          case TransactionFilter.last3Months:
            filterQuery = '3months';
            break;
          case TransactionFilter.last6Months:
            filterQuery = '6months';
            break;
        }
      }

      final response = await _transactionsApi.getBankTransactions(
        transactionType: tab.key == 'All' ? null : tab.key,
        filter: filterQuery,
        body: requestBody,
      );

      final totalPages = (response.total / 10).ceil();
      final hasMoreData = response.total > 10;

      // Cache the data
      _cachedTransactions[cacheKey] = response.data;
      _currentPages[cacheKey] = 1;
      _totalPages[cacheKey] = totalPages;
      _hasMoreData[cacheKey] = hasMoreData;

      state = state.copyWith(
        status: ApiStatus.success,
        transactions: response.data,
        currentPage: 1,
        totalPages: totalPages,
        totalItems: response.total,
        hasMoreData: hasMoreData,
        errorMessage: null,
      );
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: failure.message,
      );
    } catch (e) {
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// Load more transactions (pagination)
  Future<void> loadMoreTransactions() async {
    final cacheKey = _getCacheKey(state.currentTab!, state.currentFilter);

    // Check if we have more data to load
    final hasMoreData = _hasMoreData[cacheKey] ?? false;

    // Prevent multiple simultaneous calls
    if (!hasMoreData || state.status == ApiStatus.moreLoading) {
      debugPrint(
        'LoadMore blocked: hasMore=$hasMoreData, isLoading=${state.status == ApiStatus.moreLoading}',
      );
      return;
    }

    // Set loading more state
    state = state.copyWith(status: ApiStatus.moreLoading);
    debugPrint('LoadMore started for $cacheKey');

    final nextPage = (_currentPages[cacheKey] ?? 1) + 1;

    try {
      // Build request body
      final Map<String, dynamic> requestBody = {
        'page': nextPage,
        'pageSize': 10,
      };

      // Convert filter to query parameter
      String? filterQuery;
      if (state.currentFilter != null) {
        switch (state.currentFilter!) {
          case TransactionFilter.last30Days:
            filterQuery = '30days';
            break;
          case TransactionFilter.last3Months:
            filterQuery = '3months';
            break;
          case TransactionFilter.last6Months:
            filterQuery = '6months';
            break;
        }
      }

      final response = await _transactionsApi.getBankTransactions(
        transactionType: state.currentTab!.key == 'All'
            ? null
            : state.currentTab!.key,
        filter: filterQuery,
        body: requestBody,
      );

      final totalPages = (response.total / 10).ceil();
      final hasMoreData = nextPage < totalPages;

      final newTransactions = response.data;
      final existingTransactions = _cachedTransactions[cacheKey] ?? [];

      // Append new data to cache
      _cachedTransactions[cacheKey] = [
        ...existingTransactions,
        ...newTransactions,
      ];
      _currentPages[cacheKey] = nextPage;
      _hasMoreData[cacheKey] = hasMoreData;

      state = state.copyWith(
        status: ApiStatus.success,
        transactions: _cachedTransactions[cacheKey]!,
        currentPage: nextPage,
        totalPages: totalPages,
        totalItems: response.total,
        hasMoreData: hasMoreData,
        errorMessage: null,
      );

      debugPrint(
        'LoadMore completed for $cacheKey: added ${newTransactions.length} items',
      );
    } on DioException catch (error, stackTrace) {
      final failure = _dioExceptionMapper.mapDioExceptionToFailure(
        error,
        stackTrace,
      );
      debugPrint('Error loading more transactions: ${failure.message}');
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: failure.message,
      );
    } catch (e) {
      debugPrint('Error loading more transactions: ${e.toString()}');
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: e.toString(),
      );
    } finally {
      state = state.copyWith(status: ApiStatus.success);
      debugPrint('LoadMore finished for $cacheKey');
    }
  }

  /// Clear cache for a specific tab
  void _clearTabCache(String cacheKey) {
    _cachedTransactions[cacheKey] = [];
    _currentPages[cacheKey] = 0;
    _totalPages[cacheKey] = 0;
    _hasMoreData[cacheKey] = false;
  }

  /// Clear all cache
  void clearCache() {
    _cachedTransactions.clear();
    _currentPages.clear();
    _totalPages.clear();
    _hasMoreData.clear();
  }

  /// Refresh current tab (for pull-to-refresh)
  Future<void> refreshCurrentTab() async {
    debugPrint('Refreshing current tab: ${state.currentTab?.key}');
    if (state.currentTab != null) {
      await loadTransactions(
        state.currentTab!,
        filter: state.currentFilter,
        forceRefresh: true,
      );
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(status: ApiStatus.initial, errorMessage: null);
  }
}

/// Provider for transactions state
final transactionsProvider =
    StateNotifierProvider.autoDispose<TransactionsNotifier, TransactionsState>((
      ref,
    ) {
      final transactionsApi = ref.watch(transactionsApiProvider);
      return TransactionsNotifier(transactionsApi);
    }, name: 'transactionsProvider');
