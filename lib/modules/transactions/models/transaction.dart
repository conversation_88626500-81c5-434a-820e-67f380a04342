import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/enums/transaction_status.dart';

part 'transaction.freezed.dart';
part 'transaction.g.dart';

@freezed
abstract class Transaction with _$Transaction {
  const factory Transaction({
    required String transactionType,
    required double amount,
    required DateTime date,
    required TransactionStatus status,
  }) = _Transaction;

  factory Transaction.fromJson(Map<String, dynamic> json) =>
      _$TransactionFromJson(json);
}

@freezed
abstract class TransactionsResponse with _$TransactionsResponse {
  const factory TransactionsResponse({
    required bool status,
    required int statusCode,
    required String message,
    required List<Transaction> data,
    @Default(0) int page,
    @Default(0) int perPage,
    @Default(0) int total,
  }) = _TransactionsResponse;

  factory TransactionsResponse.fromJson(Map<String, dynamic> json) =>
      _$TransactionsResponseFromJson(json);
}
