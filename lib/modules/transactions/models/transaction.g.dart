// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Transaction _$TransactionFromJson(Map<String, dynamic> json) => _Transaction(
  transactionType: json['transactionType'] as String,
  amount: (json['amount'] as num).toDouble(),
  date: DateTime.parse(json['date'] as String),
  status: $enumDecode(_$TransactionStatusEnumMap, json['status']),
);

Map<String, dynamic> _$TransactionToJson(_Transaction instance) =>
    <String, dynamic>{
      'transactionType': instance.transactionType,
      'amount': instance.amount,
      'date': instance.date.toIso8601String(),
      'status': _$TransactionStatusEnumMap[instance.status]!,
    };

const _$TransactionStatusEnumMap = {
  TransactionStatus.successful: 'Successful',
  TransactionStatus.success: 'Success',
  TransactionStatus.failed: 'Failed',
  TransactionStatus.pending: 'Pending',
  TransactionStatus.cancelled: 'Cancelled',
  TransactionStatus.canceled: 'Canceled',
  TransactionStatus.rejected: 'Rejected',
  TransactionStatus.reversed: 'Reversed',
  TransactionStatus.refunded: 'Refunded',
  TransactionStatus.notFunded: 'NotFunded',
  TransactionStatus.removed: 'Removed',
  TransactionStatus.sold: 'Sold',
};

_TransactionsResponse _$TransactionsResponseFromJson(
  Map<String, dynamic> json,
) => _TransactionsResponse(
  status: json['status'] as bool,
  statusCode: (json['statusCode'] as num).toInt(),
  message: json['message'] as String,
  data: (json['data'] as List<dynamic>)
      .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
      .toList(),
  page: (json['page'] as num?)?.toInt() ?? 0,
  perPage: (json['perPage'] as num?)?.toInt() ?? 0,
  total: (json['total'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$TransactionsResponseToJson(
  _TransactionsResponse instance,
) => <String, dynamic>{
  'status': instance.status,
  'statusCode': instance.statusCode,
  'message': instance.message,
  'data': instance.data,
  'page': instance.page,
  'perPage': instance.perPage,
  'total': instance.total,
};
