import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/modules/transactions/providers/transactions_provider.dart';
import 'package:maisour/modules/transactions/widgets/transaction_filter_modal.dart';
import 'package:maisour/modules/transactions/widgets/transaction_item.dart';
import 'package:maisour/modules/transactions/widgets/transaction_tab.dart';
import 'package:maisour/modules/transactions/widgets/transactions_tab_bar.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';

class TransactionsScreen extends ConsumerStatefulWidget {
  const TransactionsScreen({super.key});

  @override
  ConsumerState<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends ConsumerState<TransactionsScreen> {
  TransactionTab selectedTab = TransactionTab(key: 'All', title: 'All');
  int selectedIndex = 0;
  TransactionFilter? selectedFilter;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Load initial transactions for All tab
      ref
          .read(transactionsProvider.notifier)
          .loadTransactions(selectedTab, filter: selectedFilter);
    });

    // Add scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      debugPrint(
        'Scroll threshold reached: pixels=${_scrollController.position.pixels}, maxScroll=${_scrollController.position.maxScrollExtent}',
      );

      // Load more when user is 200 pixels away from bottom
      ref.read(transactionsProvider.notifier).loadMoreTransactions();
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.transactions.tr()),
        centerTitle: false,
        actions: [_buildFilterIcon()],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(kToolbarHeight),
          child: TransactionsTabBar(
            selectedIndex: selectedIndex,
            onTabChanged: (tab, index) {
              setState(() {
                selectedTab = tab;
                selectedIndex = index;
              });

              // Reset scroll position when switching tabs
              if (_scrollController.hasClients) {
                _scrollController.jumpTo(0);
              }

              // Load transactions when tab changes
              ref
                  .read(transactionsProvider.notifier)
                  .loadTransactions(tab, filter: selectedFilter);
            },
          ),
        ),
      ),
      body: Consumer(
        builder: (context, ref, child) {
          // Use actual transactions provider
          final transactionsState = ref.watch(transactionsProvider);

          debugPrint(
            'UI rebuild: status=${transactionsState.status}, isLoadingMore=${transactionsState.status == ApiStatus.moreLoading} for tab ${selectedTab.key}',
          );

          // Show shimmer for initial loading
          if (transactionsState.status == ApiStatus.loading) {
            return AppShimmerLoader.list(itemCount: 10, itemHeight: 80.w);
          }

          // Show error state
          if (transactionsState.status == ApiStatus.error) {
            return Center(
              child: Text(
                'Error: ${transactionsState.errorMessage ?? 'Unknown error'}',
              ),
            );
          }

          // Show transactions list for both success and moreLoading states
          final transactions = transactionsState.transactions;
          final isLoadingMore =
              transactionsState.status == ApiStatus.moreLoading;

          // Show empty state if no transactions and not loading more
          if (transactions.isEmpty && !isLoadingMore) {
            return _buildEmptyState(context, user);
          }

          final itemCount = transactions.length + (isLoadingMore ? 1 : 0);
          debugPrint(
            'Item count: ${transactions.length} transactions + ${isLoadingMore ? 1 : 0} loader = $itemCount for tab ${selectedTab.key}',
          );

          return ListView.builder(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            itemCount: itemCount,
            itemBuilder: (context, index) {
              debugPrint(
                'Building item at index $index for tab ${selectedTab.key}',
              );

              // Show bottom loader when loading more data
              if (isLoadingMore && index == itemCount - 1) {
                debugPrint('Showing bottom loader at index $index');
                return AppCircularLoader.medium().paddingSymmetric(
                  vertical: 16,
                );
              }

              // Regular transaction item
              final transaction = transactions[index];
              final isLast = index == transactions.length - 1;
              debugPrint(
                'Showing transaction at index $index: ${transaction.transactionType}',
              );

              return _buildTransactionItem(transaction, user, isLast);
            },
          );
        },
      ),
    );
  }

  Widget _buildFilterIcon() {
    return GestureDetector(
      onTap: () => _showFilterModal(context),
      child:
          (selectedFilter != null
                  ? Assets.icons.filterActive.svg()
                  : Assets.icons.filter.svg())
              .paddingEnd(16.w),
    );
  }

  void _showFilterModal(BuildContext context) {
    TransactionFilterModal.show(
      context,
      currentFilter: selectedFilter,
      onFilterSelected: (filter) {
        setState(() {
          selectedFilter = filter;
        });

        // Reset scroll position when applying filter
        if (_scrollController.hasClients) {
          _scrollController.jumpTo(0);
        }

        // Load transactions with filter (cache will be handled by provider)
        ref
            .read(transactionsProvider.notifier)
            .loadTransactions(selectedTab, filter: filter);
      },
    );
  }

  Widget _buildTransactionItem(
    Transaction transaction,
    dynamic user,
    bool isLast,
  ) {
    return TransactionItem(
      transaction: transaction,
      user: user,
      isLast: isLast,
    );
  }

  Widget _buildEmptyState(BuildContext context, dynamic user) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Icon
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                width: 46.w,
                height: 46.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary.alphaPercent(10),
                ),
              ),
              Assets.icons.coin.svg(
                width: 28.w,
                height: 28.w,
                colorFilter: ColorFilter.mode(
                  AppColors.primary,
                  BlendMode.srcIn,
                ),
              ),
            ],
          ),
          16.h.heightBox,
          // Heading
          Text(
            LocaleKeys.noTransactionsYet.tr(),
            style: AppTextStyles.text16.bold.dark900,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
