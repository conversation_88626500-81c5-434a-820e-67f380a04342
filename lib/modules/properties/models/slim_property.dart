import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/modules/properties/models/property_feature.dart';
import 'package:maisour/modules/properties/models/property_image.dart';
import 'package:maisour/shared/enums/property_status.dart';

part 'slim_property.freezed.dart';
part 'slim_property.g.dart';

@freezed
abstract class SlimProperty with _$SlimProperty {
  const factory SlimProperty({
    required int id,
    required String code,
    required String titleInEnglish,
    required String titleInArabic,
    required String cityInEnglish,
    required String cityInArabic,
    required String countryInEnglish,
    required String countryInArabic,
    required PropertyStatus propertyStatus,
    required double fiveYearExpectedReturn,
    required List<PropertyFeature> propertyFeatures,
    required List<PropertyImage> propertyImages,
    required double rentAmount,
    required double totalPrice,
    required double propertyManagementFee,
    required double totalRentalOtherCost,
    required double expectedAnnualAppreciation,
    required bool limitedVisibility,
    required double funded,
    required double totalInvestment,
    required String mapLocation,
    required DateTime purchaseDate,
  }) = _SlimProperty;

  factory SlimProperty.fromJson(Map<String, dynamic> json) =>
      _$SlimPropertyFromJson(json);
}

@JsonSerializable(includeIfNull: false)
class SlimPropertyPaginator {
  final bool status;
  final int statusCode;
  final String message;
  final List<SlimProperty> data;
  final int page;
  final int perPage;
  final int total;

  const SlimPropertyPaginator({
    required this.status,
    required this.statusCode,
    required this.message,
    required this.data,
    required this.page,
    required this.perPage,
    required this.total,
  });

  bool get hasNext => (page * perPage) < total;
  int get nextPage => page + 1;

  factory SlimPropertyPaginator.fromJson(Map<String, dynamic> json) =>
      _$SlimPropertyPaginatorFromJson(json);
}
