// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'properties_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$PropertiesState {

 ApiStatus get status; List<SlimProperty> get properties; String? get errorMessage; String? get errorKey; bool get hasMoreData; int get currentPage; int get totalPages; SortOption? get currentSortOption;
/// Create a copy of PropertiesState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertiesStateCopyWith<PropertiesState> get copyWith => _$PropertiesStateCopyWithImpl<PropertiesState>(this as PropertiesState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertiesState&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other.properties, properties)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorKey, errorKey) || other.errorKey == errorKey)&&(identical(other.hasMoreData, hasMoreData) || other.hasMoreData == hasMoreData)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.totalPages, totalPages) || other.totalPages == totalPages)&&(identical(other.currentSortOption, currentSortOption) || other.currentSortOption == currentSortOption));
}


@override
int get hashCode => Object.hash(runtimeType,status,const DeepCollectionEquality().hash(properties),errorMessage,errorKey,hasMoreData,currentPage,totalPages,currentSortOption);

@override
String toString() {
  return 'PropertiesState(status: $status, properties: $properties, errorMessage: $errorMessage, errorKey: $errorKey, hasMoreData: $hasMoreData, currentPage: $currentPage, totalPages: $totalPages, currentSortOption: $currentSortOption)';
}


}

/// @nodoc
abstract mixin class $PropertiesStateCopyWith<$Res>  {
  factory $PropertiesStateCopyWith(PropertiesState value, $Res Function(PropertiesState) _then) = _$PropertiesStateCopyWithImpl;
@useResult
$Res call({
 ApiStatus status, List<SlimProperty> properties, String? errorMessage, String? errorKey, bool hasMoreData, int currentPage, int totalPages, SortOption? currentSortOption
});




}
/// @nodoc
class _$PropertiesStateCopyWithImpl<$Res>
    implements $PropertiesStateCopyWith<$Res> {
  _$PropertiesStateCopyWithImpl(this._self, this._then);

  final PropertiesState _self;
  final $Res Function(PropertiesState) _then;

/// Create a copy of PropertiesState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? properties = null,Object? errorMessage = freezed,Object? errorKey = freezed,Object? hasMoreData = null,Object? currentPage = null,Object? totalPages = null,Object? currentSortOption = freezed,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ApiStatus,properties: null == properties ? _self.properties : properties // ignore: cast_nullable_to_non_nullable
as List<SlimProperty>,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorKey: freezed == errorKey ? _self.errorKey : errorKey // ignore: cast_nullable_to_non_nullable
as String?,hasMoreData: null == hasMoreData ? _self.hasMoreData : hasMoreData // ignore: cast_nullable_to_non_nullable
as bool,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,totalPages: null == totalPages ? _self.totalPages : totalPages // ignore: cast_nullable_to_non_nullable
as int,currentSortOption: freezed == currentSortOption ? _self.currentSortOption : currentSortOption // ignore: cast_nullable_to_non_nullable
as SortOption?,
  ));
}

}


/// Adds pattern-matching-related methods to [PropertiesState].
extension PropertiesStatePatterns on PropertiesState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertiesState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertiesState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertiesState value)  $default,){
final _that = this;
switch (_that) {
case _PropertiesState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertiesState value)?  $default,){
final _that = this;
switch (_that) {
case _PropertiesState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ApiStatus status,  List<SlimProperty> properties,  String? errorMessage,  String? errorKey,  bool hasMoreData,  int currentPage,  int totalPages,  SortOption? currentSortOption)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertiesState() when $default != null:
return $default(_that.status,_that.properties,_that.errorMessage,_that.errorKey,_that.hasMoreData,_that.currentPage,_that.totalPages,_that.currentSortOption);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ApiStatus status,  List<SlimProperty> properties,  String? errorMessage,  String? errorKey,  bool hasMoreData,  int currentPage,  int totalPages,  SortOption? currentSortOption)  $default,) {final _that = this;
switch (_that) {
case _PropertiesState():
return $default(_that.status,_that.properties,_that.errorMessage,_that.errorKey,_that.hasMoreData,_that.currentPage,_that.totalPages,_that.currentSortOption);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ApiStatus status,  List<SlimProperty> properties,  String? errorMessage,  String? errorKey,  bool hasMoreData,  int currentPage,  int totalPages,  SortOption? currentSortOption)?  $default,) {final _that = this;
switch (_that) {
case _PropertiesState() when $default != null:
return $default(_that.status,_that.properties,_that.errorMessage,_that.errorKey,_that.hasMoreData,_that.currentPage,_that.totalPages,_that.currentSortOption);case _:
  return null;

}
}

}

/// @nodoc


class _PropertiesState implements PropertiesState {
  const _PropertiesState({this.status = ApiStatus.initial, final  List<SlimProperty> properties = const [], this.errorMessage, this.errorKey, this.hasMoreData = false, this.currentPage = 0, this.totalPages = 0, this.currentSortOption}): _properties = properties;
  

@override@JsonKey() final  ApiStatus status;
 final  List<SlimProperty> _properties;
@override@JsonKey() List<SlimProperty> get properties {
  if (_properties is EqualUnmodifiableListView) return _properties;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_properties);
}

@override final  String? errorMessage;
@override final  String? errorKey;
@override@JsonKey() final  bool hasMoreData;
@override@JsonKey() final  int currentPage;
@override@JsonKey() final  int totalPages;
@override final  SortOption? currentSortOption;

/// Create a copy of PropertiesState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertiesStateCopyWith<_PropertiesState> get copyWith => __$PropertiesStateCopyWithImpl<_PropertiesState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertiesState&&(identical(other.status, status) || other.status == status)&&const DeepCollectionEquality().equals(other._properties, _properties)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.errorKey, errorKey) || other.errorKey == errorKey)&&(identical(other.hasMoreData, hasMoreData) || other.hasMoreData == hasMoreData)&&(identical(other.currentPage, currentPage) || other.currentPage == currentPage)&&(identical(other.totalPages, totalPages) || other.totalPages == totalPages)&&(identical(other.currentSortOption, currentSortOption) || other.currentSortOption == currentSortOption));
}


@override
int get hashCode => Object.hash(runtimeType,status,const DeepCollectionEquality().hash(_properties),errorMessage,errorKey,hasMoreData,currentPage,totalPages,currentSortOption);

@override
String toString() {
  return 'PropertiesState(status: $status, properties: $properties, errorMessage: $errorMessage, errorKey: $errorKey, hasMoreData: $hasMoreData, currentPage: $currentPage, totalPages: $totalPages, currentSortOption: $currentSortOption)';
}


}

/// @nodoc
abstract mixin class _$PropertiesStateCopyWith<$Res> implements $PropertiesStateCopyWith<$Res> {
  factory _$PropertiesStateCopyWith(_PropertiesState value, $Res Function(_PropertiesState) _then) = __$PropertiesStateCopyWithImpl;
@override @useResult
$Res call({
 ApiStatus status, List<SlimProperty> properties, String? errorMessage, String? errorKey, bool hasMoreData, int currentPage, int totalPages, SortOption? currentSortOption
});




}
/// @nodoc
class __$PropertiesStateCopyWithImpl<$Res>
    implements _$PropertiesStateCopyWith<$Res> {
  __$PropertiesStateCopyWithImpl(this._self, this._then);

  final _PropertiesState _self;
  final $Res Function(_PropertiesState) _then;

/// Create a copy of PropertiesState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? properties = null,Object? errorMessage = freezed,Object? errorKey = freezed,Object? hasMoreData = null,Object? currentPage = null,Object? totalPages = null,Object? currentSortOption = freezed,}) {
  return _then(_PropertiesState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as ApiStatus,properties: null == properties ? _self._properties : properties // ignore: cast_nullable_to_non_nullable
as List<SlimProperty>,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,errorKey: freezed == errorKey ? _self.errorKey : errorKey // ignore: cast_nullable_to_non_nullable
as String?,hasMoreData: null == hasMoreData ? _self.hasMoreData : hasMoreData // ignore: cast_nullable_to_non_nullable
as bool,currentPage: null == currentPage ? _self.currentPage : currentPage // ignore: cast_nullable_to_non_nullable
as int,totalPages: null == totalPages ? _self.totalPages : totalPages // ignore: cast_nullable_to_non_nullable
as int,currentSortOption: freezed == currentSortOption ? _self.currentSortOption : currentSortOption // ignore: cast_nullable_to_non_nullable
as SortOption?,
  ));
}


}

// dart format on
