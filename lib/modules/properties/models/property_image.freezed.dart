// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'property_image.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PropertyImage {

 int get id; String get imageUrl; bool get coverImage;
/// Create a copy of PropertyImage
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyImageCopyWith<PropertyImage> get copyWith => _$PropertyImageCopyWithImpl<PropertyImage>(this as PropertyImage, _$identity);

  /// Serializes this PropertyImage to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl,coverImage);

@override
String toString() {
  return 'PropertyImage(id: $id, imageUrl: $imageUrl, coverImage: $coverImage)';
}


}

/// @nodoc
abstract mixin class $PropertyImageCopyWith<$Res>  {
  factory $PropertyImageCopyWith(PropertyImage value, $Res Function(PropertyImage) _then) = _$PropertyImageCopyWithImpl;
@useResult
$Res call({
 int id, String imageUrl, bool coverImage
});




}
/// @nodoc
class _$PropertyImageCopyWithImpl<$Res>
    implements $PropertyImageCopyWith<$Res> {
  _$PropertyImageCopyWithImpl(this._self, this._then);

  final PropertyImage _self;
  final $Res Function(PropertyImage) _then;

/// Create a copy of PropertyImage
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? imageUrl = null,Object? coverImage = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,coverImage: null == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [PropertyImage].
extension PropertyImagePatterns on PropertyImage {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyImage value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyImage() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyImage value)  $default,){
final _that = this;
switch (_that) {
case _PropertyImage():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyImage value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyImage() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String imageUrl,  bool coverImage)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyImage() when $default != null:
return $default(_that.id,_that.imageUrl,_that.coverImage);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String imageUrl,  bool coverImage)  $default,) {final _that = this;
switch (_that) {
case _PropertyImage():
return $default(_that.id,_that.imageUrl,_that.coverImage);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String imageUrl,  bool coverImage)?  $default,) {final _that = this;
switch (_that) {
case _PropertyImage() when $default != null:
return $default(_that.id,_that.imageUrl,_that.coverImage);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyImage implements PropertyImage {
  const _PropertyImage({required this.id, required this.imageUrl, required this.coverImage});
  factory _PropertyImage.fromJson(Map<String, dynamic> json) => _$PropertyImageFromJson(json);

@override final  int id;
@override final  String imageUrl;
@override final  bool coverImage;

/// Create a copy of PropertyImage
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyImageCopyWith<_PropertyImage> get copyWith => __$PropertyImageCopyWithImpl<_PropertyImage>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyImageToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyImage&&(identical(other.id, id) || other.id == id)&&(identical(other.imageUrl, imageUrl) || other.imageUrl == imageUrl)&&(identical(other.coverImage, coverImage) || other.coverImage == coverImage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,imageUrl,coverImage);

@override
String toString() {
  return 'PropertyImage(id: $id, imageUrl: $imageUrl, coverImage: $coverImage)';
}


}

/// @nodoc
abstract mixin class _$PropertyImageCopyWith<$Res> implements $PropertyImageCopyWith<$Res> {
  factory _$PropertyImageCopyWith(_PropertyImage value, $Res Function(_PropertyImage) _then) = __$PropertyImageCopyWithImpl;
@override @useResult
$Res call({
 int id, String imageUrl, bool coverImage
});




}
/// @nodoc
class __$PropertyImageCopyWithImpl<$Res>
    implements _$PropertyImageCopyWith<$Res> {
  __$PropertyImageCopyWithImpl(this._self, this._then);

  final _PropertyImage _self;
  final $Res Function(_PropertyImage) _then;

/// Create a copy of PropertyImage
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? imageUrl = null,Object? coverImage = null,}) {
  return _then(_PropertyImage(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,imageUrl: null == imageUrl ? _self.imageUrl : imageUrl // ignore: cast_nullable_to_non_nullable
as String,coverImage: null == coverImage ? _self.coverImage : coverImage // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
