import 'package:freezed_annotation/freezed_annotation.dart';

part 'property_image.freezed.dart';
part 'property_image.g.dart';

@freezed
abstract class PropertyImage with _$PropertyImage {
  const factory PropertyImage({
    required int id,
    required String imageUrl,
    required bool coverImage,
  }) = _PropertyImage;

  factory PropertyImage.fromJson(Map<String, dynamic> json) =>
      _$PropertyImageFromJson(json);
}
