// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'property_feature.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PropertyFeature {

 int get id; String get nameInEnglish; String get nameInArabic;
/// Create a copy of PropertyFeature
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PropertyFeatureCopyWith<PropertyFeature> get copyWith => _$PropertyFeatureCopyWithImpl<PropertyFeature>(this as PropertyFeature, _$identity);

  /// Serializes this PropertyFeature to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PropertyFeature&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic);

@override
String toString() {
  return 'PropertyFeature(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic)';
}


}

/// @nodoc
abstract mixin class $PropertyFeatureCopyWith<$Res>  {
  factory $PropertyFeatureCopyWith(PropertyFeature value, $Res Function(PropertyFeature) _then) = _$PropertyFeatureCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic
});




}
/// @nodoc
class _$PropertyFeatureCopyWithImpl<$Res>
    implements $PropertyFeatureCopyWith<$Res> {
  _$PropertyFeatureCopyWithImpl(this._self, this._then);

  final PropertyFeature _self;
  final $Res Function(PropertyFeature) _then;

/// Create a copy of PropertyFeature
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [PropertyFeature].
extension PropertyFeaturePatterns on PropertyFeature {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _PropertyFeature value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _PropertyFeature() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _PropertyFeature value)  $default,){
final _that = this;
switch (_that) {
case _PropertyFeature():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _PropertyFeature value)?  $default,){
final _that = this;
switch (_that) {
case _PropertyFeature() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _PropertyFeature() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic)  $default,) {final _that = this;
switch (_that) {
case _PropertyFeature():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic)?  $default,) {final _that = this;
switch (_that) {
case _PropertyFeature() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _PropertyFeature implements PropertyFeature {
  const _PropertyFeature({required this.id, required this.nameInEnglish, required this.nameInArabic});
  factory _PropertyFeature.fromJson(Map<String, dynamic> json) => _$PropertyFeatureFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;

/// Create a copy of PropertyFeature
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PropertyFeatureCopyWith<_PropertyFeature> get copyWith => __$PropertyFeatureCopyWithImpl<_PropertyFeature>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PropertyFeatureToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PropertyFeature&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic);

@override
String toString() {
  return 'PropertyFeature(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic)';
}


}

/// @nodoc
abstract mixin class _$PropertyFeatureCopyWith<$Res> implements $PropertyFeatureCopyWith<$Res> {
  factory _$PropertyFeatureCopyWith(_PropertyFeature value, $Res Function(_PropertyFeature) _then) = __$PropertyFeatureCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic
});




}
/// @nodoc
class __$PropertyFeatureCopyWithImpl<$Res>
    implements _$PropertyFeatureCopyWith<$Res> {
  __$PropertyFeatureCopyWithImpl(this._self, this._then);

  final _PropertyFeature _self;
  final $Res Function(_PropertyFeature) _then;

/// Create a copy of PropertyFeature
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,}) {
  return _then(_PropertyFeature(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
