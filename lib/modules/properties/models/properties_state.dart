import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/modules/properties/widgets/sort_properties_modal.dart';
import 'slim_property.dart';

part 'properties_state.freezed.dart';

/// State class for properties
@freezed
abstract class PropertiesState with _$PropertiesState {
  const factory PropertiesState({
    @Default(ApiStatus.initial) ApiStatus status,
    @Default([]) List<SlimProperty> properties,
    String? errorMessage,
    String? errorKey,
    @Default(false) bool hasMoreData,
    @Default(0) int currentPage,
    @Default(0) int totalPages,
    SortOption? currentSortOption,
  }) = _PropertiesState;
}
