// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slim_property.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SlimProperty {

 int get id; String get code; String get titleInEnglish; String get titleInArabic; String get cityInEnglish; String get cityInArabic; String get countryInEnglish; String get countryInArabic; PropertyStatus get propertyStatus; double get fiveYearExpectedReturn; List<PropertyFeature> get propertyFeatures; List<PropertyImage> get propertyImages; double get rentAmount; double get totalPrice; double get propertyManagementFee; double get totalRentalOtherCost; double get expectedAnnualAppreciation; bool get limitedVisibility; double get funded; double get totalInvestment; String get mapLocation; DateTime get purchaseDate;
/// Create a copy of SlimProperty
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SlimPropertyCopyWith<SlimProperty> get copyWith => _$SlimPropertyCopyWithImpl<SlimProperty>(this as SlimProperty, _$identity);

  /// Serializes this SlimProperty to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SlimProperty&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.propertyStatus, propertyStatus) || other.propertyStatus == propertyStatus)&&(identical(other.fiveYearExpectedReturn, fiveYearExpectedReturn) || other.fiveYearExpectedReturn == fiveYearExpectedReturn)&&const DeepCollectionEquality().equals(other.propertyFeatures, propertyFeatures)&&const DeepCollectionEquality().equals(other.propertyImages, propertyImages)&&(identical(other.rentAmount, rentAmount) || other.rentAmount == rentAmount)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.propertyManagementFee, propertyManagementFee) || other.propertyManagementFee == propertyManagementFee)&&(identical(other.totalRentalOtherCost, totalRentalOtherCost) || other.totalRentalOtherCost == totalRentalOtherCost)&&(identical(other.expectedAnnualAppreciation, expectedAnnualAppreciation) || other.expectedAnnualAppreciation == expectedAnnualAppreciation)&&(identical(other.limitedVisibility, limitedVisibility) || other.limitedVisibility == limitedVisibility)&&(identical(other.funded, funded) || other.funded == funded)&&(identical(other.totalInvestment, totalInvestment) || other.totalInvestment == totalInvestment)&&(identical(other.mapLocation, mapLocation) || other.mapLocation == mapLocation)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,code,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,propertyStatus,fiveYearExpectedReturn,const DeepCollectionEquality().hash(propertyFeatures),const DeepCollectionEquality().hash(propertyImages),rentAmount,totalPrice,propertyManagementFee,totalRentalOtherCost,expectedAnnualAppreciation,limitedVisibility,funded,totalInvestment,mapLocation,purchaseDate]);

@override
String toString() {
  return 'SlimProperty(id: $id, code: $code, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, propertyStatus: $propertyStatus, fiveYearExpectedReturn: $fiveYearExpectedReturn, propertyFeatures: $propertyFeatures, propertyImages: $propertyImages, rentAmount: $rentAmount, totalPrice: $totalPrice, propertyManagementFee: $propertyManagementFee, totalRentalOtherCost: $totalRentalOtherCost, expectedAnnualAppreciation: $expectedAnnualAppreciation, limitedVisibility: $limitedVisibility, funded: $funded, totalInvestment: $totalInvestment, mapLocation: $mapLocation, purchaseDate: $purchaseDate)';
}


}

/// @nodoc
abstract mixin class $SlimPropertyCopyWith<$Res>  {
  factory $SlimPropertyCopyWith(SlimProperty value, $Res Function(SlimProperty) _then) = _$SlimPropertyCopyWithImpl;
@useResult
$Res call({
 int id, String code, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, PropertyStatus propertyStatus, double fiveYearExpectedReturn, List<PropertyFeature> propertyFeatures, List<PropertyImage> propertyImages, double rentAmount, double totalPrice, double propertyManagementFee, double totalRentalOtherCost, double expectedAnnualAppreciation, bool limitedVisibility, double funded, double totalInvestment, String mapLocation, DateTime purchaseDate
});




}
/// @nodoc
class _$SlimPropertyCopyWithImpl<$Res>
    implements $SlimPropertyCopyWith<$Res> {
  _$SlimPropertyCopyWithImpl(this._self, this._then);

  final SlimProperty _self;
  final $Res Function(SlimProperty) _then;

/// Create a copy of SlimProperty
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? code = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? propertyStatus = null,Object? fiveYearExpectedReturn = null,Object? propertyFeatures = null,Object? propertyImages = null,Object? rentAmount = null,Object? totalPrice = null,Object? propertyManagementFee = null,Object? totalRentalOtherCost = null,Object? expectedAnnualAppreciation = null,Object? limitedVisibility = null,Object? funded = null,Object? totalInvestment = null,Object? mapLocation = null,Object? purchaseDate = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,propertyStatus: null == propertyStatus ? _self.propertyStatus : propertyStatus // ignore: cast_nullable_to_non_nullable
as PropertyStatus,fiveYearExpectedReturn: null == fiveYearExpectedReturn ? _self.fiveYearExpectedReturn : fiveYearExpectedReturn // ignore: cast_nullable_to_non_nullable
as double,propertyFeatures: null == propertyFeatures ? _self.propertyFeatures : propertyFeatures // ignore: cast_nullable_to_non_nullable
as List<PropertyFeature>,propertyImages: null == propertyImages ? _self.propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,rentAmount: null == rentAmount ? _self.rentAmount : rentAmount // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,propertyManagementFee: null == propertyManagementFee ? _self.propertyManagementFee : propertyManagementFee // ignore: cast_nullable_to_non_nullable
as double,totalRentalOtherCost: null == totalRentalOtherCost ? _self.totalRentalOtherCost : totalRentalOtherCost // ignore: cast_nullable_to_non_nullable
as double,expectedAnnualAppreciation: null == expectedAnnualAppreciation ? _self.expectedAnnualAppreciation : expectedAnnualAppreciation // ignore: cast_nullable_to_non_nullable
as double,limitedVisibility: null == limitedVisibility ? _self.limitedVisibility : limitedVisibility // ignore: cast_nullable_to_non_nullable
as bool,funded: null == funded ? _self.funded : funded // ignore: cast_nullable_to_non_nullable
as double,totalInvestment: null == totalInvestment ? _self.totalInvestment : totalInvestment // ignore: cast_nullable_to_non_nullable
as double,mapLocation: null == mapLocation ? _self.mapLocation : mapLocation // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: null == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [SlimProperty].
extension SlimPropertyPatterns on SlimProperty {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SlimProperty value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SlimProperty() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SlimProperty value)  $default,){
final _that = this;
switch (_that) {
case _SlimProperty():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SlimProperty value)?  $default,){
final _that = this;
switch (_that) {
case _SlimProperty() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  PropertyStatus propertyStatus,  double fiveYearExpectedReturn,  List<PropertyFeature> propertyFeatures,  List<PropertyImage> propertyImages,  double rentAmount,  double totalPrice,  double propertyManagementFee,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool limitedVisibility,  double funded,  double totalInvestment,  String mapLocation,  DateTime purchaseDate)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SlimProperty() when $default != null:
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.propertyStatus,_that.fiveYearExpectedReturn,_that.propertyFeatures,_that.propertyImages,_that.rentAmount,_that.totalPrice,_that.propertyManagementFee,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.limitedVisibility,_that.funded,_that.totalInvestment,_that.mapLocation,_that.purchaseDate);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  PropertyStatus propertyStatus,  double fiveYearExpectedReturn,  List<PropertyFeature> propertyFeatures,  List<PropertyImage> propertyImages,  double rentAmount,  double totalPrice,  double propertyManagementFee,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool limitedVisibility,  double funded,  double totalInvestment,  String mapLocation,  DateTime purchaseDate)  $default,) {final _that = this;
switch (_that) {
case _SlimProperty():
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.propertyStatus,_that.fiveYearExpectedReturn,_that.propertyFeatures,_that.propertyImages,_that.rentAmount,_that.totalPrice,_that.propertyManagementFee,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.limitedVisibility,_that.funded,_that.totalInvestment,_that.mapLocation,_that.purchaseDate);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String code,  String titleInEnglish,  String titleInArabic,  String cityInEnglish,  String cityInArabic,  String countryInEnglish,  String countryInArabic,  PropertyStatus propertyStatus,  double fiveYearExpectedReturn,  List<PropertyFeature> propertyFeatures,  List<PropertyImage> propertyImages,  double rentAmount,  double totalPrice,  double propertyManagementFee,  double totalRentalOtherCost,  double expectedAnnualAppreciation,  bool limitedVisibility,  double funded,  double totalInvestment,  String mapLocation,  DateTime purchaseDate)?  $default,) {final _that = this;
switch (_that) {
case _SlimProperty() when $default != null:
return $default(_that.id,_that.code,_that.titleInEnglish,_that.titleInArabic,_that.cityInEnglish,_that.cityInArabic,_that.countryInEnglish,_that.countryInArabic,_that.propertyStatus,_that.fiveYearExpectedReturn,_that.propertyFeatures,_that.propertyImages,_that.rentAmount,_that.totalPrice,_that.propertyManagementFee,_that.totalRentalOtherCost,_that.expectedAnnualAppreciation,_that.limitedVisibility,_that.funded,_that.totalInvestment,_that.mapLocation,_that.purchaseDate);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SlimProperty implements SlimProperty {
  const _SlimProperty({required this.id, required this.code, required this.titleInEnglish, required this.titleInArabic, required this.cityInEnglish, required this.cityInArabic, required this.countryInEnglish, required this.countryInArabic, required this.propertyStatus, required this.fiveYearExpectedReturn, required final  List<PropertyFeature> propertyFeatures, required final  List<PropertyImage> propertyImages, required this.rentAmount, required this.totalPrice, required this.propertyManagementFee, required this.totalRentalOtherCost, required this.expectedAnnualAppreciation, required this.limitedVisibility, required this.funded, required this.totalInvestment, required this.mapLocation, required this.purchaseDate}): _propertyFeatures = propertyFeatures,_propertyImages = propertyImages;
  factory _SlimProperty.fromJson(Map<String, dynamic> json) => _$SlimPropertyFromJson(json);

@override final  int id;
@override final  String code;
@override final  String titleInEnglish;
@override final  String titleInArabic;
@override final  String cityInEnglish;
@override final  String cityInArabic;
@override final  String countryInEnglish;
@override final  String countryInArabic;
@override final  PropertyStatus propertyStatus;
@override final  double fiveYearExpectedReturn;
 final  List<PropertyFeature> _propertyFeatures;
@override List<PropertyFeature> get propertyFeatures {
  if (_propertyFeatures is EqualUnmodifiableListView) return _propertyFeatures;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyFeatures);
}

 final  List<PropertyImage> _propertyImages;
@override List<PropertyImage> get propertyImages {
  if (_propertyImages is EqualUnmodifiableListView) return _propertyImages;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_propertyImages);
}

@override final  double rentAmount;
@override final  double totalPrice;
@override final  double propertyManagementFee;
@override final  double totalRentalOtherCost;
@override final  double expectedAnnualAppreciation;
@override final  bool limitedVisibility;
@override final  double funded;
@override final  double totalInvestment;
@override final  String mapLocation;
@override final  DateTime purchaseDate;

/// Create a copy of SlimProperty
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SlimPropertyCopyWith<_SlimProperty> get copyWith => __$SlimPropertyCopyWithImpl<_SlimProperty>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SlimPropertyToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SlimProperty&&(identical(other.id, id) || other.id == id)&&(identical(other.code, code) || other.code == code)&&(identical(other.titleInEnglish, titleInEnglish) || other.titleInEnglish == titleInEnglish)&&(identical(other.titleInArabic, titleInArabic) || other.titleInArabic == titleInArabic)&&(identical(other.cityInEnglish, cityInEnglish) || other.cityInEnglish == cityInEnglish)&&(identical(other.cityInArabic, cityInArabic) || other.cityInArabic == cityInArabic)&&(identical(other.countryInEnglish, countryInEnglish) || other.countryInEnglish == countryInEnglish)&&(identical(other.countryInArabic, countryInArabic) || other.countryInArabic == countryInArabic)&&(identical(other.propertyStatus, propertyStatus) || other.propertyStatus == propertyStatus)&&(identical(other.fiveYearExpectedReturn, fiveYearExpectedReturn) || other.fiveYearExpectedReturn == fiveYearExpectedReturn)&&const DeepCollectionEquality().equals(other._propertyFeatures, _propertyFeatures)&&const DeepCollectionEquality().equals(other._propertyImages, _propertyImages)&&(identical(other.rentAmount, rentAmount) || other.rentAmount == rentAmount)&&(identical(other.totalPrice, totalPrice) || other.totalPrice == totalPrice)&&(identical(other.propertyManagementFee, propertyManagementFee) || other.propertyManagementFee == propertyManagementFee)&&(identical(other.totalRentalOtherCost, totalRentalOtherCost) || other.totalRentalOtherCost == totalRentalOtherCost)&&(identical(other.expectedAnnualAppreciation, expectedAnnualAppreciation) || other.expectedAnnualAppreciation == expectedAnnualAppreciation)&&(identical(other.limitedVisibility, limitedVisibility) || other.limitedVisibility == limitedVisibility)&&(identical(other.funded, funded) || other.funded == funded)&&(identical(other.totalInvestment, totalInvestment) || other.totalInvestment == totalInvestment)&&(identical(other.mapLocation, mapLocation) || other.mapLocation == mapLocation)&&(identical(other.purchaseDate, purchaseDate) || other.purchaseDate == purchaseDate));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,code,titleInEnglish,titleInArabic,cityInEnglish,cityInArabic,countryInEnglish,countryInArabic,propertyStatus,fiveYearExpectedReturn,const DeepCollectionEquality().hash(_propertyFeatures),const DeepCollectionEquality().hash(_propertyImages),rentAmount,totalPrice,propertyManagementFee,totalRentalOtherCost,expectedAnnualAppreciation,limitedVisibility,funded,totalInvestment,mapLocation,purchaseDate]);

@override
String toString() {
  return 'SlimProperty(id: $id, code: $code, titleInEnglish: $titleInEnglish, titleInArabic: $titleInArabic, cityInEnglish: $cityInEnglish, cityInArabic: $cityInArabic, countryInEnglish: $countryInEnglish, countryInArabic: $countryInArabic, propertyStatus: $propertyStatus, fiveYearExpectedReturn: $fiveYearExpectedReturn, propertyFeatures: $propertyFeatures, propertyImages: $propertyImages, rentAmount: $rentAmount, totalPrice: $totalPrice, propertyManagementFee: $propertyManagementFee, totalRentalOtherCost: $totalRentalOtherCost, expectedAnnualAppreciation: $expectedAnnualAppreciation, limitedVisibility: $limitedVisibility, funded: $funded, totalInvestment: $totalInvestment, mapLocation: $mapLocation, purchaseDate: $purchaseDate)';
}


}

/// @nodoc
abstract mixin class _$SlimPropertyCopyWith<$Res> implements $SlimPropertyCopyWith<$Res> {
  factory _$SlimPropertyCopyWith(_SlimProperty value, $Res Function(_SlimProperty) _then) = __$SlimPropertyCopyWithImpl;
@override @useResult
$Res call({
 int id, String code, String titleInEnglish, String titleInArabic, String cityInEnglish, String cityInArabic, String countryInEnglish, String countryInArabic, PropertyStatus propertyStatus, double fiveYearExpectedReturn, List<PropertyFeature> propertyFeatures, List<PropertyImage> propertyImages, double rentAmount, double totalPrice, double propertyManagementFee, double totalRentalOtherCost, double expectedAnnualAppreciation, bool limitedVisibility, double funded, double totalInvestment, String mapLocation, DateTime purchaseDate
});




}
/// @nodoc
class __$SlimPropertyCopyWithImpl<$Res>
    implements _$SlimPropertyCopyWith<$Res> {
  __$SlimPropertyCopyWithImpl(this._self, this._then);

  final _SlimProperty _self;
  final $Res Function(_SlimProperty) _then;

/// Create a copy of SlimProperty
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? code = null,Object? titleInEnglish = null,Object? titleInArabic = null,Object? cityInEnglish = null,Object? cityInArabic = null,Object? countryInEnglish = null,Object? countryInArabic = null,Object? propertyStatus = null,Object? fiveYearExpectedReturn = null,Object? propertyFeatures = null,Object? propertyImages = null,Object? rentAmount = null,Object? totalPrice = null,Object? propertyManagementFee = null,Object? totalRentalOtherCost = null,Object? expectedAnnualAppreciation = null,Object? limitedVisibility = null,Object? funded = null,Object? totalInvestment = null,Object? mapLocation = null,Object? purchaseDate = null,}) {
  return _then(_SlimProperty(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,code: null == code ? _self.code : code // ignore: cast_nullable_to_non_nullable
as String,titleInEnglish: null == titleInEnglish ? _self.titleInEnglish : titleInEnglish // ignore: cast_nullable_to_non_nullable
as String,titleInArabic: null == titleInArabic ? _self.titleInArabic : titleInArabic // ignore: cast_nullable_to_non_nullable
as String,cityInEnglish: null == cityInEnglish ? _self.cityInEnglish : cityInEnglish // ignore: cast_nullable_to_non_nullable
as String,cityInArabic: null == cityInArabic ? _self.cityInArabic : cityInArabic // ignore: cast_nullable_to_non_nullable
as String,countryInEnglish: null == countryInEnglish ? _self.countryInEnglish : countryInEnglish // ignore: cast_nullable_to_non_nullable
as String,countryInArabic: null == countryInArabic ? _self.countryInArabic : countryInArabic // ignore: cast_nullable_to_non_nullable
as String,propertyStatus: null == propertyStatus ? _self.propertyStatus : propertyStatus // ignore: cast_nullable_to_non_nullable
as PropertyStatus,fiveYearExpectedReturn: null == fiveYearExpectedReturn ? _self.fiveYearExpectedReturn : fiveYearExpectedReturn // ignore: cast_nullable_to_non_nullable
as double,propertyFeatures: null == propertyFeatures ? _self._propertyFeatures : propertyFeatures // ignore: cast_nullable_to_non_nullable
as List<PropertyFeature>,propertyImages: null == propertyImages ? _self._propertyImages : propertyImages // ignore: cast_nullable_to_non_nullable
as List<PropertyImage>,rentAmount: null == rentAmount ? _self.rentAmount : rentAmount // ignore: cast_nullable_to_non_nullable
as double,totalPrice: null == totalPrice ? _self.totalPrice : totalPrice // ignore: cast_nullable_to_non_nullable
as double,propertyManagementFee: null == propertyManagementFee ? _self.propertyManagementFee : propertyManagementFee // ignore: cast_nullable_to_non_nullable
as double,totalRentalOtherCost: null == totalRentalOtherCost ? _self.totalRentalOtherCost : totalRentalOtherCost // ignore: cast_nullable_to_non_nullable
as double,expectedAnnualAppreciation: null == expectedAnnualAppreciation ? _self.expectedAnnualAppreciation : expectedAnnualAppreciation // ignore: cast_nullable_to_non_nullable
as double,limitedVisibility: null == limitedVisibility ? _self.limitedVisibility : limitedVisibility // ignore: cast_nullable_to_non_nullable
as bool,funded: null == funded ? _self.funded : funded // ignore: cast_nullable_to_non_nullable
as double,totalInvestment: null == totalInvestment ? _self.totalInvestment : totalInvestment // ignore: cast_nullable_to_non_nullable
as double,mapLocation: null == mapLocation ? _self.mapLocation : mapLocation // ignore: cast_nullable_to_non_nullable
as String,purchaseDate: null == purchaseDate ? _self.purchaseDate : purchaseDate // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
