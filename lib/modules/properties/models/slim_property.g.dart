// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'slim_property.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SlimPropertyPaginator _$SlimPropertyPaginatorFromJson(
  Map<String, dynamic> json,
) => SlimPropertyPaginator(
  status: json['status'] as bool,
  statusCode: (json['statusCode'] as num).toInt(),
  message: json['message'] as String,
  data: (json['data'] as List<dynamic>)
      .map((e) => SlimProperty.fromJson(e as Map<String, dynamic>))
      .toList(),
  page: (json['page'] as num).toInt(),
  perPage: (json['perPage'] as num).toInt(),
  total: (json['total'] as num).toInt(),
);

Map<String, dynamic> _$SlimPropertyPaginatorToJson(
  SlimPropertyPaginator instance,
) => <String, dynamic>{
  'status': instance.status,
  'statusCode': instance.statusCode,
  'message': instance.message,
  'data': instance.data,
  'page': instance.page,
  'perPage': instance.perPage,
  'total': instance.total,
};

_SlimProperty _$SlimPropertyFromJson(
  Map<String, dynamic> json,
) => _SlimProperty(
  id: (json['id'] as num).toInt(),
  code: json['code'] as String,
  titleInEnglish: json['titleInEnglish'] as String,
  titleInArabic: json['titleInArabic'] as String,
  cityInEnglish: json['cityInEnglish'] as String,
  cityInArabic: json['cityInArabic'] as String,
  countryInEnglish: json['countryInEnglish'] as String,
  countryInArabic: json['countryInArabic'] as String,
  propertyStatus: $enumDecode(_$PropertyStatusEnumMap, json['propertyStatus']),
  fiveYearExpectedReturn: (json['fiveYearExpectedReturn'] as num).toDouble(),
  propertyFeatures: (json['propertyFeatures'] as List<dynamic>)
      .map((e) => PropertyFeature.fromJson(e as Map<String, dynamic>))
      .toList(),
  propertyImages: (json['propertyImages'] as List<dynamic>)
      .map((e) => PropertyImage.fromJson(e as Map<String, dynamic>))
      .toList(),
  rentAmount: (json['rentAmount'] as num).toDouble(),
  totalPrice: (json['totalPrice'] as num).toDouble(),
  propertyManagementFee: (json['propertyManagementFee'] as num).toDouble(),
  totalRentalOtherCost: (json['totalRentalOtherCost'] as num).toDouble(),
  expectedAnnualAppreciation: (json['expectedAnnualAppreciation'] as num)
      .toDouble(),
  limitedVisibility: json['limitedVisibility'] as bool,
  funded: (json['funded'] as num).toDouble(),
  totalInvestment: (json['totalInvestment'] as num).toDouble(),
  mapLocation: json['mapLocation'] as String,
  purchaseDate: DateTime.parse(json['purchaseDate'] as String),
);

Map<String, dynamic> _$SlimPropertyToJson(_SlimProperty instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'titleInEnglish': instance.titleInEnglish,
      'titleInArabic': instance.titleInArabic,
      'cityInEnglish': instance.cityInEnglish,
      'cityInArabic': instance.cityInArabic,
      'countryInEnglish': instance.countryInEnglish,
      'countryInArabic': instance.countryInArabic,
      'propertyStatus': _$PropertyStatusEnumMap[instance.propertyStatus]!,
      'fiveYearExpectedReturn': instance.fiveYearExpectedReturn,
      'propertyFeatures': instance.propertyFeatures,
      'propertyImages': instance.propertyImages,
      'rentAmount': instance.rentAmount,
      'totalPrice': instance.totalPrice,
      'propertyManagementFee': instance.propertyManagementFee,
      'totalRentalOtherCost': instance.totalRentalOtherCost,
      'expectedAnnualAppreciation': instance.expectedAnnualAppreciation,
      'limitedVisibility': instance.limitedVisibility,
      'funded': instance.funded,
      'totalInvestment': instance.totalInvestment,
      'mapLocation': instance.mapLocation,
      'purchaseDate': instance.purchaseDate.toIso8601String(),
    };

const _$PropertyStatusEnumMap = {
  PropertyStatus.comingSoon: 'ComingSoon',
  PropertyStatus.cancelled: 'Cancelled',
  PropertyStatus.sold: 'Sold',
  PropertyStatus.funded: 'Funded',
  PropertyStatus.request: 'Request',
  PropertyStatus.live: 'Live',
};
