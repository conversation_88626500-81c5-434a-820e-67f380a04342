import 'package:freezed_annotation/freezed_annotation.dart';

part 'property_feature.freezed.dart';
part 'property_feature.g.dart';

@freezed
abstract class PropertyFeature with _$PropertyFeature {
  const factory PropertyFeature({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
  }) = _PropertyFeature;

  factory PropertyFeature.fromJson(Map<String, dynamic> json) =>
      _$PropertyFeatureFromJson(json);
}
