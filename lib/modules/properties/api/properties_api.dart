import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import '../models/slim_property.dart';

part 'properties_api.g.dart';

@RestApi()
abstract class PropertiesApi {
  factory PropertiesApi(Dio dio, {String? baseUrl}) = _PropertiesApi;

  @GET('${ApiEndpoints.propertiesList}/{propertyType}')
  Future<SlimPropertyPaginator> getPropertiesList(
    @Path('propertyType') String propertyType,
    @Query('page') int page,
    @Query('pageSize') int pageSize,
    @Query('sortBy') String? sortBy,
    @Query('sortDirection') String? sortDirection,
  );
}
