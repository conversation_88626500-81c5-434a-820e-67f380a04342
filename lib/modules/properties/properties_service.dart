import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/properties/api/properties_api.dart';
import 'package:maisour/modules/properties/providers/properties_api_provider.dart';
import 'models/slim_property.dart';

/// Provider for PropertiesService
final propertiesServiceProvider = Provider.autoDispose<PropertiesService>((
  ref,
) {
  final propertiesApi = ref.watch(propertiesApiProvider);
  return PropertiesService(propertiesApi);
}, name: 'propertiesServiceProvider');

class PropertiesService {
  final PropertiesApi _propertiesApi;

  PropertiesService(this._propertiesApi);

  /// Get properties list by property type
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<SlimPropertyPaginator> getPropertiesList({
    required String propertyType,
    required int page,
    required int pageSize,
    String? sortBy,
    String? sortDirection,
  }) async {
    return await _propertiesApi.getPropertiesList(
      propertyType,
      page,
      pageSize,
      sortBy,
      sortDirection,
    );
  }
}
