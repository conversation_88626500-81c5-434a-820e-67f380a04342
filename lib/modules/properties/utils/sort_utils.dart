import '../widgets/sort_properties_modal.dart';

class SortUtils {
  /// Converts SortOption to API parameters
  /// Returns a map with 'sortBy' and 'sortDirection' keys
  static Map<String, String> getSortParameters(SortOption? sortOption) {
    switch (sortOption) {
      case SortOption.propertyOldestToLatest:
        return {'sortBy': 'purchaseDate', 'sortDirection': 'asc'};
      case SortOption.fundingStatusHighestToLowest:
        return {'sortBy': 'funded', 'sortDirection': 'desc'};
      default:
        // Default sorting
        return {'sortBy': 'purchaseDate', 'sortDirection': 'desc'};
    }
  }
}
