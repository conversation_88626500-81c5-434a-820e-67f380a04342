import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/properties/properties_service.dart';
import 'package:maisour/modules/properties/widgets/property_tab.dart';
import 'package:maisour/modules/properties/utils/sort_utils.dart';
import 'package:maisour/modules/properties/widgets/sort_properties_modal.dart';
import '../models/slim_property.dart';
import '../models/properties_state.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:dio/dio.dart';

class PropertiesNotifier extends StateNotifier<PropertiesState>
    with DioExceptionMapper {
  final PropertiesService _propertiesService;

  // Cache management
  final Map<String, List<SlimProperty>> _cachedProperties = {};
  final Map<String, int> _currentPages = {};
  final Map<String, int> _totalPages = {};
  final Map<String, bool> _hasMoreData = {};

  PropertiesNotifier(this._propertiesService) : super(const PropertiesState());

  /// Check if currently loading more data for a specific tab
  bool isLoadingMore(PropertyTab tab) {
    return state.status == ApiStatus.moreLoading;
  }

  /// Check if data exists in cache for a tab
  bool _hasCachedData(PropertyTab tab) {
    final propertyType = tab.key;
    return _cachedProperties.containsKey(propertyType) &&
        _cachedProperties[propertyType]!.isNotEmpty;
  }

  /// Load properties with smart caching
  Future<void> loadProperties(
    PropertyTab tab, {
    SortOption? sortOption,
    bool forceRefresh = false,
  }) async {
    final propertyType = tab.key;
    final needsPagination = tab.key != 'Live' && tab.key != 'ComingSoon';
    final effectiveSortOption = sortOption ?? state.currentSortOption;

    debugPrint(
      'loadProperties called: tab=$propertyType, sortOption=$effectiveSortOption, forceRefresh=$forceRefresh',
    );
    debugPrint('Has cached data: ${_hasCachedData(tab)}');

    // Check if we can use cached data (only if no sort option provided and not force refresh)
    if (!forceRefresh && sortOption == null && _hasCachedData(tab)) {
      debugPrint('Using cached data for $propertyType');
      final cachedProperties = _cachedProperties[propertyType]!;
      state = state.copyWith(
        status: ApiStatus.success,
        properties: cachedProperties,
        hasMoreData: _hasMoreData[propertyType] ?? false,
        currentPage: _currentPages[propertyType] ?? 0,
        totalPages: _totalPages[propertyType] ?? 0,
      );
      return;
    }

    // Clear cache if force refresh or sort option provided
    if (forceRefresh || sortOption != null) {
      debugPrint('Clearing cache for $propertyType');
      _clearTabCache(propertyType);
    }

    // Load from API
    await _loadFromApi(tab, effectiveSortOption, needsPagination);
  }

  /// Load data from API
  Future<void> _loadFromApi(
    PropertyTab tab,
    SortOption? sortOption,
    bool needsPagination,
  ) async {
    final propertyType = tab.key;

    try {
      state = state.copyWith(status: ApiStatus.loading);

      final page = needsPagination ? 1 : 0;
      final pageSize = needsPagination ? 5 : 0;
      final sortParams = SortUtils.getSortParameters(sortOption);

      debugPrint('Making API call: page=$page, pageSize=$pageSize');

      final response = await _propertiesService.getPropertiesList(
        propertyType: propertyType,
        page: page,
        pageSize: pageSize,
        sortBy: sortParams['sortBy']!,
        sortDirection: sortParams['sortDirection']!,
      );

      if (response.status) {
        final newProperties = response.data;
        debugPrint(
          'API returned ${newProperties.length} properties for $propertyType',
        );

        // Cache the data
        _cachedProperties[propertyType] = newProperties;

        if (needsPagination) {
          _currentPages[propertyType] = page;
          _totalPages[propertyType] = (response.total / pageSize).ceil();
          _hasMoreData[propertyType] = response.hasNext;
        } else {
          _currentPages[propertyType] = 0;
          _totalPages[propertyType] = 1;
          _hasMoreData[propertyType] = false;
        }

        state = state.copyWith(
          status: ApiStatus.success,
          properties: newProperties,
          hasMoreData: _hasMoreData[propertyType] ?? false,
          currentPage: _currentPages[propertyType] ?? 0,
          totalPages: _totalPages[propertyType] ?? 0,
        );
      } else {
        state = state.copyWith(
          status: ApiStatus.error,
          errorMessage: response.message,
        );
      }
    } catch (error, stackTrace) {
      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        state = state.copyWith(
          status: ApiStatus.error,
          errorMessage: failure.message,
          errorKey: failure.errorKey,
        );
      } else {
        state = state.copyWith(
          status: ApiStatus.error,
          errorMessage: error.toString(),
        );
      }
    }
  }

  /// Load more properties for pagination
  Future<void> loadMoreProperties(PropertyTab tab) async {
    final propertyType = tab.key;
    final needsPagination = tab.key != 'Live' && tab.key != 'ComingSoon';

    // Prevent multiple simultaneous calls
    if (!needsPagination ||
        !(_hasMoreData[propertyType] ?? false) ||
        state.status == ApiStatus.moreLoading) {
      debugPrint(
        'LoadMore blocked: needsPagination=$needsPagination, hasMore=${_hasMoreData[propertyType]}, isLoading=${state.status == ApiStatus.moreLoading}',
      );
      return;
    }

    // Set loading more state
    state = state.copyWith(status: ApiStatus.moreLoading);
    debugPrint('LoadMore started for $propertyType');

    final nextPage = (_currentPages[propertyType] ?? 1) + 1;
    final pageSize = 5;
    final sortParams = SortUtils.getSortParameters(state.currentSortOption);

    try {
      final response = await _propertiesService.getPropertiesList(
        propertyType: propertyType,
        page: nextPage,
        pageSize: pageSize,
        sortBy: sortParams['sortBy']!,
        sortDirection: sortParams['sortDirection']!,
      );

      if (response.status) {
        final newProperties = response.data;
        final existingProperties = _cachedProperties[propertyType] ?? [];

        // Append new data to cache
        _cachedProperties[propertyType] = [
          ...existingProperties,
          ...newProperties,
        ];
        _currentPages[propertyType] = nextPage;
        _hasMoreData[propertyType] = response.hasNext;

        state = state.copyWith(
          status: ApiStatus.success,
          properties: _cachedProperties[propertyType]!,
          hasMoreData: _hasMoreData[propertyType] ?? false,
          currentPage: _currentPages[propertyType] ?? 0,
          totalPages: _totalPages[propertyType] ?? 0,
        );
        debugPrint(
          'LoadMore completed for $propertyType: added ${newProperties.length} items',
        );
      } else {
        // Handle API error but don't change status to error for pagination
        debugPrint('API error in loadMore: ${response.message}');
        state = state.copyWith(status: ApiStatus.success);
      }
    } catch (error, stackTrace) {
      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        debugPrint('Error loading more properties: ${failure.message}');
      } else {
        debugPrint('Error loading more properties: ${error.toString()}');
      }
      // Don't change status to error for pagination failures, just keep current state
      state = state.copyWith(status: ApiStatus.success);
    }
  }

  /// Clear cache for a specific tab
  void _clearTabCache(String propertyType) {
    _cachedProperties[propertyType] = [];
    _currentPages[propertyType] = 0;
    _totalPages[propertyType] = 0;
    _hasMoreData[propertyType] = false;
  }

  /// Clear all cache
  void clearCache() {
    _cachedProperties.clear();
    _currentPages.clear();
    _totalPages.clear();
    _hasMoreData.clear();
  }

  /// Refresh current tab (for pull-to-refresh)
  Future<void> refreshCurrentTab(PropertyTab tab) async {
    debugPrint('Refreshing current tab: ${tab.key}');
    await loadProperties(tab, forceRefresh: true);
  }

  bool hasMoreData(PropertyTab tab) {
    return _hasMoreData[tab.key] ?? false;
  }

  /// Apply sorting and reload properties
  Future<void> applySorting(PropertyTab tab, SortOption? sortOption) async {
    debugPrint('applySorting called: tab=${tab.key}, sortOption=$sortOption');

    // Clear all cache to reload with new sorting
    clearCache();

    // Update state with new sort option
    state = state.copyWith(currentSortOption: sortOption);
    debugPrint('Updated currentSortOption to: $sortOption');

    // Reload properties with the new sort option
    await loadProperties(tab, sortOption: sortOption);
  }
}

final propertiesProvider =
    AutoDisposeStateNotifierProvider<PropertiesNotifier, PropertiesState>((
      ref,
    ) {
      final propertiesService = ref.watch(propertiesServiceProvider);
      return PropertiesNotifier(propertiesService);
    }, name: 'propertiesProvider');
