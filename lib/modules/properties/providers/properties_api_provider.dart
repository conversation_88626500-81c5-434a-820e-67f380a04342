import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/properties/api/properties_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for properties module
final propertiesApiProvider = Provider.autoDispose<PropertiesApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return PropertiesApi(dio);
}, name: 'propertiesApiProvider');
