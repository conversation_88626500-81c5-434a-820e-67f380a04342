import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FirstTimeOnboardingBottomsheet extends StatelessWidget {
  const FirstTimeOnboardingBottomsheet({super.key});

  static Future<void> show(BuildContext context) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const FirstTimeOnboardingBottomsheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        24.h.heightBox,
        Text(
          LocaleKeys.verifyYourAccount.tr(),
          style: AppTextStyles.text18.bold.dark900,
          textAlign: TextAlign.center,
        ),
        8.h.heightBox,
        Text(
          LocaleKeys.verifyYourAccountDesc.tr(),
          style: AppTextStyles.text14.dark300,
          textAlign: TextAlign.center,
        ),
        20.h.heightBox,

        AppButton(
          text: LocaleKeys.verifyAccount.tr(),
          onPressed: () {
            Navigator.of(context).pop();
            GoRouter.of(context).pushNamed(RouteName.onboarding.name);
          },
        ),
        8.h.heightBox,
        AppButton(
          type: ButtonType.text,
          text: LocaleKeys.doItLater.tr(),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        8.h.heightBox,
      ],
    ).paddingHorizontal(16.w);
  }
}
