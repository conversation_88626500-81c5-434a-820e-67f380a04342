import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class EmptyProperties extends StatelessWidget {
  const EmptyProperties({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.noProperty.image(width: 250.w, height: 250.w),

            16.h.heightBox,

            // Main heading
            Text(
              LocaleKeys.noPropertiesAvailable.tr(),
              style: AppTextStyles.text20.bold.dark900,
              textAlign: TextAlign.center,
            ),
            8.w.heightBox,

            // Sub-text
            Text(
              LocaleKeys.noPropertiesAvailableDesc.tr(),
              style: AppTextStyles.text14.medium.dark300,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
