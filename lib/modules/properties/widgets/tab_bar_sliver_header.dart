import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'chip_tab_bar_header.dart';
import 'property_tab.dart';

class TabBarSliverHeader extends StatefulWidget {
  final void Function(PropertyTab selectedTab, int index) onTabChanged;
  final int selectedIndex;
  final Function() onFilterPressed;
  final bool isFilterApplied;

  const TabBarSliverHeader({
    super.key,
    required this.onTabChanged,
    required this.selectedIndex,
    required this.onFilterPressed,
    this.isFilterApplied = false,
  });

  @override
  State<TabBarSliverHeader> createState() => _TabBarSliverHeaderState();
}

class _TabBarSliverHeaderState extends State<TabBarSliverHeader> {
  final List<PropertyTab> tabs = [
    PropertyTab(key: 'Live', title: LocaleKeys.live.tr()),
    PropertyTab(key: 'ComingSoon', title: LocaleKeys.comingSoon.tr()),
    PropertyTab(key: 'Funded', title: LocaleKeys.funded.tr()),
    PropertyTab(key: 'Sold', title: LocaleKeys.sold.tr()),
  ];

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
      pinned: true,
      delegate: ChipTabBarHeader(
        tabs: tabs.map((tab) => tab.title).toList(),
        selectedIndex: widget.selectedIndex,
        onTabSelected: (index) {
          widget.onTabChanged(tabs[index], index);
        },
        onFilterPressed: widget.onFilterPressed,
        isFilterApplied: widget.isFilterApplied,
      ),
    );
  }
}
