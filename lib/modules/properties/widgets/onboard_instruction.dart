import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class OnboardInstruction extends StatelessWidget {
  const OnboardInstruction({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 40.w,
          width: 40.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.dark.shade900,
          ),
          child: Icon(Icons.lock_outline, color: AppColors.white),
        ),
        8.w.heightBox,
        Text(
          LocaleKeys.completeYourOnboarding.tr(),
          style: AppTextStyles.text16.bold.dark900,
        ),
        4.w.heightBox,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              LocaleKeys.toViewMoreDetailsPlease.tr(),
              style: AppTextStyles.text14.medium.dark300,
            ),
            4.w.widthBox,
            Text(
              LocaleKeys.contactUs.tr().toLowerCase(),
              style: AppTextStyles.text14.medium.primary.underline(),
            ).onTap(() {
              GoRouter.of(context).pushNamed(RouteName.getHelp.name);
            }),
          ],
        ),
      ],
    );
  }
}
