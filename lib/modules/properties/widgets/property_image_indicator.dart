import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';

class PropertyImageIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;

  const PropertyImageIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalPages, (index) => _buildIndicator(index)),
    );
  }

  Widget _buildIndicator(int index) {
    final isActive = currentPage == index;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 3.w),
      width: isActive ? 18.w : 6.w,
      height: 6.w,
      decoration: BoxDecoration(
        color: isActive ? AppColors.white : AppColors.white.alphaPercent(60),
        borderRadius: BorderRadius.circular(4.r),
      ),
    );
  }
}
