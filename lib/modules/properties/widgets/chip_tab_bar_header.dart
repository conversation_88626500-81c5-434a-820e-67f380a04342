import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class ChipTabBarHeader extends SliverPersistentHeaderDelegate {
  final List<String> tabs;
  final int selectedIndex;
  final ValueChanged<int> onTabSelected;
  final VoidCallback onFilterPressed;
  final bool isFilterApplied;

  ChipTabBarHeader({
    required this.tabs,
    required this.selectedIndex,
    required this.onTabSelected,
    required this.onFilterPressed,
    this.isFilterApplied = false,
  });

  @override
  double get minExtent => 56.w;
  @override
  double get maxExtent => 56.w;

  @override
  Widget build(context, shrinkOffset, overlapsContent) {
    return Container(
      color: AppColors.white,
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsetsGeometry.directional(start: 8.w),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: tabs.length,
                itemBuilder: (_, index) {
                  final isSelected = index == selectedIndex;
                  return Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.primary.alphaPercent(10)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    padding: EdgeInsetsGeometry.symmetric(
                      horizontal: 14.w,
                      vertical: 8.w,
                    ),
                    margin: EdgeInsetsGeometry.directional(
                      start: 8.w,
                      top: 10.w,
                      bottom: 10.w,
                    ),
                    child: Center(
                      child: Text(
                        tabs[index],
                        style: isSelected
                            ? AppTextStyles.text12.bold.primary
                            : AppTextStyles.text12.medium.gray600,
                      ),
                    ),
                  ).onTap(() {
                    onTabSelected(index);
                  });
                },
              ),
            ),
          ),
          isFilterApplied
              ? Assets.icons.sortingActive
                    .svg(width: 24.w, height: 24.w)
                    .onTap(() => onFilterPressed())
              : Assets.icons.sort
                    .svg(width: 24.w, height: 24.w)
                    .onTap(() => onFilterPressed()),
          14.w.widthBox,
        ],
      ),
    );
  }

  @override
  bool shouldRebuild(covariant ChipTabBarHeader oldDelegate) {
    return oldDelegate.selectedIndex != selectedIndex ||
        oldDelegate.tabs != tabs ||
        oldDelegate.isFilterApplied != isFilterApplied;
  }
}
