import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/properties/models/property_feature.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class PropertyFeatures extends StatelessWidget {
  final List<PropertyFeature> propertyFeatures;
  final String languageCode;
  const PropertyFeatures({
    super.key,
    required this.propertyFeatures,
    required this.languageCode,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          8.w.widthBox,
          ...propertyFeatures.map(
            (feature) => Container(
              margin: EdgeInsetsDirectional.only(end: 8.w),
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.w),
              decoration: BoxDecoration(
                color: AppColors.gray.alphaPercent(10),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                feature.getName(languageCode),
                style: AppTextStyles.text12.gray600,
              ),
            ),
          ),
          8.w.widthBox,
        ],
      ),
    );
  }
}
