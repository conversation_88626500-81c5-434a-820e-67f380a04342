import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

enum SortOption { propertyOldestToLatest, fundingStatusHighestToLowest }

class SortPropertiesModal extends StatefulWidget {
  final SortOption? currentSortOption;
  final Function(SortOption? sortOption) onSortSelected;

  const SortPropertiesModal({
    super.key,
    this.currentSortOption,
    required this.onSortSelected,
  });

  static Future<void> show(
    BuildContext context, {
    SortOption? currentSortOption,
    required Function(SortOption? sortOption) onSortSelected,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SortPropertiesModal(
        currentSortOption: currentSortOption,
        onSortSelected: onSortSelected,
      ),
    );
  }

  @override
  State<SortPropertiesModal> createState() => _SortPropertiesModalState();
}

class _SortPropertiesModalState extends State<SortPropertiesModal> {
  SortOption? selectedSortOption;

  @override
  void initState() {
    super.initState();
    selectedSortOption = widget.currentSortOption;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        16.h.heightBox,

        // Title
        Text(
          LocaleKeys.sortBy.tr(),
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ),
        20.h.heightBox,

        // Sort options
        _buildSortOption(
          SortOption.propertyOldestToLatest,
          LocaleKeys.propertyOldestToLatest.tr(),
        ),
        12.h.heightBox,
        _buildSortOption(
          SortOption.fundingStatusHighestToLowest,
          LocaleKeys.fundingStatusHighestToLowest.tr(),
        ),

        16.h.heightBox,
      ],
    ).paddingHorizontal(16.w);
  }

  Widget _buildSortOption(SortOption option, String title) {
    final isSelected = selectedSortOption == option;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: AppTextStyles.text14.medium.dark900),
        if (isSelected)
          Assets.icons.check.svg(
            width: 20.w,
            height: 20.w,
            colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
          ),
      ],
    ).onTap(() {
      if (isSelected) {
        // If same option is selected, unselect it (set to null)
        Navigator.of(context).pop();
        widget.onSortSelected(null);
      } else {
        // If different option is selected, apply the new sorting
        Navigator.of(context).pop();
        widget.onSortSelected(option);
      }
    });
  }
}
