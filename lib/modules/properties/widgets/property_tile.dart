import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/properties/models/slim_property.dart';
import 'package:maisour/modules/properties/widgets/onboard_instruction.dart';
import 'package:maisour/modules/properties/widgets/property_features.dart';
import 'package:maisour/modules/properties/widgets/property_image_indicator.dart';
import 'package:maisour/modules/properties/widgets/suspended_instruction.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/widgets/layouts/blur.dart';

class PropertyTile extends StatefulWidget {
  final SlimProperty property;
  final bool showNewlyAddedTag;
  final AppUser user;
  const PropertyTile({
    super.key,
    required this.property,
    required this.showNewlyAddedTag,
    required this.user,
  });

  @override
  State<PropertyTile> createState() => _PropertyTileState();
}

class _PropertyTileState extends State<PropertyTile> {
  int _currentImage = 0;

  @override
  Widget build(BuildContext context) {
    final images = widget.property.sortedImagesWithCoverFirst;
    final locale = Localizations.localeOf(context).languageCode;
    return Container(
      margin: EdgeInsets.only(bottom: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray.shade100),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          8.w.heightBox,
          // Image carousel
          SizedBox(
                height: 230.w,
                child: Stack(
                  children: [
                    PageView.builder(
                      itemCount: images.length,
                      onPageChanged: (index) =>
                          setState(() => _currentImage = index),
                      itemBuilder: (context, index) {
                        final imageUrl = images[index].imageUrl;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: CachedNetworkImage(
                            imageUrl: imageUrl,
                            height: 230.w,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                AppCircularLoader.medium(),
                            errorWidget: (context, url, error) => Container(
                              color: AppColors.gray.shade200,
                              child: const Center(
                                child: Icon(Icons.broken_image, size: 48),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    // Indicator
                    if (images.length > 1)
                      PositionedDirectional(
                        bottom: 8,
                        start: 0,
                        end: 0,
                        child: PropertyImageIndicator(
                          currentPage: _currentImage,
                          totalPages: images.length,
                        ),
                      ),

                    if (widget.showNewlyAddedTag)
                      PositionedDirectional(
                        top: 16,
                        start: 16,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 10.w,
                            vertical: 6.w,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.white.alphaPercent(90),
                            borderRadius: BorderRadius.circular(24.r),
                          ),
                          child: Row(
                            children: [
                              widget.property.isNewlyAdded
                                  ? Assets.icons.success.svg(
                                      width: 14.w,
                                      height: 14.w,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.dark.shade900,
                                        BlendMode.srcIn,
                                      ),
                                    )
                                  : Assets.icons.trending.svg(
                                      width: 14.w,
                                      height: 14.w,
                                      colorFilter: ColorFilter.mode(
                                        AppColors.dark.shade900,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                              3.w.widthBox,
                              Text(
                                widget.property.isNewlyAdded
                                    ? LocaleKeys.newlyAdded.tr()
                                    : LocaleKeys.trending.tr(),
                                style: AppTextStyles.text10.medium.dark900,
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              )
              .blurred(disable: widget.property.limitedVisibility == false)
              .paddingSymmetric(horizontal: 8.w),
          if (widget.property.propertyFeatures.isNotEmpty &&
              widget.property.limitedVisibility == false)
            // Tags row
            PropertyFeatures(
              propertyFeatures: widget.property.propertyFeatures,
              languageCode: locale,
            ).paddingOnly(top: 16.w),
          8.w.heightBox,
          // Title and price
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              widget.property.getTitle(locale),
              style: AppTextStyles.text20.bold.dark900,
            ),
          ),
          12.w.heightBox,
          Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        if (widget.user.usesImageSymbol)
                          Assets.images.dirham
                              .image(width: 28.w, height: 28.w)
                              .paddingEnd(4)
                        else
                          Text(
                            '${widget.user.currencyCode.currencySymbol} ',
                            style: AppTextStyles.text20.bold.dark900,
                          ),
                        Text(
                          widget.user.getCurrencyValue(
                            widget.property.totalPrice,
                          ),
                          style: AppTextStyles.text20.bold.dark900,
                        ),
                      ],
                    ),

                    Text(
                      '${widget.property.funded.toStringAsFixed(0)}% ${LocaleKeys.funded.tr()}',
                      style: AppTextStyles.text14.medium.dark300,
                    ),
                  ],
                ),
              ),
              8.w.heightBox,
              // Progress bar
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: LinearProgressIndicator(
                  value: (widget.property.funded / 100).clamp(0.0, 1.0),
                  minHeight: 6,
                  backgroundColor: AppColors.gray.shade100,
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              8.w.heightBox,
              // Expected returns
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: AppColors.gray.shade100),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.w,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _returnsRow(
                        LocaleKeys.expectedAnnualNetRent.tr(),
                        '${widget.property.expectedAnnualNetRentPercentage}%',
                      ),
                      _returnsRow(
                        LocaleKeys.expected5YearsNetRent.tr(),
                        '${widget.property.expected5YearsNetRentPercentage}%',
                      ),
                      _returnsRow(
                        LocaleKeys.expected5YearsNetReturns.tr(),
                        '${widget.property.fiveYearExpectedReturn}%',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ).blurred(
            disable: widget.property.limitedVisibility == false,
            visibleChild: widget.user.accountStatus == AccountStatus.registered
                ? const OnboardInstruction()
                : SuspendedInstruction(),
          ),

          16.w.heightBox,
        ],
      ),
    );
  }

  Widget _returnsRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: AppTextStyles.text12.medium.gray600),
          Text(value, style: AppTextStyles.text12.bold.gray900),
        ],
      ),
    );
  }
}
