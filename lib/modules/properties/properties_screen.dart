import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/shared/widgets/banners/verification_pending_banner.dart';
import 'package:maisour/shared/widgets/banners/verification_rejected_banner.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/app_bars/app_sliver_app_bar.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'widgets/tab_bar_sliver_header.dart';
import 'widgets/property_tab.dart';
import 'package:maisour/shared/widgets/banners/onboarding_banner.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';
import 'widgets/first_time_onboarding_bottomsheet.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'providers/properties_provider.dart';
import 'widgets/property_tile.dart';
import 'widgets/empty_properties.dart';
import 'widgets/sort_properties_modal.dart';
import 'dart:async';

class PropertiesScreen extends ConsumerStatefulWidget {
  const PropertiesScreen({super.key});

  @override
  ConsumerState<PropertiesScreen> createState() => _PropertiesScreenState();
}

class _PropertiesScreenState extends ConsumerState<PropertiesScreen> {
  PropertyTab selectedTab = PropertyTab(
    key: 'Live',
    title: LocaleKeys.live.tr(),
  );
  int selectedIndex = 0;
  final ScrollController _scrollController = ScrollController();

  // Scroll debouncing
  Timer? _scrollDebounceTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowOnboarding(context);
      // Load initial properties for Live tab
      ref.read(propertiesProvider.notifier).loadProperties(selectedTab);
    });

    // Add scroll listener for pagination with debouncing
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollDebounceTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Cancel previous timer if it exists
    _scrollDebounceTimer?.cancel();

    // Debounce scroll events to prevent rapid API calls
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 150), () {
      _handleScrollForPagination();
    });
  }

  void _handleScrollForPagination() {
    // Check if we're near the bottom
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 300) {
      final propertiesState = ref.read(propertiesProvider);

      // Only load more if we're not already loading and have more data
      if (propertiesState.status != ApiStatus.moreLoading &&
          propertiesState.hasMoreData) {
        debugPrint(
          'Scroll threshold reached: pixels=${_scrollController.position.pixels}, maxScroll=${_scrollController.position.maxScrollExtent}',
        );

        // Load more when user is 300 pixels away from bottom
        ref.read(propertiesProvider.notifier).loadMoreProperties(selectedTab);
      }
    }
  }

  void _showSortModal(BuildContext context) {
    final currentSortOption = ref.read(propertiesProvider).currentSortOption;

    SortPropertiesModal.show(
      context,
      currentSortOption: currentSortOption,
      onSortSelected: (sortOption) {
        // Apply sorting and reload properties
        ref
            .read(propertiesProvider.notifier)
            .applySorting(selectedTab, sortOption);
      },
    );
  }

  Future<void> _checkAndShowOnboarding(BuildContext context) async {
    final appUser = ref.read(userProvider);
    final sharedPrefs = ref.read(sharedPrefsManagerProvider);

    final shouldShow =
        appUser?.verifiedStatus == VerifiedStatus.started ||
        appUser?.verifiedStatus == VerifiedStatus.notYet;
    final isFirstTime = sharedPrefs.firstHomeVisit;

    if (shouldShow && isFirstTime) {
      await sharedPrefs.setFirstHomeVisit(false);
      if (context.mounted) {
        await FirstTimeOnboardingBottomsheet.show(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appUser = ref.readCurrentUser!;
    final showOnboardingBanner =
        appUser.verifiedStatus == VerifiedStatus.started ||
        appUser.verifiedStatus == VerifiedStatus.notYet;

    final showVerificationPendingBanner =
        appUser.verifiedStatus == VerifiedStatus.pending;

    final showVerificationRejectedBanner =
        appUser.verifiedStatus == VerifiedStatus.notVerified;

    // Check if Live tab is selected
    final isLiveTabSelected = selectedTab.key == 'Live';

    return SafeArea(
      child: RefreshIndicator.adaptive(
        color: AppColors.primary,
        onRefresh: () async {
          // Refresh current tab
          await ref
              .read(propertiesProvider.notifier)
              .refreshCurrentTab(selectedTab);
        },
        child: Container(
          color: AppColors.gray.shade100,
          child: CustomScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              AppSliverAppBar(title: LocaleKeys.properties.tr()),
              Consumer(
                builder: (context, ref, child) {
                  final currentSortOption = ref
                      .watch(propertiesProvider)
                      .currentSortOption;
                  final isFilterApplied = currentSortOption != null;
                  debugPrint(
                    'Building TabBarSliverHeader: currentSortOption=$currentSortOption, isFilterApplied=$isFilterApplied',
                  );

                  return TabBarSliverHeader(
                    key: ValueKey('filter_$currentSortOption'),
                    selectedIndex: selectedIndex,
                    onTabChanged: (tab, index) {
                      setState(() {
                        selectedTab = tab;
                        selectedIndex = index;
                      });

                      // Reset scroll position when switching tabs
                      if (_scrollController.hasClients) {
                        _scrollController.animateTo(
                          0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }

                      // Load properties when tab changes (will use cache if available)
                      ref.read(propertiesProvider.notifier).loadProperties(tab);
                    },
                    onFilterPressed: () {
                      _showSortModal(context);
                    },
                    isFilterApplied: isFilterApplied,
                  );
                },
              ),
              if (showOnboardingBanner && isLiveTabSelected)
                const OnboardingBanner(),
              if (showVerificationPendingBanner && isLiveTabSelected)
                const VerificationPendingBanner(),
              if (showVerificationRejectedBanner && isLiveTabSelected)
                const VerificationRejectedBanner(),
              Consumer(
                builder: (context, ref, child) {
                  final propertiesState = ref.watch(propertiesProvider);
                  debugPrint(
                    'UI rebuild: status=${propertiesState.status}, isLoadingMore=${propertiesState.status == ApiStatus.moreLoading} for tab ${selectedTab.key}',
                  );

                  // Show shimmer for initial loading
                  if (propertiesState.status == ApiStatus.loading) {
                    return SliverPadding(
                      padding: EdgeInsets.all(16.w),
                      sliver: SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          return Container(
                            height: 400.h,
                            margin: EdgeInsets.only(
                              bottom: index == 2 ? 0 : 16.h,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.gray.shade200,
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ).applyShimmer();
                        }, childCount: 3),
                      ),
                    );
                  }

                  // Show error state
                  if (propertiesState.status == ApiStatus.error) {
                    return SliverToBoxAdapter(
                      child: Center(
                        child: Text(
                          'Error: ${propertiesState.errorMessage ?? 'Unknown error'}',
                        ),
                      ),
                    );
                  }

                  if (propertiesState.status == ApiStatus.success) {
                    // Show properties list
                    final properties = propertiesState.properties;
                    final isLoadingMore =
                        propertiesState.status == ApiStatus.moreLoading;

                    // Show empty state if no properties
                    if (properties.isEmpty && !isLoadingMore) {
                      return SliverFillRemaining(
                        hasScrollBody: false,
                        child: Container(
                          color: AppColors.gray.shade100,
                          child: EmptyProperties(),
                        ),
                      );
                    }

                    final itemCount =
                        properties.length +
                        (isLiveTabSelected ? 1 : 0) +
                        (isLoadingMore ? 1 : 0);
                    debugPrint(
                      'Item count: ${properties.length} properties + ${isLiveTabSelected ? 1 : 0} banner + ${isLoadingMore ? 1 : 0} loader = $itemCount for tab ${selectedTab.key}',
                    );

                    return SliverPadding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.w,
                      ),
                      sliver: SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                          debugPrint(
                            'Building item at index $index for tab ${selectedTab.key}, isLiveTabSelected: $isLiveTabSelected',
                          );

                          // Show bottom loader when loading more data
                          if (isLoadingMore && index == itemCount - 1) {
                            debugPrint('Showing bottom loader at index $index');
                            return AppCircularLoader.medium().paddingSymmetric(
                              vertical: 16,
                            );
                          }

                          // Show banner at the end when Live tab is selected
                          if (isLiveTabSelected && index == properties.length) {
                            debugPrint(
                              'Showing Live tab banner at index $index',
                            );
                            return SizedBox.shrink();
                            // return Container(
                            //   height: 100,
                            //   margin: const EdgeInsets.symmetric(vertical: 8),
                            //   decoration: BoxDecoration(
                            //     color: Colors.blue,
                            //     borderRadius: BorderRadius.circular(8),
                            //   ),
                            //   child: const Center(
                            //     child: Text(
                            //       'Live Tab Banner',
                            //       style: TextStyle(
                            //         color: Colors.white,
                            //         fontSize: 16,
                            //         fontWeight: FontWeight.bold,
                            //       ),
                            //     ),
                            //   ),
                            // );
                          }

                          // Add extra padding at the bottom for pull-to-refresh (only for last item)
                          if (index == itemCount - 1) {
                            return Column(
                              children: [
                                // Regular property item
                                PropertyTile(
                                  property: properties[index],
                                  showNewlyAddedTag: isLiveTabSelected,
                                  user: appUser,
                                ).onTap(() {
                                  GoRouter.of(context).pushNamed(
                                    RouteName.propertyDetails.name,
                                    extra: properties[index].id,
                                  );
                                }),
                                // Extra padding for pull-to-refresh
                                SizedBox(height: 200.h),
                              ],
                            );
                          }

                          // Regular property item
                          final property = properties[index];
                          debugPrint(
                            'Showing property at index $index: ${property.titleInEnglish}',
                          );

                          return PropertyTile(
                            property: property,
                            showNewlyAddedTag: isLiveTabSelected,
                            user: appUser,
                          ).onTap(() {
                            GoRouter.of(context).pushNamed(
                              RouteName.propertyDetails.name,
                              extra: property.id,
                            );
                          });
                        }, childCount: itemCount),
                      ),
                    );
                  }

                  return const SliverToBoxAdapter(child: SizedBox.shrink());
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
