import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/modules/welcome/widgets/welcome_app_bar.dart';
import 'package:maisour/modules/welcome/widgets/welcome_slider.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/language_provider.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class WelcomeScreen extends ConsumerWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(currentLanguageProvider);
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: WelcomeAppBar(),
        body: Column(
          children: [
            // Image Slider Section
            Expanded(flex: 3, child: WelcomeSlider()),

            // Buttons Section
            AppButton(
              text: LocaleKeys.startInvesting.tr(),
              type: ButtonType.filled,
              onPressed: () => context.goNamed(RouteName.login.name),
            ).paddingHorizontal(16.w),
            20.h.heightBox,
            RegulatedByDsfa(),
            30.h.heightBox,
          ],
        ),
      ),
    );
  }
}
