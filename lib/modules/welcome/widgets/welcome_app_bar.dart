import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/welcome/widgets/language_dropdown_button.dart';

class WelcomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  const WelcomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Assets.images.logoText.image(height: 26.h),
      centerTitle: false,
      actions: [const LanguageDropdownButton(), 16.w.widthBox],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
