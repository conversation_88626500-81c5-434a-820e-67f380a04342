import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/welcome/widgets/welcome_slide_item.dart';
import 'package:maisour/modules/welcome/widgets/welcome_page_indicators.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/language_provider.dart';

class WelcomeSlider extends ConsumerStatefulWidget {
  const WelcomeSlider({super.key});

  @override
  ConsumerState<WelcomeSlider> createState() => _WelcomeSliderState();
}

class _WelcomeSliderState extends ConsumerState<WelcomeSlider> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Move slides creation to build method so translations update dynamically
  List<WelcomeSlideData> get _slides => [
    WelcomeSlideData(
      title: LocaleKeys.welcomeTitle1.tr(),
      description: LocaleKeys.welcomeDesc1.tr(),
      image: Assets.images.welcome1.image(),
    ),
    WelcomeSlideData(
      title: LocaleKeys.welcomeTitle2.tr(),
      description: LocaleKeys.welcomeDesc2.tr(),
      image: Assets.images.welcome2.image(),
    ),
    WelcomeSlideData(
      title: LocaleKeys.welcomeTitle3.tr(),
      description: LocaleKeys.welcomeDesc3.tr(),
      image: Assets.images.welcome3.image(),
    ),
    WelcomeSlideData(
      title: LocaleKeys.welcomeTitle4.tr(),
      description: LocaleKeys.welcomeDesc4.tr(),
      image: Assets.images.welcome4.image(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    // Watch language provider to rebuild when language changes
    ref.watch(currentLanguageProvider);
    return Column(
      children: [
        // Slider
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemCount: _slides.length,
            itemBuilder: (context, index) {
              return WelcomeSlideItem(slide: _slides[index]);
            },
          ),
        ),

        20.h.heightBox,

        // Page Indicators
        WelcomePageIndicators(
          currentPage: _currentPage,
          totalPages: _slides.length,
        ),

        25.h.heightBox,
      ],
    );
  }
}

class WelcomeSlideData {
  final String title;
  final String description;
  final Widget? image;

  WelcomeSlideData({
    required this.title,
    required this.description,
    this.image,
  });
}
