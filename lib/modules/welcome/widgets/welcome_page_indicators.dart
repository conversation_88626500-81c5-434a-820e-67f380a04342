import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';

class WelcomePageIndicators extends StatelessWidget {
  final int currentPage;
  final int totalPages;

  const WelcomePageIndicators({
    super.key,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(totalPages, (index) => _buildIndicator(index)),
    );
  }

  Widget _buildIndicator(int index) {
    final isActive = currentPage == index;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6.w),
      width: isActive ? 18.h : 8.h,
      height: 8.h,
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary : AppColors.dark.alphaPercent(10),
        borderRadius: BorderRadius.circular(4.r),
      ),
    );
  }
}
