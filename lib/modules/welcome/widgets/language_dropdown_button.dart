import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/providers/language_provider.dart';

class LanguageDropdownButton extends ConsumerWidget {
  const LanguageDropdownButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Map<String, String> languages = AppConstants.languageNames;
    final String selected = ref.watch(currentLanguageProvider);

    return PopupMenuButton<String>(
      elevation: 0.8,
      onSelected: (String languageCode) {
        ref
            .read(languageProvider.notifier)
            .changeLanguage(context, languageCode);
      },
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      offset: const Offset(0, 50),
      itemBuilder: (context) {
        return languages.entries.map((entry) {
          final isSelected = entry.key == selected;

          return PopupMenuItem<String>(
            value: entry.key,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(entry.value, style: AppTextStyles.text14.medium.dark400),
                if (isSelected) ...[
                  12.widthBox,
                  Assets.icons.check.svg(
                    width: 16.w,
                    height: 16.w,
                    colorFilter: ColorFilter.mode(
                      AppColors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ],
              ],
            ),
          );
        }).toList();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.gray.shade200),
          borderRadius: BorderRadius.circular(8.r),
          color: Colors.white,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Assets.icons.globe.svg(
              width: 16.w,
              height: 16.w,
              colorFilter: ColorFilter.mode(
                AppColors.dark.shade400,
                BlendMode.srcIn,
              ),
            ),
            8.w.widthBox,
            Text(
              languages[selected] ?? selected,
              style: AppTextStyles.text14.bold.dark400,
            ),
          ],
        ),
      ),
    );
  }
}
