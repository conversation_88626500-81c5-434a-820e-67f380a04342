import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class RegulatedByDsfa extends StatelessWidget {
  final double? iconSize;
  final TextStyle? textStyle;
  const RegulatedByDsfa({super.key, this.iconSize, this.textStyle});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Assets.icons.shield.svg(
          width: iconSize ?? 18.w,
          height: iconSize ?? 18.w,
          colorFilter: ColorFilter.mode(AppColors.success, BlendMode.srcIn),
        ),
        2.w.widthBox,
        Text(
          LocaleKeys.maisourRegulatedDSFA.tr(),
          style: textStyle ?? AppTextStyles.text12.medium.dark300,
        ),
      ],
    );
  }
}
