import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/modules/welcome/widgets/welcome_slider.dart';

class WelcomeSlideItem extends StatelessWidget {
  final WelcomeSlideData slide;

  const WelcomeSlideItem({super.key, required this.slide});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        20.h.heightBox,

        // Image placeholder - replace with actual image when available
        _buildImagePlaceholder(),

        20.h.heightBox,

        // Title
        Text(
          slide.title,
          textAlign: TextAlign.center,
          style: AppTextStyles.text20.bold.dark900,
        ).paddingHorizontal(20.w),

        6.h.heightBox,

        // Description
        Text(
          slide.description,
          textAlign: TextAlign.center,
          style: AppTextStyles.text14.medium.gray,
        ).paddingHorizontal(20.w),
      ],
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      width: double.infinity,
      height: 300.h,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
      child: slide.image,
    );
  }
}
