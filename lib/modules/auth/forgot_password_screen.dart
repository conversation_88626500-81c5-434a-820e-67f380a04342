import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/resend_timer_provider.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key, required this.email});

  final String email;

  @override
  ConsumerState<ForgotPasswordScreen> createState() =>
      _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  late final ProviderSubscription _forgotPasswordSubscription;

  @override
  void initState() {
    super.initState();

    _emailController.text = widget.email;

    // Listen to auth state changes for forgot password
    _forgotPasswordSubscription = ref.listenManual<AuthState>(
      authNotifierProvider('forgotPassword'),
      (previous, next) {
        if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

        if (next.status == ApiStatus.success) {
          // Reset link sent successfully (success but not authenticated means forgot password success)
          context.showSuccessToast(
            LocaleKeys.success.tr(),
            LocaleKeys.forgotPasswordSuccess.tr(),
          );
          // Start resend timer using provider
          ref.read(forgotPasswordTimerProvider.notifier).startTimer();
        } else if (next.status == ApiStatus.error) {
          // Show error message
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: next.errorKey,
            fallbackMessage: next.errorMessage ?? '',
          );
          context.showErrorToast(title, message);
        }
      },
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _forgotPasswordSubscription.close();
    super.dispose();
  }

  void _onSendResetLinkPressed() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(authNotifierProvider('forgotPassword').notifier)
          .sendResetLink(email: _emailController.text.trim());
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider('forgotPassword'));
    final timerState = ref.watch(forgotPasswordTimerProvider);
    final isLoading = authState.status == ApiStatus.loading;
    final isButtonEnabled = timerState.canResend && !isLoading;

    return GradientBackground(
      child:
          Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.transparent,
            appBar: AppBar(),
            body: SafeArea(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    24.h.heightBox,
                    Text(
                      LocaleKeys.forgotPassword.tr(),
                      style: AppTextStyles.text24.bold.dark900,
                    ),
                    8.h.heightBox,
                    Text(
                      LocaleKeys.forgotPasswordSubtitle.tr(),
                      style: AppTextStyles.text14.medium.dark300,
                    ),

                    16.h.heightBox,

                    // Email Field
                    AppTextField(
                      labelText: LocaleKeys.email.tr(),
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      validator: AppValidators.email,
                      inputFormatters: InputFormatters.emailFormatters(),
                    ),

                    2.h.heightBox,

                    // Resend message
                    if (!timerState.canResend && timerState.countdown > 0)
                      Row(
                        children: [
                          Text(
                            '${LocaleKeys.didntReceiveEmail.tr()} ',
                            style: AppTextStyles.text14.medium.dark300,
                          ),

                          Text(
                            '${LocaleKeys.resendIn.tr()} ${timerState.formattedTime}',
                            style: AppTextStyles.text14.medium.dark300
                                .underline(
                                  underlineColor: AppColors.dark.shade300,
                                ),
                          ),
                        ],
                      ),

                    32.h.heightBox,
                    // Send Reset Link Button
                    AppButton(
                      text: !timerState.canResend && timerState.countdown > 0
                          ? LocaleKeys.resendResetLink.tr()
                          : LocaleKeys.sendResetLink.tr(),
                      onPressed: _onSendResetLinkPressed,
                      isLoading: !isButtonEnabled,
                      showLoader: false,
                    ),
                  ],
                ).paddingHorizontal(16.w),
              ),
            ),
          ).onTap(() {
            FocusManager.instance.primaryFocus?.unfocus();
          }),
    );
  }
}
