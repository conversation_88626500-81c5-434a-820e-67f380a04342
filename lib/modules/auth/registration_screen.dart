import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/auth/models/hear_about_maisour_option.dart';
import 'package:maisour/modules/auth/models/register_request.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/modules/auth/widgets/strong_password_field.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_dropdown_field.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class RegistrationScreen extends ConsumerStatefulWidget {
  const RegistrationScreen({
    super.key,
    required this.email,
    required this.signUpType,
    this.fullName,
  });

  final String email;
  final String signUpType;
  final String? fullName;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _RegistrationScreenState();
}

class _RegistrationScreenState extends ConsumerState<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _referralCodeController = TextEditingController();
  final _otherHearAboutController = TextEditingController();
  HearAboutMaisourOption? _selectedHearAbout;
  String key = UniqueKey().toString();

  @override
  void initState() {
    super.initState();
    ref
        .read(authNotifierProvider('registration$key').notifier)
        .getHearAboutMaisourOptions();
    if (widget.fullName != null) {
      _fullNameController.text = widget.fullName!;
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _passwordController.dispose();
    _referralCodeController.dispose();
    _otherHearAboutController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AuthState>(authNotifierProvider('registration$key'), (
      previous,
      next,
    ) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;
      if (next.status == ApiStatus.success) {
        GoRouter.of(
          context,
        ).pushNamed(RouteName.emailActivation.name, extra: widget.email);
      } else if (next.status == ApiStatus.error) {
        // Get localized error message based on errorKey
        final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
          errorKey: next.errorKey,
          fallbackMessage: next.errorMessage ?? 'Authentication failed',
        );

        // Use new modern toast system
        context.showErrorToast(title, message);
      }
    });
    return GradientBackground(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        appBar: AppBar(),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  24.h.heightBox,
                  Text(
                    LocaleKeys.createAccount.tr(),
                    style: AppTextStyles.text24.bold.dark900,
                  ),
                  8.h.heightBox,
                  Text(
                    LocaleKeys.createAccountWith.tr(),
                    style: AppTextStyles.text14.medium.dark300,
                  ),
                  2.h.heightBox,
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          widget.email,
                          style: AppTextStyles.text14.medium.dark900.underline(
                            underlineColor: AppColors.dark.shade900,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      2.w.widthBox,
                      Assets.icons.pencilLine.svg().onTap(() {
                        GoRouter.of(context).pop();
                      }),
                    ],
                  ),

                  16.h.heightBox,

                  AppTextField(
                    labelText: "${LocaleKeys.fullName.tr()}*",
                    controller: _fullNameController,
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    textCapitalization: TextCapitalization.words,
                    validator: (value) =>
                        AppValidators.required(LocaleKeys.fullName.tr(), value),
                    inputFormatters: InputFormatters.fullNameFormatters(),
                    helperText: LocaleKeys.fullNameHelperText.tr(),
                  ),

                  16.h.heightBox,

                  PasswordFieldWithStrength(
                    controller: _passwordController,
                    validator: (value) => AppValidators.strongPassword(value),
                  ),

                  10.h.heightBox,

                  AppTextField(
                    labelText: LocaleKeys.referralCode.tr(),
                    controller: _referralCodeController,
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.done,
                    inputFormatters: InputFormatters.referralCodeFormatters(),
                  ),

                  16.h.heightBox,

                  Consumer(
                    builder: (context, ref, child) {
                      final authState = ref.watch(
                        authNotifierProvider('registration$key'),
                      );
                      final options = authState.hearAboutOptions ?? [];

                      final allOptions = [
                        ...options,
                        HearAboutMaisourOption(
                          id: -1, // Special ID for "Others"
                          nameInEnglish: LocaleKeys.others.tr(),
                          nameInArabic: LocaleKeys.others.tr(),
                          isOther: true,
                        ),
                      ];

                      return AppDropdownField<HearAboutMaisourOption>(
                        labelText: LocaleKeys.hearAboutMaisour.tr(),
                        itemAsString: (item) =>
                            context.locale.languageCode == 'ar'
                            ? item.nameInArabic
                            : item.nameInEnglish,
                        items: allOptions,
                        selectedItem: _selectedHearAbout,
                        onChanged: (value) {
                          setState(() {
                            _selectedHearAbout = value;
                          });
                        },
                      );
                    },
                  ),

                  if (_selectedHearAbout?.isOther ?? false) ...[
                    16.h.heightBox,
                    AppTextField(
                      labelText: LocaleKeys.pleaseSpecify.tr(),
                      controller: _otherHearAboutController,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      inputFormatters: InputFormatters.maxLengthFormatters(),
                      validator: (value) => AppValidators.required(
                        LocaleKeys.pleaseSpecify.tr(),
                        value,
                      ),
                    ),
                  ],

                  32.h.heightBox,
                  AppButton(
                    text: LocaleKeys.createAccount.tr(),
                    onPressed: _onCreateAccount,
                  ),
                  32.h.heightBox,
                ],
              ).paddingHorizontal(16.w),
            ),
          ),
        ),
      ),
    );
  }

  void _onCreateAccount() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(authNotifierProvider('registration$key').notifier)
          .signUp(
            RegisterRequest.create(
              email: widget.email,
              password: _passwordController.text.trim(),
              fullName: _fullNameController.text.trim(),
              referralCode: _referralCodeController.text.trim(),
              signUpType: widget.signUpType,
              hearAboutMaisour: getSelectedHearAbout(),
              langKey: context.locale.languageCode,
            ),
          );
    }
  }

  String getSelectedHearAbout() {
    if (_selectedHearAbout?.isOther == true) {
      return _otherHearAboutController.text;
    }
    return _selectedHearAbout?.nameInEnglish ?? '';
  }
}
