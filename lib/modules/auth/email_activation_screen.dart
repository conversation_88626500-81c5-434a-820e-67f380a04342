import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class EmailActivationScreen extends ConsumerWidget {
  final String email;

  const EmailActivationScreen({super.key, required this.email});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authNotifierProvider('emailActivation'), (
      previous,
      next,
    ) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

      if (next.status == ApiStatus.success) {
        context.showSuccessToast(
          LocaleKeys.emailSentSuccessfully.tr(),
          LocaleKeys.emailVerificationLink.tr(namedArgs: {'email': email}),
        );
      } else if (next.status == ApiStatus.error) {
        // Get localized error message based on errorKey
        final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
          errorKey: next.errorKey,
          fallbackMessage: next.errorMessage ?? 'Authentication failed',
        );

        // Use new modern toast system
        context.showErrorToast(title, message);
      }
    });

    final authState = ref.watch(authNotifierProvider('emailActivation'));
    final isLoading = authState.status == ApiStatus.loading;

    return GradientBackground(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.transparent,
        body: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.images.verifyEmail.image(height: 200.h),

              16.h.heightBox,

              Text(
                LocaleKeys.verifyYourEmail.tr(),
                style: AppTextStyles.text20.bold.dark900,
              ),

              8.h.heightBox,

              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${LocaleKeys.anEmailSentTo.tr()} ',
                      style: AppTextStyles.text14.medium.dark300,
                    ),
                    TextSpan(
                      text: '$email.',
                      style: AppTextStyles.text14.bold.dark900,
                    ),
                    TextSpan(
                      text: '${LocaleKeys.checkYourInbox.tr()} ',
                      style: AppTextStyles.text14.medium.dark300,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),

              24.h.heightBox,

              AppButton(
                text: LocaleKeys.resendEmail.tr(),
                isLoading: isLoading,
                onPressed: () => _onResendEmail(ref),
              ),

              10.h.heightBox,

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.arrow_back),
                  TextButton(
                    onPressed: () => context.goNamed(RouteName.login.name),
                    child: Text(
                      LocaleKeys.backToLogin.tr(),
                      style: AppTextStyles.text14.bold.dark,
                    ),
                  ),
                ],
              ),
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    );
  }

  Future<void> _onResendEmail(WidgetRef ref) async {
    final notifier = ref.read(authNotifierProvider('emailActivation').notifier);
    await notifier.resendActivationEmail(email);
  }
}
