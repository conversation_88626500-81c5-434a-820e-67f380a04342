import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:maisour/modules/auth/models/hear_about_maisour_option.dart';
import 'package:maisour/modules/auth/models/register_request.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';
import 'package:maisour/shared/services/notifications/notification_service_impl.dart';
import 'package:maisour/shared/services/notifications/notification_service.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:maisour/modules/auth/auth_service.dart';
import 'package:maisour/modules/auth/models/login_response.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/services/token/token_service.dart';
import 'package:maisour/shared/services/token/token_service_impl.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/constants/appsflyer_events.dart';
import 'package:maisour/shared/services/logout/logout_service_impl.dart';
import 'package:maisour/shared/services/logout/logout_service.dart';

final authNotifierProvider =
    StateNotifierProvider.family<AuthNotifier, AuthState, String>((
      ref,
      screenName,
    ) {
      final authService = ref.watch(authServiceProvider);
      final dio = ref.watch(networkServiceProvider);
      final tokenService = ref.watch(tokenServiceProvider(dio));
      final userNotifier = ref.watch(userProvider.notifier);
      final appsFlyerService = ref.watch(appsFlyerServiceProvider);
      final logoutService = ref.watch(logoutServiceProvider);
      final notificationService = ref.watch(notificationServiceProvider);
      return AuthNotifier(
        authService,
        tokenService,
        userNotifier,
        appsFlyerService,
        logoutService,
        notificationService,
      );
    }, name: 'authNotifierProvider');

class AuthNotifier extends StateNotifier<AuthState> with DioExceptionMapper {
  final AuthService _authService;
  final TokenService _tokenService;
  final UserNotifier _userNotifier;
  final AppsFlyerService _appsFlyerService;
  final LogoutService _logoutService;
  final NotificationService _notificationService;

  AuthNotifier(
    this._authService,
    this._tokenService,
    this._userNotifier,
    this._appsFlyerService,
    this._logoutService,
    this._notificationService,
  ) : super(const AuthState());

  Future<void> signInWithGoogle() async {
    state = state.copyWith(status: ApiStatus.loading);
    String? userEmail;
    String? fullName;
    try {
      final GoogleSignInAccount googleSignInAccount = await _authService
          .loginWithGoogle();

      final GoogleSignInAuthentication googleSignInAuthentication =
          await googleSignInAccount.authentication;

      // Extract user data for API call
      final idToken = googleSignInAuthentication.idToken;
      userEmail = googleSignInAccount.email;
      fullName = googleSignInAccount.displayName;

      debugPrint('Google Sign In Success - Email: $userEmail');

      // Check if idToken is available (required by backend)
      if (idToken == null || idToken.isEmpty) {
        state = state.copyWith(
          status: ApiStatus.error,
          loginType: 'GOOGLE',
          errorMessage: LocaleKeys.somethingWentWrong.tr(),
        );
        return;
      }

      final loginResponse = await _authService.allowsLogin(
        email: userEmail,
        loginType: 'GOOGLE',
        idToken: idToken,
      );

      // Store JWT token in secure storage (JWT token = access token, refresh token = empty)
      await _tokenService.saveToken(
        loginResponse.jwtToken!, // JWT token as access token
        loginResponse.refreshToken!, // Empty refresh token as specified
      );

      // Sign out from Google platform for security
      await _authService.signOut();

      // Login successful - save login response and sync with global user provider
      state = state.copyWith(
        status: ApiStatus.success,
        loginType: 'GOOGLE',
        email: userEmail,
        fullName: fullName,
        loginResponse: loginResponse,
      );

      // Sync user data with global provider
      _userNotifier.setUser(loginResponse.appUser!);

      await _appsFlyerService.logEvent(
        AppsFlyerEvents.login,
        eventValues: {'login_type': 'google'},
      );

      // Send FCM token to backend after successful login
      await _notificationService.sendFCMTokenToBackend(loginResponse.appUser);
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        loginType: 'GOOGLE',
        email: userEmail,
        fullName: fullName,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  Future<void> signInWithApple() async {
    state = state.copyWith(status: ApiStatus.loading);
    String? userEmail;
    String? fullName;

    try {
      final AuthorizationCredentialAppleID credential = await _authService
          .loginWithApple();

      // Get user email for account status check
      userEmail = credential.email;
      fullName = credential.givenName;

      if (userEmail != null && userEmail.isNotEmpty) {
        debugPrint('Apple Sign In Success - Email: $userEmail');

        // Check if identityToken is available (required by backend)
        final identityToken = credential.identityToken;
        if (identityToken == null || identityToken.isEmpty) {
          state = state.copyWith(
            status: ApiStatus.error,
            errorMessage: LocaleKeys.somethingWentWrong.tr(),
            loginType: 'APPLE',
          );
          return;
        }

        final loginResponse = await _authService.allowsLogin(
          email: userEmail,
          loginType: 'APPLE',
          idToken: identityToken,
        );

        // Store JWT token in secure storage (JWT token = access token, refresh token = empty)
        await _tokenService.saveToken(
          loginResponse.jwtToken!, // JWT token as access token
          loginResponse.refreshToken!, // Empty refresh token as specified
        );

        // Login successful - save login response and sync with global user provider
        state = state.copyWith(
          status: ApiStatus.success,
          loginType: 'APPLE',
          loginResponse: loginResponse,
          email: userEmail,
          fullName: fullName,
        );

        // Sync user data with global provider
        _userNotifier.setUser(loginResponse.appUser!);

        await _appsFlyerService.logEvent(
          AppsFlyerEvents.login,
          eventValues: {'login_type': 'apple'},
        );

        // Send FCM token to backend after successful login
        await _notificationService.sendFCMTokenToBackend(loginResponse.appUser);
      } else {
        // No email available from Apple - show email required error
        debugPrint('Apple Sign In - No email provided, email is required');

        state = state.copyWith(
          status: ApiStatus.error,
          errorMessage: 'Apple Sign In was cancelled',
          errorKey:
              'emailRequired', // Do not chnage , its handle in auth to show custom mesage.
          loginType: 'APPLE',
          email: userEmail,
        );
      }
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        loginType: 'APPLE',
        email: userEmail,
        fullName: fullName,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  Future<void> checkEmailLogin({required String email}) async {
    state = state.copyWith(status: ApiStatus.loading, loginType: 'EMAIL');
    try {
      final loginResponse = await _authService.allowsLogin(
        email: email,
        loginType: 'EMAIL',
        idToken: '',
      );

      state = state.copyWith(
        status: ApiStatus.success,
        loginType: 'EMAIL',
        loginResponse: loginResponse,
        email: email,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        loginType: 'EMAIL',
        email: email,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  Future<void> signInWithEmail({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      debugPrint('Email Login Started - Email: $email');

      final loginResponse = await _authService.emailLogin(
        email: email,
        password: password,
      );

      // Store JWT token in secure storage (JWT token = access token, refresh token = empty)
      await _tokenService.saveToken(
        loginResponse.jwtToken!, // JWT token as access token
        loginResponse.refreshToken!, // Empty refresh token as specified
      );

      // Login successful - save login response and sync with global user provider
      state = state.copyWith(
        status: ApiStatus.success,
        loginResponse: loginResponse,
      );

      // Sync user data with global provider
      _userNotifier.setUser(loginResponse.appUser!);

      await _appsFlyerService.logEvent(
        AppsFlyerEvents.login,
        eventValues: {'login_type': 'email'},
      );

      // Send FCM token to backend after successful login
      await _notificationService.sendFCMTokenToBackend(loginResponse.appUser);

      debugPrint(
        'Email Login Success - User: ${loginResponse.appUser!.user.email}',
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey =
            failure.errorKey ??
            (error.response?.statusCode == 401 ? 'invalidCredentials' : null);
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  /// Send reset link for forgot password
  Future<void> sendResetLink({required String email}) async {
    state = state.copyWith(status: ApiStatus.loading);

    try {
      debugPrint('Forgot Password Started - Email: $email');

      await _authService.sendResetLink(email: email);

      // Success - reset link sent
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
      );

      debugPrint('Reset link sent successfully to: $email');
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  // Inside AuthNotifier class
  Future<void> getHearAboutMaisourOptions() async {
    try {
      final response = await _authService.getHearAboutMaisourOptions();
      state = state.copyWith(hearAboutOptions: response);
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  Future<void> signUp(RegisterRequest registerRequest) async {
    state = state.copyWith(status: ApiStatus.loading);
    try {
      final ipAddress = await _authService.getPublicIp();
      final response = await _authService.signUp(
        registerRequest.copyWith(ipAddress: ipAddress),
      );
      state = state.copyWith(
        status: ApiStatus.success,
        loginResponse: response,
      );
      // Log AppsFlyer complete registration event
      await _appsFlyerService.logEvent(
        AppsFlyerEvents.completeRegistration,
        eventValues: {
          'registration_method': registerRequest.signUpType.toLowerCase(),
        },
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  Future<void> resendActivationEmail(String email) async {
    state = state.copyWith(status: ApiStatus.loading);
    try {
      final response = await _authService.resendActivationEmail(email: email);
      state = state.copyWith(
        status: ApiStatus.success,
        loginResponse: response,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }

  /// Sign out - comprehensive logout
  Future<void> signOut() async {
    try {
      debugPrint('🚀 Starting sign out process...');

      // Perform comprehensive logout cleanup (includes token clearing via secure storage)
      await _logoutService.performComprehensiveLogout();

      // Reset auth state
      state = const AuthState();

      debugPrint('✅ Sign out completed successfully');
    } catch (error) {
      debugPrint('❌ Error during sign out: $error');
      // Ensure state is reset even if some cleanup fails
      state = const AuthState();
      rethrow;
    }
  }

  /// Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = state.copyWith(status: ApiStatus.loading);
    try {
      await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      state = state.copyWith(status: ApiStatus.success);
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
        debugPrint('Mapped Error Message: $errorMessage');
        debugPrint('Error Key: $errorKey');
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      debugPrint('Auth State Updated - Error: $errorMessage, Key: $errorKey');
    }
  }
}

class AuthState {
  final ApiStatus status;
  final String? errorMessage;
  final String? errorKey;
  final String? loginType;
  final String? email;
  final String? fullName;
  final LoginResponse? loginResponse;
  final List<HearAboutMaisourOption>? hearAboutOptions;

  const AuthState({
    this.status = ApiStatus.initial,
    this.errorMessage,
    this.errorKey,
    this.loginType,
    this.email,
    this.fullName,
    this.loginResponse,
    this.hearAboutOptions,
  });

  // Convenience getters
  String? get jwtToken => loginResponse?.jwtToken;
  bool get isAuthenticated => status == ApiStatus.success && jwtToken != null;

  AuthState copyWith({
    ApiStatus? status,
    String? errorMessage,
    String? errorKey,
    String? loginType,
    String? email,
    String? fullName,
    LoginResponse? loginResponse,
    List<HearAboutMaisourOption>? hearAboutOptions,
  }) {
    return AuthState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
      loginType: loginType ?? this.loginType,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      loginResponse: loginResponse ?? this.loginResponse,
      hearAboutOptions: hearAboutOptions ?? this.hearAboutOptions,
    );
  }
}
