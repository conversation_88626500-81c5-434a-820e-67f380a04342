import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/modules/auth/widgets/auth_footer_text.dart';
import 'package:maisour/modules/auth/widgets/email_required_dialog.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/dialogs/account_closed_dialog.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AuthState>(authNotifierProvider('login'), (previous, next) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;
      if (next.status == ApiStatus.success) {
        if (next.loginType == 'EMAIL') {
          GoRouter.of(
            context,
          ).pushNamed(RouteName.password.name, extra: next.email);
        } else {
          debugPrint('Authentication successful, navigate to home');
          GoRouter.of(context).goNamed(RouteName.properties.name);
        }
      } else if (next.status == ApiStatus.error) {
        // Handle special cases based on errorKey
        if (next.errorKey == 'emailRequired') {
          // Show email required dialog for Apple Sign-In
          EmailRequiredDialog.show(context);
        } else if (next.errorKey == 'accountClosed') {
          AccountClosedDialog.show(context);
        } else if (next.errorKey == 'userNotFound') {
          // User account not found, redirect to register screen
          GoRouter.of(context).pushNamed(
            RouteName.registration.name,
            extra: {
              'email': next.email!,
              'signUpType': next.loginType!,
              'fullName': next.fullName,
            },
          );
        } else {
          // Get localized error message based on errorKey
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: next.errorKey,
            fallbackMessage: next.errorMessage ?? 'Authentication failed',
          );

          // Use new modern toast system
          context.showErrorToast(
            title,
            message,
            duration: const Duration(seconds: 5),
            onTap: (item) {
              if (next.errorKey == 'domainNotAllowed' ||
                  next.errorKey == 'accountDisabled' ||
                  next.errorKey == 'accountClosed') {
                UrlLauncherService.launchPhone(AppConstants.supportPhone);
              }
            },
          );
        }
      }
    });

    final authState = ref.watch(authNotifierProvider('login'));
    final isLoading = authState.status == ApiStatus.loading;

    return GradientBackground(
      child: SafeArea(
        child:
            Scaffold(
              resizeToAvoidBottomInset: false,
              backgroundColor: Colors.transparent,
              body: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Assets.images.logo.image(width: 48.w),
                    32.h.heightBox,
                    Text(
                      LocaleKeys.getStarted.tr(),
                      style: AppTextStyles.text24.bold.dark900,
                    ),
                    8.h.heightBox,
                    Text(
                      LocaleKeys.loginSubtitle.tr(),
                      style: AppTextStyles.text14.medium.dark300,
                    ),

                    16.h.heightBox,
                    // Email Field
                    AppTextField(
                      labelText: LocaleKeys.email.tr(),
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      validator: AppValidators.email,
                      inputFormatters: InputFormatters.emailFormatters(),
                    ),

                    20.h.heightBox,
                    // Login Button
                    AppButton(
                      text: LocaleKeys.continueText.tr(),
                      onPressed: isLoading ? () {} : _onContinuePressed,
                      isLoading: (isLoading && authState.loginType == 'EMAIL'),
                    ),

                    32.heightBox,
                    Row(
                      children: [
                        Expanded(child: Divider(color: Colors.grey.shade200)),
                        Text(
                          LocaleKeys.orText.tr(),
                          style: AppTextStyles.text14.medium.dark300,
                        ).paddingHorizontal(8.w),
                        Expanded(child: Divider(color: Colors.grey.shade200)),
                      ],
                    ),

                    32.heightBox,

                    // Apple Sign In Button (iOS only)
                    if (MyPlatform.isIOS) ...[
                      AppButton(
                        type: ButtonType.outlined,
                        onPressed: isLoading ? () {} : _handleAppleSignIn,
                        text: LocaleKeys.continueWithApple.tr(),
                        height: 44.w,
                        icon: Assets.icons.apple.svg(height: 16.w),
                      ),
                      16.h.heightBox,
                    ],

                    // Google Sign In Button
                    AppButton(
                      type: ButtonType.outlined,
                      text: LocaleKeys.continueWithGoogle.tr(),
                      onPressed: isLoading ? () {} : _handleGoogleSignIn,
                      height: 44.w,
                      icon: Assets.icons.google.svg(height: 16.w),
                    ),

                    Spacer(),
                    // Dynamic Footer Text
                    const AuthFooterText(),
                    12.h.heightBox,
                  ],
                ).paddingHorizontal(16.w),
              ),
            ).onTap(() {
              FocusManager.instance.primaryFocus?.unfocus();
            }),
      ),
    );
  }

  void _onContinuePressed() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(authNotifierProvider('login').notifier)
          .checkEmailLogin(email: _emailController.text.trim());
    }
  }

  void _handleGoogleSignIn() {
    ref.read(authNotifierProvider('login').notifier).signInWithGoogle();
  }

  void _handleAppleSignIn() {
    ref.read(authNotifierProvider('login').notifier).signInWithApple();
  }
}
