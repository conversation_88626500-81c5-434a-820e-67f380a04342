import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class PasswordScreen extends ConsumerStatefulWidget {
  final String email;

  const PasswordScreen({super.key, required this.email});

  @override
  ConsumerState<PasswordScreen> createState() => _PasswordScreenState();
}

class _PasswordScreenState extends ConsumerState<PasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _passwordController = TextEditingController();
  bool _isPasswordVisible = false;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AuthState>(authNotifierProvider('password'), (previous, next) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

      if (next.status == ApiStatus.success) {
        debugPrint('Authentication successful, navigate to home');
        GoRouter.of(context).goNamed(RouteName.properties.name);
      } else if (next.status == ApiStatus.error) {
        // Get localized error message based on errorKey
        final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
          errorKey: next.errorKey,
          fallbackMessage: next.errorMessage ?? 'Authentication failed',
        );

        // Use new modern toast system
        context.showErrorToast(title, message);
      }
    });

    final authState = ref.watch(authNotifierProvider('password'));
    final isLoading = authState.status == ApiStatus.loading;
    return GradientBackground(
      child:
          Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: Colors.transparent,
            appBar: AppBar(),
            body: SafeArea(
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    24.h.heightBox,
                    Text(
                      LocaleKeys.welcomeBack.tr(),
                      style: AppTextStyles.text24.bold.dark900,
                    ),
                    8.h.heightBox,
                    Text(
                      LocaleKeys.passwordSubtitle.tr(),
                      style: AppTextStyles.text14.medium.dark300,
                    ),
                    2.h.heightBox,
                    Text(
                      widget.email,
                      style: AppTextStyles.text14.medium.dark900.underline(
                        underlineColor: AppColors.dark.shade900,
                      ),
                    ),

                    16.h.heightBox,

                    // Password Field
                    AppTextField(
                      labelText: LocaleKeys.password.tr(),
                      controller: _passwordController,
                      keyboardType: TextInputType.visiblePassword,
                      obscureText: !_isPasswordVisible,
                      textInputAction: TextInputAction.done,
                      validator: AppValidators.password,
                      inputFormatters: InputFormatters.passwordFormatters(),
                      suffixIcon: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                        child: _isPasswordVisible
                            ? Assets.icons.eye.svg(
                                width: 24.w,
                                height: 24.w,
                                fit: BoxFit.scaleDown,
                                colorFilter: ColorFilter.mode(
                                  AppColors.dark.shade300,
                                  BlendMode.srcIn,
                                ),
                              )
                            : Assets.icons.eyeClosed.svg(
                                width: 24.w,
                                height: 24.w,
                                fit: BoxFit.scaleDown,
                                colorFilter: ColorFilter.mode(
                                  AppColors.dark.shade300,
                                  BlendMode.srcIn,
                                ),
                              ),
                      ),
                    ),

                    8.h.heightBox,

                    Text(
                      LocaleKeys.forgotPassword.tr(),
                      style: AppTextStyles.text12.medium.primary,
                    ).onTap(() {
                      _navigateForgotPasword();
                    }),

                    32.h.heightBox,

                    AppButton(
                      text: LocaleKeys.login.tr(),
                      onPressed: _onLoginPressed,
                      isLoading: isLoading,
                    ),
                  ],
                ).paddingHorizontal(16.w),
              ),
            ),
          ).onTap(() {
            FocusManager.instance.primaryFocus?.unfocus();
          }),
    );
  }

  void _onLoginPressed() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(authNotifierProvider('password').notifier)
          .signInWithEmail(
            email: widget.email,
            password: _passwordController.text.trim(),
          );
    }
  }

  void _navigateForgotPasword() {
    GoRouter.of(
      context,
    ).pushNamed(RouteName.forgotPassword.name, extra: widget.email);
  }
}
