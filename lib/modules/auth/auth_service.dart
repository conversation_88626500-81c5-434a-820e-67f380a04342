import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:maisour/modules/auth/models/hear_about_maisour_option.dart';
import 'package:maisour/modules/auth/models/register_request.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:maisour/modules/auth/api/auth_api.dart';
import 'package:maisour/modules/auth/models/login_request.dart';
import 'package:maisour/modules/auth/models/login_response.dart';
import 'package:maisour/modules/auth/providers/auth_api_provider.dart';
import 'package:maisour/shared/models/app_user.dart';

final authServiceProvider = Provider.autoDispose<AuthService>((ref) {
  final authApi = ref.watch(authApiProvider);
  return AuthService(authApi);
}, name: 'authServiceProvider');

class AuthService {
  final AuthApi _authApi;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  AuthService(this._authApi);

  // Google Sign In - throws exception on error
  Future<GoogleSignInAccount> loginWithGoogle() async {
    final GoogleSignInAccount? googleSignInAccount = await _googleSignIn
        .signIn();

    if (googleSignInAccount == null) {
      throw Exception('Google Sign In was cancelled');
    }

    return googleSignInAccount;
  }

  // Apple Sign In - throws exception on error
  Future<AuthorizationCredentialAppleID> loginWithApple() async {
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );
    return credential;
  }

  // Social login using social login API
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<LoginResponse> allowsLogin({
    required String email,
    required String loginType, // 'google' or 'apple'
    required String idToken,
  }) async {
    final request = LoginRequest.create(
      email: email,
      loginType: loginType,
      idToken: idToken,
    );
    return await _authApi.allowsLogin(request);
  }

  // Email login using email login API
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<LoginResponse> emailLogin({
    required String email,
    required String password,
  }) async {
    final request = LoginRequest.emailLogin(email: email, password: password);
    return await _authApi.emailLogin(request);
  }

  // Forgot password - send reset link
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<LoginResponse> sendResetLink({required String email}) async {
    return await _authApi.sendResetLink(email);
  }

  // Get hear about Maisour options
  Future<List<HearAboutMaisourOption>> getHearAboutMaisourOptions() async {
    return await _authApi.getHearAboutMaisours();
  }

  // Sign up using register API
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<LoginResponse> signUp(RegisterRequest registerRequest) async {
    return await _authApi.signUp(registerRequest);
  }

  // Get current user data (requires authentication)
  // Throws DioException on error - provider will handle with DioExceptionMapper
  Future<AppUser> getCurrentUser() async {
    return await _authApi.getCurrentUser();
  }

  // Get public IP
  Future<String> getPublicIp() async {
    final response = await _authApi.getPublicIp();
    return response;
  }

  // Resend activation email
  Future<LoginResponse> resendActivationEmail({required String email}) async {
    return await _authApi.resendActivationEmail(email);
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    await _authApi.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
  }

  // Update user language preference
  Future<void> updateLanguage({required String languageCode}) async {
    final request = {'langKey': languageCode};
    await _authApi.updateLanguage(request);
  }

  // Update user currency preference
  Future<void> updateCurrency({required String currencyCode}) async {
    final request = {'currencyCode': currencyCode};
    await _authApi.updateCurrency(request);
  }

  // Sign out
  Future<void> signOut() async {
    await _googleSignIn.signOut();
  }
}
