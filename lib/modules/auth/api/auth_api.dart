import 'package:dio/dio.dart';
import 'package:maisour/modules/auth/models/hear_about_maisour_option.dart';
import 'package:maisour/modules/auth/models/register_request.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/modules/auth/models/login_request.dart';
import 'package:maisour/modules/auth/models/login_response.dart';
import 'package:maisour/shared/models/app_user.dart';

part 'auth_api.g.dart';

@RestApi()
abstract class AuthApi {
  factory AuthApi(Dio dio, {String? baseUrl}) = _AuthApi;

  /// Social login endpoint for Google/Apple login
  @POST(ApiEndpoints.allowsLogin)
  Future<LoginResponse> allowsLogin(@Body() LoginRequest request);

  /// Email login endpoint for email/password login
  @POST(ApiEndpoints.login)
  Future<LoginResponse> emailLogin(@Body() LoginRequest request);

  /// Forgot password - send reset link
  @GET(ApiEndpoints.forgotPassword)
  Future<LoginResponse> sendResetLink(@Query('email') String email);

  /// Get current user data (requires authentication)
  @GET(ApiEndpoints.getUser)
  Future<AppUser> getCurrentUser();

  /// Sign up endpoint for new user registration
  @POST(ApiEndpoints.signUp)
  Future<LoginResponse> signUp(@Body() RegisterRequest request);

  /// Get hear about Maisours
  @GET(ApiEndpoints.hearAboutMaisours)
  Future<List<HearAboutMaisourOption>> getHearAboutMaisours();

  /// Get public IP
  @GET(ApiEndpoints.getPublicIp)
  Future<String> getPublicIp();

  /// Resend activation email
  @GET(ApiEndpoints.resendActivationEmail)
  Future<LoginResponse> resendActivationEmail(@Query('email') String email);

  /// Change password
  @POST(ApiEndpoints.changePassword)
  Future<void> changePassword({
    @Query('currentPassword') required String currentPassword,
    @Query('newPassword') required String newPassword,
  });

  /// Update user language preference
  @POST(ApiEndpoints.updateLanguage)
  Future<void> updateLanguage(@Body() Map<String, String> request);

  /// Update user currency preference
  @POST(ApiEndpoints.updateCurrency)
  Future<void> updateCurrency(@Body() Map<String, String> request);

  /// Logout endpoint
  @POST(ApiEndpoints.logout)
  Future<void> logout();
}
