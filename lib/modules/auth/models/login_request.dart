import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_request.freezed.dart';
part 'login_request.g.dart';

/// Common request model for both social and email login
@freezed
abstract class LoginRequest with _$LoginRequest {
  const factory LoginRequest({
    required String email,
    required String loginType, // 'apple', 'google', 'email'
    @Default('') String deviceType, // Will be set automatically
    String? idToken,
    String? password, // For email login only
  }) = _LoginRequest;

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  /// Factory for social login (Google/Apple)
  factory LoginRequest.create({
    required String email,
    required String loginType, // 'google' or 'apple'
    required String idToken,
  }) {
    return LoginRequest(
      email: email,
      loginType: loginType,
      deviceType: MyPlatform.isAndroid ? 'ANDROID' : 'IOS',
      idToken: idToken,
    );
  }

  /// Factory for email login
  factory LoginRequest.emailLogin({
    required String email,
    required String password,
  }) {
    return LoginRequest(
      email: email,
      loginType: 'EMAIL',
      deviceType: MyPlatform.isAndroid ? 'ANDROID' : 'IOS',
      password: password,
    );
  }
}
