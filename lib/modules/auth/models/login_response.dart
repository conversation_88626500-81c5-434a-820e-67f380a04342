import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/models/app_user.dart';

part 'login_response.freezed.dart';
part 'login_response.g.dart';

/// Common response model for both social and email login
@freezed
abstract class LoginResponse with _$LoginResponse {
  const factory LoginResponse({
    String? jwtToken,
    String? refreshToken,
    AppUser? appUser,
  }) = _LoginResponse;

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);
}
