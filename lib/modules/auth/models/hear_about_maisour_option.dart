import 'package:freezed_annotation/freezed_annotation.dart';

part 'hear_about_maisour_option.freezed.dart';
part 'hear_about_maisour_option.g.dart';

@freezed
abstract class HearAboutMaisourOption with _$HearAboutMaisourOption {
  const factory HearAboutMaisourOption({
    required int id,
    required String nameInEnglish,
    required String nameInArabic,
    @Default(false) bool isOther,
  }) = _HearAboutMaisourOption;

  factory HearAboutMaisourOption.fromJson(Map<String, dynamic> json) =>
      _$HearAboutMaisourOptionFromJson(json);
}
