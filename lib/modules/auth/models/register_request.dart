import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'register_request.freezed.dart';
part 'register_request.g.dart';

@freezed
abstract class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    required String fullName,
    required String email,
    required String password,
    required String deviceType,
    String? ipAddress,
    required String signUpType,
    String? referralCode,
    String? hearAboutMaisour,
    required String langKey,
  }) = _RegisterRequest;

  /// Factory for social login (Google/Apple)
  factory RegisterRequest.create({
    required String email,
    required String signUpType, // 'google' or 'apple',
    required String fullName,
    required String password,
    String? referralCode,
    String? hearAboutMaisour,
    required String langKey,
  }) {
    return RegisterRequest(
      email: email,
      signUpType: signUpType,
      deviceType: MyPlatform.isAndroid ? 'ANDROID' : 'IOS',
      fullName: fullName,
      password: password,
      referralCode: referralCode,
      hearAboutMaisour: hearAboutMaisour,
      langKey: langKey,
    );
  }

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}
