// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RegisterRequest {

 String get fullName; String get email; String get password; String get deviceType; String? get ipAddress; String get signUpType; String? get referralCode; String? get hearAboutMaisour; String get langKey;
/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RegisterRequestCopyWith<RegisterRequest> get copyWith => _$RegisterRequestCopyWithImpl<RegisterRequest>(this as RegisterRequest, _$identity);

  /// Serializes this RegisterRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RegisterRequest&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.deviceType, deviceType) || other.deviceType == deviceType)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.signUpType, signUpType) || other.signUpType == signUpType)&&(identical(other.referralCode, referralCode) || other.referralCode == referralCode)&&(identical(other.hearAboutMaisour, hearAboutMaisour) || other.hearAboutMaisour == hearAboutMaisour)&&(identical(other.langKey, langKey) || other.langKey == langKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,email,password,deviceType,ipAddress,signUpType,referralCode,hearAboutMaisour,langKey);

@override
String toString() {
  return 'RegisterRequest(fullName: $fullName, email: $email, password: $password, deviceType: $deviceType, ipAddress: $ipAddress, signUpType: $signUpType, referralCode: $referralCode, hearAboutMaisour: $hearAboutMaisour, langKey: $langKey)';
}


}

/// @nodoc
abstract mixin class $RegisterRequestCopyWith<$Res>  {
  factory $RegisterRequestCopyWith(RegisterRequest value, $Res Function(RegisterRequest) _then) = _$RegisterRequestCopyWithImpl;
@useResult
$Res call({
 String fullName, String email, String password, String deviceType, String? ipAddress, String signUpType, String? referralCode, String? hearAboutMaisour, String langKey
});




}
/// @nodoc
class _$RegisterRequestCopyWithImpl<$Res>
    implements $RegisterRequestCopyWith<$Res> {
  _$RegisterRequestCopyWithImpl(this._self, this._then);

  final RegisterRequest _self;
  final $Res Function(RegisterRequest) _then;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? fullName = null,Object? email = null,Object? password = null,Object? deviceType = null,Object? ipAddress = freezed,Object? signUpType = null,Object? referralCode = freezed,Object? hearAboutMaisour = freezed,Object? langKey = null,}) {
  return _then(_self.copyWith(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,deviceType: null == deviceType ? _self.deviceType : deviceType // ignore: cast_nullable_to_non_nullable
as String,ipAddress: freezed == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String?,signUpType: null == signUpType ? _self.signUpType : signUpType // ignore: cast_nullable_to_non_nullable
as String,referralCode: freezed == referralCode ? _self.referralCode : referralCode // ignore: cast_nullable_to_non_nullable
as String?,hearAboutMaisour: freezed == hearAboutMaisour ? _self.hearAboutMaisour : hearAboutMaisour // ignore: cast_nullable_to_non_nullable
as String?,langKey: null == langKey ? _self.langKey : langKey // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [RegisterRequest].
extension RegisterRequestPatterns on RegisterRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RegisterRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RegisterRequest value)  $default,){
final _that = this;
switch (_that) {
case _RegisterRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RegisterRequest value)?  $default,){
final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String fullName,  String email,  String password,  String deviceType,  String? ipAddress,  String signUpType,  String? referralCode,  String? hearAboutMaisour,  String langKey)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that.fullName,_that.email,_that.password,_that.deviceType,_that.ipAddress,_that.signUpType,_that.referralCode,_that.hearAboutMaisour,_that.langKey);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String fullName,  String email,  String password,  String deviceType,  String? ipAddress,  String signUpType,  String? referralCode,  String? hearAboutMaisour,  String langKey)  $default,) {final _that = this;
switch (_that) {
case _RegisterRequest():
return $default(_that.fullName,_that.email,_that.password,_that.deviceType,_that.ipAddress,_that.signUpType,_that.referralCode,_that.hearAboutMaisour,_that.langKey);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String fullName,  String email,  String password,  String deviceType,  String? ipAddress,  String signUpType,  String? referralCode,  String? hearAboutMaisour,  String langKey)?  $default,) {final _that = this;
switch (_that) {
case _RegisterRequest() when $default != null:
return $default(_that.fullName,_that.email,_that.password,_that.deviceType,_that.ipAddress,_that.signUpType,_that.referralCode,_that.hearAboutMaisour,_that.langKey);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _RegisterRequest implements RegisterRequest {
  const _RegisterRequest({required this.fullName, required this.email, required this.password, required this.deviceType, this.ipAddress, required this.signUpType, this.referralCode, this.hearAboutMaisour, required this.langKey});
  factory _RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);

@override final  String fullName;
@override final  String email;
@override final  String password;
@override final  String deviceType;
@override final  String? ipAddress;
@override final  String signUpType;
@override final  String? referralCode;
@override final  String? hearAboutMaisour;
@override final  String langKey;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RegisterRequestCopyWith<_RegisterRequest> get copyWith => __$RegisterRequestCopyWithImpl<_RegisterRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RegisterRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RegisterRequest&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.email, email) || other.email == email)&&(identical(other.password, password) || other.password == password)&&(identical(other.deviceType, deviceType) || other.deviceType == deviceType)&&(identical(other.ipAddress, ipAddress) || other.ipAddress == ipAddress)&&(identical(other.signUpType, signUpType) || other.signUpType == signUpType)&&(identical(other.referralCode, referralCode) || other.referralCode == referralCode)&&(identical(other.hearAboutMaisour, hearAboutMaisour) || other.hearAboutMaisour == hearAboutMaisour)&&(identical(other.langKey, langKey) || other.langKey == langKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,fullName,email,password,deviceType,ipAddress,signUpType,referralCode,hearAboutMaisour,langKey);

@override
String toString() {
  return 'RegisterRequest(fullName: $fullName, email: $email, password: $password, deviceType: $deviceType, ipAddress: $ipAddress, signUpType: $signUpType, referralCode: $referralCode, hearAboutMaisour: $hearAboutMaisour, langKey: $langKey)';
}


}

/// @nodoc
abstract mixin class _$RegisterRequestCopyWith<$Res> implements $RegisterRequestCopyWith<$Res> {
  factory _$RegisterRequestCopyWith(_RegisterRequest value, $Res Function(_RegisterRequest) _then) = __$RegisterRequestCopyWithImpl;
@override @useResult
$Res call({
 String fullName, String email, String password, String deviceType, String? ipAddress, String signUpType, String? referralCode, String? hearAboutMaisour, String langKey
});




}
/// @nodoc
class __$RegisterRequestCopyWithImpl<$Res>
    implements _$RegisterRequestCopyWith<$Res> {
  __$RegisterRequestCopyWithImpl(this._self, this._then);

  final _RegisterRequest _self;
  final $Res Function(_RegisterRequest) _then;

/// Create a copy of RegisterRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? fullName = null,Object? email = null,Object? password = null,Object? deviceType = null,Object? ipAddress = freezed,Object? signUpType = null,Object? referralCode = freezed,Object? hearAboutMaisour = freezed,Object? langKey = null,}) {
  return _then(_RegisterRequest(
fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,password: null == password ? _self.password : password // ignore: cast_nullable_to_non_nullable
as String,deviceType: null == deviceType ? _self.deviceType : deviceType // ignore: cast_nullable_to_non_nullable
as String,ipAddress: freezed == ipAddress ? _self.ipAddress : ipAddress // ignore: cast_nullable_to_non_nullable
as String?,signUpType: null == signUpType ? _self.signUpType : signUpType // ignore: cast_nullable_to_non_nullable
as String,referralCode: freezed == referralCode ? _self.referralCode : referralCode // ignore: cast_nullable_to_non_nullable
as String?,hearAboutMaisour: freezed == hearAboutMaisour ? _self.hearAboutMaisour : hearAboutMaisour // ignore: cast_nullable_to_non_nullable
as String?,langKey: null == langKey ? _self.langKey : langKey // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
