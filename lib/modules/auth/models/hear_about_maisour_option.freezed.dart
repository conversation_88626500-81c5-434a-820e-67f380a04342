// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'hear_about_maisour_option.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HearAboutMaisourOption {

 int get id; String get nameInEnglish; String get nameInArabic; bool get isOther;
/// Create a copy of HearAboutMaisourOption
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HearAboutMaisourOptionCopyWith<HearAboutMaisourOption> get copyWith => _$HearAboutMaisourOptionCopyWithImpl<HearAboutMaisourOption>(this as HearAboutMaisourOption, _$identity);

  /// Serializes this HearAboutMaisourOption to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HearAboutMaisourOption&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.isOther, isOther) || other.isOther == isOther));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,isOther);

@override
String toString() {
  return 'HearAboutMaisourOption(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, isOther: $isOther)';
}


}

/// @nodoc
abstract mixin class $HearAboutMaisourOptionCopyWith<$Res>  {
  factory $HearAboutMaisourOptionCopyWith(HearAboutMaisourOption value, $Res Function(HearAboutMaisourOption) _then) = _$HearAboutMaisourOptionCopyWithImpl;
@useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, bool isOther
});




}
/// @nodoc
class _$HearAboutMaisourOptionCopyWithImpl<$Res>
    implements $HearAboutMaisourOptionCopyWith<$Res> {
  _$HearAboutMaisourOptionCopyWithImpl(this._self, this._then);

  final HearAboutMaisourOption _self;
  final $Res Function(HearAboutMaisourOption) _then;

/// Create a copy of HearAboutMaisourOption
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? isOther = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,isOther: null == isOther ? _self.isOther : isOther // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [HearAboutMaisourOption].
extension HearAboutMaisourOptionPatterns on HearAboutMaisourOption {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _HearAboutMaisourOption value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _HearAboutMaisourOption() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _HearAboutMaisourOption value)  $default,){
final _that = this;
switch (_that) {
case _HearAboutMaisourOption():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _HearAboutMaisourOption value)?  $default,){
final _that = this;
switch (_that) {
case _HearAboutMaisourOption() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  bool isOther)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _HearAboutMaisourOption() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.isOther);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int id,  String nameInEnglish,  String nameInArabic,  bool isOther)  $default,) {final _that = this;
switch (_that) {
case _HearAboutMaisourOption():
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.isOther);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int id,  String nameInEnglish,  String nameInArabic,  bool isOther)?  $default,) {final _that = this;
switch (_that) {
case _HearAboutMaisourOption() when $default != null:
return $default(_that.id,_that.nameInEnglish,_that.nameInArabic,_that.isOther);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _HearAboutMaisourOption implements HearAboutMaisourOption {
  const _HearAboutMaisourOption({required this.id, required this.nameInEnglish, required this.nameInArabic, this.isOther = false});
  factory _HearAboutMaisourOption.fromJson(Map<String, dynamic> json) => _$HearAboutMaisourOptionFromJson(json);

@override final  int id;
@override final  String nameInEnglish;
@override final  String nameInArabic;
@override@JsonKey() final  bool isOther;

/// Create a copy of HearAboutMaisourOption
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HearAboutMaisourOptionCopyWith<_HearAboutMaisourOption> get copyWith => __$HearAboutMaisourOptionCopyWithImpl<_HearAboutMaisourOption>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HearAboutMaisourOptionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HearAboutMaisourOption&&(identical(other.id, id) || other.id == id)&&(identical(other.nameInEnglish, nameInEnglish) || other.nameInEnglish == nameInEnglish)&&(identical(other.nameInArabic, nameInArabic) || other.nameInArabic == nameInArabic)&&(identical(other.isOther, isOther) || other.isOther == isOther));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,nameInEnglish,nameInArabic,isOther);

@override
String toString() {
  return 'HearAboutMaisourOption(id: $id, nameInEnglish: $nameInEnglish, nameInArabic: $nameInArabic, isOther: $isOther)';
}


}

/// @nodoc
abstract mixin class _$HearAboutMaisourOptionCopyWith<$Res> implements $HearAboutMaisourOptionCopyWith<$Res> {
  factory _$HearAboutMaisourOptionCopyWith(_HearAboutMaisourOption value, $Res Function(_HearAboutMaisourOption) _then) = __$HearAboutMaisourOptionCopyWithImpl;
@override @useResult
$Res call({
 int id, String nameInEnglish, String nameInArabic, bool isOther
});




}
/// @nodoc
class __$HearAboutMaisourOptionCopyWithImpl<$Res>
    implements _$HearAboutMaisourOptionCopyWith<$Res> {
  __$HearAboutMaisourOptionCopyWithImpl(this._self, this._then);

  final _HearAboutMaisourOption _self;
  final $Res Function(_HearAboutMaisourOption) _then;

/// Create a copy of HearAboutMaisourOption
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? nameInEnglish = null,Object? nameInArabic = null,Object? isOther = null,}) {
  return _then(_HearAboutMaisourOption(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as int,nameInEnglish: null == nameInEnglish ? _self.nameInEnglish : nameInEnglish // ignore: cast_nullable_to_non_nullable
as String,nameInArabic: null == nameInArabic ? _self.nameInArabic : nameInArabic // ignore: cast_nullable_to_non_nullable
as String,isOther: null == isOther ? _self.isOther : isOther // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
