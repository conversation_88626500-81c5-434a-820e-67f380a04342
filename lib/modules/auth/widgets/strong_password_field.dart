import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';

class PasswordFieldWithStrength extends StatefulWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator;

  const PasswordFieldWithStrength({
    super.key,
    required this.controller,
    this.validator,
  });

  @override
  State<PasswordFieldWithStrength> createState() =>
      _PasswordFieldWithStrengthState();
}

class _PasswordFieldWithStrengthState extends State<PasswordFieldWithStrength> {
  bool _isPasswordVisible = false;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: widget.controller,
      builder: (context, value, _) {
        final password = value.text;
        final strength = AppValidators.checkPasswordStrength(password);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTextField(
              labelText: "${LocaleKeys.password.tr()}*",
              controller: widget.controller,
              obscureText: !_isPasswordVisible,
              textInputAction: TextInputAction.next,
              validator: widget.validator,
              inputFormatters: InputFormatters.passwordFormatters(),
              helperText: password.isEmpty
                  ? LocaleKeys.passwordHelperText.tr(
                      namedArgs: {
                        'minLength': '8',
                        'upperCaseCount': '1',
                        'lowerCaseCount': '1',
                        'numberCount': '1',
                        'specialCharacterCount': '1',
                      },
                    )
                  : null,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              suffixIcon: GestureDetector(
                onTap: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
                child: _isPasswordVisible
                    ? Assets.icons.eye.svg(
                        width: 24.w,
                        height: 24.w,
                        fit: BoxFit.scaleDown,
                        colorFilter: ColorFilter.mode(
                          AppColors.dark.shade300,
                          BlendMode.srcIn,
                        ),
                      )
                    : Assets.icons.eyeClosed.svg(
                        width: 24.w,
                        height: 24.w,
                        fit: BoxFit.scaleDown,
                        colorFilter: ColorFilter.mode(
                          AppColors.dark.shade300,
                          BlendMode.srcIn,
                        ),
                      ),
              ),
            ),
            if (password.isNotEmpty) ...[
              2.h.heightBox,
              _buildPasswordStrengthIndicators(strength),
              // 4.h.heightBox,
            ],
          ],
        );
      },
    );
  }

  Widget _buildPasswordStrengthIndicators(Map<String, bool> strength) {
    return Wrap(
      spacing: 8.w,
      //runSpacing: 8.h,
      children: [
        _buildStrengthIndicator(
          label: '8 characters',
          isValid: strength['hasMinLength'] ?? false,
        ),
        _buildStrengthIndicator(
          label: '1 number',
          isValid: strength['hasNumber'] ?? false,
        ),
        _buildStrengthIndicator(
          label: '1 lowercase',
          isValid: strength['hasLowercase'] ?? false,
        ),
        _buildStrengthIndicator(
          label: '1 uppercase',
          isValid: strength['hasUppercase'] ?? false,
        ),
        _buildStrengthIndicator(
          label: '1 special character',
          isValid: strength['hasSpecialChar'] ?? false,
        ),
      ],
    );
  }

  Widget _buildStrengthIndicator({
    required String label,
    required bool isValid,
  }) {
    return Chip(
      label: Text(
        label,
        style: isValid
            ? AppTextStyles.text10.medium.success
            : AppTextStyles.text10.medium.gray,
      ),
      backgroundColor: isValid
          ? AppColors.success.alphaPercent(5)
          : AppColors.gray.alphaPercent(5),
      side: BorderSide(color: isValid ? AppColors.success : AppColors.gray),
      shape: const StadiumBorder(),
      avatar: Icon(
        isValid ? Icons.check : Icons.close,
        color: isValid ? AppColors.success : AppColors.gray,
        size: 16.w,
      ),
    );
  }
}
