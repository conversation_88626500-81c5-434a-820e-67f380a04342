import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

/// Dialog shown when Apple Sign-In doesn't provide email
/// Specific to Apple authentication flow only
class EmailRequiredDialog {
  /// Shows email required alert dialog for Apple Sign-In
  ///
  /// [context] - BuildContext for showing dialog
  /// [onRetry] - Optional callback when user wants to retry Apple Sign-In
  /// [onCancel] - Optional callback when user cancels
  static void show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(LocaleKeys.emailRequired.tr()),
          content: Text(LocaleKeys.emailRequiredMessage.tr()),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: Text(
                LocaleKeys.cancel.tr(),
                style: AppTextStyles.textStyle.danger,
              ),
            ),
          ],
        );
      },
    );
  }
}
