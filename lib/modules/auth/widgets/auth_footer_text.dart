import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/constants/app_constants.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

/// Common authentication footer widget that displays terms and privacy policy links
///
/// Shows "By continuing you accepting Terms of Use & Privacy Policy."
/// with clickable links that open the respective pages in browser.
class AuthFooterText extends StatelessWidget {
  const AuthFooterText({super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        text: '${LocaleKeys.byContinuingYouAccepting.tr()} ',
        style: AppTextStyles.text12.medium.gray600,
        children: [
          TextSpan(
            text: LocaleKeys.termOfUse.tr(),
            style: AppTextStyles.text12.medium.primary.underline(),
            recognizer: TapGestureRecognizer()
              ..onTap = () =>
                  UrlLauncherService.launchURL(AppConstants.termsCondition),
          ),
          TextSpan(text: ' & ', style: AppTextStyles.text12.medium.gray600),
          TextSpan(
            text: LocaleKeys.privacyPolicy.tr(),
            style: AppTextStyles.text12.medium.primary.underline(),
            recognizer: TapGestureRecognizer()
              ..onTap = () =>
                  UrlLauncherService.launchURL(AppConstants.privacyPolicy),
          ),
          TextSpan(text: '.', style: AppTextStyles.text12.medium.gray600),
        ],
      ),
    );
  }
}
