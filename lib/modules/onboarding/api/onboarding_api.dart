import 'package:dio/dio.dart';
import 'package:maisour/modules/onboarding/models/idwise_decision_log.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/shared/models/industry_type.dart';
import '../models/onboarding_status.dart';
import 'package:maisour/shared/models/country.dart';
import 'package:maisour/modules/onboarding/models/employment_detail_request.dart';
import 'package:maisour/modules/onboarding/models/tax_residency_request.dart';

part 'onboarding_api.g.dart';

@RestApi()
abstract class OnboardingApi {
  factory OnboardingApi(Dio dio, {String? baseUrl}) = _OnboardingApi;

  @GET(ApiEndpoints.onboardingStatus)
  Future<OnboardingStatus> getOnboardingStatus();

  @GET(ApiEndpoints.idWiseDecisionLog)
  Future<IdWiseDecisionLog> getIdWiseDecisionLog(
    @Query('flowId') String flowId,
  );

  @POST(ApiEndpoints.saveIdWiseLogs)
  Future<void> saveIdWiseLogs(
    @Field('flowId') String flowId,
    @Field('journeyId') String journeyId,
    @Field('action') String action,
  );

  /// Get industry types for employment information
  @GET(ApiEndpoints.industryTypes)
  Future<List<IndustryType>> getIndustryTypes();

  /// Get AML countries for dropdown
  @GET(ApiEndpoints.amlCountries)
  Future<List<Country>> getAmlCountries();

  /// Add employment detail
  @POST(ApiEndpoints.addEmploymentDetail)
  Future<void> addEmploymentDetail(@Body() EmploymentDetailRequest request);

  /// Get country of residency
  @GET(ApiEndpoints.countryOfResidency)
  Future<Map<String, String>> getCountryOfResidency();

  /// Save tax residency information
  @POST(ApiEndpoints.saveTaxResidencyInformation)
  Future<void> saveTaxResidencyInformation(@Body() TaxResidencyRequest request);
}
