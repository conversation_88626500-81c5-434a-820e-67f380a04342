import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/modules/onboarding/onboarding_service.dart';
import 'providers/verification_decision_provider.dart';
import 'package:maisour/shared/enums/verification_type.dart';

class AddressVerificationScreen extends ConsumerStatefulWidget {
  const AddressVerificationScreen({super.key});

  @override
  ConsumerState<AddressVerificationScreen> createState() =>
      _AddressVerificationScreenState();
}

class _AddressVerificationScreenState
    extends ConsumerState<AddressVerificationScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      ref
          .read(verificationDecisionProvider(VerificationType.address).notifier)
          .refresh();
    }
  }

  Future<void> _handleStartVerification(
    BuildContext context,
    WidgetRef ref,
  ) async {
    await ref
        .read(verificationDecisionProvider(VerificationType.address).notifier)
        .refresh();
    final decisionAsync = ref.read(
      verificationDecisionProvider(VerificationType.address),
    );
    decisionAsync.whenData((decision) async {
      if (decision.isRetry) {
        await _startVerification(context, ref, decision);
      } else {
        if (GoRouter.of(context).canPop()) {
          GoRouter.of(context).pop();
        }
      }
    });
  }

  Future<void> _startVerification(
    BuildContext context,
    WidgetRef ref,
    dynamic decision,
  ) async {
    try {
      final currentAppUser = ref.readCurrentUser;
      final userFullName = (decision.fullName ?? '').trim().isEmpty
          ? currentAppUser?.user.fullName
          : decision.fullName;
      final appUserId = currentAppUser?.id.toString();
      final updatedDecision = decision.copyWith(
        fullName: userFullName,
        currentUserId: appUserId,
      );

      await ref
          .read(onboardingServiceProvider)
          .startIdWiseVerification(
            context: context,
            decisionLog: updatedDecision,
            type: VerificationType.address,
            needToWait: () {
              context.showWarningToast(
                LocaleKeys.addressVerification.tr(),
                LocaleKeys.verificationInProgressWait.tr(),
              );
            },
            onJourneyCompleted: () {
              GoRouter.of(context).replaceNamed(
                RouteName.verificationInProgress.name,
                extra: VerificationType.address,
              );
            },
            onError: () {
              _startVerification(context, ref, decision);
            },
            onJourneyCancelled: () {
              context.showWarningToast(
                LocaleKeys.oops.tr(),
                LocaleKeys.verificationJourneyCancelled.tr(),
              );
            },
            onJourneyBlocked: () {
              context.showWarningToast(
                LocaleKeys.oops.tr(),
                LocaleKeys.verificationJourneyBlocked.tr(),
              );
            },
          );
    } catch (error) {
      debugPrint(
        'AddressVerificationScreen: Exception during verification: $error',
      );
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.verifyYourAddress.tr(),
                style: AppTextStyles.text20.bold.dark900,
              ),
              8.h.heightBox,
              Text(
                LocaleKeys.addressVerificationDesc.tr(),
                style: AppTextStyles.text14.medium.dark300,
              ),
              12.h.heightBox,
              Expanded(
                child: Center(
                  child: Assets.images.utilityBillInstruction.image(
                    width: 320.w,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              12.h.heightBox,
              Consumer(
                builder: (context, ref, child) {
                  final decisionAsync = ref.watch(
                    verificationDecisionProvider(VerificationType.address),
                  );
                  return AppButton(
                    text: LocaleKeys.startVerification.tr(),
                    isLoading: decisionAsync.isLoading,
                    onPressed: () => _handleStartVerification(context, ref),
                  );
                },
              ),
              20.h.heightBox,
              const RegulatedByDsfa(),
              8.h.heightBox,
            ],
          ),
        ),
      ),
    );
  }
}
