import 'dart:async';
import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/onboarding_service.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/modules/onboarding/models/onboarding_status.dart';
import 'package:maisour/modules/onboarding/models/idwise_decision_log.dart';
import 'package:flutter/material.dart';

enum VerificationPollingStatus { idle, polling, success, failure }

class VerificationPollingState {
  final VerificationPollingStatus status;
  final int pollCount;
  final String? error;

  VerificationPollingState({
    required this.status,
    required this.pollCount,
    this.error,
  });

  VerificationPollingState copyWith({
    VerificationPollingStatus? status,
    int? pollCount,
    String? error,
  }) {
    return VerificationPollingState(
      status: status ?? this.status,
      pollCount: pollCount ?? this.pollCount,
      error: error ?? this.error,
    );
  }

  static VerificationPollingState initial() => VerificationPollingState(
    status: VerificationPollingStatus.idle,
    pollCount: 0,
  );
}

class VerificationPollingNotifier
    extends StateNotifier<VerificationPollingState> {
  final OnboardingService _service;
  final VerificationType type;
  Timer? _timer;
  bool _isPolling = false;

  VerificationPollingNotifier(this._service, this.type)
    : super(VerificationPollingState.initial()) {
    log('VerificationPollingNotifier created for type: $type');
    if (type == VerificationType.passportWithSelfie ||
        type == VerificationType.passport) {
      _startPolling();
    } else {
      Future.delayed(const Duration(seconds: 1), () {
        state = state.copyWith(status: VerificationPollingStatus.success);
      });
    }
  }

  void _startPolling() {
    if (_isPolling) {
      log('Polling already in progress, skipping');
      return;
    }

    log('Starting polling for type: $type');
    _isPolling = true;
    state = state.copyWith(
      status: VerificationPollingStatus.polling,
      pollCount: 0,
    );

    // Start polling after 10 seconds
    _timer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      log('Polling tick ${state.pollCount + 1}');

      // Increment poll count
      final newPollCount = state.pollCount + 1;
      state = state.copyWith(pollCount: newPollCount);

      log('Making API calls for tick $newPollCount');

      try {
        // Make both API calls
        final results = await Future.wait([
          _service.getOnboardingStatus(),
          _service.getIdWiseDecisionLog(type.flowId),
        ]);

        final status = results[0] as OnboardingStatus;
        final decision = results[1] as IdWiseDecisionLog;

        log(
          'API results - isPOIComplete: ${status.isPOIComplete}, finalDecision: ${decision.finalDecision}, isRetry: ${decision.isRetry}',
        );

        // Check for success
        if (status.isPOIComplete) {
          log('✅ Polling SUCCESS: isPOIComplete is true');
          _stopPolling();
          await _service.clearLastIdWiseVerifiedTime();
          state = state.copyWith(status: VerificationPollingStatus.success);
          return;
        }

        // Check for failure
        if (decision.finalDecision != 'Complete' && decision.isRetry == true) {
          log('❌ Polling FAILURE: finalDecision not Complete and isRetry true');
          _stopPolling();
          await _service.clearLastIdWiseVerifiedTime();
          state = state.copyWith(status: VerificationPollingStatus.failure);
          return;
        }

        // Check for timeout
        if (newPollCount >= 6) {
          log('⏰ Polling TIMEOUT: reached max attempts (6)');
          _stopPolling();
          await _service.clearLastIdWiseVerifiedTime();
          state = state.copyWith(status: VerificationPollingStatus.failure);
          return;
        }

        log('⏳ Polling continues... attempt $newPollCount/6');
      } catch (e) {
        log('❌ Polling ERROR: $e');
        if (newPollCount >= 6) {
          _stopPolling();
          await _service.clearLastIdWiseVerifiedTime();
          state = state.copyWith(
            status: VerificationPollingStatus.failure,
            error: e.toString(),
          );
        }
      }
    });
  }

  void _stopPolling() {
    log('Stopping polling');
    _isPolling = false;
    _timer?.cancel();
    _timer = null;
  }

  /// Manually stop polling (for cleanup)
  void stopPolling() {
    _stopPolling();
  }

  /// Reset the polling state
  void reset() {
    _stopPolling();
    state = VerificationPollingState.initial();
  }

  @override
  void dispose() {
    log('VerificationPollingNotifier disposing');
    _stopPolling();
    super.dispose();
  }
}

class VerificationPollingArgs {
  final BuildContext context;
  final VerificationType type;
  VerificationPollingArgs(this.context, this.type);
}

// Simple provider without family - we'll use a single instance
final verificationPollingProvider = StateNotifierProvider.autoDispose
    .family<
      VerificationPollingNotifier,
      VerificationPollingState,
      VerificationType
    >((ref, type) {
      final service = ref.read(onboardingServiceProvider);
      return VerificationPollingNotifier(service, type);
    }, name: 'verificationPollingProvider');
