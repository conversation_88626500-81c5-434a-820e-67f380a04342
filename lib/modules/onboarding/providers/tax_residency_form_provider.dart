import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/models/country.dart';
import '../models/tax_residency_form_state.dart';
import 'onboarding_api_provider.dart';

class TaxResidencyFormNotifier
    extends AutoDisposeAsyncNotifier<TaxResidencyFormState> {
  @override
  Future<TaxResidencyFormState> build() async {
    final onboardingApi = ref.read(onboardingApiProvider);
    try {
      final response = await onboardingApi.getCountryOfResidency();
      final country = response['countryOfResidency'];
      return TaxResidencyFormState(countryOfResidency: country ?? '');
    } catch (e) {
      return TaxResidencyFormState();
    }
  }

  void setSelectedCountryOfResidency(Country? value) => state = AsyncData(
    state.value!.copyWith(selectedCountryOfResidency: value),
  );
  void setHasTin(YesNoNa? value) =>
      state = AsyncData(state.value!.copyWith(hasTin: value, tinNumber: ''));
  void setTinNumber(String value) =>
      state = AsyncData(state.value!.copyWith(tinNumber: value));
  void setIsResidentOtherCountry(YesNoNa? value) => state = AsyncData(
    state.value!.copyWith(
      isResidentOtherCountry: value,
      otherTinNumber: '',
      selectedAdditionalTaxResidencyCountry: null,
    ),
  );
  void setSelectedAdditionalTaxResidencyCountry(Country? value) =>
      state = AsyncData(
        state.value!.copyWith(selectedAdditionalTaxResidencyCountry: value),
      );
  void setOtherTinNumber(String value) =>
      state = AsyncData(state.value!.copyWith(otherTinNumber: value));
}

final taxResidencyFormProvider =
    AutoDisposeAsyncNotifierProvider<
      TaxResidencyFormNotifier,
      TaxResidencyFormState
    >(TaxResidencyFormNotifier.new);
