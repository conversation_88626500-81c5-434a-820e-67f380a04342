import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/onboarding_service.dart';
import 'package:maisour/modules/onboarding/models/idwise_decision_log.dart';
import 'package:maisour/shared/enums/verification_type.dart';

class VerificationDecisionNotifier
    extends
        AutoDisposeFamilyAsyncNotifier<IdWiseDecisionLog, VerificationType> {
  @override
  Future<IdWiseDecisionLog> build(VerificationType type) async {
    final onboardingService = ref.read(onboardingServiceProvider);
    return onboardingService.getIdWiseDecisionLog(type.flowId);
  }

  /// Refresh the decision log
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final onboardingService = ref.read(onboardingServiceProvider);
      return onboardingService.getIdWiseDecisionLog(arg.flowId);
    });
  }

  /// Retry the API call if it failed
  Future<void> retry() async {
    state = await AsyncValue.guard(() async {
      final onboardingService = ref.read(onboardingServiceProvider);
      return onboardingService.getIdWiseDecisionLog(arg.flowId);
    });
  }
}

final verificationDecisionProvider =
    AutoDisposeAsyncNotifierProvider.family<
      VerificationDecisionNotifier,
      IdWiseDecisionLog,
      VerificationType
    >(
      () => VerificationDecisionNotifier(),
      name: 'verificationDecisionProvider',
    );
