import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/providers/onboarding_api_provider.dart';
import 'package:maisour/shared/models/industry_type.dart';

/// Provider for fetching industry types from the API
/// Caches the result to avoid multiple API calls
final industryTypesProvider = FutureProvider.autoDispose<List<IndustryType>>((
  ref,
) async {
  final onboardingApi = ref.watch(onboardingApiProvider);
  return await onboardingApi.getIndustryTypes();
}, name: 'industryTypesProvider');
