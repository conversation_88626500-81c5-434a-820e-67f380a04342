import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/api/onboarding_api.dart';
import 'package:maisour/modules/onboarding/models/tax_residency_request.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:dio/dio.dart';

/// Retrofit API provider for onboarding module
final onboardingApiProvider = AutoDisposeProvider<OnboardingApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return OnboardingApi(dio);
}, name: 'onboardingApiProvider');

/// Provider for saving tax residency information
final saveTaxResidencyProvider =
    AutoDisposeAsyncNotifierProvider<SaveTaxResidencyNotifier, void>(
      () => SaveTaxResidencyNotifier(),
    );

/// Notifier for saving tax residency information
class SaveTaxResidencyNotifier extends AutoDisposeAsyncNotifier<void>
    with DioExceptionMapper {
  @override
  Future<void> build() async {
    // Initial state - no operation
  }

  /// Save tax residency information
  Future<void> saveTaxResidency(TaxResidencyRequest request) async {
    state = const AsyncValue.loading();
    try {
      final api = ref.read(onboardingApiProvider);
      await api.saveTaxResidencyInformation(request);
      state = const AsyncValue.data(null);
    } catch (e, st) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, st);
        state = AsyncValue.error(failure, st);
      } else {
        state = AsyncValue.error(Failure(message: e.toString()), st);
      }
    }
  }
}
