import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/models/employment_detail_request.dart';
import 'package:maisour/modules/onboarding/providers/onboarding_api_provider.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:dio/dio.dart';

class EmploymentSubmitNotifier extends AutoDisposeAsyncNotifier<bool>
    with DioExceptionMapper {
  @override
  Future<bool> build() async => false;

  Future<void> submit(EmploymentDetailRequest request) async {
    state = const AsyncValue.loading();
    try {
      await ref.read(onboardingApiProvider).addEmploymentDetail(request);
      state = const AsyncValue.data(true);
    } catch (e, st) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, st);
        state = AsyncValue.error(failure, st);
      } else {
        state = AsyncValue.error(Failure(message: e.toString()), st);
      }
    }
  }
}

final employmentSubmitProvider =
    AutoDisposeAsyncNotifierProvider<EmploymentSubmitNotifier, bool>(
      EmploymentSubmitNotifier.new,
      name: 'employmentSubmitProvider',
    );
