import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:idwise_flutter_sdk/idwise.dart';
import 'package:maisour/config/app_environment.dart';
import 'package:maisour/modules/onboarding/api/onboarding_api.dart';
import 'package:maisour/modules/onboarding/models/onboarding_status.dart';
import 'package:maisour/modules/onboarding/providers/onboarding_api_provider.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';
import 'package:flutter/material.dart';
import 'package:maisour/modules/onboarding/models/idwise_decision_log.dart';

final onboardingServiceProvider = Provider.autoDispose<OnboardingService>((
  ref,
) {
  final onboardingApi = ref.watch(onboardingApiProvider);
  final sharedPrefsManager = ref.watch(sharedPrefsManagerProvider);
  return OnboardingService(onboardingApi, sharedPrefsManager);
}, name: 'onboardingServiceProvider');

class OnboardingService {
  final OnboardingApi _onboardingApi;
  final SharedPrefsManager _prefs;
  bool _isIdWiseInitialized = false;

  OnboardingService(this._onboardingApi, this._prefs);

  /// Get onboarding status from API
  /// Throws DioException on error - provider will handle with DioExceptionMapper
  Future<OnboardingStatus> getOnboardingStatus() async {
    return await _onboardingApi.getOnboardingStatus();
  }

  /// Returns true if user can start a new journey, false if must wait.
  bool canStartIdWiseJourney() {
    final last = _prefs.lastVerificationTime;
    if (last == null) return true;
    return DateTime.now().difference(last).inSeconds >= 120;
  }

  /// Call this after a successful journey
  Future<void> setLastIdWiseVerifiedNow() async {
    await _prefs.setLastVerificationTime(DateTime.now());
  }

  /// Main method to start IDWise verification for passport/address
  Future<void> startIdWiseVerification({
    required BuildContext context,
    required VerificationType type,
    required IdWiseDecisionLog decisionLog,
    required VoidCallback needToWait,
    required VoidCallback onJourneyCompleted,
    required VoidCallback onError,
    required VoidCallback onJourneyCancelled,
    required VoidCallback onJourneyBlocked,
  }) async {
    if (!canStartIdWiseJourney()) {
      // wait for 2 minutes
      needToWait();
      return;
    }

    if (decisionLog.journeyId != '-1' && decisionLog.journeyId.isNotEmpty) {
      resumeIdWiseJourney(
        context: context,
        type: type,
        decisionLog: decisionLog,
        onJourneyCompleted: onJourneyCompleted,
        onError: onError,
        onJourneyCancelled: onJourneyCancelled,
        onJourneyBlocked: onJourneyBlocked,
      );
    } else {
      startIdWiseJourney(
        context: context,
        type: type,
        decisionLog: decisionLog,
        onJourneyCompleted: onJourneyCompleted,
        onError: onError,
        onJourneyCancelled: onJourneyCancelled,
        onJourneyBlocked: onJourneyBlocked,
      );
    }
  }

  void startIdWiseJourney({
    required BuildContext context,
    required VerificationType type,
    required IdWiseDecisionLog decisionLog,
    required VoidCallback onError,
    required VoidCallback onJourneyCancelled,
    required VoidCallback onJourneyCompleted,
    required VoidCallback onJourneyBlocked,
  }) {
    log('Starting IDWise journey for type: $type');
    Map<String, String> applicantDetails = {"full_name": decisionLog.fullName};
    IDWise.startJourney(
      flowId: type.flowId,
      referenceNo: decisionLog.currentUserId,
      locale: context.locale.languageCode,
      applicantDetails: applicantDetails,
      journeyCallbacks: setupIdWiseCallbacks(
        context,
        type,
        onError,
        onJourneyCancelled,
        onJourneyCompleted,
        onJourneyBlocked,
      ),
    );
  }

  void resumeIdWiseJourney({
    required BuildContext context,
    required VerificationType type,
    required IdWiseDecisionLog decisionLog,
    required VoidCallback onError,
    required VoidCallback onJourneyCancelled,
    required VoidCallback onJourneyCompleted,
    required VoidCallback onJourneyBlocked,
  }) {
    log('Resuming IDWise journey for type: $type');
    IDWise.resumeJourney(
      type.flowId,
      decisionLog.journeyId,
      context.locale.languageCode,
      setupIdWiseCallbacks(
        context,
        type,
        onError,
        onJourneyCancelled,
        onJourneyCompleted,
        onJourneyBlocked,
      ),
    );
  }

  /// For clear the last verification time
  Future<void> clearLastIdWiseVerifiedTime() async {
    await _prefs.clearLastVerificationTime();
  }

  IDWiseJourneyCallbacks setupIdWiseCallbacks(
    BuildContext context,
    VerificationType type,
    VoidCallback onError,
    VoidCallback onJourneyCancelled,
    VoidCallback onJourneyCompleted,
    VoidCallback onJourneyBlocked,
  ) {
    final callbacks = IDWiseJourneyCallbacks(
      onError: (dynamic error) {
        log("IDWiseSDKCallback: onError: $error");
        onError();
      },
      onJourneyCancelled: (dynamic journeyCancelledInfo) {
        log("IDWiseSDKCallback: onJourneyCancelled: $journeyCancelledInfo");
        // Save IdWise Journey Log in Backend.
        saveIdWiseLogs(
          type.flowId,
          journeyCancelledInfo['journeyId'],
          'Journey Cancelled',
        );
        onJourneyCancelled();
      },
      onJourneyCompleted: (dynamic journeyCompletedInfo) {
        log("IDWiseSDKCallback: onJourneyCompleted: $journeyCompletedInfo");
        // Set last verification time to now.
        setLastIdWiseVerifiedNow();
        // Save IdWise Journey Log in Backend.
        saveIdWiseLogs(
          type.flowId,
          journeyCompletedInfo['journeyId'],
          'Journey Completed',
        );
        onJourneyCompleted();
      },
      onJourneyResumed: (dynamic journeyResumedInfo) {
        log("IDWiseSDKCallback: onJourneyResumed: $journeyResumedInfo");
        // Save IdWise Journey Log in Backend.
        saveIdWiseLogs(
          type.flowId,
          journeyResumedInfo['journeyId'],
          'Journey Resumed',
        );
      },
      onJourneyStarted: (dynamic journeyStartedInfo) {
        log("IDWiseSDKCallback: onJourneyStarted: $journeyStartedInfo");
        // Save IdWise Journey Log in Backend.
        saveIdWiseLogs(
          type.flowId,
          journeyStartedInfo['journeyId'],
          'Journey Started',
        );
      },
      onJourneyBlocked: (dynamic journeyBlockedInfo) {
        log("IDWiseSDKCallback: onJourneyBlocked: $journeyBlockedInfo");
        onJourneyBlocked();
      },
    );

    return callbacks;
  }

  /// Private method to initialize the IDWise SDK
  void _initializeIdWiseSdk() {
    if (_isIdWiseInitialized) {
      log('IDWise SDK already initialized, skipping.');
      return;
    }
    try {
      log('Initializing IDWise SDK');
      IDWise.initialize(
        clientKey: AppEnvironment.idwiseClientKey,
        theme: IDWiseTheme.SYSTEM_DEFAULT,
        onError: (error) {
          log(error.toString());
        },
      );
      _isIdWiseInitialized = true;
    } catch (e, stack) {
      log('IDWise SDK initialization failed: $e\n$stack');
      // Optionally, show a toast or report to a crash service here
    }
  }

  /// Fetches the IdWiseDecisionLog and initializes SDK if retry is allowed
  Future<IdWiseDecisionLog> getIdWiseDecisionLog(String flowId) async {
    final log = await _onboardingApi.getIdWiseDecisionLog(flowId);
    if (log.isRetry) {
      _initializeIdWiseSdk(); // Fire-and-forget
    }
    return log;
  }

  /// Save IDWise logs to API
  Future<void> saveIdWiseLogs(
    String flowId,
    String journeyId,
    String action,
  ) async {
    await _onboardingApi.saveIdWiseLogs(flowId, journeyId, action);
  }
}
