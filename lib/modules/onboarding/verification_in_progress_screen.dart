import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/modules/onboarding/widgets/failure_view.dart';
import 'package:maisour/modules/onboarding/widgets/pending_view.dart';
import 'package:maisour/modules/onboarding/widgets/success_view.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'providers/verification_polling_provider.dart';

class VerificationInProgressScreen extends ConsumerWidget {
  final VerificationType verificationType;
  const VerificationInProgressScreen({
    super.key,
    required this.verificationType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pollingState = ref.watch(
      verificationPollingProvider(verificationType),
    );
    Widget child;
    switch (pollingState.status) {
      case VerificationPollingStatus.success:
        child = SuccessView(verificationType: verificationType);
        break;
      case VerificationPollingStatus.failure:
        child = FailureView(verificationType: verificationType);
        break;
      case VerificationPollingStatus.polling:
      case VerificationPollingStatus.idle:
        child = PendingView(verificationType: verificationType);
        break;
    }
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: child,
          ),
        ),
      ),
    );
  }
}
