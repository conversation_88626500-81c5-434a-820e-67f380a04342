import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/app_router.dart';
import 'widgets/onboarding_step.dart';
import 'providers/onboarding_status_provider.dart';

class OnboardingScreen extends ConsumerWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingStatusAsync = ref.watch(onboardingStatusFutureProvider);

    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: onboardingStatusAsync.when(
            // Loading state
            loading: () => AppShimmerLoader.card(
              itemCount: 4,
              itemHeight: 200,
              padding: EdgeInsets.zero,
            ),

            // Error state
            error: (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text(LocaleKeys.somethingWentWrong.tr())],
              ),
            ),

            // Success state
            data: (onboardingStatus) => Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.onboardingTitle.tr(),
                          style: AppTextStyles.text20.bold.dark900,
                        ),
                        8.h.heightBox,
                        Text(
                          LocaleKeys.onboardingSubtitle.tr(),
                          style: AppTextStyles.text14.medium.dark300,
                        ),
                        20.h.heightBox,
                        OnboardingStep(
                          number: 1,
                          title: LocaleKeys.verifyYourPassport.tr(),
                          isCompleted: onboardingStatus.isPOIComplete,
                          description: LocaleKeys.verifyYourPassportDesc.tr(),
                          image: Assets.images.passport.image(),
                        ),
                        16.h.heightBox,
                        OnboardingStep(
                          number: 2,
                          title: LocaleKeys.verifyYourAddress.tr(),
                          isCompleted: onboardingStatus.isPOAComplete,
                          description: LocaleKeys.verifyYourAddressDesc.tr(),
                          image: Assets.images.address.image(),
                        ),
                        16.h.heightBox,
                        OnboardingStep(
                          number: 3,
                          title: LocaleKeys.employmentInfo.tr(),
                          isCompleted: onboardingStatus.isEmploymentComplete,
                          description: LocaleKeys.employmentInfoDesc.tr(),
                          image: Assets.images.employmentInfo.image(),
                        ),
                        16.h.heightBox,
                        OnboardingStep(
                          number: 4,
                          title: LocaleKeys.taxResidencyInformation.tr(),
                          isCompleted: onboardingStatus.isTaxInfoComplete,
                          description: LocaleKeys.taxResidencyInformationDesc
                              .tr(),
                          image: Assets.images.taxResidencyInformation.image(),
                        ),
                      ],
                    ),
                  ),
                ),
                8.h.heightBox,
                AppButton(
                  text: LocaleKeys.startVerification.tr(),
                  onPressed: () {
                    if (!onboardingStatus.isPOIComplete) {
                      GoRouter.of(context).pushReplacementNamed(
                        RouteName.passportVerification.name,
                        extra: VerificationType.passportWithSelfie,
                      );
                    } else if (!onboardingStatus.isPOAComplete) {
                      GoRouter.of(context).pushReplacementNamed(
                        RouteName.addressVerification.name,
                      );
                    } else if (!onboardingStatus.isEmploymentComplete) {
                      GoRouter.of(context).pushReplacementNamed(
                        RouteName.employmentInformation.name,
                      );
                    } else if (!onboardingStatus.isTaxInfoComplete) {
                      GoRouter.of(context).pushReplacementNamed(
                        RouteName.taxResidencyInformation.name,
                        extra: true,
                      );
                    }
                  },
                ),
                AppButton(
                  type: ButtonType.text,
                  text: LocaleKeys.doItLater.tr(),
                  onPressed: () {
                    GoRouter.of(context).pop();
                  },
                ),
                SizedBox(height: 2.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
