import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/form_fields/country_dropdown.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/modules/onboarding/providers/tax_residency_form_provider.dart';
import 'package:maisour/modules/onboarding/providers/onboarding_api_provider.dart';
import 'package:maisour/modules/onboarding/models/tax_residency_form_state.dart';
import 'package:maisour/modules/onboarding/models/tax_residency_request.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/shared/models/failure.dart';

class TaxResidencyInformationScreen extends ConsumerStatefulWidget {
  const TaxResidencyInformationScreen({
    super.key,
    required this.fromOnboarding,
  });
  final bool fromOnboarding;

  @override
  ConsumerState<TaxResidencyInformationScreen> createState() =>
      _TaxResidencyInformationScreenState();
}

class _TaxResidencyInformationScreenState
    extends ConsumerState<TaxResidencyInformationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _tinController = TextEditingController();
  final _otherTinController = TextEditingController();

  @override
  void dispose() {
    _tinController.dispose();
    _otherTinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formAsync = ref.watch(taxResidencyFormProvider);
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: formAsync.when(
          loading: () => Center(child: AppCircularLoader.large()),
          error: (e, st) => Center(child: Text('Error: $e')),
          data: (formState) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.taxResidencyInformation.tr(),
                          style: AppTextStyles.text20.bold.dark900,
                        ),
                        8.h.heightBox,
                        Text(
                          LocaleKeys.taxResidencyInformationScreenDesc.tr(),
                          style: AppTextStyles.text14.medium.dark300,
                        ),
                        24.h.heightBox,

                        formState.countryOfResidency.isNotEmpty
                            ? Row(
                                children: [
                                  Text(
                                    "${LocaleKeys.countryOfResidence.tr()}: ",
                                    style: AppTextStyles.text14.medium.dark300,
                                  ),

                                  Text(
                                    formState.countryOfResidency,
                                    style: AppTextStyles.text14.bold.dark900,
                                  ),
                                ],
                              )
                            : CountryDropdown(
                                labelText: LocaleKeys.countryOfResidence.tr(),
                                value: formState.selectedCountryOfResidency,
                                validator: (value) => AppValidators.required(
                                  LocaleKeys.countryOfResidence.tr(),
                                  value?.country,
                                ),
                                onChanged: (val) {
                                  ref
                                      .read(taxResidencyFormProvider.notifier)
                                      .setSelectedCountryOfResidency(val);
                                },
                              ),

                        24.h.heightBox,
                        if (formState.countryOfResidency.isNotEmpty ||
                            formState.selectedCountryOfResidency != null) ...[
                          Text(
                            LocaleKeys.doYouHaveATaxIdentificationNumber.tr(
                              namedArgs: {
                                'country':
                                    formState.countryOfResidency.isNotEmpty
                                    ? formState.countryOfResidency
                                    : formState
                                              .selectedCountryOfResidency
                                              ?.country ??
                                          '',
                              },
                            ),
                            style: AppTextStyles.text14.medium.dark900,
                          ),
                          RadioGroup<YesNoNa>(
                            groupValue: formState.hasTin,
                            onChanged: (val) {
                              if (val != YesNoNa.yes) {
                                _tinController.clear();
                              }
                              ref
                                  .read(taxResidencyFormProvider.notifier)
                                  .setHasTin(val);
                            },
                            child: Row(
                              children: [
                                _buildRadio(YesNoNa.yes, LocaleKeys.yes.tr()),
                                6.w.widthBox,
                                _buildRadio(YesNoNa.no, LocaleKeys.no.tr()),
                                6.w.widthBox,
                                _buildRadio(
                                  YesNoNa.notApplicable,
                                  LocaleKeys.notApplicable.tr(),
                                ),
                              ],
                            ),
                          ),
                          if (formState.hasTin == YesNoNa.yes)
                            AppTextField(
                              controller: _tinController,
                              labelText: "${LocaleKeys.tinNumber.tr()}*",
                              validator: AppValidators.tinNumber,
                              inputFormatters: InputFormatters.tinFormatters(),
                            ),
                          24.h.heightBox,
                          Text(
                            LocaleKeys.areYouAlsoAResidentInAnyOtherCountry
                                .tr(),
                          ),
                          RadioGroup(
                            groupValue: formState.isResidentOtherCountry,
                            onChanged: (val) {
                              if (val != YesNoNa.yes) {
                                _otherTinController.clear();
                              }
                              ref
                                  .read(taxResidencyFormProvider.notifier)
                                  .setIsResidentOtherCountry(val);
                            },
                            child: Row(
                              children: [
                                _buildRadio(YesNoNa.yes, LocaleKeys.yes.tr()),
                                _buildRadio(YesNoNa.no, LocaleKeys.no.tr()),
                                _buildRadio(
                                  YesNoNa.notApplicable,
                                  LocaleKeys.notApplicable.tr(),
                                ),
                              ],
                            ),
                          ),
                          if (formState.isResidentOtherCountry ==
                              YesNoNa.yes) ...[
                            CountryDropdown(
                              labelText: LocaleKeys
                                  .additionalTaxResidencyCountry
                                  .tr(),
                              value: formState
                                  .selectedAdditionalTaxResidencyCountry,
                              validator: (value) => AppValidators.required(
                                LocaleKeys.additionalTaxResidencyCountry.tr(),
                                value?.country,
                              ),
                              onChanged: ref
                                  .read(taxResidencyFormProvider.notifier)
                                  .setSelectedAdditionalTaxResidencyCountry,
                            ),
                            12.h.heightBox,
                            AppTextField(
                              controller: _otherTinController,
                              labelText: "${LocaleKeys.tinNumber.tr()}*",
                              validator: AppValidators.tinNumber,
                              inputFormatters: InputFormatters.tinFormatters(),
                            ),
                          ],
                          24.h.heightBox,
                        ],
                      ],
                    ),
                  ),
                ),
              ),
              // Bottom buttons section - always visible
              Column(
                children: [
                  Text(
                    LocaleKeys
                        .byContinuingYouConfirmThatTheAboveInformationIsAccurateAndCompleteToTheBestOfYourKnowledge
                        .tr(),
                    style: AppTextStyles.text12.medium.dark300,
                    textAlign: TextAlign.center,
                  ),
                  12.h.heightBox,
                  Consumer(
                    builder: (context, ref, child) {
                      final saveAsync = ref.watch(saveTaxResidencyProvider);

                      return AppButton(
                        text: LocaleKeys.submit.tr(),
                        isLoading: saveAsync.isLoading,
                        onPressed: () {
                          _handleSubmit(formState);
                        },
                      );
                    },
                  ),
                  8.h.heightBox,
                  AppButton(
                    type: ButtonType.text,
                    text: LocaleKeys.doItLater.tr(),
                    onPressed: () {
                      GoRouter.of(context).pop();
                    },
                  ),
                  2.h.heightBox,
                ],
              ),
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

  Widget _buildRadio(YesNoNa value, String label) {
    return Row(
      children: [
        Radio<YesNoNa>(value: value, activeColor: AppColors.primary),
        Text(label, style: AppTextStyles.text14.medium.dark900),
      ],
    );
  }

  /// Handle form submission
  Future<void> _handleSubmit(TaxResidencyFormState formState) async {
    if (_formKey.currentState?.validate() ?? false) {
      ref
          .read(taxResidencyFormProvider.notifier)
          .setTinNumber(_tinController.text);
      ref
          .read(taxResidencyFormProvider.notifier)
          .setOtherTinNumber(_otherTinController.text);

      // Create request object
      final request = TaxResidencyRequest(
        countryOfResidency: formState.countryOfResidency.isNotEmpty
            ? formState.countryOfResidency
            : formState.selectedCountryOfResidency?.country,
        tinStatus: formState.hasTin!.apiValue,
        tinNumber: formState.hasTin == YesNoNa.yes ? _tinController.text : null,
        residentInOtherCountry: formState.isResidentOtherCountry!.apiValue,
        additionalTaxResidencyCountryId:
            formState.isResidentOtherCountry == YesNoNa.yes
            ? formState.selectedAdditionalTaxResidencyCountry?.id
            : null,
        tinNumberOtherCountry: formState.isResidentOtherCountry == YesNoNa.yes
            ? _otherTinController.text
            : null,
        atOnboardingTime: widget.fromOnboarding,
      );

      try {
        await ref
            .read(saveTaxResidencyProvider.notifier)
            .saveTaxResidency(request);
        if (!mounted) return;
        context.showSuccessToast(
          LocaleKeys.success.tr(),
          LocaleKeys.taxResidencySubmittedSuccessfully.tr(),
        );

        if (widget.fromOnboarding == true) {
          GoRouter.of(
            context,
          ).pushReplacementNamed(RouteName.phoneVerification.name);
        } else {
          GoRouter.of(context).pop();
          ref.notifierCurrentUser.refreshUserFromServer();
        }
      } catch (error) {
        if (!mounted) return;
        final failure = error is Failure
            ? error
            : Failure(message: error.toString());
        context.showErrorToast('Error', failure.message);
      }
    }
  }
}
