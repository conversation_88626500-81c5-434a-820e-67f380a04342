import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/modules/onboarding/models/idwise_decision_log.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/onboarding_service.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'providers/verification_decision_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';

class PassportVerificationScreen extends ConsumerStatefulWidget {
  const PassportVerificationScreen({super.key, required this.verificationType});
  final VerificationType verificationType;

  @override
  ConsumerState<PassportVerificationScreen> createState() =>
      _PassportVerificationScreenState();
}

class _PassportVerificationScreenState
    extends ConsumerState<PassportVerificationScreen>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    debugPrint('PassportVerificationScreen: initState');

    // Call API when screen starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(verificationDecisionProvider(widget.verificationType).notifier)
          .refresh();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    debugPrint('PassportVerificationScreen: dispose');
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint(
      'PassportVerificationScreen: AppLifecycleState changed to $state',
    );
    super.didChangeAppLifecycleState(state);

    // Only call API on resume
    if (state == AppLifecycleState.resumed) {
      debugPrint('PassportVerificationScreen: App resumed - calling API');
      ref
          .read(verificationDecisionProvider(widget.verificationType).notifier)
          .refresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('PassportVerificationScreen: build');

    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.verifyYourPassport.tr(),
                style: AppTextStyles.text20.bold.dark900,
              ),
              8.h.heightBox,
              Text(
                LocaleKeys.passportSubtitle.tr(),
                style: AppTextStyles.text14.medium.dark300,
              ),
              12.h.heightBox,
              Expanded(
                child: Center(
                  child: Assets.images.passportInstruction.image(
                    width: 320.w,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              12.h.heightBox,
              // Consumer for attempt count and button only
              Consumer(
                builder: (context, ref, child) {
                  final passportDecisionAsync = ref.watch(
                    verificationDecisionProvider(widget.verificationType),
                  );

                  return passportDecisionAsync.when(
                    // Loading state
                    loading: () => Column(
                      children: [
                        Center(
                          child: Text(
                            LocaleKeys.passportAttempt.tr(
                              namedArgs: {'attempt': '...'},
                            ),
                            style: AppTextStyles.text12.medium.dark300,
                          ),
                        ),
                        8.h.heightBox,
                        AppButton(
                          text: LocaleKeys.startVerification.tr(),
                          isLoading: true,
                          onPressed: () {},
                        ),
                      ],
                    ),

                    // Error state
                    error: (error, stackTrace) => Column(
                      children: [
                        Center(
                          child: Text(
                            LocaleKeys.somethingWentWrong.tr(),
                            style: AppTextStyles.text12.medium.dark300,
                          ),
                        ),
                      ],
                    ),

                    // Success state
                    data: (decision) {
                      if (!decision.isRetry) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (GoRouter.of(context).canPop()) {
                            GoRouter.of(context).pop();
                          }
                        });
                        return const SizedBox.shrink();
                      }

                      // Show attempt count and button
                      return Column(
                        children: [
                          Center(
                            child: Text(
                              LocaleKeys.passportAttempt.tr(
                                namedArgs: {'attempt': '${decision.attempt}/3'},
                              ),
                              style: AppTextStyles.text12.medium.dark300,
                            ),
                          ),
                          8.h.heightBox,
                          AppButton(
                            text: LocaleKeys.startVerification.tr(),
                            isLoading: false,
                            onPressed: () {
                              if (decision.attempt >= 3) {
                                context.showWarningToast(
                                  LocaleKeys.tooManyAttempts.tr(),
                                  LocaleKeys.tooManyAttemptsDesc.tr(),
                                );
                              } else {
                                _handleStartVerification(
                                  context,
                                  ref,
                                  decision,
                                );
                              }
                            },
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
              20.h.heightBox,
              const RegulatedByDsfa(),
              8.h.heightBox,
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleStartVerification(
    BuildContext context,
    WidgetRef ref,
    dynamic decision,
  ) async {
    debugPrint('PassportVerificationScreen: Start verification button pressed');

    try {
      // Call API again and show loader on button
      await ref
          .read(verificationDecisionProvider(widget.verificationType).notifier)
          .refresh();

      // Get updated decision after API call
      final updatedDecision = ref.read(
        verificationDecisionProvider(widget.verificationType),
      );

      updatedDecision.whenData((newDecision) {
        if (newDecision.isRetry) {
          // Call _startVerification method after API success
          _startVerification(context, ref, newDecision);
        }
      });
    } catch (error) {
      debugPrint('PassportVerificationScreen: Error fetching decision: $error');
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }

  Future<void> _startVerification(
    BuildContext context,
    WidgetRef ref,
    IdWiseDecisionLog decision,
  ) async {
    if (decision.isRetry) {
      debugPrint(
        'PassportVerificationScreen: Starting verification - attempt ${decision.attempt}',
      );
      try {
        final currentAppUser = ref.readCurrentUser;
        final userFullName = currentAppUser?.user.fullName ?? '';
        final appUserId = currentAppUser?.id.toString();
        final updatedDecision = decision.copyWith(
          fullName: userFullName,
          currentUserId: appUserId,
        );

        await ref
            .read(onboardingServiceProvider)
            .startIdWiseVerification(
              context: context,
              decisionLog: updatedDecision,
              type: VerificationType.passportWithSelfie,
              needToWait: () {
                context.showWarningToast(
                  LocaleKeys.passportVerification.tr(),
                  LocaleKeys.verificationInProgressWait.tr(),
                );
              },
              onJourneyCompleted: () {
                GoRouter.of(context).replaceNamed(
                  RouteName.verificationInProgress.name,
                  extra: VerificationType.passportWithSelfie,
                );
              },
              onError: () {
                _startVerification(context, ref, decision);
              },
              onJourneyCancelled: () {
                context.showWarningToast(
                  LocaleKeys.oops.tr(),
                  LocaleKeys.verificationJourneyCancelled.tr(),
                );
              },
              onJourneyBlocked: () {
                context.showWarningToast(
                  LocaleKeys.oops.tr(),
                  LocaleKeys.verificationJourneyBlocked.tr(),
                );
              },
            );
      } catch (error) {
        debugPrint(
          'PassportVerificationScreen: Exception during verification: $error',
        );
        if (context.mounted) {
          context.showErrorToast(
            LocaleKeys.oops.tr(),
            LocaleKeys.somethingWentWrong.tr(),
          );
        }
      }
    }
  }
}
