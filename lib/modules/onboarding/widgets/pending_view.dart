import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/app_circular_loader.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/enums/verification_type.dart';

class PendingView extends StatelessWidget {
  final VerificationType verificationType;
  const PendingView({super.key, required this.verificationType});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        AppCircularLoader(),
        8.h.heightBox,
        Text(
          LocaleKeys.verificationInProgress.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        8.h.heightBox,
        Text(
          LocaleKeys.verificationInProgressDesc.tr(),
          style: AppTextStyles.text14.dark300,
        ),
      ],
    );
  }
}
