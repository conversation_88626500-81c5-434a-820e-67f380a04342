import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/modules/onboarding/widgets/verification_failure_reasons_bottomsheet.dart';

class FailureView extends StatelessWidget {
  final VerificationType verificationType;
  final VoidCallback? onRetry;
  const FailureView({super.key, required this.verificationType, this.onRetry});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.icons.failed.svg(width: 70.w),
              16.h.heightBox,
              Text(
                LocaleKeys.passportVerificationFailed.tr(),
                style: AppTextStyles.text18.bold.dark900,
              ),
              8.h.heightBox,
              Text(
                LocaleKeys.passportVerificationFailedDesc.tr(),
                style: AppTextStyles.text14.dark300,
                textAlign: TextAlign.center,
              ),
              16.h.heightBox,
              AppButton(
                onPressed: () {
                  VerificationFailureReasonsBottomSheet.show(context);
                },
                text: LocaleKeys.viewDetails.tr(),
                type: ButtonType.text,
                textColor: AppColors.primary,
              ),
            ],
          ),
        ),
        AppButton(
          text: LocaleKeys.tryAgain.tr(),
          onPressed: () {
            if (verificationType == VerificationType.passport) {
              GoRouter.of(context).pushReplacementNamed(
                RouteName.passportVerification.name,
                extra: VerificationType.passport,
              );
            } else if (verificationType ==
                VerificationType.passportWithSelfie) {
              GoRouter.of(context).pushReplacementNamed(
                RouteName.passportVerification.name,
                extra: VerificationType.passportWithSelfie,
              );
            }
          },
        ),
        2.h.heightBox,
        AppButton(
          type: ButtonType.text,
          text: LocaleKeys.doItLater.tr(),
          onPressed: () {
            GoRouter.of(context).pop();
          },
        ),
        2.h.heightBox,
      ],
    );
  }
}
