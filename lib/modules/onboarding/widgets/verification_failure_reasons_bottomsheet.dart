import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';

class VerificationFailureReasonsBottomSheet extends StatelessWidget {
  const VerificationFailureReasonsBottomSheet({super.key});

  static Future<void> show(BuildContext context) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const VerificationFailureReasonsBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          LocaleKeys.possibleReasonsForVerificationFailure.tr(),
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ).paddingHorizontal(16.w),
        12.h.heightBox,
        Divider(color: AppColors.gray.shade100, height: 1),
        4.h.heightBox,
        Html(
          data: LocaleKeys.possibleReasonsForVerificationFailureDesc.tr(),
        ).paddingHorizontal(16.w),
        8.h.heightBox,
      ],
    );
  }
}
