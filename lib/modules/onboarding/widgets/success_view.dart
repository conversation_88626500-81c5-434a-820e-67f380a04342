import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/enums/verification_type.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class SuccessView extends StatelessWidget {
  final VerificationType verificationType;
  const SuccessView({super.key, required this.verificationType});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.icons.successFilled.svg(width: 70.w),
              16.h.heightBox,
              Text(title, style: AppTextStyles.text18.bold.dark900),
              8.h.heightBox,
              Text(
                description,
                style: AppTextStyles.text14.dark300,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        AppButton(
          onPressed: () {
            if (verificationType == VerificationType.passport) {
              GoRouter.of(context).pop();
            } else if (verificationType ==
                VerificationType.passportWithSelfie) {
              GoRouter.of(
                context,
              ).pushReplacementNamed(RouteName.addressVerification.name);
            } else if (verificationType == VerificationType.address) {
              GoRouter.of(
                context,
              ).pushReplacementNamed(RouteName.employmentInformation.name);
            }
          },
          text: LocaleKeys.continueText.tr(),
        ),
        2.h.heightBox,
        AppButton(
          type: ButtonType.text,
          text: LocaleKeys.doItLater.tr(),
          onPressed: () {
            GoRouter.of(context).pop();
          },
        ),
        2.h.heightBox,
      ],
    );
  }

  String get title {
    switch (verificationType) {
      case VerificationType.passport:
        return LocaleKeys.passportVerificationCompleted.tr();
      case VerificationType.passportWithSelfie:
        return LocaleKeys.passportVerificationCompleted.tr();
      case VerificationType.address:
        return LocaleKeys.addressVerificationCompleted.tr();
    }
  }

  String get description {
    switch (verificationType) {
      case VerificationType.passport:
        return '';
      case VerificationType.passportWithSelfie:
        return LocaleKeys.passportVerificationCompletedDesc.tr();
      case VerificationType.address:
        return LocaleKeys.addressVerificationCompletedDesc.tr();
    }
  }
}
