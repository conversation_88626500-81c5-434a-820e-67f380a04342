import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class OnboardingStep extends StatelessWidget {
  final int number;
  final String title;
  final bool isCompleted;
  final String description;
  final Widget image;

  const OnboardingStep({
    super.key,
    required this.number,
    required this.title,
    required this.isCompleted,
    required this.description,
    required this.image,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppColors.success.alphaPercent(5)
                      : AppColors.warning.alphaPercent(5),
                ),
                width: double.infinity,
                height: 72.w,
                alignment: AlignmentDirectional.centerEnd,
                child: image,
              ),
              20.h.heightBox,
              Row(
                children: [
                  Text(title, style: AppTextStyles.text16.bold.dark900),
                  8.w.widthBox,
                  Container(
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? AppColors.success.alphaPercent(10)
                          : AppColors.warning.alphaPercent(10),
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    padding: EdgeInsetsGeometry.symmetric(
                      horizontal: 8.w,
                      vertical: 4.w,
                    ),
                    child: Center(
                      child: Text(
                        isCompleted
                            ? LocaleKeys.completed.tr()
                            : LocaleKeys.pending.tr(),
                        style: isCompleted
                            ? AppTextStyles.text10.semiBold.success
                            : AppTextStyles.text10.semiBold.warning,
                      ),
                    ),
                  ),
                ],
              ).paddingHorizontal(16.w),
              8.h.heightBox,
              Text(
                description,
                style: AppTextStyles.text12.medium.dark300,
              ).paddingHorizontal(16.w),
              16.w.heightBox,
            ],
          ),
        ),
        PositionedDirectional(
          start: 16.w,
          top: 58.w,
          child: CircleAvatar(
            radius: 14.w,
            backgroundColor: AppColors.gray.shade700,
            child: Text(
              number.toString(),
              style: AppTextStyles.text16.bold.white,
            ),
          ),
        ),
      ],
    );
  }
}
