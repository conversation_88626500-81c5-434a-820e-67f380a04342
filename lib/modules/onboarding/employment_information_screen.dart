import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/enums/employment_status.dart';
import 'package:maisour/shared/enums/annual_income.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_dropdown_field.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/models/industry_type.dart';
import 'package:maisour/shared/models/country.dart';
import 'package:maisour/shared/widgets/form_fields/industry_type_dropdown.dart';
import 'package:maisour/shared/widgets/form_fields/country_dropdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/onboarding/models/employment_detail_request.dart';
import 'package:maisour/modules/onboarding/providers/employment_submit_provider.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/models/failure.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';

class EmploymentInformationScreen extends ConsumerStatefulWidget {
  const EmploymentInformationScreen({super.key});

  @override
  ConsumerState<EmploymentInformationScreen> createState() =>
      _EmploymentInformationScreenState();
}

class _EmploymentInformationScreenState
    extends ConsumerState<EmploymentInformationScreen> {
  /// Form key for validation
  final _formKey = GlobalKey<FormState>();

  /// Selected employment status
  EmploymentStatus? _selectedEmploymentStatus;

  /// Business name controller for employed users
  final _businessNameController = TextEditingController();

  /// Selected industry type for employed users
  IndustryType? _selectedIndustryType;

  /// Role controller for employed users
  final _roleController = TextEditingController();

  /// Work address controller for employed users
  final _workAddressController = TextEditingController();

  /// Selected annual income for employed users
  AnnualIncome? _selectedAnnualIncome;

  /// Selected country for employed users
  Country? _selectedCountryOfBirth;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _prefillData();
    });
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _roleController.dispose();
    _workAddressController.dispose();
    super.dispose();
  }

  void _prefillData() {
    final currentUser = ref.readCurrentUser;
    if (currentUser != null && currentUser.workStatus != null) {
      setState(() {
        _selectedEmploymentStatus = currentUser.workStatus;

        if (_selectedEmploymentStatus == EmploymentStatus.employed) {
          _businessNameController.text = currentUser.employer ?? '';
          _roleController.text = currentUser.roleLov ?? '';
          _workAddressController.text = currentUser.workAddress ?? '';
        }

        if (currentUser.annualIncome != null) {
          _selectedAnnualIncome = currentUser.annualIncome;
        }

        if (currentUser.industryLov != null) {
          _selectedIndustryType = IndustryType(
            id: 0,
            industryType: currentUser.industryLov!,
            industryTypeInArabic: currentUser.industryLov!,
          );
        }

        if (currentUser.countryOfBirth != null) {
          _selectedCountryOfBirth = Country(
            id: 0,
            country: currentUser.countryOfBirth!,
            countryInArabic: currentUser.countryOfBirth!,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Text(
              LocaleKeys.employmentInfo.tr(),
              style: AppTextStyles.text20.bold.dark900,
            ),
            8.h.heightBox,
            Text(
              LocaleKeys.employmentInfoDesc.tr(),
              style: AppTextStyles.text14.medium.dark300,
            ),
            10.h.heightBox,

            // Form fields section
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      10.w.heightBox,
                      AppDropdownField<EmploymentStatus>(
                        labelText: '${LocaleKeys.employmentStatus.tr()}*',
                        itemAsString: (item) => item.title,
                        items: EmploymentStatus.values,
                        showSearchBox: false,
                        selectedItem: _selectedEmploymentStatus,
                        onChanged: (value) {
                          setState(() {
                            _selectedEmploymentStatus = value;
                            // Clear all employed-related fields when employment status changes
                            if (value != EmploymentStatus.employed) {
                              _businessNameController.clear();
                              _selectedIndustryType = null;
                              _roleController.clear();
                              _workAddressController.clear();
                            }
                          });
                        },
                        validator: (value) => AppValidators.required(
                          LocaleKeys.employmentStatus.tr(),
                          value?.value,
                        ),
                      ),
                      16.h.heightBox,

                      // Conditional fields for employed users
                      if (_selectedEmploymentStatus ==
                          EmploymentStatus.employed) ...[
                        AppTextField(
                          controller: _businessNameController,
                          labelText: '${LocaleKeys.companyName.tr()}*',
                          validator: (value) => AppValidators.required(
                            LocaleKeys.companyName.tr(),
                            value,
                          ),
                          inputFormatters: InputFormatters.maxLengthFormatters(
                            maxLength: 50,
                          ),
                          textCapitalization: TextCapitalization.words,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                        ),

                        16.h.heightBox,

                        IndustryTypeDropdown(
                          value: _selectedIndustryType,
                          onChanged: (val) => _selectedIndustryType = val,
                        ),
                        16.h.heightBox,

                        AppTextField(
                          controller: _roleController,
                          labelText: '${LocaleKeys.role.tr()}*',
                          validator: (value) => AppValidators.required(
                            LocaleKeys.role.tr(),
                            value,
                          ),
                          inputFormatters: InputFormatters.maxLengthFormatters(
                            maxLength: 50,
                          ),
                          textCapitalization: TextCapitalization.words,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                        ),

                        16.h.heightBox,

                        AppTextField(
                          controller: _workAddressController,
                          labelText: '${LocaleKeys.workAddress.tr()}*',
                          validator: (value) => AppValidators.required(
                            LocaleKeys.workAddress.tr(),
                            value,
                          ),
                          inputFormatters: InputFormatters.maxLengthFormatters(
                            maxLength: 80,
                          ),
                          minLines: 3,
                          maxLines: 5,
                          textCapitalization: TextCapitalization.sentences,
                          keyboardType: TextInputType.streetAddress,
                          textInputAction: TextInputAction.next,
                        ),
                        16.h.heightBox,
                      ],

                      // Annual income dropdown for employed users
                      AppDropdownField<AnnualIncome>(
                        labelText: '${LocaleKeys.annualIncome.tr()}*',
                        itemAsString: (item) => item.title,
                        items: AnnualIncome.values,
                        selectedItem: _selectedAnnualIncome,
                        onChanged: (value) => _selectedAnnualIncome = value,
                        validator: (value) => AppValidators.required(
                          LocaleKeys.annualIncome.tr(),
                          value?.value,
                        ),
                      ),
                      16.h.heightBox,

                      CountryDropdown(
                        labelText: '${LocaleKeys.countryOfBirth.tr()}*',
                        value: _selectedCountryOfBirth,
                        onChanged: (val) => _selectedCountryOfBirth = val,
                        validator: (value) => AppValidators.required(
                          LocaleKeys.countryOfBirth.tr(),
                          value?.country,
                        ),
                        helperText: LocaleKeys.countryOfBirthHelperText.tr(),
                      ),
                      16.h.heightBox,
                    ],
                  ),
                ),
              ),
            ),

            // Bottom buttons section - always visible
            Consumer(
              builder: (context, ref, _) {
                final async = ref.watch(employmentSubmitProvider);
                ref.listen<AsyncValue<bool>>(employmentSubmitProvider, (
                  prev,
                  next,
                ) {
                  if (next is AsyncData && next.value == true) {
                    context.showSuccessToast(
                      LocaleKeys.success.tr(),
                      LocaleKeys.employmentInfoSubmittedSuccessfully.tr(),
                    );
                    if (ref.readCurrentUser?.workStatus != null) {
                      GoRouter.of(context).pop();
                      ref.notifierCurrentUser.refreshUserFromServer();
                    } else {
                      GoRouter.of(context).pushReplacementNamed(
                        RouteName.taxResidencyInformation.name,
                        extra: true,
                      );
                    }
                  } else if (next is AsyncError) {
                    final error = next.error;
                    String? errorKey;
                    String? errorMessage;
                    if (error is Failure) {
                      errorKey = error.errorKey;
                      errorMessage = error.message;
                    } else {
                      errorMessage = error.toString();
                    }
                    final (
                      title,
                      message,
                    ) = ErrorMessageHelper.getLocalizedErrorMessage(
                      errorKey: errorKey,
                      fallbackMessage: errorMessage,
                    );
                    context.showErrorToast(title, message);
                  }
                });
                return Column(
                  children: [
                    AppButton(
                      text: LocaleKeys.submit.tr(),
                      isLoading: async.isLoading,
                      onPressed: () => _handleSubmit(ref),
                    ),
                    8.h.heightBox,
                    AppButton(
                      type: ButtonType.text,
                      text: LocaleKeys.doItLater.tr(),
                      onPressed: () {
                        GoRouter.of(context).pop();
                      },
                    ),
                    2.h.heightBox,
                  ],
                );
              },
            ),
          ],
        ).paddingHorizontal(16.w),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }

  void _handleSubmit(WidgetRef ref) {
    if (_formKey.currentState?.validate() ?? false) {
      final request = EmploymentDetailRequest(
        workStatus: _selectedEmploymentStatus?.value ?? '',
        workAddress: _workAddressController.text,
        employer: _businessNameController.text,
        role: _roleController.text,
        industry: _selectedIndustryType?.industryType ?? '',
        annualIncome: _selectedAnnualIncome?.value ?? '',
        countryOfBirth: _selectedCountryOfBirth?.country ?? '',
      );
      ref.read(employmentSubmitProvider.notifier).submit(request);
    }
  }
}
