import 'package:freezed_annotation/freezed_annotation.dart';

part 'idwise_decision_log.freezed.dart';
part 'idwise_decision_log.g.dart';

@freezed
abstract class IdWiseDecisionLog with _$IdWiseDecisionLog {
  const factory IdWiseDecisionLog({
    @Default(0) int attempt,
    @Default(false) bool isRetry,
    @Default('') String journeyId,
    @Default('') String finalDecision,
    @JsonKey(name: 'full_name') @Default('') String fullName,
    @JsonKey(includeFromJson: false, includeToJson: false)
    String? currentUserId,
  }) = _IdWiseDecisionLog;

  factory IdWiseDecisionLog.fromJson(Map<String, dynamic> json) =>
      _$IdWiseDecisionLogFromJson(json);
}
