// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tax_residency_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TaxResidencyRequest {

 String? get countryOfResidency; String get tinStatus; String? get tinNumber; String get residentInOtherCountry; int? get additionalTaxResidencyCountryId; String? get tinNumberOtherCountry; bool get atOnboardingTime;
/// Create a copy of TaxResidencyRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TaxResidencyRequestCopyWith<TaxResidencyRequest> get copyWith => _$TaxResidencyRequestCopyWithImpl<TaxResidencyRequest>(this as TaxResidencyRequest, _$identity);

  /// Serializes this TaxResidencyRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TaxResidencyRequest&&(identical(other.countryOfResidency, countryOfResidency) || other.countryOfResidency == countryOfResidency)&&(identical(other.tinStatus, tinStatus) || other.tinStatus == tinStatus)&&(identical(other.tinNumber, tinNumber) || other.tinNumber == tinNumber)&&(identical(other.residentInOtherCountry, residentInOtherCountry) || other.residentInOtherCountry == residentInOtherCountry)&&(identical(other.additionalTaxResidencyCountryId, additionalTaxResidencyCountryId) || other.additionalTaxResidencyCountryId == additionalTaxResidencyCountryId)&&(identical(other.tinNumberOtherCountry, tinNumberOtherCountry) || other.tinNumberOtherCountry == tinNumberOtherCountry)&&(identical(other.atOnboardingTime, atOnboardingTime) || other.atOnboardingTime == atOnboardingTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,countryOfResidency,tinStatus,tinNumber,residentInOtherCountry,additionalTaxResidencyCountryId,tinNumberOtherCountry,atOnboardingTime);

@override
String toString() {
  return 'TaxResidencyRequest(countryOfResidency: $countryOfResidency, tinStatus: $tinStatus, tinNumber: $tinNumber, residentInOtherCountry: $residentInOtherCountry, additionalTaxResidencyCountryId: $additionalTaxResidencyCountryId, tinNumberOtherCountry: $tinNumberOtherCountry, atOnboardingTime: $atOnboardingTime)';
}


}

/// @nodoc
abstract mixin class $TaxResidencyRequestCopyWith<$Res>  {
  factory $TaxResidencyRequestCopyWith(TaxResidencyRequest value, $Res Function(TaxResidencyRequest) _then) = _$TaxResidencyRequestCopyWithImpl;
@useResult
$Res call({
 String? countryOfResidency, String tinStatus, String? tinNumber, String residentInOtherCountry, int? additionalTaxResidencyCountryId, String? tinNumberOtherCountry, bool atOnboardingTime
});




}
/// @nodoc
class _$TaxResidencyRequestCopyWithImpl<$Res>
    implements $TaxResidencyRequestCopyWith<$Res> {
  _$TaxResidencyRequestCopyWithImpl(this._self, this._then);

  final TaxResidencyRequest _self;
  final $Res Function(TaxResidencyRequest) _then;

/// Create a copy of TaxResidencyRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? countryOfResidency = freezed,Object? tinStatus = null,Object? tinNumber = freezed,Object? residentInOtherCountry = null,Object? additionalTaxResidencyCountryId = freezed,Object? tinNumberOtherCountry = freezed,Object? atOnboardingTime = null,}) {
  return _then(_self.copyWith(
countryOfResidency: freezed == countryOfResidency ? _self.countryOfResidency : countryOfResidency // ignore: cast_nullable_to_non_nullable
as String?,tinStatus: null == tinStatus ? _self.tinStatus : tinStatus // ignore: cast_nullable_to_non_nullable
as String,tinNumber: freezed == tinNumber ? _self.tinNumber : tinNumber // ignore: cast_nullable_to_non_nullable
as String?,residentInOtherCountry: null == residentInOtherCountry ? _self.residentInOtherCountry : residentInOtherCountry // ignore: cast_nullable_to_non_nullable
as String,additionalTaxResidencyCountryId: freezed == additionalTaxResidencyCountryId ? _self.additionalTaxResidencyCountryId : additionalTaxResidencyCountryId // ignore: cast_nullable_to_non_nullable
as int?,tinNumberOtherCountry: freezed == tinNumberOtherCountry ? _self.tinNumberOtherCountry : tinNumberOtherCountry // ignore: cast_nullable_to_non_nullable
as String?,atOnboardingTime: null == atOnboardingTime ? _self.atOnboardingTime : atOnboardingTime // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [TaxResidencyRequest].
extension TaxResidencyRequestPatterns on TaxResidencyRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TaxResidencyRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TaxResidencyRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TaxResidencyRequest value)  $default,){
final _that = this;
switch (_that) {
case _TaxResidencyRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TaxResidencyRequest value)?  $default,){
final _that = this;
switch (_that) {
case _TaxResidencyRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String? countryOfResidency,  String tinStatus,  String? tinNumber,  String residentInOtherCountry,  int? additionalTaxResidencyCountryId,  String? tinNumberOtherCountry,  bool atOnboardingTime)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TaxResidencyRequest() when $default != null:
return $default(_that.countryOfResidency,_that.tinStatus,_that.tinNumber,_that.residentInOtherCountry,_that.additionalTaxResidencyCountryId,_that.tinNumberOtherCountry,_that.atOnboardingTime);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String? countryOfResidency,  String tinStatus,  String? tinNumber,  String residentInOtherCountry,  int? additionalTaxResidencyCountryId,  String? tinNumberOtherCountry,  bool atOnboardingTime)  $default,) {final _that = this;
switch (_that) {
case _TaxResidencyRequest():
return $default(_that.countryOfResidency,_that.tinStatus,_that.tinNumber,_that.residentInOtherCountry,_that.additionalTaxResidencyCountryId,_that.tinNumberOtherCountry,_that.atOnboardingTime);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String? countryOfResidency,  String tinStatus,  String? tinNumber,  String residentInOtherCountry,  int? additionalTaxResidencyCountryId,  String? tinNumberOtherCountry,  bool atOnboardingTime)?  $default,) {final _that = this;
switch (_that) {
case _TaxResidencyRequest() when $default != null:
return $default(_that.countryOfResidency,_that.tinStatus,_that.tinNumber,_that.residentInOtherCountry,_that.additionalTaxResidencyCountryId,_that.tinNumberOtherCountry,_that.atOnboardingTime);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _TaxResidencyRequest implements TaxResidencyRequest {
  const _TaxResidencyRequest({this.countryOfResidency, required this.tinStatus, this.tinNumber, required this.residentInOtherCountry, this.additionalTaxResidencyCountryId, this.tinNumberOtherCountry, required this.atOnboardingTime});
  factory _TaxResidencyRequest.fromJson(Map<String, dynamic> json) => _$TaxResidencyRequestFromJson(json);

@override final  String? countryOfResidency;
@override final  String tinStatus;
@override final  String? tinNumber;
@override final  String residentInOtherCountry;
@override final  int? additionalTaxResidencyCountryId;
@override final  String? tinNumberOtherCountry;
@override final  bool atOnboardingTime;

/// Create a copy of TaxResidencyRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TaxResidencyRequestCopyWith<_TaxResidencyRequest> get copyWith => __$TaxResidencyRequestCopyWithImpl<_TaxResidencyRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TaxResidencyRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TaxResidencyRequest&&(identical(other.countryOfResidency, countryOfResidency) || other.countryOfResidency == countryOfResidency)&&(identical(other.tinStatus, tinStatus) || other.tinStatus == tinStatus)&&(identical(other.tinNumber, tinNumber) || other.tinNumber == tinNumber)&&(identical(other.residentInOtherCountry, residentInOtherCountry) || other.residentInOtherCountry == residentInOtherCountry)&&(identical(other.additionalTaxResidencyCountryId, additionalTaxResidencyCountryId) || other.additionalTaxResidencyCountryId == additionalTaxResidencyCountryId)&&(identical(other.tinNumberOtherCountry, tinNumberOtherCountry) || other.tinNumberOtherCountry == tinNumberOtherCountry)&&(identical(other.atOnboardingTime, atOnboardingTime) || other.atOnboardingTime == atOnboardingTime));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,countryOfResidency,tinStatus,tinNumber,residentInOtherCountry,additionalTaxResidencyCountryId,tinNumberOtherCountry,atOnboardingTime);

@override
String toString() {
  return 'TaxResidencyRequest(countryOfResidency: $countryOfResidency, tinStatus: $tinStatus, tinNumber: $tinNumber, residentInOtherCountry: $residentInOtherCountry, additionalTaxResidencyCountryId: $additionalTaxResidencyCountryId, tinNumberOtherCountry: $tinNumberOtherCountry, atOnboardingTime: $atOnboardingTime)';
}


}

/// @nodoc
abstract mixin class _$TaxResidencyRequestCopyWith<$Res> implements $TaxResidencyRequestCopyWith<$Res> {
  factory _$TaxResidencyRequestCopyWith(_TaxResidencyRequest value, $Res Function(_TaxResidencyRequest) _then) = __$TaxResidencyRequestCopyWithImpl;
@override @useResult
$Res call({
 String? countryOfResidency, String tinStatus, String? tinNumber, String residentInOtherCountry, int? additionalTaxResidencyCountryId, String? tinNumberOtherCountry, bool atOnboardingTime
});




}
/// @nodoc
class __$TaxResidencyRequestCopyWithImpl<$Res>
    implements _$TaxResidencyRequestCopyWith<$Res> {
  __$TaxResidencyRequestCopyWithImpl(this._self, this._then);

  final _TaxResidencyRequest _self;
  final $Res Function(_TaxResidencyRequest) _then;

/// Create a copy of TaxResidencyRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? countryOfResidency = freezed,Object? tinStatus = null,Object? tinNumber = freezed,Object? residentInOtherCountry = null,Object? additionalTaxResidencyCountryId = freezed,Object? tinNumberOtherCountry = freezed,Object? atOnboardingTime = null,}) {
  return _then(_TaxResidencyRequest(
countryOfResidency: freezed == countryOfResidency ? _self.countryOfResidency : countryOfResidency // ignore: cast_nullable_to_non_nullable
as String?,tinStatus: null == tinStatus ? _self.tinStatus : tinStatus // ignore: cast_nullable_to_non_nullable
as String,tinNumber: freezed == tinNumber ? _self.tinNumber : tinNumber // ignore: cast_nullable_to_non_nullable
as String?,residentInOtherCountry: null == residentInOtherCountry ? _self.residentInOtherCountry : residentInOtherCountry // ignore: cast_nullable_to_non_nullable
as String,additionalTaxResidencyCountryId: freezed == additionalTaxResidencyCountryId ? _self.additionalTaxResidencyCountryId : additionalTaxResidencyCountryId // ignore: cast_nullable_to_non_nullable
as int?,tinNumberOtherCountry: freezed == tinNumberOtherCountry ? _self.tinNumberOtherCountry : tinNumberOtherCountry // ignore: cast_nullable_to_non_nullable
as String?,atOnboardingTime: null == atOnboardingTime ? _self.atOnboardingTime : atOnboardingTime // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
