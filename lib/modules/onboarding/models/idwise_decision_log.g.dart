// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'idwise_decision_log.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_IdWiseDecisionLog _$IdWiseDecisionLogFromJson(Map<String, dynamic> json) =>
    _IdWiseDecisionLog(
      attempt: (json['attempt'] as num?)?.toInt() ?? 0,
      isRetry: json['isRetry'] as bool? ?? false,
      journeyId: json['journeyId'] as String? ?? '',
      finalDecision: json['finalDecision'] as String? ?? '',
      fullName: json['full_name'] as String? ?? '',
    );

Map<String, dynamic> _$IdWiseDecisionLogToJson(_IdWiseDecisionLog instance) =>
    <String, dynamic>{
      'attempt': instance.attempt,
      'isRetry': instance.isRetry,
      'journeyId': instance.journeyId,
      'finalDecision': instance.finalDecision,
      'full_name': instance.fullName,
    };
