// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OnboardingStatus {

 bool get isPOIComplete; bool get isPOAComplete; bool get isEmploymentComplete; bool get isTaxInfoComplete;
/// Create a copy of OnboardingStatus
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$OnboardingStatusCopyWith<OnboardingStatus> get copyWith => _$OnboardingStatusCopyWithImpl<OnboardingStatus>(this as OnboardingStatus, _$identity);

  /// Serializes this OnboardingStatus to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnboardingStatus&&(identical(other.isPOIComplete, isPOIComplete) || other.isPOIComplete == isPOIComplete)&&(identical(other.isPOAComplete, isPOAComplete) || other.isPOAComplete == isPOAComplete)&&(identical(other.isEmploymentComplete, isEmploymentComplete) || other.isEmploymentComplete == isEmploymentComplete)&&(identical(other.isTaxInfoComplete, isTaxInfoComplete) || other.isTaxInfoComplete == isTaxInfoComplete));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isPOIComplete,isPOAComplete,isEmploymentComplete,isTaxInfoComplete);

@override
String toString() {
  return 'OnboardingStatus(isPOIComplete: $isPOIComplete, isPOAComplete: $isPOAComplete, isEmploymentComplete: $isEmploymentComplete, isTaxInfoComplete: $isTaxInfoComplete)';
}


}

/// @nodoc
abstract mixin class $OnboardingStatusCopyWith<$Res>  {
  factory $OnboardingStatusCopyWith(OnboardingStatus value, $Res Function(OnboardingStatus) _then) = _$OnboardingStatusCopyWithImpl;
@useResult
$Res call({
 bool isPOIComplete, bool isPOAComplete, bool isEmploymentComplete, bool isTaxInfoComplete
});




}
/// @nodoc
class _$OnboardingStatusCopyWithImpl<$Res>
    implements $OnboardingStatusCopyWith<$Res> {
  _$OnboardingStatusCopyWithImpl(this._self, this._then);

  final OnboardingStatus _self;
  final $Res Function(OnboardingStatus) _then;

/// Create a copy of OnboardingStatus
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isPOIComplete = null,Object? isPOAComplete = null,Object? isEmploymentComplete = null,Object? isTaxInfoComplete = null,}) {
  return _then(_self.copyWith(
isPOIComplete: null == isPOIComplete ? _self.isPOIComplete : isPOIComplete // ignore: cast_nullable_to_non_nullable
as bool,isPOAComplete: null == isPOAComplete ? _self.isPOAComplete : isPOAComplete // ignore: cast_nullable_to_non_nullable
as bool,isEmploymentComplete: null == isEmploymentComplete ? _self.isEmploymentComplete : isEmploymentComplete // ignore: cast_nullable_to_non_nullable
as bool,isTaxInfoComplete: null == isTaxInfoComplete ? _self.isTaxInfoComplete : isTaxInfoComplete // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [OnboardingStatus].
extension OnboardingStatusPatterns on OnboardingStatus {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _OnboardingStatus value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _OnboardingStatus() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _OnboardingStatus value)  $default,){
final _that = this;
switch (_that) {
case _OnboardingStatus():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _OnboardingStatus value)?  $default,){
final _that = this;
switch (_that) {
case _OnboardingStatus() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isPOIComplete,  bool isPOAComplete,  bool isEmploymentComplete,  bool isTaxInfoComplete)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _OnboardingStatus() when $default != null:
return $default(_that.isPOIComplete,_that.isPOAComplete,_that.isEmploymentComplete,_that.isTaxInfoComplete);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isPOIComplete,  bool isPOAComplete,  bool isEmploymentComplete,  bool isTaxInfoComplete)  $default,) {final _that = this;
switch (_that) {
case _OnboardingStatus():
return $default(_that.isPOIComplete,_that.isPOAComplete,_that.isEmploymentComplete,_that.isTaxInfoComplete);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isPOIComplete,  bool isPOAComplete,  bool isEmploymentComplete,  bool isTaxInfoComplete)?  $default,) {final _that = this;
switch (_that) {
case _OnboardingStatus() when $default != null:
return $default(_that.isPOIComplete,_that.isPOAComplete,_that.isEmploymentComplete,_that.isTaxInfoComplete);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _OnboardingStatus implements OnboardingStatus {
  const _OnboardingStatus({required this.isPOIComplete, required this.isPOAComplete, required this.isEmploymentComplete, required this.isTaxInfoComplete});
  factory _OnboardingStatus.fromJson(Map<String, dynamic> json) => _$OnboardingStatusFromJson(json);

@override final  bool isPOIComplete;
@override final  bool isPOAComplete;
@override final  bool isEmploymentComplete;
@override final  bool isTaxInfoComplete;

/// Create a copy of OnboardingStatus
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnboardingStatusCopyWith<_OnboardingStatus> get copyWith => __$OnboardingStatusCopyWithImpl<_OnboardingStatus>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$OnboardingStatusToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnboardingStatus&&(identical(other.isPOIComplete, isPOIComplete) || other.isPOIComplete == isPOIComplete)&&(identical(other.isPOAComplete, isPOAComplete) || other.isPOAComplete == isPOAComplete)&&(identical(other.isEmploymentComplete, isEmploymentComplete) || other.isEmploymentComplete == isEmploymentComplete)&&(identical(other.isTaxInfoComplete, isTaxInfoComplete) || other.isTaxInfoComplete == isTaxInfoComplete));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isPOIComplete,isPOAComplete,isEmploymentComplete,isTaxInfoComplete);

@override
String toString() {
  return 'OnboardingStatus(isPOIComplete: $isPOIComplete, isPOAComplete: $isPOAComplete, isEmploymentComplete: $isEmploymentComplete, isTaxInfoComplete: $isTaxInfoComplete)';
}


}

/// @nodoc
abstract mixin class _$OnboardingStatusCopyWith<$Res> implements $OnboardingStatusCopyWith<$Res> {
  factory _$OnboardingStatusCopyWith(_OnboardingStatus value, $Res Function(_OnboardingStatus) _then) = __$OnboardingStatusCopyWithImpl;
@override @useResult
$Res call({
 bool isPOIComplete, bool isPOAComplete, bool isEmploymentComplete, bool isTaxInfoComplete
});




}
/// @nodoc
class __$OnboardingStatusCopyWithImpl<$Res>
    implements _$OnboardingStatusCopyWith<$Res> {
  __$OnboardingStatusCopyWithImpl(this._self, this._then);

  final _OnboardingStatus _self;
  final $Res Function(_OnboardingStatus) _then;

/// Create a copy of OnboardingStatus
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isPOIComplete = null,Object? isPOAComplete = null,Object? isEmploymentComplete = null,Object? isTaxInfoComplete = null,}) {
  return _then(_OnboardingStatus(
isPOIComplete: null == isPOIComplete ? _self.isPOIComplete : isPOIComplete // ignore: cast_nullable_to_non_nullable
as bool,isPOAComplete: null == isPOAComplete ? _self.isPOAComplete : isPOAComplete // ignore: cast_nullable_to_non_nullable
as bool,isEmploymentComplete: null == isEmploymentComplete ? _self.isEmploymentComplete : isEmploymentComplete // ignore: cast_nullable_to_non_nullable
as bool,isTaxInfoComplete: null == isTaxInfoComplete ? _self.isTaxInfoComplete : isTaxInfoComplete // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
