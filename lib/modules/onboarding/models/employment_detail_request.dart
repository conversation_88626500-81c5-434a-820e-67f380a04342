import 'package:freezed_annotation/freezed_annotation.dart';

part 'employment_detail_request.freezed.dart';
part 'employment_detail_request.g.dart';

@freezed
abstract class EmploymentDetailRequest with _$EmploymentDetailRequest {
  const factory EmploymentDetailRequest({
    required String workStatus,
    required String workAddress,
    required String employer,
    required String role,
    required String industry,
    required String annualIncome,
    required String countryOfBirth,
  }) = _EmploymentDetailRequest;

  factory EmploymentDetailRequest.fromJson(Map<String, dynamic> json) =>
      _$EmploymentDetailRequestFromJson(json);
}
