import 'package:freezed_annotation/freezed_annotation.dart';

part 'tax_residency_request.freezed.dart';
part 'tax_residency_request.g.dart';

@freezed
abstract class TaxResidencyRequest with _$TaxResidencyRequest {
  const factory TaxResidencyRequest({
    String? countryOfResidency,
    required String tinStatus,
    String? tinNumber,
    required String residentInOtherCountry,
    int? additionalTaxResidencyCountryId,
    String? tinNumberOtherCountry,
    required bool atOnboardingTime,
  }) = _TaxResidencyRequest;

  factory TaxResidencyRequest.fromJson(Map<String, dynamic> json) =>
      _$TaxResidencyRequestFromJson(json);
}
