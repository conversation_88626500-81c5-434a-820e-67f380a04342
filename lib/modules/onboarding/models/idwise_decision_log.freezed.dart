// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'idwise_decision_log.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$IdWiseDecisionLog {

 int get attempt; bool get isRetry; String get journeyId; String get finalDecision;@JsonKey(name: 'full_name') String get fullName;@JsonKey(includeFromJson: false, includeToJson: false) String? get currentUserId;
/// Create a copy of IdWiseDecisionLog
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$IdWiseDecisionLogCopyWith<IdWiseDecisionLog> get copyWith => _$IdWiseDecisionLogCopyWithImpl<IdWiseDecisionLog>(this as IdWiseDecisionLog, _$identity);

  /// Serializes this IdWiseDecisionLog to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is IdWiseDecisionLog&&(identical(other.attempt, attempt) || other.attempt == attempt)&&(identical(other.isRetry, isRetry) || other.isRetry == isRetry)&&(identical(other.journeyId, journeyId) || other.journeyId == journeyId)&&(identical(other.finalDecision, finalDecision) || other.finalDecision == finalDecision)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.currentUserId, currentUserId) || other.currentUserId == currentUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,attempt,isRetry,journeyId,finalDecision,fullName,currentUserId);

@override
String toString() {
  return 'IdWiseDecisionLog(attempt: $attempt, isRetry: $isRetry, journeyId: $journeyId, finalDecision: $finalDecision, fullName: $fullName, currentUserId: $currentUserId)';
}


}

/// @nodoc
abstract mixin class $IdWiseDecisionLogCopyWith<$Res>  {
  factory $IdWiseDecisionLogCopyWith(IdWiseDecisionLog value, $Res Function(IdWiseDecisionLog) _then) = _$IdWiseDecisionLogCopyWithImpl;
@useResult
$Res call({
 int attempt, bool isRetry, String journeyId, String finalDecision,@JsonKey(name: 'full_name') String fullName,@JsonKey(includeFromJson: false, includeToJson: false) String? currentUserId
});




}
/// @nodoc
class _$IdWiseDecisionLogCopyWithImpl<$Res>
    implements $IdWiseDecisionLogCopyWith<$Res> {
  _$IdWiseDecisionLogCopyWithImpl(this._self, this._then);

  final IdWiseDecisionLog _self;
  final $Res Function(IdWiseDecisionLog) _then;

/// Create a copy of IdWiseDecisionLog
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? attempt = null,Object? isRetry = null,Object? journeyId = null,Object? finalDecision = null,Object? fullName = null,Object? currentUserId = freezed,}) {
  return _then(_self.copyWith(
attempt: null == attempt ? _self.attempt : attempt // ignore: cast_nullable_to_non_nullable
as int,isRetry: null == isRetry ? _self.isRetry : isRetry // ignore: cast_nullable_to_non_nullable
as bool,journeyId: null == journeyId ? _self.journeyId : journeyId // ignore: cast_nullable_to_non_nullable
as String,finalDecision: null == finalDecision ? _self.finalDecision : finalDecision // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,currentUserId: freezed == currentUserId ? _self.currentUserId : currentUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [IdWiseDecisionLog].
extension IdWiseDecisionLogPatterns on IdWiseDecisionLog {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _IdWiseDecisionLog value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _IdWiseDecisionLog() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _IdWiseDecisionLog value)  $default,){
final _that = this;
switch (_that) {
case _IdWiseDecisionLog():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _IdWiseDecisionLog value)?  $default,){
final _that = this;
switch (_that) {
case _IdWiseDecisionLog() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int attempt,  bool isRetry,  String journeyId,  String finalDecision, @JsonKey(name: 'full_name')  String fullName, @JsonKey(includeFromJson: false, includeToJson: false)  String? currentUserId)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _IdWiseDecisionLog() when $default != null:
return $default(_that.attempt,_that.isRetry,_that.journeyId,_that.finalDecision,_that.fullName,_that.currentUserId);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int attempt,  bool isRetry,  String journeyId,  String finalDecision, @JsonKey(name: 'full_name')  String fullName, @JsonKey(includeFromJson: false, includeToJson: false)  String? currentUserId)  $default,) {final _that = this;
switch (_that) {
case _IdWiseDecisionLog():
return $default(_that.attempt,_that.isRetry,_that.journeyId,_that.finalDecision,_that.fullName,_that.currentUserId);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int attempt,  bool isRetry,  String journeyId,  String finalDecision, @JsonKey(name: 'full_name')  String fullName, @JsonKey(includeFromJson: false, includeToJson: false)  String? currentUserId)?  $default,) {final _that = this;
switch (_that) {
case _IdWiseDecisionLog() when $default != null:
return $default(_that.attempt,_that.isRetry,_that.journeyId,_that.finalDecision,_that.fullName,_that.currentUserId);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _IdWiseDecisionLog implements IdWiseDecisionLog {
  const _IdWiseDecisionLog({this.attempt = 0, this.isRetry = false, this.journeyId = '', this.finalDecision = '', @JsonKey(name: 'full_name') this.fullName = '', @JsonKey(includeFromJson: false, includeToJson: false) this.currentUserId});
  factory _IdWiseDecisionLog.fromJson(Map<String, dynamic> json) => _$IdWiseDecisionLogFromJson(json);

@override@JsonKey() final  int attempt;
@override@JsonKey() final  bool isRetry;
@override@JsonKey() final  String journeyId;
@override@JsonKey() final  String finalDecision;
@override@JsonKey(name: 'full_name') final  String fullName;
@override@JsonKey(includeFromJson: false, includeToJson: false) final  String? currentUserId;

/// Create a copy of IdWiseDecisionLog
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$IdWiseDecisionLogCopyWith<_IdWiseDecisionLog> get copyWith => __$IdWiseDecisionLogCopyWithImpl<_IdWiseDecisionLog>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$IdWiseDecisionLogToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _IdWiseDecisionLog&&(identical(other.attempt, attempt) || other.attempt == attempt)&&(identical(other.isRetry, isRetry) || other.isRetry == isRetry)&&(identical(other.journeyId, journeyId) || other.journeyId == journeyId)&&(identical(other.finalDecision, finalDecision) || other.finalDecision == finalDecision)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.currentUserId, currentUserId) || other.currentUserId == currentUserId));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,attempt,isRetry,journeyId,finalDecision,fullName,currentUserId);

@override
String toString() {
  return 'IdWiseDecisionLog(attempt: $attempt, isRetry: $isRetry, journeyId: $journeyId, finalDecision: $finalDecision, fullName: $fullName, currentUserId: $currentUserId)';
}


}

/// @nodoc
abstract mixin class _$IdWiseDecisionLogCopyWith<$Res> implements $IdWiseDecisionLogCopyWith<$Res> {
  factory _$IdWiseDecisionLogCopyWith(_IdWiseDecisionLog value, $Res Function(_IdWiseDecisionLog) _then) = __$IdWiseDecisionLogCopyWithImpl;
@override @useResult
$Res call({
 int attempt, bool isRetry, String journeyId, String finalDecision,@JsonKey(name: 'full_name') String fullName,@JsonKey(includeFromJson: false, includeToJson: false) String? currentUserId
});




}
/// @nodoc
class __$IdWiseDecisionLogCopyWithImpl<$Res>
    implements _$IdWiseDecisionLogCopyWith<$Res> {
  __$IdWiseDecisionLogCopyWithImpl(this._self, this._then);

  final _IdWiseDecisionLog _self;
  final $Res Function(_IdWiseDecisionLog) _then;

/// Create a copy of IdWiseDecisionLog
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? attempt = null,Object? isRetry = null,Object? journeyId = null,Object? finalDecision = null,Object? fullName = null,Object? currentUserId = freezed,}) {
  return _then(_IdWiseDecisionLog(
attempt: null == attempt ? _self.attempt : attempt // ignore: cast_nullable_to_non_nullable
as int,isRetry: null == isRetry ? _self.isRetry : isRetry // ignore: cast_nullable_to_non_nullable
as bool,journeyId: null == journeyId ? _self.journeyId : journeyId // ignore: cast_nullable_to_non_nullable
as String,finalDecision: null == finalDecision ? _self.finalDecision : finalDecision // ignore: cast_nullable_to_non_nullable
as String,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,currentUserId: freezed == currentUserId ? _self.currentUserId : currentUserId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
