import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:maisour/shared/models/country.dart';
part 'tax_residency_form_state.freezed.dart';

enum YesNoNa {
  yes('YES'),
  no('NO'),
  notApplicable('NOT_APPLICABLE');

  final String apiValue;
  const YesNoNa(this.apiValue);
}

@freezed
abstract class TaxResidencyFormState with _$TaxResidencyFormState {
  const factory TaxResidencyFormState({
    @Default('') String countryOfResidency,
    Country? selectedCountryOfResidency,
    @Default(YesNoNa.yes) YesNoNa? hasTin,
    @Default('') String tinNumber,
    @Default(YesNoNa.yes) YesNoNa? isResidentOtherCountry,
    Country? selectedAdditionalTaxResidencyCountry,
    @Default('') String otherTinNumber,
  }) = _TaxResidencyFormState;
}
