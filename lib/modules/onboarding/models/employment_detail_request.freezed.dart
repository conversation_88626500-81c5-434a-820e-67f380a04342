// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'employment_detail_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmploymentDetailRequest {

 String get workStatus; String get workAddress; String get employer; String get role; String get industry; String get annualIncome; String get countryOfBirth;
/// Create a copy of EmploymentDetailRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$EmploymentDetailRequestCopyWith<EmploymentDetailRequest> get copyWith => _$EmploymentDetailRequestCopyWithImpl<EmploymentDetailRequest>(this as EmploymentDetailRequest, _$identity);

  /// Serializes this EmploymentDetailRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is EmploymentDetailRequest&&(identical(other.workStatus, workStatus) || other.workStatus == workStatus)&&(identical(other.workAddress, workAddress) || other.workAddress == workAddress)&&(identical(other.employer, employer) || other.employer == employer)&&(identical(other.role, role) || other.role == role)&&(identical(other.industry, industry) || other.industry == industry)&&(identical(other.annualIncome, annualIncome) || other.annualIncome == annualIncome)&&(identical(other.countryOfBirth, countryOfBirth) || other.countryOfBirth == countryOfBirth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workStatus,workAddress,employer,role,industry,annualIncome,countryOfBirth);

@override
String toString() {
  return 'EmploymentDetailRequest(workStatus: $workStatus, workAddress: $workAddress, employer: $employer, role: $role, industry: $industry, annualIncome: $annualIncome, countryOfBirth: $countryOfBirth)';
}


}

/// @nodoc
abstract mixin class $EmploymentDetailRequestCopyWith<$Res>  {
  factory $EmploymentDetailRequestCopyWith(EmploymentDetailRequest value, $Res Function(EmploymentDetailRequest) _then) = _$EmploymentDetailRequestCopyWithImpl;
@useResult
$Res call({
 String workStatus, String workAddress, String employer, String role, String industry, String annualIncome, String countryOfBirth
});




}
/// @nodoc
class _$EmploymentDetailRequestCopyWithImpl<$Res>
    implements $EmploymentDetailRequestCopyWith<$Res> {
  _$EmploymentDetailRequestCopyWithImpl(this._self, this._then);

  final EmploymentDetailRequest _self;
  final $Res Function(EmploymentDetailRequest) _then;

/// Create a copy of EmploymentDetailRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? workStatus = null,Object? workAddress = null,Object? employer = null,Object? role = null,Object? industry = null,Object? annualIncome = null,Object? countryOfBirth = null,}) {
  return _then(_self.copyWith(
workStatus: null == workStatus ? _self.workStatus : workStatus // ignore: cast_nullable_to_non_nullable
as String,workAddress: null == workAddress ? _self.workAddress : workAddress // ignore: cast_nullable_to_non_nullable
as String,employer: null == employer ? _self.employer : employer // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,industry: null == industry ? _self.industry : industry // ignore: cast_nullable_to_non_nullable
as String,annualIncome: null == annualIncome ? _self.annualIncome : annualIncome // ignore: cast_nullable_to_non_nullable
as String,countryOfBirth: null == countryOfBirth ? _self.countryOfBirth : countryOfBirth // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// Adds pattern-matching-related methods to [EmploymentDetailRequest].
extension EmploymentDetailRequestPatterns on EmploymentDetailRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _EmploymentDetailRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _EmploymentDetailRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _EmploymentDetailRequest value)  $default,){
final _that = this;
switch (_that) {
case _EmploymentDetailRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _EmploymentDetailRequest value)?  $default,){
final _that = this;
switch (_that) {
case _EmploymentDetailRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String workStatus,  String workAddress,  String employer,  String role,  String industry,  String annualIncome,  String countryOfBirth)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _EmploymentDetailRequest() when $default != null:
return $default(_that.workStatus,_that.workAddress,_that.employer,_that.role,_that.industry,_that.annualIncome,_that.countryOfBirth);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String workStatus,  String workAddress,  String employer,  String role,  String industry,  String annualIncome,  String countryOfBirth)  $default,) {final _that = this;
switch (_that) {
case _EmploymentDetailRequest():
return $default(_that.workStatus,_that.workAddress,_that.employer,_that.role,_that.industry,_that.annualIncome,_that.countryOfBirth);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String workStatus,  String workAddress,  String employer,  String role,  String industry,  String annualIncome,  String countryOfBirth)?  $default,) {final _that = this;
switch (_that) {
case _EmploymentDetailRequest() when $default != null:
return $default(_that.workStatus,_that.workAddress,_that.employer,_that.role,_that.industry,_that.annualIncome,_that.countryOfBirth);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _EmploymentDetailRequest implements EmploymentDetailRequest {
  const _EmploymentDetailRequest({required this.workStatus, required this.workAddress, required this.employer, required this.role, required this.industry, required this.annualIncome, required this.countryOfBirth});
  factory _EmploymentDetailRequest.fromJson(Map<String, dynamic> json) => _$EmploymentDetailRequestFromJson(json);

@override final  String workStatus;
@override final  String workAddress;
@override final  String employer;
@override final  String role;
@override final  String industry;
@override final  String annualIncome;
@override final  String countryOfBirth;

/// Create a copy of EmploymentDetailRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$EmploymentDetailRequestCopyWith<_EmploymentDetailRequest> get copyWith => __$EmploymentDetailRequestCopyWithImpl<_EmploymentDetailRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$EmploymentDetailRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EmploymentDetailRequest&&(identical(other.workStatus, workStatus) || other.workStatus == workStatus)&&(identical(other.workAddress, workAddress) || other.workAddress == workAddress)&&(identical(other.employer, employer) || other.employer == employer)&&(identical(other.role, role) || other.role == role)&&(identical(other.industry, industry) || other.industry == industry)&&(identical(other.annualIncome, annualIncome) || other.annualIncome == annualIncome)&&(identical(other.countryOfBirth, countryOfBirth) || other.countryOfBirth == countryOfBirth));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,workStatus,workAddress,employer,role,industry,annualIncome,countryOfBirth);

@override
String toString() {
  return 'EmploymentDetailRequest(workStatus: $workStatus, workAddress: $workAddress, employer: $employer, role: $role, industry: $industry, annualIncome: $annualIncome, countryOfBirth: $countryOfBirth)';
}


}

/// @nodoc
abstract mixin class _$EmploymentDetailRequestCopyWith<$Res> implements $EmploymentDetailRequestCopyWith<$Res> {
  factory _$EmploymentDetailRequestCopyWith(_EmploymentDetailRequest value, $Res Function(_EmploymentDetailRequest) _then) = __$EmploymentDetailRequestCopyWithImpl;
@override @useResult
$Res call({
 String workStatus, String workAddress, String employer, String role, String industry, String annualIncome, String countryOfBirth
});




}
/// @nodoc
class __$EmploymentDetailRequestCopyWithImpl<$Res>
    implements _$EmploymentDetailRequestCopyWith<$Res> {
  __$EmploymentDetailRequestCopyWithImpl(this._self, this._then);

  final _EmploymentDetailRequest _self;
  final $Res Function(_EmploymentDetailRequest) _then;

/// Create a copy of EmploymentDetailRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? workStatus = null,Object? workAddress = null,Object? employer = null,Object? role = null,Object? industry = null,Object? annualIncome = null,Object? countryOfBirth = null,}) {
  return _then(_EmploymentDetailRequest(
workStatus: null == workStatus ? _self.workStatus : workStatus // ignore: cast_nullable_to_non_nullable
as String,workAddress: null == workAddress ? _self.workAddress : workAddress // ignore: cast_nullable_to_non_nullable
as String,employer: null == employer ? _self.employer : employer // ignore: cast_nullable_to_non_nullable
as String,role: null == role ? _self.role : role // ignore: cast_nullable_to_non_nullable
as String,industry: null == industry ? _self.industry : industry // ignore: cast_nullable_to_non_nullable
as String,annualIncome: null == annualIncome ? _self.annualIncome : annualIncome // ignore: cast_nullable_to_non_nullable
as String,countryOfBirth: null == countryOfBirth ? _self.countryOfBirth : countryOfBirth // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
