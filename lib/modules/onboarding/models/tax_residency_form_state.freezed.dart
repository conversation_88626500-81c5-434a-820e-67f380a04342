// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'tax_residency_form_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$TaxResidencyFormState {

 String get countryOfResidency; Country? get selectedCountryOfResidency; YesNoNa? get hasTin; String get tinNumber; YesNoNa? get isResidentOtherCountry; Country? get selectedAdditionalTaxResidencyCountry; String get otherTinNumber;
/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TaxResidencyFormStateCopyWith<TaxResidencyFormState> get copyWith => _$TaxResidencyFormStateCopyWithImpl<TaxResidencyFormState>(this as TaxResidencyFormState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TaxResidencyFormState&&(identical(other.countryOfResidency, countryOfResidency) || other.countryOfResidency == countryOfResidency)&&(identical(other.selectedCountryOfResidency, selectedCountryOfResidency) || other.selectedCountryOfResidency == selectedCountryOfResidency)&&(identical(other.hasTin, hasTin) || other.hasTin == hasTin)&&(identical(other.tinNumber, tinNumber) || other.tinNumber == tinNumber)&&(identical(other.isResidentOtherCountry, isResidentOtherCountry) || other.isResidentOtherCountry == isResidentOtherCountry)&&(identical(other.selectedAdditionalTaxResidencyCountry, selectedAdditionalTaxResidencyCountry) || other.selectedAdditionalTaxResidencyCountry == selectedAdditionalTaxResidencyCountry)&&(identical(other.otherTinNumber, otherTinNumber) || other.otherTinNumber == otherTinNumber));
}


@override
int get hashCode => Object.hash(runtimeType,countryOfResidency,selectedCountryOfResidency,hasTin,tinNumber,isResidentOtherCountry,selectedAdditionalTaxResidencyCountry,otherTinNumber);

@override
String toString() {
  return 'TaxResidencyFormState(countryOfResidency: $countryOfResidency, selectedCountryOfResidency: $selectedCountryOfResidency, hasTin: $hasTin, tinNumber: $tinNumber, isResidentOtherCountry: $isResidentOtherCountry, selectedAdditionalTaxResidencyCountry: $selectedAdditionalTaxResidencyCountry, otherTinNumber: $otherTinNumber)';
}


}

/// @nodoc
abstract mixin class $TaxResidencyFormStateCopyWith<$Res>  {
  factory $TaxResidencyFormStateCopyWith(TaxResidencyFormState value, $Res Function(TaxResidencyFormState) _then) = _$TaxResidencyFormStateCopyWithImpl;
@useResult
$Res call({
 String countryOfResidency, Country? selectedCountryOfResidency, YesNoNa? hasTin, String tinNumber, YesNoNa? isResidentOtherCountry, Country? selectedAdditionalTaxResidencyCountry, String otherTinNumber
});


$CountryCopyWith<$Res>? get selectedCountryOfResidency;$CountryCopyWith<$Res>? get selectedAdditionalTaxResidencyCountry;

}
/// @nodoc
class _$TaxResidencyFormStateCopyWithImpl<$Res>
    implements $TaxResidencyFormStateCopyWith<$Res> {
  _$TaxResidencyFormStateCopyWithImpl(this._self, this._then);

  final TaxResidencyFormState _self;
  final $Res Function(TaxResidencyFormState) _then;

/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? countryOfResidency = null,Object? selectedCountryOfResidency = freezed,Object? hasTin = freezed,Object? tinNumber = null,Object? isResidentOtherCountry = freezed,Object? selectedAdditionalTaxResidencyCountry = freezed,Object? otherTinNumber = null,}) {
  return _then(_self.copyWith(
countryOfResidency: null == countryOfResidency ? _self.countryOfResidency : countryOfResidency // ignore: cast_nullable_to_non_nullable
as String,selectedCountryOfResidency: freezed == selectedCountryOfResidency ? _self.selectedCountryOfResidency : selectedCountryOfResidency // ignore: cast_nullable_to_non_nullable
as Country?,hasTin: freezed == hasTin ? _self.hasTin : hasTin // ignore: cast_nullable_to_non_nullable
as YesNoNa?,tinNumber: null == tinNumber ? _self.tinNumber : tinNumber // ignore: cast_nullable_to_non_nullable
as String,isResidentOtherCountry: freezed == isResidentOtherCountry ? _self.isResidentOtherCountry : isResidentOtherCountry // ignore: cast_nullable_to_non_nullable
as YesNoNa?,selectedAdditionalTaxResidencyCountry: freezed == selectedAdditionalTaxResidencyCountry ? _self.selectedAdditionalTaxResidencyCountry : selectedAdditionalTaxResidencyCountry // ignore: cast_nullable_to_non_nullable
as Country?,otherTinNumber: null == otherTinNumber ? _self.otherTinNumber : otherTinNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}
/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CountryCopyWith<$Res>? get selectedCountryOfResidency {
    if (_self.selectedCountryOfResidency == null) {
    return null;
  }

  return $CountryCopyWith<$Res>(_self.selectedCountryOfResidency!, (value) {
    return _then(_self.copyWith(selectedCountryOfResidency: value));
  });
}/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CountryCopyWith<$Res>? get selectedAdditionalTaxResidencyCountry {
    if (_self.selectedAdditionalTaxResidencyCountry == null) {
    return null;
  }

  return $CountryCopyWith<$Res>(_self.selectedAdditionalTaxResidencyCountry!, (value) {
    return _then(_self.copyWith(selectedAdditionalTaxResidencyCountry: value));
  });
}
}


/// Adds pattern-matching-related methods to [TaxResidencyFormState].
extension TaxResidencyFormStatePatterns on TaxResidencyFormState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _TaxResidencyFormState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _TaxResidencyFormState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _TaxResidencyFormState value)  $default,){
final _that = this;
switch (_that) {
case _TaxResidencyFormState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _TaxResidencyFormState value)?  $default,){
final _that = this;
switch (_that) {
case _TaxResidencyFormState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String countryOfResidency,  Country? selectedCountryOfResidency,  YesNoNa? hasTin,  String tinNumber,  YesNoNa? isResidentOtherCountry,  Country? selectedAdditionalTaxResidencyCountry,  String otherTinNumber)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _TaxResidencyFormState() when $default != null:
return $default(_that.countryOfResidency,_that.selectedCountryOfResidency,_that.hasTin,_that.tinNumber,_that.isResidentOtherCountry,_that.selectedAdditionalTaxResidencyCountry,_that.otherTinNumber);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String countryOfResidency,  Country? selectedCountryOfResidency,  YesNoNa? hasTin,  String tinNumber,  YesNoNa? isResidentOtherCountry,  Country? selectedAdditionalTaxResidencyCountry,  String otherTinNumber)  $default,) {final _that = this;
switch (_that) {
case _TaxResidencyFormState():
return $default(_that.countryOfResidency,_that.selectedCountryOfResidency,_that.hasTin,_that.tinNumber,_that.isResidentOtherCountry,_that.selectedAdditionalTaxResidencyCountry,_that.otherTinNumber);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String countryOfResidency,  Country? selectedCountryOfResidency,  YesNoNa? hasTin,  String tinNumber,  YesNoNa? isResidentOtherCountry,  Country? selectedAdditionalTaxResidencyCountry,  String otherTinNumber)?  $default,) {final _that = this;
switch (_that) {
case _TaxResidencyFormState() when $default != null:
return $default(_that.countryOfResidency,_that.selectedCountryOfResidency,_that.hasTin,_that.tinNumber,_that.isResidentOtherCountry,_that.selectedAdditionalTaxResidencyCountry,_that.otherTinNumber);case _:
  return null;

}
}

}

/// @nodoc


class _TaxResidencyFormState implements TaxResidencyFormState {
  const _TaxResidencyFormState({this.countryOfResidency = '', this.selectedCountryOfResidency, this.hasTin = YesNoNa.yes, this.tinNumber = '', this.isResidentOtherCountry = YesNoNa.yes, this.selectedAdditionalTaxResidencyCountry, this.otherTinNumber = ''});
  

@override@JsonKey() final  String countryOfResidency;
@override final  Country? selectedCountryOfResidency;
@override@JsonKey() final  YesNoNa? hasTin;
@override@JsonKey() final  String tinNumber;
@override@JsonKey() final  YesNoNa? isResidentOtherCountry;
@override final  Country? selectedAdditionalTaxResidencyCountry;
@override@JsonKey() final  String otherTinNumber;

/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TaxResidencyFormStateCopyWith<_TaxResidencyFormState> get copyWith => __$TaxResidencyFormStateCopyWithImpl<_TaxResidencyFormState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TaxResidencyFormState&&(identical(other.countryOfResidency, countryOfResidency) || other.countryOfResidency == countryOfResidency)&&(identical(other.selectedCountryOfResidency, selectedCountryOfResidency) || other.selectedCountryOfResidency == selectedCountryOfResidency)&&(identical(other.hasTin, hasTin) || other.hasTin == hasTin)&&(identical(other.tinNumber, tinNumber) || other.tinNumber == tinNumber)&&(identical(other.isResidentOtherCountry, isResidentOtherCountry) || other.isResidentOtherCountry == isResidentOtherCountry)&&(identical(other.selectedAdditionalTaxResidencyCountry, selectedAdditionalTaxResidencyCountry) || other.selectedAdditionalTaxResidencyCountry == selectedAdditionalTaxResidencyCountry)&&(identical(other.otherTinNumber, otherTinNumber) || other.otherTinNumber == otherTinNumber));
}


@override
int get hashCode => Object.hash(runtimeType,countryOfResidency,selectedCountryOfResidency,hasTin,tinNumber,isResidentOtherCountry,selectedAdditionalTaxResidencyCountry,otherTinNumber);

@override
String toString() {
  return 'TaxResidencyFormState(countryOfResidency: $countryOfResidency, selectedCountryOfResidency: $selectedCountryOfResidency, hasTin: $hasTin, tinNumber: $tinNumber, isResidentOtherCountry: $isResidentOtherCountry, selectedAdditionalTaxResidencyCountry: $selectedAdditionalTaxResidencyCountry, otherTinNumber: $otherTinNumber)';
}


}

/// @nodoc
abstract mixin class _$TaxResidencyFormStateCopyWith<$Res> implements $TaxResidencyFormStateCopyWith<$Res> {
  factory _$TaxResidencyFormStateCopyWith(_TaxResidencyFormState value, $Res Function(_TaxResidencyFormState) _then) = __$TaxResidencyFormStateCopyWithImpl;
@override @useResult
$Res call({
 String countryOfResidency, Country? selectedCountryOfResidency, YesNoNa? hasTin, String tinNumber, YesNoNa? isResidentOtherCountry, Country? selectedAdditionalTaxResidencyCountry, String otherTinNumber
});


@override $CountryCopyWith<$Res>? get selectedCountryOfResidency;@override $CountryCopyWith<$Res>? get selectedAdditionalTaxResidencyCountry;

}
/// @nodoc
class __$TaxResidencyFormStateCopyWithImpl<$Res>
    implements _$TaxResidencyFormStateCopyWith<$Res> {
  __$TaxResidencyFormStateCopyWithImpl(this._self, this._then);

  final _TaxResidencyFormState _self;
  final $Res Function(_TaxResidencyFormState) _then;

/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? countryOfResidency = null,Object? selectedCountryOfResidency = freezed,Object? hasTin = freezed,Object? tinNumber = null,Object? isResidentOtherCountry = freezed,Object? selectedAdditionalTaxResidencyCountry = freezed,Object? otherTinNumber = null,}) {
  return _then(_TaxResidencyFormState(
countryOfResidency: null == countryOfResidency ? _self.countryOfResidency : countryOfResidency // ignore: cast_nullable_to_non_nullable
as String,selectedCountryOfResidency: freezed == selectedCountryOfResidency ? _self.selectedCountryOfResidency : selectedCountryOfResidency // ignore: cast_nullable_to_non_nullable
as Country?,hasTin: freezed == hasTin ? _self.hasTin : hasTin // ignore: cast_nullable_to_non_nullable
as YesNoNa?,tinNumber: null == tinNumber ? _self.tinNumber : tinNumber // ignore: cast_nullable_to_non_nullable
as String,isResidentOtherCountry: freezed == isResidentOtherCountry ? _self.isResidentOtherCountry : isResidentOtherCountry // ignore: cast_nullable_to_non_nullable
as YesNoNa?,selectedAdditionalTaxResidencyCountry: freezed == selectedAdditionalTaxResidencyCountry ? _self.selectedAdditionalTaxResidencyCountry : selectedAdditionalTaxResidencyCountry // ignore: cast_nullable_to_non_nullable
as Country?,otherTinNumber: null == otherTinNumber ? _self.otherTinNumber : otherTinNumber // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CountryCopyWith<$Res>? get selectedCountryOfResidency {
    if (_self.selectedCountryOfResidency == null) {
    return null;
  }

  return $CountryCopyWith<$Res>(_self.selectedCountryOfResidency!, (value) {
    return _then(_self.copyWith(selectedCountryOfResidency: value));
  });
}/// Create a copy of TaxResidencyFormState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CountryCopyWith<$Res>? get selectedAdditionalTaxResidencyCountry {
    if (_self.selectedAdditionalTaxResidencyCountry == null) {
    return null;
  }

  return $CountryCopyWith<$Res>(_self.selectedAdditionalTaxResidencyCountry!, (value) {
    return _then(_self.copyWith(selectedAdditionalTaxResidencyCountry: value));
  });
}
}

// dart format on
