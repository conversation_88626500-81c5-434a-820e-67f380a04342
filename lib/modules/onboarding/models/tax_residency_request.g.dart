// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tax_residency_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TaxResidencyRequest _$TaxResidencyRequestFromJson(Map<String, dynamic> json) =>
    _TaxResidencyRequest(
      countryOfResidency: json['countryOfResidency'] as String?,
      tinStatus: json['tinStatus'] as String,
      tinNumber: json['tinNumber'] as String?,
      residentInOtherCountry: json['residentInOtherCountry'] as String,
      additionalTaxResidencyCountryId:
          (json['additionalTaxResidencyCountryId'] as num?)?.toInt(),
      tinNumberOtherCountry: json['tinNumberOtherCountry'] as String?,
      atOnboardingTime: json['atOnboardingTime'] as bool,
    );

Map<String, dynamic> _$TaxResidencyRequestToJson(
  _TaxResidencyRequest instance,
) => <String, dynamic>{
  'countryOfResidency': instance.countryOfResidency,
  'tinStatus': instance.tinStatus,
  'tinNumber': instance.tinNumber,
  'residentInOtherCountry': instance.residentInOtherCountry,
  'additionalTaxResidencyCountryId': instance.additionalTaxResidencyCountryId,
  'tinNumberOtherCountry': instance.tinNumberOtherCountry,
  'atOnboardingTime': instance.atOnboardingTime,
};
