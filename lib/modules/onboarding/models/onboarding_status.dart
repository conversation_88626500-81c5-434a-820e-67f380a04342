import 'package:freezed_annotation/freezed_annotation.dart';

part 'onboarding_status.freezed.dart';
part 'onboarding_status.g.dart';

@freezed
abstract class OnboardingStatus with _$OnboardingStatus {
  const factory OnboardingStatus({
    required bool isPOIComplete,
    required bool isPOAComplete,
    required bool isEmploymentComplete,
    required bool isTaxInfoComplete,
  }) = _OnboardingStatus;

  factory OnboardingStatus.fromJson(Map<String, dynamic> json) =>
      _$OnboardingStatusFromJson(json);
}
