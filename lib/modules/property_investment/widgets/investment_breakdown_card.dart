import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class InvestmentBreakdownCard extends StatelessWidget {
  final double potentialRewards;
  final double numberOfShares;
  final double pricePerShare;
  final double actualInvestmentAmount;
  final double purchaseCost;
  final double transactionCost;
  final double totalCost;

  const InvestmentBreakdownCard({
    super.key,
    required this.potentialRewards,
    required this.numberOfShares,
    required this.pricePerShare,
    required this.actualInvestmentAmount,
    required this.purchaseCost,
    required this.transactionCost,
    required this.totalCost,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppColors.gray.alphaPercent(5),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          _buildBreakdownRow(
            LocaleKeys.potentialRewards.tr(),
            Currency.aed.formatCurrency(potentialRewards),
          ),
          _buildBreakdownRow(
            LocaleKeys.numberOfShares.tr(),
            numberOfShares.toStringAsFixed(2),
            isCurrency: false,
          ),
          _buildBreakdownRow(
            LocaleKeys.pricePerShare.tr(),
            Currency.aed.formatCurrency(pricePerShare),
            isCurrency: false,
          ),
          _buildBreakdownRow(
            LocaleKeys.investmentAmount.tr(),
            Currency.aed.formatCurrency(actualInvestmentAmount),
          ),
          _buildBreakdownRow(
            LocaleKeys.purchaseCost.tr(),
            Currency.aed.formatCurrency(purchaseCost),
          ),
          _buildBreakdownRow(
            LocaleKeys.transactionCost.tr(),
            Currency.aed.formatCurrency(transactionCost),
          ),
          Divider(color: AppColors.gray.shade200, height: 1),
          10.w.heightBox,
          _buildBreakdownRow(
            LocaleKeys.totalCost.tr(),
            Currency.aed.formatCurrency(totalCost),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownRow(
    String label,
    String value, {
    bool isTotal = false,
    bool isCurrency = true,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? AppTextStyles.text12.medium.dark900
                : AppTextStyles.text12.medium.gray700,
          ),
          Row(
            children: [
              if (isCurrency)
                Assets.images.dirham
                    .image(width: 16.w, height: 16.w)
                    .paddingEnd(4),
              Text(
                value,
                style: isTotal
                    ? AppTextStyles.text14.bold.dark900
                    : AppTextStyles.text14.medium.dark900,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
