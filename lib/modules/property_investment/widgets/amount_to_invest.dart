import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/property_investment/widgets/investment_breakdown_card.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/layouts/increase_decrease_amount.dart';

class AmountToInvest extends StatefulWidget {
  final PropertyDetails propertyDetails;
  final AppUser user;
  final Function(AddInvestmentRequest finacialDetailsData) onAmountChanged;
  const AmountToInvest({
    super.key,
    required this.propertyDetails,
    required this.user,
    required this.onAmountChanged,
  });

  @override
  State<AmountToInvest> createState() => _AmountToInvestState();
}

class _AmountToInvestState extends State<AmountToInvest> {
  int _totalInvestmentAmount = 0;
  double pricePerShare = 0;
  double numberOfShares = 0;
  double actualInvestmentAmount = 0;
  double purchaseCost = 0;
  double transactionCost = 0;
  double totalCost = 0;

  @override
  void initState() {
    _totalInvestmentAmount = widget.propertyDetails.minInvestmentAmount.toInt();
    pricePerShare =
        widget.propertyDetails.propertyAcquisition /
        widget.propertyDetails.numberOfShare;
    _calculateInvestmentDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IncreaseDecreaseAmount(
          initialAmount: _totalInvestmentAmount,
          minAmount: widget.propertyDetails.minInvestmentAmount.toInt(),
          maxAmount: widget.user.userAccountTypeDetails.propertyInvestmentLimit
              .toInt(),
          options: [1000, 2000, 3000, 4000],
          onAmountChanged: (int amount) {
            _totalInvestmentAmount = amount;
            _calculateInvestmentDetails(requiredRefresh: true);
          },
          onOptionSelected: (int option) {
            _totalInvestmentAmount += option;
            _calculateInvestmentDetails(requiredRefresh: true);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.walletBalance.tr(),
                    style: AppTextStyles.text12.medium.gray700,
                  ),
                  Row(
                    children: [
                      Assets.images.dirham
                          .image(width: 16.w, height: 16.w)
                          .paddingEnd(4),
                      Text(
                        '${Currency.aed.formatCurrency(widget.user.credit)}${widget.user.usesImageSymbol ? '' : ' (~ ${widget.user.currencyCode.currencySymbol} ${widget.user.getCurrencyValue(widget.user.credit)})'}',
                        style: AppTextStyles.text14.semiBold.dark900,
                      ),
                    ],
                  ),
                ],
              ),
              if (widget.propertyDetails.propertyStatus ==
                  PropertyStatus.comingSoon)
                Text(
                  LocaleKeys.yourWalletWillNotBeCharged.tr(),
                  style: AppTextStyles.text12.medium.dark300,
                ).paddingOnly(top: 8.w),
            ],
          ),
        ),
        16.w.heightBox,
        InvestmentBreakdownCard(
          potentialRewards: widget.user.rewardPoints,
          numberOfShares: numberOfShares,
          pricePerShare: pricePerShare,
          actualInvestmentAmount: actualInvestmentAmount,
          purchaseCost: purchaseCost,
          transactionCost: transactionCost,
          totalCost: totalCost,
        ),
      ],
    );
  }

  void _calculateInvestmentDetails({bool? requiredRefresh}) {
    numberOfShares = _totalInvestmentAmount / pricePerShare;
    purchaseCost = double.parse(
      ((widget.propertyDetails.totalPurchaseCost * numberOfShares) /
              widget.propertyDetails.numberOfShare)
          .toStringAsFixed(2),
    );
    transactionCost = double.parse(
      ((widget.propertyDetails.totalTransactionCost * numberOfShares) /
              widget.propertyDetails.numberOfShare)
          .toStringAsFixed(2),
    );
    actualInvestmentAmount =
        _totalInvestmentAmount - purchaseCost - transactionCost;
    totalCost = actualInvestmentAmount + purchaseCost + transactionCost;
    widget.onAmountChanged(
      AddInvestmentRequest(
        amountFee: actualInvestmentAmount,
        purchaseCost: purchaseCost,
        transactionCost: transactionCost,
        totalCost: totalCost,
        propertyId: widget.propertyDetails.id,
        numberOfShares: numberOfShares,
        perSharesPrice: pricePerShare,
      ),
    );
    if (requiredRefresh == true) {
      setState(() {});
    }
  }
}
