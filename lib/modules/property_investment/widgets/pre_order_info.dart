import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/widgets/indicators/dotted_line_painter.dart';

class PreOrderInfo extends StatelessWidget {
  const PreOrderInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background image
        PositionedDirectional(
          top: 0,
          end: 0,
          child: Assets.icons.halfCircleGreen.svg(width: 80.w, height: 80.w),
        ),

        // Main content container
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.howPreOrderWorks.tr(),
                style: AppTextStyles.text12.bold.gray,
              ),
              16.w.heightBox,
              _buildTimelineSteps(),
            ],
          ),
        ),
      ],
    ).paddingHorizontal(16.w);
  }

  Widget _buildTimelineSteps() {
    final steps = [
      _TimelineStep(
        icon: Assets.icons.buildingGreen.svg(width: 20.w, height: 20.w),
        title: LocaleKeys.showInterest.tr(),
        description: LocaleKeys.noPaymentNeededJustShowInterested.tr(),
      ),
      _TimelineStep(
        icon: Assets.icons.notificationGreen.svg(width: 20.w, height: 20.w),
        title: LocaleKeys.getNotified.tr(),
        description: LocaleKeys.weWillNotifyYouWhenThePropertyIsLive.tr(),
      ),
      _TimelineStep(
        icon: Assets.icons.handGreen.svg(width: 20.w, height: 20.w),
        title: LocaleKeys.confirmAndInvest.tr(),
        description: LocaleKeys.reviewDetailsAndMakeYourPayment.tr(),
      ),
    ];

    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        final isLast = index == steps.length - 1;
        return _buildTimelineStep(step, isLast);
      }).toList(),
    );
  }

  Widget _buildTimelineStep(_TimelineStep step, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline dot + dotted line
        Column(
          children: [
            Container(
              height: 40.w,
              width: 40.w,
              margin: EdgeInsets.only(top: 3.w),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.success.alphaPercent(10)),
                color: AppColors.success.alphaPercent(5),
              ),
              child: step.icon,
            ),
            if (!isLast)
              CustomPaint(
                painter: DottedLinePainter(),
                child: SizedBox(height: 30.h, width: 2.w),
              ),
          ],
        ),
        12.w.widthBox,
        // Content
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(step.title, style: AppTextStyles.text14.bold.dark900),
              2.w.heightBox,
              Text(
                step.description,
                style: AppTextStyles.text10.medium.dark300,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _TimelineStep {
  final Widget icon;
  final String title;
  final String description;

  const _TimelineStep({
    required this.icon,
    required this.title,
    required this.description,
  });
}
