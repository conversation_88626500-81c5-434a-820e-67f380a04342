import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/property_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';

class FinancialDetailsBottomSheet extends StatelessWidget {
  final List<PropertyPurchaseCost> propertyPurchaseCosts;
  final List<PropertyTransactionCost> propertyTransactionCosts;
  final AppUser user;
  final double totalPrice;

  const FinancialDetailsBottomSheet({
    super.key,
    required this.propertyPurchaseCosts,
    required this.propertyTransactionCosts,
    required this.user,
    required this.totalPrice,
  });

  static Future<void> show(
    BuildContext context,
    List<PropertyPurchaseCost> propertyPurchaseCosts,
    List<PropertyTransactionCost> propertyTransactionCosts,
    AppUser user,
    double totalPrice,
  ) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => FinancialDetailsBottomSheet(
        propertyPurchaseCosts: propertyPurchaseCosts,
        propertyTransactionCosts: propertyTransactionCosts,
        user: user,
        totalPrice: totalPrice,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          LocaleKeys.financialDetails.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.h.heightBox,
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: AppColors.gray.alphaPercent(5),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              16.w.heightBox,
              // Financial Details Section
              Text(
                LocaleKeys.financialDetails.tr(),
                style: AppTextStyles.text16.bold.dark900,
              ),
              12.h.heightBox,
              ...propertyPurchaseCosts.map(
                (PropertyPurchaseCost propertyPurchaseCost) =>
                    _buildCostRow(context, propertyPurchaseCost, true),
              ),
              6.w.heightBox,
            ],
          ),
        ),
        16.h.heightBox,

        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: AppColors.gray.alphaPercent(5),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              16.w.heightBox,
              // Transaction Cost Section
              Text(
                LocaleKeys.transactionCost.tr(),
                style: AppTextStyles.text16.bold.dark900,
              ),
              12.w.heightBox,
              ...propertyTransactionCosts.map(
                (PropertyTransactionCost propertyTransactionCost) =>
                    _buildCostRow(context, propertyTransactionCost, false),
              ),
              6.w.heightBox,
            ],
          ),
        ),
        24.h.heightBox,
      ],
    ).paddingSymmetric(horizontal: 16.w);
  }

  Widget _buildCostRow(
    BuildContext context,
    dynamic costItem,
    bool isPurchaseCost,
  ) {
    String itemName;
    double actualValue;
    bool isPercentage;

    if (isPurchaseCost) {
      final purchaseCost = costItem as PropertyPurchaseCost;
      itemName = purchaseCost.purchaseCostConstant.getName(
        context.locale.languageCode,
      );
      actualValue = purchaseCost.actualValue;
      isPercentage = purchaseCost.purchaseCostConstant.isPercentage;
    } else {
      final transactionCost = costItem as PropertyTransactionCost;
      itemName = transactionCost.transactionCostConstant.getName(
        context.locale.languageCode,
      );
      actualValue = transactionCost.actualValue;
      isPercentage = transactionCost.transactionCostConstant.isPercentage;
    }

    final displayValue = isPercentage
        ? (actualValue * totalPrice) / 100
        : actualValue;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            '$itemName${isPercentage ? ' ($actualValue%)' : ''}',
            style: AppTextStyles.text12.medium.gray700,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Flexible(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (user.usesImageSymbol)
                Assets.images.dirham
                    .image(width: 20.w, height: 20.w)
                    .paddingEnd(4)
              else
                Text(
                  '${user.currencyCode.currencySymbol} ',
                  style: AppTextStyles.text14.bold.dark900,
                ),
              Text(
                user.getCurrencyValue(displayValue),
                style: AppTextStyles.text14.bold.dark900,
              ),
            ],
          ),
        ),
      ],
    ).paddingOnly(bottom: 10.w);
  }
}
