import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/property_details/models/property_details.dart';
import 'package:maisour/modules/your_order/models/add_investment_request.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/modules/property_details/widgets/guaranteed_rental_banner.dart';
import 'package:maisour/modules/property_investment/widgets/property_basic_details.dart';
import 'package:maisour/modules/property_investment/widgets/amount_to_invest.dart';
import 'package:maisour/modules/property_investment/widgets/pre_order_info.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';
import 'package:maisour/shared/enums/property_status.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class PropertyInvestmentScreen extends ConsumerStatefulWidget {
  final PropertyDetails propertyDetails;

  const PropertyInvestmentScreen({super.key, required this.propertyDetails});

  @override
  ConsumerState<PropertyInvestmentScreen> createState() =>
      _PropertyInvestmentScreenState();
}

class _PropertyInvestmentScreenState
    extends ConsumerState<PropertyInvestmentScreen> {
  late AddInvestmentRequest finacialDetailsData;

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider)!;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.propertyDetails.propertyStatus == PropertyStatus.live
              ? LocaleKeys.yourInvestment.tr()
              : LocaleKeys.preOrder.tr(),
        ),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    16.w.heightBox,
                    GuaranteedRentalBanner().paddingHorizontal(16.w),
                    16.w.heightBox,
                    PropertyBasicDetails(
                      propertyDetails: widget.propertyDetails,
                      user: user,
                    ).paddingHorizontal(16.w),
                    16.w.heightBox,
                    AppSpacer(),
                    16.w.heightBox,
                    // Title and Min Investment
                    _amountToInvestWidget(user).paddingHorizontal(16.w),
                    16.w.heightBox,
                    AmountToInvest(
                      propertyDetails: widget.propertyDetails,
                      user: user,
                      onAmountChanged: (finacialDetailsData) {
                        this.finacialDetailsData = finacialDetailsData;
                      },
                    ),
                    if (widget.propertyDetails.propertyStatus ==
                        PropertyStatus.comingSoon)
                      PreOrderInfo().paddingOnly(top: 16.w),
                    16.w.heightBox,
                  ],
                ),
              ),
            ),
            AppButton(
              text: widget.propertyDetails.propertyStatus == PropertyStatus.live
                  ? LocaleKeys.placeOrder.tr()
                  : LocaleKeys.preOrderNow.tr(),
              onPressed: () => _validateInvestment(user: user),
            ).paddingHorizontal(16.w),
            if (widget.propertyDetails.propertyStatus ==
                PropertyStatus.comingSoon)
              Text(
                LocaleKeys.noPaymentYet.tr(),
                style: AppTextStyles.text12.medium.dark300,
              ).paddingOnly(top: 8.w),
            6.h.heightBox,
          ],
        ),
      ),
    );
  }

  Widget _amountToInvestWidget(AppUser user) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.amountToInvest.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        Row(
          children: [
            Assets.images.dirham.image(width: 16.w, height: 16.w).paddingEnd(4),
            Text(
              '${Currency.aed.formatCurrency(widget.propertyDetails.minInvestmentAmount)} ${LocaleKeys.minimum.tr()}',
              style: AppTextStyles.text14.medium.dark900,
            ),
          ],
        ),
      ],
    );
  }

  void _validateInvestment({required AppUser user}) {
    // I user dont have enoph balance to invest, show warning toast, and redirect to add fund screen
    if (user.balance < finacialDetailsData.totalCost) {
      context.showWarningToast(
        LocaleKeys.oops.tr(),
        LocaleKeys.notEnoughBalance.tr(),
      );
      GoRouter.of(context).pushNamed(RouteName.addFund.name);
    } else {
      GoRouter.of(context).pushNamed(
        RouteName.yourOrder.name,
        extra: {
          'finacialDetailsData': finacialDetailsData,
          'propertyDetails': widget.propertyDetails,
        },
      );
    }
  }
}
