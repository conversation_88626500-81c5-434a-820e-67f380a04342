import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/withdraw/api/withdraw_api.dart';
import 'package:maisour/modules/withdraw/withdraw_service.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Provider for WithdrawApi
final withdrawApiProvider = Provider<WithdrawApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return WithdrawApi(dio);
});

/// Provider for WithdrawService
final withdrawServiceProvider = Provider<WithdrawService>((ref) {
  final api = ref.watch(withdrawApiProvider);
  return WithdrawService(api);
});

/// Provider for WithdrawNotifier
final withdrawNotifierProvider =
    StateNotifierProvider.autoDispose<WithdrawNotifier, WithdrawState>((ref) {
      final service = ref.watch(withdrawServiceProvider);
      return WithdrawNotifier(service);
    }, name: 'withdrawNotifierProvider');

class WithdrawNotifier extends StateNotifier<WithdrawState>
    with DioExceptionMapper {
  final WithdrawService _service;

  WithdrawNotifier(this._service) : super(const WithdrawState());

  /// Generate OTP for withdrawal
  Future<void> generateOTP() async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: true,
      isProcessingWithdrawal: false,
    );

    try {
      final otpResponse = await _service.generateOTP();
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        otpId: otpResponse.id,
        isGeneratingOTP: true,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isGeneratingOTP: false,
      );
    }
  }

  /// Process withdrawal
  Future<void> processWithdrawal({
    required int amount,
    required int accountId,
    required int otpRecordId,
  }) async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: false,
      isProcessingWithdrawal: true,
    );

    try {
      await _service.withdraw(
        amount: amount,
        accountId: accountId,
        otpRecordId: otpRecordId,
      );
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        isProcessingWithdrawal: true,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isProcessingWithdrawal: false,
      );
    }
  }

  /// Reset state
  void reset() {
    state = const WithdrawState();
  }
}

class WithdrawState {
  final ApiStatus status;
  final String? errorMessage;
  final String? errorKey;
  final int? otpId;
  final bool isGeneratingOTP;
  final bool isProcessingWithdrawal;

  const WithdrawState({
    this.status = ApiStatus.initial,
    this.errorMessage,
    this.errorKey,
    this.otpId,
    this.isGeneratingOTP = false,
    this.isProcessingWithdrawal = false,
  });

  WithdrawState copyWith({
    ApiStatus? status,
    String? errorMessage,
    String? errorKey,
    int? otpId,
    bool? isGeneratingOTP,
    bool? isProcessingWithdrawal,
  }) {
    return WithdrawState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
      otpId: otpId ?? this.otpId,
      isGeneratingOTP: isGeneratingOTP ?? this.isGeneratingOTP,
      isProcessingWithdrawal:
          isProcessingWithdrawal ?? this.isProcessingWithdrawal,
    );
  }
}
