import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';

part 'withdraw_api.g.dart';

@RestApi()
abstract class WithdrawApi {
  factory WithdrawApi(Dio dio, {String baseUrl}) = _WithdrawApi;

  /// Get public IP
  @GET(ApiEndpoints.getPublicIp)
  Future<String> getPublicIp();

  @POST(ApiEndpoints.generateOTP)
  Future<OtpResponse> generateOTP(@Query('ipAddress') String ipAddress);

  @POST(ApiEndpoints.withdraw)
  Future<void> withdraw(@Body() Map<String, dynamic> body);
}
