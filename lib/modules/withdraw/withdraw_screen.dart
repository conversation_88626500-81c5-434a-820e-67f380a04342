import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/modules/withdraw/providers/withdraw_provider.dart';

import 'package:maisour/modules/withdraw/widgets/withdraw_amount.dart';
import 'package:maisour/modules/withdraw/widgets/bank_account_selection_widget.dart';

class WithdrawScreen extends ConsumerStatefulWidget {
  const WithdrawScreen({super.key});

  @override
  ConsumerState<WithdrawScreen> createState() => _WithdrawScreenState();
}

class _WithdrawScreenState extends ConsumerState<WithdrawScreen> {
  int amount = 0;
  int? selectedBankAccountId;

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider)!;

    return Consumer(
      builder: (context, ref, child) {
        // Listen to withdrawal operations state
        ref.listen<WithdrawState>(withdrawNotifierProvider, (previous, next) {
          if (next.status == ApiStatus.success &&
              next.isGeneratingOTP == true) {
            // OTP generation successful, navigate to OTP verification
            GoRouter.of(context).pushNamed(RouteName.otpVerification.name).then(
              (value) {
                if (value is int) {
                  // Call withdrawal API with OTP record ID
                  ref
                      .read(withdrawNotifierProvider.notifier)
                      .processWithdrawal(
                        amount: amount,
                        accountId: selectedBankAccountId!,
                        otpRecordId: value,
                      );
                }
              },
            );
          } else if (next.status == ApiStatus.success &&
              next.isProcessingWithdrawal == true) {
            // Withdrawal successful, show success toast and pop
            context.showSuccessToast(
              LocaleKeys.success.tr(),
              LocaleKeys.withdrawalRequestSubmittedSuccessfully.tr(),
            );
            GoRouter.of(context).pop();
          } else if (next.status == ApiStatus.error) {
            final (
              title,
              message,
            ) = ErrorMessageHelper.getLocalizedErrorMessage(
              errorKey: next.errorKey,
              fallbackMessage:
                  next.errorMessage ?? LocaleKeys.somethingWentWrong.tr(),
            );
            context.showErrorToast(title, message);
          }
        });

        final withdrawState = ref.watch(withdrawNotifierProvider);
        final isGeneratingOTP =
            withdrawState.status == ApiStatus.loading &&
            withdrawState.isGeneratingOTP == true;
        final isProcessingWithdrawal =
            withdrawState.status == ApiStatus.loading &&
            withdrawState.isProcessingWithdrawal == true;

        return Scaffold(
          appBar: AppBar(
            title: Text(LocaleKeys.withdrawCash.tr()),
            centerTitle: false,
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        16.w.heightBox,
                        WithdrawAmount(
                          user: user,
                          onAmountChanged: (newAmount) {
                            amount = newAmount;
                          },
                        ),
                        16.w.heightBox,
                        AppSpacer(),
                        BankAccountSelectionWidget(
                          onBankAccountSelected: (bankAccountId) {
                            selectedBankAccountId = bankAccountId;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                _buildActionButtons(isGeneratingOTP, isProcessingWithdrawal),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButtons(
    bool isGeneratingOTP,
    bool isProcessingWithdrawal,
  ) {
    final isLoading = isGeneratingOTP || isProcessingWithdrawal;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          AppButton(
            text: LocaleKeys.withdrawCash.tr(),
            onPressed: _handleWithdraw,
            isLoading: isLoading,
          ),
          8.h.heightBox,
          AppButton(
            type: ButtonType.text,
            text: LocaleKeys.cancel.tr(),
            onPressed: isLoading
                ? () {}
                : () {
                    GoRouter.of(context).pop();
                  },
          ),
        ],
      ),
    );
  }

  void _handleWithdraw() {
    // Generate OTP first
    ref.read(withdrawNotifierProvider.notifier).generateOTP();
  }
}
