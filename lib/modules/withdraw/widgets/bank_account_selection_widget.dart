import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/modules/bank_accounts/providers/bank_accounts_provider.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class BankAccountSelectionWidget extends ConsumerStatefulWidget {
  final Function(int) onBankAccountSelected;

  const BankAccountSelectionWidget({
    super.key,
    required this.onBankAccountSelected,
  });

  @override
  ConsumerState<BankAccountSelectionWidget> createState() =>
      _BankAccountSelectionWidgetState();
}

class _BankAccountSelectionWidgetState
    extends ConsumerState<BankAccountSelectionWidget> {
  int? _selectedBankAccountId;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bankAccountsAsync = ref.watch(bankAccountsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.selectBankAccount.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        4.h.heightBox,
        Text(
          LocaleKeys.withdrawalsProcessingTime.tr(),
          style: AppTextStyles.text12.medium.gray,
        ),
        16.h.heightBox,
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
          ),
          child: bankAccountsAsync.when(
            data: (bankAccounts) {
              if (bankAccounts.isEmpty) {
                return _buildEmptyState(context, ref);
              }

              // Sort bank accounts: primary first
              final sortedBankAccounts = List<BankAccount>.from(bankAccounts)
                ..sort((a, b) {
                  // Primary accounts first
                  if (a.isPrimary && !b.isPrimary) return -1;
                  if (!a.isPrimary && b.isPrimary) return 1;
                  // Keep original order for non-primary accounts
                  return 0;
                });

              // Auto-select primary bank if no selection
              if (_selectedBankAccountId == null &&
                  sortedBankAccounts.isNotEmpty) {
                final primaryBank = sortedBankAccounts.firstWhere(
                  (account) => account.isPrimary,
                  orElse: () => sortedBankAccounts.first,
                );
                _selectedBankAccountId = primaryBank.id;
                widget.onBankAccountSelected(primaryBank.id);
              }

              return Column(
                children: [
                  ...sortedBankAccounts.asMap().entries.map((entry) {
                    final index = entry.key;
                    final bankAccount = entry.value;
                    final isLast = index == sortedBankAccounts.length - 1;

                    return _buildBankAccountItem(bankAccount, isLast);
                  }),
                  _buildDivider(),
                  AppButton(
                    text: LocaleKeys.addBankAccount.tr(),
                    type: ButtonType.text,
                    textColor: AppColors.primary,
                    icon: Icon(Icons.add, color: AppColors.primary),
                    onPressed: () => _navigateToAddBankAccount(context, ref),
                  ),
                  4.w.heightBox,
                ],
              );
            },
            loading: () =>
                AppShimmerLoader.fixed(itemCount: 3, itemHeight: 80.w),
            error: (error, stackTrace) =>
                Center(child: Text(error.toString())).paddingAll(16.w),
          ),
        ),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildBankAccountItem(BankAccount account, bool isLast) {
    final isSelected = _selectedBankAccountId == account.id;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child:
              Row(
                children: [
                  Assets.icons.bankAccount.svg(width: 32.w, height: 32.w),
                  12.w.widthBox,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.bankName,
                          style: AppTextStyles.text14.bold.dark900,
                        ),
                        4.h.heightBox,
                        Text(
                          _formatAccountNumber(account.accountNumber),
                          style: AppTextStyles.text12.medium.gray600,
                        ),
                      ],
                    ),
                  ),
                  // Radio button
                  // Selection indicator
                  Container(
                    width: 20.w,
                    height: 20.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: isSelected ? AppColors.primary : AppColors.white,
                      border: Border.all(
                        color: isSelected
                            ? AppColors.primary
                            : AppColors.gray.shade300,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? Icon(Icons.check, size: 12.w, color: AppColors.white)
                        : null,
                  ),
                ],
              ).onTap(() {
                setState(() {
                  _selectedBankAccountId = account.id;
                });
                widget.onBankAccountSelected(account.id);
              }),
        ),
        // Divider
        if (!isLast)
          Divider(
            color: AppColors.gray.shade200,
            thickness: 1.h,
            height: 1.h,
          ).paddingStart(60.w),
      ],
    );
  }

  Widget _buildDivider() {
    return Divider(color: AppColors.gray.shade200);
  }

  /// Navigates to add bank account screen
  void _navigateToAddBankAccount(BuildContext context, WidgetRef ref) {
    GoRouter.of(context).pushNamed(RouteName.addBankAccount.name).then((value) {
      if (value is bool && value == true) {
        ref.read(bankAccountsProvider.notifier).refresh();
      }
    });
  }

  /// Builds the empty state with icon, text, and button
  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Icon
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.alphaPercent(10),
              ),
            ),
            Assets.icons.bankAccount.svg(
              width: 28.w,
              height: 28.w,
              colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
            ),
          ],
        ),
        16.h.heightBox,
        // Heading
        Text(
          LocaleKeys.addBankAccount.tr(),
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ),
        8.h.heightBox,
        // Description
        Text(
          LocaleKeys.itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName
              .tr(),
          style: AppTextStyles.text12.medium.gray700,
          textAlign: TextAlign.center,
        ),
        16.h.heightBox,
        // Button
        _buildAddBankAccountButton(context, ref),
      ],
    ).paddingAll(16.w);
  }

  /// Builds the add bank account button
  Widget _buildAddBankAccountButton(BuildContext context, WidgetRef ref) {
    return AppButton(
      width: 150.w,
      text: LocaleKeys.addAccount.tr(),
      type: ButtonType.filled,
      icon: Icon(Icons.add, color: AppColors.white, size: 16.w),
      onPressed: () => _navigateToAddBankAccount(context, ref),
    );
  }

  String _formatAccountNumber(String accountNumber) {
    if (accountNumber.length <= 4) {
      return accountNumber;
    }

    final lastFour = accountNumber.substring(accountNumber.length - 4);
    final maskedPart = '*' * (accountNumber.length - 4);

    // Format as **** **** **** 1234
    final chunks = <String>[];
    for (int i = 0; i < maskedPart.length; i += 4) {
      final end = (i + 4 < maskedPart.length) ? i + 4 : maskedPart.length;
      chunks.add(maskedPart.substring(i, end));
    }

    return '${chunks.join(' ')} $lastFour';
  }
}
