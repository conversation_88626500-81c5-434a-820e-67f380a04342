import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/layouts/increase_decrease_amount.dart';

class WithdrawAmount extends StatefulWidget {
  final Function(int) onAmountChanged;
  final AppUser user;

  const WithdrawAmount({
    super.key,
    required this.onAmountChanged,
    required this.user,
  });

  @override
  State<WithdrawAmount> createState() => _WithdrawAmountState();
}

class _WithdrawAmountState extends State<WithdrawAmount> {
  int selectedAmount = 0;

  @override
  void initState() {
    super.initState();
    selectedAmount = widget.user.credit.toInt();
    widget.onAmountChanged(selectedAmount);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.withdrawCash.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ).paddingHorizontal(16.w),
        if (!widget.user.usesImageSymbol)
          Text(
            LocaleKeys.allTransactionsInAED.tr(),
            style: AppTextStyles.text12.medium.gray,
          ).paddingSymmetric(vertical: 4.w, horizontal: 16.w),

        12.w.heightBox,
        IncreaseDecreaseAmount(
          initialAmount: selectedAmount,
          minAmount: 10,
          maxAmount: widget.user.credit.toInt(),
          options: [25, 50, 75, 100],
          isPercentage: true,
          onAmountChanged: (amount) {
            selectedAmount = amount;
            _calculateAmount();
          },
          onOptionSelected: (option) {
            selectedAmount = (widget.user.credit * option / 100).round();
            _calculateAmount();
          },
          child: _buildWalletBalanceSection(),
        ),
      ],
    );
  }

  void _calculateAmount() {
    widget.onAmountChanged(selectedAmount);
    setState(() {});
  }

  Widget _buildWalletBalanceSection() {
    return Column(
      children: [
        if (!widget.user.usesImageSymbol)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.amountInCurrency.tr(
                  namedArgs: {"currency": widget.user.currencyCode.displayName},
                ),
                style: AppTextStyles.text12.medium.gray700,
              ),
              Row(
                children: [
                  Text(
                    widget.user.currencyCode.currencySymbol,
                    style: AppTextStyles.text14.semiBold.dark900,
                  ),
                  4.w.widthBox,
                  Text(
                    widget.user.getCurrencyValue(selectedAmount.toDouble()),
                    style: AppTextStyles.text14.semiBold.dark900,
                  ),
                ],
              ),
            ],
          ),
        4.w.heightBox,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.walletBalance.tr(),
              style: AppTextStyles.text12.medium.gray700,
            ),
            Row(
              children: [
                Assets.images.dirham
                    .image(width: 18.w, height: 18.w)
                    .paddingEnd(4),
                Text(
                  '${Currency.aed.formatCurrency(widget.user.credit)}${widget.user.usesImageSymbol ? '' : ' (~ ${widget.user.currencyCode.currencySymbol} ${widget.user.getCurrencyValue(widget.user.credit)})'}',
                  style: AppTextStyles.text14.semiBold.dark900,
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}
