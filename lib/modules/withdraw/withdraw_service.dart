import 'package:maisour/modules/withdraw/api/withdraw_api.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';

class WithdrawService {
  final WithdrawApi _withdrawApi;

  WithdrawService(this._withdrawApi);

  /// Generate OTP for withdrawal
  Future<OtpResponse> generateOTP() async {
    // Get public IP address first
    final ipAddress = await _getPublicIp();

    // Call API with IP address as query parameter
    return await _withdrawApi.generateOTP(ipAddress);
  }

  /// Get public IP address
  Future<String> _getPublicIp() async {
    try {
      // Use the public IP service
      return await _withdrawApi.getPublicIp();
    } catch (e) {
      // Fallback to empty string if IP detection fails
      return '';
    }
  }

  /// Process withdrawal
  Future<void> withdraw({
    required int amount,
    required int accountId,
    required int otpRecordId,
  }) async {
    final body = {
      'amount': amount,
      'otpRecordId': otpRecordId,
      'accountId': accountId,
    };

    await _withdrawApi.withdraw(body);
  }
}
