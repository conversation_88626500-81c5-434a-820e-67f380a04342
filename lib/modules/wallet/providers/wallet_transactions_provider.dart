import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/modules/wallet/providers/wallet_api_provider.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:dio/dio.dart';

/// Wallet transactions async provider
final walletTransactionsProvider =
    FutureProvider.autoDispose<List<Transaction>>((ref) async {
      final walletApi = ref.watch(walletApiProvider);

      try {
        final response = await walletApi.getBankTransactions({
          'page': 1,
          'pageSize': 5,
        });

        if (response.status) {
          return response.data;
        } else {
          throw Exception(response.message);
        }
      } on DioException catch (error, stackTrace) {
        final failure = _DioExceptionMapperHelper().mapDioExceptionToFailure(
          error,
          stackTrace,
        );
        throw Exception(failure.message);
      } catch (error) {
        throw Exception(error.toString());
      }
    }, name: 'walletTransactionsProvider');

/// Helper class to use DioExceptionMapper mixin
class _DioExceptionMapperHelper with DioExceptionMapper {}
