import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/modules/wallet/widgets/bank_account_card.dart';
import 'package:maisour/modules/wallet/widgets/protected_instruction.dart';
import 'package:maisour/modules/wallet/widgets/transactions_card.dart';
import 'package:maisour/modules/wallet/widgets/wallet_card.dart';
import 'package:maisour/modules/welcome/widgets/regulated_by_dsfa.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/app_bars/app_sliver_app_bar.dart';
import 'package:maisour/shared/widgets/layouts/spacer.dart';

class WalletScreen extends ConsumerWidget {
  const WalletScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider)!;

    return CustomScrollView(
      slivers: [
        AppSliverAppBar(
          title: LocaleKeys.wallet.tr(),
          pinned: true,
          floating: false,
          snap: false,
        ),
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              16.w.heightBox,
              // Wallet card with balances and topup button
              WalletCard(user: user).paddingHorizontal(16.w),
              16.w.heightBox,
              AppSpacer(),
              TransactionsCard(),
              AppSpacer(),
              BankAccountCard(user: user),
              16.w.heightBox,
              ProtectedInstruction().paddingHorizontal(16.w),
              16.w.heightBox,
              RegulatedByDsfa(
                iconSize: 24.w,
                textStyle: AppTextStyles.text14.medium.dark300,
              ),
              16.w.heightBox,
            ],
          ),
        ),
      ],
    );
  }
}
