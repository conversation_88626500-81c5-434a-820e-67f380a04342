import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';
import 'package:maisour/shared/constants/api_constants.dart';

part 'wallet_api.g.dart';

@RestApi()
abstract class WalletApi {
  factory WalletApi(Dio dio, {String baseUrl}) = _WalletApi;

  @POST(ApiEndpoints.getBankTransactions)
  Future<TransactionsResponse> getBankTransactions(
    @Body() Map<String, dynamic> request,
  );
}
