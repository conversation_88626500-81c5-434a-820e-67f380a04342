import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/modules/wallet/providers/wallet_transactions_provider.dart';
import 'package:maisour/modules/transactions/widgets/transaction_item.dart';
import 'package:maisour/modules/transactions/models/transaction.dart';

import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';

class TransactionsCard extends ConsumerWidget {
  const TransactionsCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(walletTransactionsProvider);

    return transactionsAsync.when(
      loading: () => _buildLoadingState(context),
      error: (error, stackTrace) => _buildErrorState(context, error.toString()),
      data: (transactions) {
        if (transactions.isEmpty) {
          return _buildEmptyTransactions(context);
        } else {
          return _buildTransactionsList(
            context,
            transactions,
            ref.read(userProvider)!,
          );
        }
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Column(
      children: [
        _buildTitle(context, []),
        16.h.heightBox,
        AppShimmerLoader.fixed(itemCount: 3, itemHeight: 64.h, itemSpacing: 12),
      ],
    ).paddingAll(16.w);
  }

  Widget _buildTransactionsList(
    BuildContext context,
    List<Transaction> transactions,
    AppUser user,
  ) {
    return Column(
      children: [
        _buildTitle(context, transactions).paddingAll(16.w),

        Column(
          children: [
            // Show all transactions (up to 3, but can be less)
            ...transactions
                .take(3)
                .toList()
                .asMap()
                .entries
                .map(
                  (entry) => TransactionItem(
                    transaction: entry.value,
                    user: user,
                    isLast:
                        entry.key ==
                        transactions.take(3).length -
                            1, // Last item in displayed list
                  ),
                ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Text(
        errorMessage,
        style: AppTextStyles.text14.medium.gray700,
        textAlign: TextAlign.center,
      ),
    ).paddingAll(16.w);
  }

  Widget _buildTitle(BuildContext context, List<Transaction> transactions) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          LocaleKeys.transactions.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        // Header with "View all" button
        if (transactions.length > 3)
          Text(
            LocaleKeys.viewAll.tr(),
            style: AppTextStyles.text14.semiBold.primary,
          ).onTap(() {
            _navigateToTransactionsList(context);
          }),
      ],
    );
  }

  Widget _buildEmptyTransactions(BuildContext context) {
    return Column(
      children: [
        _buildTitle(context, []),
        16.h.heightBox,
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Icon
            Stack(
              alignment: AlignmentDirectional.center,
              children: [
                Container(
                  width: 46.w,
                  height: 46.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.alphaPercent(10),
                  ),
                ),
                Assets.icons.coin.svg(
                  width: 28.w,
                  height: 28.w,
                  colorFilter: ColorFilter.mode(
                    AppColors.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
            16.h.heightBox,
            // Heading
            Text(
              LocaleKeys.noTransactionsYet.tr(),
              style: AppTextStyles.text16.bold.dark900,
              textAlign: TextAlign.center,
            ),
            8.h.heightBox,
            // Description
            Text(
              LocaleKeys.thisIsWhereYourTransactionsWillBeListed.tr(),
              style: AppTextStyles.text12.medium.gray700,
              textAlign: TextAlign.center,
            ),
            16.h.heightBox,
            // Button
            _buildStartInvestingButton(context),
          ],
        ),
      ],
    ).paddingAll(16.w);
  }

  /// Navigates to transactions list screen
  void _navigateToTransactionsList(BuildContext context) {
    GoRouter.of(context).pushNamed(RouteName.transactions.name);
  }

  /// Builds the start investing button
  Widget _buildStartInvestingButton(BuildContext context) {
    return AppButton(
      width: 150.w,
      text: LocaleKeys.startInvesting.tr(),
      type: ButtonType.filled,
      onPressed: () => _navigateToProperties(context),
    );
  }

  /// Navigates to properties screen
  void _navigateToProperties(BuildContext context) {
    // Navigate to properties screen to start investing
    // You can customize this navigation based on your app's flow
    // For now, we'll navigate to the properties screen
    GoRouter.of(context).goNamed(RouteName.properties.name);
  }
}
