import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';

class ProtectedInstruction extends StatelessWidget {
  const ProtectedInstruction({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PositionedDirectional(
          top: 0,
          end: 0,
          child: Assets.icons.halfCircleGreen.svg(width: 80.w, height: 80.w),
        ),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: AppColors.success.alphaPercent(5),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.yourMoneyIsProtected.tr(),
                style: AppTextStyles.text12.bold.dark900,
              ),
              12.w.heightBox,
              _buildInstruction(LocaleKeys.maisourRegulatedDSFA.tr()),
              8.w.heightBox,
              _buildInstruction(
                LocaleKeys.thePropertiesAreRegisteredInDLD.tr(),
              ),
              8.w.heightBox,
              _buildInstruction(
                LocaleKeys.weHaveAPlanForSafeKeepingAssets.tr(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInstruction(String text) {
    return Row(
      children: [
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              width: 16.w,
              height: 16.w,
              decoration: BoxDecoration(
                color: AppColors.success,
                shape: BoxShape.circle,
              ),
            ),
            Assets.icons.check.svg(
              width: 10.w,
              height: 10.w,
              colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
            ),
          ],
        ),
        8.w.widthBox,
        Text(text, style: AppTextStyles.text12.medium.dark300),
      ],
    );
  }
}
