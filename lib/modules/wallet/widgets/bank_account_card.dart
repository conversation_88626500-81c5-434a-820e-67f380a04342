import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/modules/bank_accounts/models/bank_account.dart';
import 'package:maisour/modules/bank_accounts/providers/bank_accounts_provider.dart';
import 'package:maisour/modules/bank_accounts/widgets/bank_account_list_item.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';

class BankAccountCard extends ConsumerWidget {
  const BankAccountCard({super.key, required this.user});

  final AppUser user;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bankAccountsAsync = ref.watch(bankAccountsProvider);

    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            LocaleKeys.bankAccounts.tr(),
            style: AppTextStyles.text18.bold.dark900,
          ),
          4.h.heightBox,
          Text(
            LocaleKeys.safeTransfersIn23Days.tr(),
            style: AppTextStyles.text12.medium.gray,
          ),

          16.h.heightBox,
          // Bank accounts content
          bankAccountsAsync.when(
            data: (bankAccounts) {
              if (bankAccounts.isEmpty) {
                return Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(
                    vertical: 24.w,
                    horizontal: 16.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(color: AppColors.gray.shade100),
                  ),
                  child: _buildEmptyState(context, ref),
                );
              }

              // Sort bank accounts: primary first
              final sortedBankAccounts = List<BankAccount>.from(bankAccounts)
                ..sort((a, b) {
                  // Primary accounts first
                  if (a.isPrimary && !b.isPrimary) return -1;
                  if (!a.isPrimary && b.isPrimary) return 1;
                  // Keep original order for non-primary accounts
                  return 0;
                });

              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(color: AppColors.gray.shade100),
                ),
                child: Column(
                  children: [
                    ...sortedBankAccounts.asMap().entries.map((entry) {
                      final index = entry.key;
                      final bankAccount = entry.value;
                      final isLast = index == sortedBankAccounts.length - 1;

                      return BankAccountListItem(
                        bankAccount: bankAccount,
                        isPrimary: bankAccount.isPrimary,
                        isLast: isLast,
                        onTap: () => _navigateToViewBankAccount(
                          context,
                          ref,
                          bankAccount,
                        ),
                      );
                    }),

                    Divider(color: AppColors.gray.shade200),

                    AppButton(
                      text: LocaleKeys.addBankAccount.tr(),
                      type: ButtonType.text,
                      textColor: AppColors.primary,
                      icon: Icon(Icons.add, color: AppColors.primary),
                      onPressed: () => _navigateToAddBankAccount(context, ref),
                    ),
                    4.w.heightBox,
                  ],
                ),
              );
            },
            loading: () =>
                AppShimmerLoader.fixed(itemCount: 3, itemHeight: 80.w),
            error: (error, stackTrace) =>
                Center(child: Text(error.toString())).paddingAll(16.w),
          ),
        ],
      ),
    );
  }

  /// Builds the empty state with icon, text, and button
  Widget _buildEmptyState(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Icon
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              width: 46.w,
              height: 46.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primary.alphaPercent(10),
              ),
            ),
            Assets.icons.bankAccount.svg(
              width: 28.w,
              height: 28.w,
              colorFilter: ColorFilter.mode(AppColors.primary, BlendMode.srcIn),
            ),
          ],
        ),
        16.h.heightBox,
        // Heading
        Text(
          LocaleKeys.addBankAccount.tr(),
          style: AppTextStyles.text16.bold.dark900,
          textAlign: TextAlign.center,
        ),
        8.h.heightBox,
        // Description
        Text(
          LocaleKeys.itMustBeAPersonalOrJointlyOwnedBankAccountUnderYourName
              .tr(),
          style: AppTextStyles.text12.medium.gray700,
          textAlign: TextAlign.center,
        ),
        16.h.heightBox,
        // Button
        _buildAddBankAccountButton(context, ref),
      ],
    );
  }

  /// Builds the add bank account button
  Widget _buildAddBankAccountButton(BuildContext context, WidgetRef ref) {
    return AppButton(
      width: 150.w,
      text: LocaleKeys.addAccount.tr(),
      type: ButtonType.filled,
      icon: Icon(Icons.add, color: AppColors.white, size: 16.w),
      onPressed: () => _navigateToAddBankAccount(context, ref),
    );
  }

  /// Navigates to add bank account screen
  void _navigateToAddBankAccount(BuildContext context, WidgetRef ref) {
    // If still user has not verified their account, show a warning toast
    if (user.verifiedStatus == VerifiedStatus.notYet ||
        user.verifiedStatus == VerifiedStatus.started) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.pleaseVerifyYourAccount.tr(),
      );
    }
    // If user's identity verification is under review, show a warning toast
    else if (user.verifiedStatus == VerifiedStatus.pending) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourIdentityVerificationIsUnderReview.tr(),
      );
    } else if (user.phoneNumber == null) {
      GoRouter.of(context).pushNamed(RouteName.phoneVerification.name);
    }
    // If user's passport is expired, show a warning toast
    else if (user.passportExpirationStatus.isExpired &&
        user.accountStatus == AccountStatus.suspended) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourPassportIsExpired.tr(),
      );
    } else if (!user.canInvest) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.asPerAmlRegulationsAccountRestricted.tr(),
      );
    } else {
      GoRouter.of(context).pushNamed(RouteName.addBankAccount.name).then((
        value,
      ) {
        if (value is bool && value == true) {
          ref.read(bankAccountsProvider.notifier).refresh();
        }
      });
    }
  }

  void _navigateToViewBankAccount(
    BuildContext context,
    WidgetRef ref,
    BankAccount bankAccount,
  ) {
    GoRouter.of(context)
        .pushNamed(RouteName.viewBankAccount.name, extra: bankAccount)
        .then((value) {
          ref.read(bankAccountsProvider.notifier).refresh();
        });
  }
}
