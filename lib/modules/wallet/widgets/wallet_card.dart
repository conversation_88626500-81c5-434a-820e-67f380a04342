import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/enums/account_status.dart';
import 'package:maisour/shared/enums/currency.dart';
import 'package:maisour/shared/enums/verified_status.dart';
import 'package:maisour/shared/extensions/app_user_extension.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';

class WalletCard extends ConsumerWidget {
  final AppUser user;

  const WalletCard({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // Bottom section - Reward Balance
        _buildRewardBalanceSection(),
        Container(
          padding: EdgeInsets.all(16.w),
          margin: EdgeInsets.only(bottom: 50.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(color: AppColors.gray.shade100),
            color: AppColors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        LocaleKeys.walletBalance.tr(),
                        style: AppTextStyles.text14.medium.gray,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildCurrencyIcon(
                            26.w,
                            AppTextStyles.text24.bold.dark,
                          ),
                          4.w.widthBox,
                          Text(
                            user.getCurrencyValue(user.balance),
                            style: AppTextStyles.text24.bold.dark,
                          ),
                        ],
                      ),
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        LocaleKeys.cashBalance.tr(),
                        style: AppTextStyles.text12.medium.gray,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildCurrencyIcon(
                            20.w,
                            AppTextStyles.text16.bold.dark,
                          ),
                          4.w.widthBox,
                          Text(
                            user.getCurrencyValue(user.credit),
                            style: AppTextStyles.text16.bold.dark,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),

              16.h.heightBox,
              // Middle section - Top up button
              Row(
                children: [
                  Expanded(
                    child: AppButton(
                      text: LocaleKeys.topUp.tr(),
                      type: ButtonType.filled,
                      onPressed: () => _navigateToTopup(context),
                      icon: Icon(Icons.add, color: AppColors.white, size: 16.w),
                    ),
                  ),
                  if (user.credit > 0)
                    Expanded(
                      child: AppButton(
                        text: LocaleKeys.withdraw.tr(),
                        type: ButtonType.outlined,
                        borderColor: AppColors.gray.shade200,
                        onPressed: () => _navigateToWithdraw(context),
                      ).paddingStart(10.w),
                    ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds the currency icon (circular with currency symbol)
  Widget _buildCurrencyIcon(double imageSize, TextStyle textStyle) {
    return user.usesImageSymbol
        ? Assets.images.dirham.image(width: imageSize, height: imageSize)
        : Text('${user.currencyCode.currencySymbol} ', style: textStyle);
  }

  /// Builds the reward balance section with gift icon and chevron
  Widget _buildRewardBalanceSection() {
    return Container(
      height: 80.h,
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 42.w,
        bottom: 10.w,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: AppColors.primary.alphaPercent(10),
      ),
      child: Row(
        children: [
          // Gift icon
          Stack(
            alignment: AlignmentDirectional.center,
            children: [
              Container(
                height: 32.w,
                width: 32.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.primary,
                ),
              ),
              Assets.icons.gift.svg(
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(AppColors.white, BlendMode.srcIn),
              ),
            ],
          ),
          8.w.widthBox,
          // Reward Balance label
          Text(
            LocaleKeys.rewardBalance.tr(),
            style: AppTextStyles.text14.bold.dark900,
          ),
          Spacer(),
          // Amount with currency icon
          Row(
            children: [
              _buildCurrencyIcon(20.w, AppTextStyles.text14.bold.dark900),
              4.w.widthBox,
              Text(
                user.getCurrencyValue(user.rewardPoints),
                style: AppTextStyles.text14.bold.dark900,
              ),
            ],
          ),
          // 2.w.widthBox,
          // // Chevron icon
          // Icon(Icons.chevron_right, color: AppColors.gray, size: 24.w),
        ],
      ),
    );
  }

  /// Navigates to the topup screen
  void _navigateToTopup(BuildContext context) {
    // If still user has not verified their account, show a warning toast
    if (user.verifiedStatus == VerifiedStatus.notYet ||
        user.verifiedStatus == VerifiedStatus.started) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.pleaseVerifyYourAccount.tr(),
      );
    }
    // If user's identity verification is under review, show a warning toast
    else if (user.verifiedStatus == VerifiedStatus.pending) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourIdentityVerificationIsUnderReview.tr(),
      );
    } else if (user.phoneNumber == null) {
      GoRouter.of(context).pushNamed(RouteName.phoneVerification.name);
    }
    // If user's passport is expired, show a warning toast
    else if (user.passportExpirationStatus.isExpired &&
        user.accountStatus == AccountStatus.suspended) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourPassportIsExpired.tr(),
      );
    } else if (!user.canInvest) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.asPerAmlRegulationsAccountRestricted.tr(),
      );
    } else {
      GoRouter.of(context).pushNamed(RouteName.addFund.name);
    }
  }

  void _navigateToWithdraw(BuildContext context) {
    // If still user has not verified their account, show a warning toast
    if (user.verifiedStatus == VerifiedStatus.notYet ||
        user.verifiedStatus == VerifiedStatus.started) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.pleaseVerifyYourAccount.tr(),
      );
    }
    // If user's identity verification is under review, show a warning toast
    else if (user.verifiedStatus == VerifiedStatus.pending) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourIdentityVerificationIsUnderReview.tr(),
      );
    } else if (user.phoneNumber == null) {
      GoRouter.of(context).pushNamed(RouteName.phoneVerification.name);
    }
    // If user's passport is expired, show a warning toast
    else if (user.passportExpirationStatus.isExpired &&
        user.accountStatus == AccountStatus.suspended) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.yourPassportIsExpired.tr(),
      );
    } else if (!user.canInvest) {
      context.showWarningToast(
        LocaleKeys.actionRestricted.tr(),
        LocaleKeys.asPerAmlRegulationsAccountRestricted.tr(),
      );
    } else {
      GoRouter.of(context).pushNamed(RouteName.withdraw.name);
    }
  }
}
