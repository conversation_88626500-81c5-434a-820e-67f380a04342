import 'package:dio/dio.dart';
import 'package:maisour/modules/close_account/models/account_closure_verification.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import 'package:retrofit/retrofit.dart';

part 'close_account_api.g.dart';

@RestApi()
abstract class CloseAccountApi {
  factory CloseAccountApi(Dio dio, {String? baseUrl}) = _CloseAccountApi;

  /// Verify account closure conditions
  @GET(ApiEndpoints.verifyAccountClosure)
  Future<AccountClosureVerificationResponse> verifyAccountClosure();

  /// Get public IP
  @GET(ApiEndpoints.getPublicIp)
  Future<String> getPublicIp();

  /// Generate OTP for account closure
  @POST(ApiEndpoints.generateOTP)
  Future<OtpResponse> generateOTP(@Query('ipAddress') String ipAddress);

  /// Close account with OTP verification
  @POST(ApiEndpoints.closeAccount)
  Future<void> closeAccount({@Body() required Map<String, dynamic> body});
}
