import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/close_account/close_account_service.dart';
import 'package:maisour/modules/close_account/providers/close_account_providers.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Provider for CloseAccountOperationsNotifier
final closeAccountOperationsNotifierProvider =
    StateNotifierProvider.autoDispose<
      CloseAccountOperationsNotifier,
      CloseAccountOperationsState
    >((ref) {
      final service = ref.watch(closeAccountServiceProvider);
      return CloseAccountOperationsNotifier(service);
    }, name: 'closeAccountOperationsNotifierProvider');

class CloseAccountOperationsNotifier
    extends StateNotifier<CloseAccountOperationsState>
    with DioExceptionMapper {
  final CloseAccountService _service;

  CloseAccountOperationsNotifier(this._service)
    : super(const CloseAccountOperationsState());

  /// Generate OTP for account closure
  Future<void> generateOTP() async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: true,
      isClosingAccount: false,
    );

    try {
      final otpResponse = await _service.generateOTP();
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        otpId: otpResponse.id,
        isGeneratingOTP: true,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isGeneratingOTP: false,
      );
    }
  }

  /// Close account with OTP verification
  Future<void> closeAccount({
    required int otpRecordId,
    required String reason,
  }) async {
    state = state.copyWith(
      status: ApiStatus.loading,
      isGeneratingOTP: false,
      isClosingAccount: true,
    );

    try {
      await _service.closeAccount(otpRecordId: otpRecordId, reason: reason);
      state = state.copyWith(
        status: ApiStatus.success,
        errorMessage: null,
        errorKey: null,
        isClosingAccount: true,
      );
    } catch (error, stackTrace) {
      String errorMessage;
      String? errorKey;

      if (error is DioException) {
        final failure = mapDioExceptionToFailure(error, stackTrace);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = error.toString();
      }

      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
        isClosingAccount: false,
      );
    }
  }
}

class CloseAccountOperationsState {
  final ApiStatus status;
  final String? errorMessage;
  final String? errorKey;
  final int? otpId;
  final bool isGeneratingOTP;
  final bool isClosingAccount;

  const CloseAccountOperationsState({
    this.status = ApiStatus.initial,
    this.errorMessage,
    this.errorKey,
    this.otpId,
    this.isGeneratingOTP = false,
    this.isClosingAccount = false,
  });

  CloseAccountOperationsState copyWith({
    ApiStatus? status,
    String? errorMessage,
    String? errorKey,
    int? otpId,
    bool? isGeneratingOTP,
    bool? isClosingAccount,
  }) {
    return CloseAccountOperationsState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
      otpId: otpId ?? this.otpId,
      isGeneratingOTP: isGeneratingOTP ?? this.isGeneratingOTP,
      isClosingAccount: isClosingAccount ?? this.isClosingAccount,
    );
  }
}
