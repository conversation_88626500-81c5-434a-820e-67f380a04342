import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/close_account/models/account_closure_verification.dart';
import 'package:maisour/modules/close_account/providers/close_account_api_provider.dart';
import 'package:maisour/modules/close_account/close_account_service.dart';

/// Provider for close account service
final closeAccountServiceProvider = Provider.autoDispose<CloseAccountService>((
  ref,
) {
  final closeAccountApi = ref.watch(closeAccountApiProvider);
  return CloseAccountService(closeAccountApi);
}, name: 'closeAccountServiceProvider');

/// Notifier for account closure verification state management
class AccountClosureVerificationNotifier
    extends StateNotifier<AsyncValue<List<AccountClosureCondition>>> {
  final CloseAccountService _service;

  AccountClosureVerificationNotifier(this._service)
    : super(const AsyncValue.data([]));

  /// Verify account closure conditions
  Future<void> verifyAccountClosure() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return await _service.verifyAccountClosure();
    });
  }

  /// Check if all conditions are valid
  bool get areAllConditionsValid {
    final conditions = state.value;
    if (conditions == null || conditions.isEmpty) return false;
    return conditions.every((condition) => condition.valid);
  }

  /// Get invalid conditions
  List<AccountClosureCondition> get invalidConditions {
    final conditions = state.value;
    if (conditions == null) return [];
    return conditions.where((condition) => !condition.valid).toList();
  }
}

/// Provider for account closure verification notifier
final accountClosureVerificationNotifierProvider =
    StateNotifierProvider.autoDispose<
      AccountClosureVerificationNotifier,
      AsyncValue<List<AccountClosureCondition>>
    >(
      (ref) => AccountClosureVerificationNotifier(
        ref.read(closeAccountServiceProvider),
      ),
      name: 'accountClosureVerificationNotifierProvider',
    );
