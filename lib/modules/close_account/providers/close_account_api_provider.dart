import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/modules/close_account/api/close_account_api.dart';
import 'package:maisour/shared/services/network_service.dart';

/// Retrofit API provider for close account module
final closeAccountApiProvider = Provider.autoDispose<CloseAccountApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return CloseAccountApi(dio);
}, name: 'closeAccountApiProvider');
