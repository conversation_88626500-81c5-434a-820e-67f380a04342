import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/modules/close_account/providers/close_account_providers.dart';
import 'package:maisour/modules/close_account/providers/close_account_operations_provider.dart';
import 'package:maisour/modules/close_account/models/account_closure_verification.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/enums/api_status.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';

class CloseAccountScreen extends ConsumerStatefulWidget {
  const CloseAccountScreen({super.key});

  @override
  ConsumerState<CloseAccountScreen> createState() => _CloseAccountScreenState();
}

class _CloseAccountScreenState extends ConsumerState<CloseAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _reasonForClosureController =
      TextEditingController();
  late final TextEditingController _fullNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  String _reasonForClosure = '';

  @override
  void initState() {
    super.initState();
    _initializeFormData();
  }

  void _initializeFormData() {
    final user = ref.readCurrentUser;
    if (user != null) {
      _fullNameController = TextEditingController(text: user.user.fullName);
      _emailController = TextEditingController(text: user.user.email);
      _phoneController = TextEditingController(
        text: user.phoneNumber ?? LocaleKeys.notProvided.tr(),
      );
    } else {
      _fullNameController = TextEditingController();
      _emailController = TextEditingController();
      _phoneController = TextEditingController();
    }
  }

  @override
  void dispose() {
    _reasonForClosureController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _onSubmit() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      // Store the reason for later use
      _reasonForClosure = _reasonForClosureController.text.trim();

      // First verify account closure conditions
      ref
          .read(accountClosureVerificationNotifierProvider.notifier)
          .verifyAccountClosure();
    }
  }

  /// Handle successful account closure - perform logout and clear data
  Future<void> _handleAccountClosureSuccess(
    BuildContext context,
    WidgetRef ref,
  ) async {
    try {
      // Show success toast
      context.showSuccessToast(
        LocaleKeys.success.tr(),
        LocaleKeys.yourAccountHasBeenSuccessfullyClosed.tr(),
      );

      // Perform comprehensive logout (same as logout functionality)
      final authNotifier = ref.read(
        authNotifierProvider('close_account').notifier,
      );
      await authNotifier.signOut();

      // Navigate to login screen and clear all routes
      if (context.mounted) {
        context.goNamed(RouteName.login.name);
      }
    } catch (error) {
      debugPrint('❌ Error during account closure logout: $error');
      // Even if logout fails, navigate to login screen
      if (context.mounted) {
        context.goNamed(RouteName.login.name);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<
      AsyncValue<List<AccountClosureCondition>>
    >(accountClosureVerificationNotifierProvider, (previous, next) {
      if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

      // Only handle state changes from loading to data/error, not initial state
      if (previous?.isLoading == true && !next.isLoading) {
        next.when(
          data: (conditions) {
            final notifier = ref.read(
              accountClosureVerificationNotifierProvider.notifier,
            );
            if (notifier.areAllConditionsValid) {
              // All conditions are valid, proceed with OTP generation
              ref
                  .read(closeAccountOperationsNotifierProvider.notifier)
                  .generateOTP();
            } else {
              // Show invalid conditions
              final invalidConditions = notifier.invalidConditions;
              final message = invalidConditions
                  .map((condition) => '• ${condition.message}')
                  .join('\n');
              context.showErrorToast(
                LocaleKeys.actionRequired.tr(),
                'You must full fill these actions to can close your account:\n$message', // no need to translate because its comes from BE
                duration: const Duration(seconds: 15),
              );
            }
          },
          error: (error, stackTrace) {
            context.showErrorToast(LocaleKeys.oops.tr(), error.toString());
          },
          loading: () {},
        );
      }
    });

    // Listen to close account operations state
    ref.listen<CloseAccountOperationsState>(
      closeAccountOperationsNotifierProvider,
      (previous, next) {
        if (!(ModalRoute.of(context)?.isCurrent ?? true)) return;

        if (next.status == ApiStatus.success && next.isGeneratingOTP == true) {
          // OTP generation successful, navigate to OTP verification
          GoRouter.of(context).pushNamed(RouteName.otpVerification.name).then((
            value,
          ) {
            if (value is int) {
              // Call close account API with OTP record ID and reason
              ref
                  .read(closeAccountOperationsNotifierProvider.notifier)
                  .closeAccount(otpRecordId: value, reason: _reasonForClosure);
            }
          });
        } else if (next.status == ApiStatus.success &&
            next.isClosingAccount == true) {
          // Account closure successful - perform logout and clear data
          _handleAccountClosureSuccess(context, ref);
        } else if (next.status == ApiStatus.error) {
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: next.errorKey,
            fallbackMessage:
                next.errorMessage ?? LocaleKeys.somethingWentWrong.tr(),
          );
          context.showErrorToast(title, message);
        }
      },
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.closeAccount.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child:
            Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Instruction Text
                          Container(
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: AppColors.warning.alphaPercent(10),
                              borderRadius: BorderRadius.circular(8.w),
                              border: Border.all(
                                color: AppColors.warning.alphaPercent(30),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              LocaleKeys.deleteAccountActionRequired.tr(),
                              style: AppTextStyles.text14.medium.dark700,
                            ),
                          ),

                          16.h.heightBox,

                          // Full Name Field (Non-editable)
                          AppTextField(
                            controller: _fullNameController,
                            labelText: LocaleKeys.fullName.tr(),
                            readOnly: true,
                            keyboardType: TextInputType.name,
                            textInputAction: TextInputAction.next,
                            textCapitalization: TextCapitalization.words,
                          ),

                          16.h.heightBox,

                          // Email Field (Non-editable)
                          AppTextField(
                            controller: _emailController,
                            labelText: LocaleKeys.email.tr(),
                            readOnly: true,
                            keyboardType: TextInputType.emailAddress,
                            textInputAction: TextInputAction.next,
                          ),

                          16.h.heightBox,

                          // Phone Number Field (Non-editable)
                          AppTextField(
                            controller: _phoneController,
                            labelText: LocaleKeys.phoneNumber.tr(),
                            readOnly: true,
                            keyboardType: TextInputType.phone,
                            textInputAction: TextInputAction.next,
                          ),

                          16.h.heightBox,

                          // Reason for Closing Account Field
                          AppTextField(
                            labelText:
                                '${LocaleKeys.reasonForClosingAccount.tr()}*',
                            controller: _reasonForClosureController,
                            keyboardType: TextInputType.multiline,
                            textInputAction: TextInputAction.newline,
                            minLines: 4,
                            maxLines: 8,
                            validator: (value) => AppValidators.required(
                              LocaleKeys.reasonForClosingAccount.tr(),
                              value,
                            ),
                          ),
                        ],
                      ).paddingAll(16.w),
                    ),
                  ),

                  // Bottom Buttons
                  Container(
                    padding: EdgeInsets.all(16.w),
                    child: Column(
                      children: [
                        Consumer(
                          builder: (context, ref, child) {
                            final verificationState = ref.watch(
                              accountClosureVerificationNotifierProvider,
                            );
                            final operationsState = ref.watch(
                              closeAccountOperationsNotifierProvider,
                            );
                            final isLoading =
                                verificationState.isLoading ||
                                operationsState.isGeneratingOTP ||
                                operationsState.isClosingAccount;

                            return AppButton(
                              text: LocaleKeys.submit.tr(),
                              onPressed: _onSubmit,
                              backgroundColor: AppColors.danger,
                              isLoading: isLoading,
                            );
                          },
                        ),
                        8.h.heightBox,
                        AppButton(
                          type: ButtonType.text,
                          text: LocaleKeys.cancel.tr(),
                          onPressed: () => GoRouter.of(context).pop(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ).onTap(() {
              FocusManager.instance.primaryFocus?.unfocus();
            }),
      ),
    );
  }
}
