import 'package:dio/dio.dart';
import 'package:maisour/modules/close_account/api/close_account_api.dart';
import 'package:maisour/modules/close_account/models/account_closure_verification.dart';
import 'package:maisour/modules/phone_verification/models/otp_response.dart';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';

/// Service class for handling close account operations
class CloseAccountService with DioExceptionMapper {
  final CloseAccountApi _closeAccountApi;

  CloseAccountService(this._closeAccountApi);

  /// Verify account closure conditions
  Future<List<AccountClosureCondition>> verifyAccountClosure() async {
    try {
      final response = await _closeAccountApi.verifyAccountClosure();
      return response.result;
    } catch (e) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        throw failure;
      }
      throw Exception('Failed to verify account closure: $e');
    }
  }

  /// Generate OTP for account closure
  Future<OtpResponse> generateOTP() async {
    try {
      // Get public IP address first
      final ipAddress = await _closeAccountApi.getPublicIp();

      // Call API with IP address as query parameter
      final otpResponse = await _closeAccountApi.generateOTP(ipAddress);
      return otpResponse;
    } catch (e) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        throw failure;
      }
      throw Exception('Failed to generate OTP: $e');
    }
  }

  /// Close account with OTP verification
  Future<void> closeAccount({
    required int otpRecordId,
    required String reason,
  }) async {
    try {
      await _closeAccountApi.closeAccount(
        body: {'otpRecordId': otpRecordId, 'closureReason': reason},
      );
    } catch (e) {
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        throw failure;
      }
      throw Exception('Failed to close account: $e');
    }
  }
}
