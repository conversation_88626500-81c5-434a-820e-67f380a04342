// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_closure_verification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AccountClosureCondition _$AccountClosureConditionFromJson(
  Map<String, dynamic> json,
) => _AccountClosureCondition(
  condition: json['condition'] as String,
  valid: json['valid'] as bool,
  message: json['message'] as String?,
);

Map<String, dynamic> _$AccountClosureConditionToJson(
  _AccountClosureCondition instance,
) => <String, dynamic>{
  'condition': instance.condition,
  'valid': instance.valid,
  'message': instance.message,
};

_AccountClosureVerificationResponse
_$AccountClosureVerificationResponseFromJson(Map<String, dynamic> json) =>
    _AccountClosureVerificationResponse(
      result: (json['result'] as List<dynamic>)
          .map(
            (e) => AccountClosureCondition.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
    );

Map<String, dynamic> _$AccountClosureVerificationResponseToJson(
  _AccountClosureVerificationResponse instance,
) => <String, dynamic>{'result': instance.result};
