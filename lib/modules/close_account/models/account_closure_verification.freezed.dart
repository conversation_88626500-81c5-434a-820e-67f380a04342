// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_closure_verification.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountClosureCondition {

 String get condition; bool get valid; String? get message;
/// Create a copy of AccountClosureCondition
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountClosureConditionCopyWith<AccountClosureCondition> get copyWith => _$AccountClosureConditionCopyWithImpl<AccountClosureCondition>(this as AccountClosureCondition, _$identity);

  /// Serializes this AccountClosureCondition to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountClosureCondition&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.valid, valid) || other.valid == valid)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,condition,valid,message);

@override
String toString() {
  return 'AccountClosureCondition(condition: $condition, valid: $valid, message: $message)';
}


}

/// @nodoc
abstract mixin class $AccountClosureConditionCopyWith<$Res>  {
  factory $AccountClosureConditionCopyWith(AccountClosureCondition value, $Res Function(AccountClosureCondition) _then) = _$AccountClosureConditionCopyWithImpl;
@useResult
$Res call({
 String condition, bool valid, String? message
});




}
/// @nodoc
class _$AccountClosureConditionCopyWithImpl<$Res>
    implements $AccountClosureConditionCopyWith<$Res> {
  _$AccountClosureConditionCopyWithImpl(this._self, this._then);

  final AccountClosureCondition _self;
  final $Res Function(AccountClosureCondition) _then;

/// Create a copy of AccountClosureCondition
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? condition = null,Object? valid = null,Object? message = freezed,}) {
  return _then(_self.copyWith(
condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,valid: null == valid ? _self.valid : valid // ignore: cast_nullable_to_non_nullable
as bool,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [AccountClosureCondition].
extension AccountClosureConditionPatterns on AccountClosureCondition {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AccountClosureCondition value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AccountClosureCondition() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AccountClosureCondition value)  $default,){
final _that = this;
switch (_that) {
case _AccountClosureCondition():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AccountClosureCondition value)?  $default,){
final _that = this;
switch (_that) {
case _AccountClosureCondition() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String condition,  bool valid,  String? message)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AccountClosureCondition() when $default != null:
return $default(_that.condition,_that.valid,_that.message);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String condition,  bool valid,  String? message)  $default,) {final _that = this;
switch (_that) {
case _AccountClosureCondition():
return $default(_that.condition,_that.valid,_that.message);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String condition,  bool valid,  String? message)?  $default,) {final _that = this;
switch (_that) {
case _AccountClosureCondition() when $default != null:
return $default(_that.condition,_that.valid,_that.message);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AccountClosureCondition implements AccountClosureCondition {
  const _AccountClosureCondition({required this.condition, required this.valid, this.message});
  factory _AccountClosureCondition.fromJson(Map<String, dynamic> json) => _$AccountClosureConditionFromJson(json);

@override final  String condition;
@override final  bool valid;
@override final  String? message;

/// Create a copy of AccountClosureCondition
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountClosureConditionCopyWith<_AccountClosureCondition> get copyWith => __$AccountClosureConditionCopyWithImpl<_AccountClosureCondition>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountClosureConditionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountClosureCondition&&(identical(other.condition, condition) || other.condition == condition)&&(identical(other.valid, valid) || other.valid == valid)&&(identical(other.message, message) || other.message == message));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,condition,valid,message);

@override
String toString() {
  return 'AccountClosureCondition(condition: $condition, valid: $valid, message: $message)';
}


}

/// @nodoc
abstract mixin class _$AccountClosureConditionCopyWith<$Res> implements $AccountClosureConditionCopyWith<$Res> {
  factory _$AccountClosureConditionCopyWith(_AccountClosureCondition value, $Res Function(_AccountClosureCondition) _then) = __$AccountClosureConditionCopyWithImpl;
@override @useResult
$Res call({
 String condition, bool valid, String? message
});




}
/// @nodoc
class __$AccountClosureConditionCopyWithImpl<$Res>
    implements _$AccountClosureConditionCopyWith<$Res> {
  __$AccountClosureConditionCopyWithImpl(this._self, this._then);

  final _AccountClosureCondition _self;
  final $Res Function(_AccountClosureCondition) _then;

/// Create a copy of AccountClosureCondition
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? condition = null,Object? valid = null,Object? message = freezed,}) {
  return _then(_AccountClosureCondition(
condition: null == condition ? _self.condition : condition // ignore: cast_nullable_to_non_nullable
as String,valid: null == valid ? _self.valid : valid // ignore: cast_nullable_to_non_nullable
as bool,message: freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$AccountClosureVerificationResponse {

 List<AccountClosureCondition> get result;
/// Create a copy of AccountClosureVerificationResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountClosureVerificationResponseCopyWith<AccountClosureVerificationResponse> get copyWith => _$AccountClosureVerificationResponseCopyWithImpl<AccountClosureVerificationResponse>(this as AccountClosureVerificationResponse, _$identity);

  /// Serializes this AccountClosureVerificationResponse to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountClosureVerificationResponse&&const DeepCollectionEquality().equals(other.result, result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(result));

@override
String toString() {
  return 'AccountClosureVerificationResponse(result: $result)';
}


}

/// @nodoc
abstract mixin class $AccountClosureVerificationResponseCopyWith<$Res>  {
  factory $AccountClosureVerificationResponseCopyWith(AccountClosureVerificationResponse value, $Res Function(AccountClosureVerificationResponse) _then) = _$AccountClosureVerificationResponseCopyWithImpl;
@useResult
$Res call({
 List<AccountClosureCondition> result
});




}
/// @nodoc
class _$AccountClosureVerificationResponseCopyWithImpl<$Res>
    implements $AccountClosureVerificationResponseCopyWith<$Res> {
  _$AccountClosureVerificationResponseCopyWithImpl(this._self, this._then);

  final AccountClosureVerificationResponse _self;
  final $Res Function(AccountClosureVerificationResponse) _then;

/// Create a copy of AccountClosureVerificationResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? result = null,}) {
  return _then(_self.copyWith(
result: null == result ? _self.result : result // ignore: cast_nullable_to_non_nullable
as List<AccountClosureCondition>,
  ));
}

}


/// Adds pattern-matching-related methods to [AccountClosureVerificationResponse].
extension AccountClosureVerificationResponsePatterns on AccountClosureVerificationResponse {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AccountClosureVerificationResponse value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AccountClosureVerificationResponse value)  $default,){
final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AccountClosureVerificationResponse value)?  $default,){
final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( List<AccountClosureCondition> result)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse() when $default != null:
return $default(_that.result);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( List<AccountClosureCondition> result)  $default,) {final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse():
return $default(_that.result);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( List<AccountClosureCondition> result)?  $default,) {final _that = this;
switch (_that) {
case _AccountClosureVerificationResponse() when $default != null:
return $default(_that.result);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AccountClosureVerificationResponse implements AccountClosureVerificationResponse {
  const _AccountClosureVerificationResponse({required final  List<AccountClosureCondition> result}): _result = result;
  factory _AccountClosureVerificationResponse.fromJson(Map<String, dynamic> json) => _$AccountClosureVerificationResponseFromJson(json);

 final  List<AccountClosureCondition> _result;
@override List<AccountClosureCondition> get result {
  if (_result is EqualUnmodifiableListView) return _result;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_result);
}


/// Create a copy of AccountClosureVerificationResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountClosureVerificationResponseCopyWith<_AccountClosureVerificationResponse> get copyWith => __$AccountClosureVerificationResponseCopyWithImpl<_AccountClosureVerificationResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AccountClosureVerificationResponseToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountClosureVerificationResponse&&const DeepCollectionEquality().equals(other._result, _result));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_result));

@override
String toString() {
  return 'AccountClosureVerificationResponse(result: $result)';
}


}

/// @nodoc
abstract mixin class _$AccountClosureVerificationResponseCopyWith<$Res> implements $AccountClosureVerificationResponseCopyWith<$Res> {
  factory _$AccountClosureVerificationResponseCopyWith(_AccountClosureVerificationResponse value, $Res Function(_AccountClosureVerificationResponse) _then) = __$AccountClosureVerificationResponseCopyWithImpl;
@override @useResult
$Res call({
 List<AccountClosureCondition> result
});




}
/// @nodoc
class __$AccountClosureVerificationResponseCopyWithImpl<$Res>
    implements _$AccountClosureVerificationResponseCopyWith<$Res> {
  __$AccountClosureVerificationResponseCopyWithImpl(this._self, this._then);

  final _AccountClosureVerificationResponse _self;
  final $Res Function(_AccountClosureVerificationResponse) _then;

/// Create a copy of AccountClosureVerificationResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? result = null,}) {
  return _then(_AccountClosureVerificationResponse(
result: null == result ? _self._result : result // ignore: cast_nullable_to_non_nullable
as List<AccountClosureCondition>,
  ));
}


}

// dart format on
