import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_closure_verification.freezed.dart';
part 'account_closure_verification.g.dart';

@freezed
abstract class AccountClosureCondition with _$AccountClosureCondition {
  const factory AccountClosureCondition({
    required String condition,
    required bool valid,
    String? message,
  }) = _AccountClosureCondition;

  factory AccountClosureCondition.fromJson(Map<String, dynamic> json) =>
      _$AccountClosureConditionFromJson(json);
}

@freezed
abstract class AccountClosureVerificationResponse
    with _$AccountClosureVerificationResponse {
  const factory AccountClosureVerificationResponse({
    required List<AccountClosureCondition> result,
  }) = _AccountClosureVerificationResponse;

  factory AccountClosureVerificationResponse.fromJson(
    Map<String, dynamic> json,
  ) => _$AccountClosureVerificationResponseFromJson(json);
}
