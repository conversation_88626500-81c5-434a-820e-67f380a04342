import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/app_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/modules/splash/providers/splash_provider.dart';
import 'package:maisour/modules/splash/widgets/splash_view.dart';
import 'package:maisour/shared/enums/splash_decision.dart';
import 'package:maisour/shared/models/app_update_info.dart';
import 'package:maisour/modules/splash/widgets/app_update_dialog.dart';
import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:maisour/shared/widgets/layouts/gradient_background.dart';

class SplashScreen extends ConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AsyncValue<SplashResult>>(splashNotifierProvider, (_, state) {
      state.whenOrNull(
        data: (result) => _handleNavigation(context, result, ref),
      );
    });

    return GradientBackground(
      child: const Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(child: SplashView()),
      ),
    );
  }

  void _handleNavigation(
    BuildContext context,
    SplashResult result,
    WidgetRef ref,
  ) {
    switch (result.decision) {
      case SplashDecision.jailbroken:
        _showJailbreakAlertAndExit(context);
        break;
      case SplashDecision.noInternet:
        _navigateToNoInternet(context);
        break;
      case SplashDecision.updateRequired:
        _showUpdateDialog(context, result.updateInfo!, ref);
        break;
      case SplashDecision.welcome:
        _navigateToWelcome(context);
        break;
      case SplashDecision.login:
        _navigateToLogin(context);
        break;
      case SplashDecision.home:
        _navigateToHome(context);
        break;
    }
  }

  void _showJailbreakAlertAndExit(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog.adaptive(
        title: Text(LocaleKeys.deviceIsRooted.tr()),
        content: Text(
          MyPlatform.isAndroid
              ? LocaleKeys.deviceIsRootedDesc.tr()
              : LocaleKeys.deviceIsJailbrokenDesc.tr(),
        ),
        actions: [
          TextButton(
            onPressed: () => SystemNavigator.pop(), // Close app
            style: TextButton.styleFrom(foregroundColor: AppColors.danger),
            child: Text(LocaleKeys.cancel.tr(), style: AppTextStyles.textStyle),
          ),
        ],
      ),
    );
  }

  void _showUpdateDialog(
    BuildContext context,
    AppUpdateInfo updateInfo,
    WidgetRef ref,
  ) {
    AppUpdateDialog.show(
      context,
      ref,
      updateInfo,
      onOptionalUpdateDismissed: () => _continueWithNormalFlow(context, ref),
    );
  }

  void _continueWithNormalFlow(BuildContext context, WidgetRef ref) async {
    try {
      // Invalidate the splash provider to refresh and recheck all conditions
      ref.invalidate(splashNotifierProvider);
      debugPrint('🔄 Splash provider invalidated - rechecking conditions');
    } catch (e) {
      // If anything fails, default to welcome screen
      if (context.mounted) {
        _navigateToWelcome(context);
      }
    }
  }

  void _navigateToNoInternet(BuildContext context) {
    context.goNamed(RouteName.noInternet.name);
  }

  void _navigateToWelcome(BuildContext context) {
    context.goNamed(RouteName.welcome.name);
  }

  void _navigateToLogin(BuildContext context) {
    context.goNamed(RouteName.login.name);
  }

  void _navigateToHome(BuildContext context) {
    context.goNamed(RouteName.properties.name);
  }
}
