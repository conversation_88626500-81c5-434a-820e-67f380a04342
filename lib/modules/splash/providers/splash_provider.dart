import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';
import 'package:maisour/shared/services/token/token_service_impl.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'package:maisour/shared/services/firebase_remote_config_service.dart';
import 'package:maisour/modules/splash/splash_service.dart';
import 'package:maisour/modules/auth/auth_service.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/services/connectivity/connectivity_provider.dart';
import 'package:maisour/shared/services/permissions/permission_service_impl.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service_impl.dart';
import 'package:maisour/shared/services/notifications/notification_service_impl.dart';
import 'package:maisour/shared/enums/splash_decision.dart';

final splashServiceProvider = Provider.autoDispose<SplashService>((ref) {
  final dio = ref.watch(networkServiceProvider);
  final tokenService = ref.watch(tokenServiceProvider(dio));
  final authService = ref.watch(authServiceProvider);
  final userNotifier = ref.watch(userProvider.notifier);
  final connectivityService = ref.watch(connectivityServiceProvider);
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  final permissionService = ref.watch(permissionServiceProvider);
  final appsFlyerService = ref.watch(appsFlyerServiceProvider);
  final notificationService = ref.watch(notificationServiceProvider);
  final sharedPrefsManager = ref.watch(sharedPrefsManagerProvider);
  return SplashService(
    tokenService,
    authService,
    userNotifier,
    connectivityService,
    remoteConfigService,
    permissionService,
    appsFlyerService,
    notificationService,
    sharedPrefsManager,
  );
}, name: 'splashServiceProvider');

final splashNotifierProvider =
    StateNotifierProvider.autoDispose<SplashNotifier, AsyncValue<SplashResult>>(
      (ref) {
        final service = ref.watch(splashServiceProvider);
        return SplashNotifier(service);
      },
      name: 'splashNotifierProvider',
    );

class SplashNotifier extends StateNotifier<AsyncValue<SplashResult>> {
  final SplashService _service;

  SplashNotifier(this._service) : super(const AsyncValue.loading()) {
    checkNavigation();
  }

  Future<void> checkNavigation() async {
    state = const AsyncValue.loading();

    try {
      await Future.delayed(const Duration(milliseconds: 2500));
      final result = await _service.decideNavigation();
      state = AsyncValue.data(result);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}
