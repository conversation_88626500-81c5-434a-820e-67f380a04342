import 'package:flutter/foundation.dart';
import 'package:maisour/config/app_environment.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager.dart';
import 'package:maisour/shared/services/token/token_service.dart';
import 'package:maisour/shared/enums/splash_decision.dart';
import 'package:maisour/modules/auth/auth_service.dart';
import 'package:maisour/shared/providers/user_provider.dart';
import 'package:maisour/shared/services/connectivity/connectivity_service.dart';
import 'package:maisour/shared/services/firebase_remote_config_service.dart';
import 'package:maisour/shared/services/permissions/permission_service.dart';
import 'package:maisour/shared/services/appsflyer/appsflyer_service.dart';
import 'package:maisour/shared/services/notifications/notification_service.dart';
import 'package:maisour/shared/models/app_user.dart';
import 'package:safe_device/safe_device.dart';

class SplashService {
  final TokenService _tokenService;
  final AuthService _authService;
  final UserNotifier _userNotifier;
  final ConnectivityService _connectivityService;
  final RemoteConfigService _remoteConfigService;
  final PermissionService _permissionService;
  final AppsFlyerService _appsFlyerService;
  final NotificationService _notificationService;
  final SharedPrefsManager _sharedPrefsManager;

  SplashService(
    this._tokenService,
    this._authService,
    this._userNotifier,
    this._connectivityService,
    this._remoteConfigService,
    this._permissionService,
    this._appsFlyerService,
    this._notificationService,
    this._sharedPrefsManager,
  );

  Future<SplashResult> decideNavigation() async {
    // 1. First check internet connectivity
    final hasInternet = await _connectivityService
        .hasReliableInternetConnection();
    if (!hasInternet) return SplashResult.noInternet();

    // 2. Check if device is rooted/jailbroken
    final isSecure = await _isDeviceSecure();
    if (!isSecure) return SplashResult.jailbroken();

    // 3. 🔥 CHECK FOR UPDATES EARLY - BEFORE AUTHENTICATION
    try {
      await _remoteConfigService.initialize();
      final updateInfo = await _remoteConfigService.checkForUpdate();

      if (updateInfo.hasUpdate) {
        debugPrint('🚨 Update required - blocking app access');
        return SplashResult.updateRequired(updateInfo);
      }
    } catch (e) {
      debugPrint('❌ Remote config check failed: $e');
      // Continue with normal flow if remote config fails
    }

    // 4. 📱 REQUEST PERMISSIONS (ATT + NOTIFICATIONS)
    await _requestPermissions();

    // 5. Check if user is already logged in
    final accessToken = await _tokenService.getAccessToken();
    if (accessToken != null && accessToken.isNotEmpty) {
      try {
        // 6. Fetch current user data and sync with global provider
        final appUser = await _authService.getCurrentUser();
        _userNotifier.setUser(appUser);

        // Send FCM token to backend after successful authentication
        _sendFCMTokenToBackendInBackground(appUser);

        return SplashResult.home();
      } catch (e) {
        // If fetching user fails, clear tokens and go to welcome
        await _tokenService.clearTokens();
        return SplashResult.login();
      }
    }

    // 7. Not logged in - if its first launch show welcome else show login
    final isFirstLaunch = _sharedPrefsManager.firstLaunch;
    if (isFirstLaunch) {
      // Set first launch to false
      _sharedPrefsManager.setFirstLaunch(false);
      return SplashResult.welcome();
    }
    return SplashResult.login();
  }

  /// Request all required permissions for app startup and initialize services
  Future<void> _requestPermissions() async {
    try {
      debugPrint('🚀 Requesting required app startup permissions...');

      // Request required permissions (ATT + Notifications) at once
      final Map<String, bool> permissionResults = await _permissionService
          .requestRequiredPermissions();

      // Check permission results
      final attGranted = permissionResults['att'] ?? false;
      final notificationGranted = permissionResults['notification'] ?? false;

      // Initialize services after permission decisions
      await _initializeServices();

      debugPrint(
        '✅ All required permissions processed and services initialized',
      );
      debugPrint(
        '📊 Startup Permission Summary: ATT=${attGranted ? "✅" : "❌"}, Notifications=${notificationGranted ? "✅" : "❌"}',
      );
    } catch (e) {
      debugPrint('❌ Error requesting required permissions: $e');
      // Continue with app flow even if permission requests fail
      await _initializeServices();
    }
  }

  /// Initialize AppsFlyer and Notification services
  Future<void> _initializeServices() async {
    // Initialize AppsFlyer
    try {
      await _appsFlyerService.initialize();
      debugPrint('✅ AppsFlyer service initialized');
    } catch (appsFlyerError) {
      debugPrint('❌ Error initializing AppsFlyer: $appsFlyerError');
    }

    // Initialize Notification service
    try {
      await _notificationService.initialize();
      debugPrint('✅ Notification service initialized');
    } catch (notificationError) {
      debugPrint(
        '❌ Error initializing notification service: $notificationError',
      );
    }
  }

  Future<bool> _isDeviceSecure() async {
    try {
      final isJailbroken = AppEnvironment.isProduction
          ? await SafeDevice.isJailBroken
          : false;
      final isRealDevice = AppEnvironment.isProduction
          ? await SafeDevice.isRealDevice
          : true;
      return !isJailbroken && isRealDevice;
    } catch (e) {
      return false; // Default to not secure if check fails
    }
  }

  /// Send FCM token to backend in background without blocking
  Future<void> _sendFCMTokenToBackendInBackground([
    AppUser? currentUser,
  ]) async {
    try {
      debugPrint('🚀 Starting background FCM token sending...');

      // Send FCM token to backend using notification service
      await _notificationService.sendFCMTokenToBackend(currentUser);

      debugPrint('✅ Background FCM token sending completed');
    } catch (e) {
      debugPrint('❌ Error in background FCM token sending: $e');
      // Don't throw error to avoid breaking app flow
    }
  }
}
