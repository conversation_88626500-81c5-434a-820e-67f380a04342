import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/models/app_update_info.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/services/preferences/shared_prefs_manager_provider.dart';

class AppUpdateDialog extends ConsumerWidget {
  final AppUpdateInfo updateInfo;

  const AppUpdateDialog._({super.key, required this.updateInfo});

  /// Shows update dialog with proper handling for force/optional updates
  static Future<void> show(
    BuildContext context,
    WidgetRef ref,
    AppUpdateInfo updateInfo, {
    VoidCallback? onOptionalUpdateDismissed,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AppUpdateDialog._(updateInfo: updateInfo),
    ).then((_) {
      // If dialog is dismissed (optional update), call the callback
      // Use a post-frame callback to ensure navigation happens after dialog is fully dismissed
      if (!updateInfo.isForceUpdate && context.mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onOptionalUpdateDismissed?.call();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Builder(
      builder: (context) {
        return AlertDialog.adaptive(
          title: Text(LocaleKeys.updateMaisourApp.tr()),
          content: Text(LocaleKeys.updateRecommendationMessage.tr()),
          actions: [
            // Show "Later" button only for optional updates (not force updates)
            if (!updateInfo.isForceUpdate)
              TextButton(
                onPressed: () => _handleLaterPressed(context, ref),
                child: Text(
                  LocaleKeys.later.tr(),
                  style: AppTextStyles.textStyle.danger,
                ),
              ),

            TextButton(
              onPressed: () => _handleUpdate(context),
              child: Text(
                LocaleKeys.updateNow.tr(),
                style: AppTextStyles.textStyle.primary,
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleLaterPressed(BuildContext context, WidgetRef ref) async {
    // Save preference that user dismissed this specific version
    if (updateInfo.availableVersion != null) {
      final sharedPrefsManager = ref.read(sharedPrefsManagerProvider);
      await sharedPrefsManager.setDismissedUpdateVersion(
        updateInfo.availableVersion!,
      );

      debugPrint(
        '💾 Optional update dismissed for version: ${updateInfo.availableVersion}',
      );
    }

    // Close only the dialog
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  void _handleUpdate(BuildContext context) {
    debugPrint('🚀 Launching app store for update');
    UrlLauncherService.launchAppStore();
  }
}
