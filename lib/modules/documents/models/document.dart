import 'package:freezed_annotation/freezed_annotation.dart';

part 'document.freezed.dart';
part 'document.g.dart';

@freezed
abstract class Document with _$Document {
  const factory Document({
    required int id,
    required String documentUrl,
    required String title,
  }) = _Document;

  factory Document.fromJson(Map<String, dynamic> json) =>
      _$DocumentFromJson(json);
}

@freezed
abstract class DocumentsResponse with _$DocumentsResponse {
  const factory DocumentsResponse({
    required bool status,
    required int statusCode,
    required String message,
    required List<Document> data,
  }) = _DocumentsResponse;

  factory DocumentsResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentsResponseFromJson(json);
}
