import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:maisour/shared/services/network_service.dart';
import 'api/documents_api.dart';
import 'models/document.dart';

final documentsApiProvider = Provider<DocumentsApi>((ref) {
  final dio = ref.watch(networkServiceProvider);
  return DocumentsApi(dio);
});

final documentsServiceProvider = Provider<DocumentsService>((ref) {
  final documentsApi = ref.watch(documentsApiProvider);
  return DocumentsService(documentsApi);
});

class DocumentsService {
  final DocumentsApi _documentsApi;

  DocumentsService(this._documentsApi);

  Future<DocumentsResponse> getUserDocuments() async {
    return await _documentsApi.getUserDocuments();
  }

  Future<void> uploadDocument({
    required String fileName,
    required File file,
  }) async {
    return await _documentsApi.uploadDocument(fileName: fileName, file: file);
  }
}
