import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:file_picker/file_picker.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/services/url_launcher_service.dart';
import 'package:maisour/shared/widgets/indicators/app_shimmer_loader.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/user_provider_extension.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'providers/documents_provider.dart';
import 'models/document.dart';
import 'widgets/document_tile.dart';
import 'widgets/empty_documents.dart';
import 'widgets/bottom_sheet/upload_document_bottom_sheet.dart';

class DocumentsScreen extends ConsumerStatefulWidget {
  const DocumentsScreen({super.key});

  @override
  ConsumerState<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends ConsumerState<DocumentsScreen> {
  int? _lastDocumentCount;

  @override
  Widget build(BuildContext context) {
    final documentsAsync = ref.watch(documentsProvider);

    return RefreshIndicator.adaptive(
      color: AppColors.primary,
      onRefresh: () async {
        ref.invalidate(documentsProvider);
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(LocaleKeys.documents.tr()),
          centerTitle: false,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: documentsAsync.when(
                  data: (documents) => _buildDocumentsList(documents),
                  loading: () => _buildLoadingState(),
                  error: (error, stackTrace) => _buildErrorState(error),
                ),
              ),
              // Upload Button
              _buildUploadButton(context, ref),
              12.w.heightBox,
            ],
          ).paddingHorizontal(16.w),
        ),
      ),
    );
  }

  /// Build the documents list or empty state
  Widget _buildDocumentsList(List<Document> documents) {
    // Update user's totalDocument count if it has changed (using post-frame callback)
    _updateUserDocumentCountIfNeeded(documents.length);

    if (documents.isEmpty) {
      return EmptyDocuments();
    }

    return ListView.builder(
      itemCount: documents.length,
      itemBuilder: (context, index) {
        final document = documents[index];
        return DocumentTile(
          document: document,
          onTap: () => _onDocumentTap(document, context),
        );
      },
    );
  }

  /// Update user's totalDocument count if it has changed (using post-frame callback)
  void _updateUserDocumentCountIfNeeded(int newDocumentCount) {
    if (_lastDocumentCount != newDocumentCount) {
      _lastDocumentCount = newDocumentCount;

      // Use post-frame callback to avoid updating during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateUserDocumentCount(newDocumentCount);
      });
    }
  }

  /// Build the loading state with shimmer
  Widget _buildLoadingState() {
    return AppShimmerLoader.list(itemCount: 5, itemHeight: 80);
  }

  /// Build the error state
  Widget _buildErrorState(Object error) {
    return Center(child: Text(error.toString()));
  }

  /// Build the upload button
  Widget _buildUploadButton(BuildContext context, WidgetRef ref) {
    return AppButton(
      text: LocaleKeys.uploadDocument.tr(),
      onPressed: () => _onUploadDocument(context, ref),
      type: ButtonType.filled,
    );
  }

  /// Handle document tap
  void _onDocumentTap(Document document, BuildContext context) {
    UrlLauncherService.launchURL(document.documentUrl);
  }

  /// Handle upload document button tap
  Future<void> _onUploadDocument(BuildContext context, WidgetRef ref) async {
    try {
      // Configure file picker with specific file types
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          // PDF files
          'pdf',
          // Image files
          'jpg', 'jpeg', 'png',
        ],
        allowMultiple: false, // Single file only
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Validate file size (max 4MB)
        const maxFileSize = 4 * 1024 * 1024; // 4MB in bytes
        if (file.size > maxFileSize) {
          if (context.mounted) {
            context.showErrorToast(
              LocaleKeys.fileTooLarge.tr(),
              LocaleKeys.pleaseSelectASmallerFile.tr(),
            );
          }
          return;
        }

        // Validate file extension
        final extension = file.extension?.toLowerCase();
        if (extension == null || !_isValidFileType(extension)) {
          if (context.mounted) {
            context.showErrorToast(
              LocaleKeys.invalidFileType.tr(),
              LocaleKeys.pleaseSelectAPDFOrImageFile.tr(),
            );
          }
          return;
        }

        // Show bottom sheet for file name input
        if (context.mounted) {
          _showFileNameBottomSheet(context, ref, file);
        }
      } else {
        // User cancelled file selection
        debugPrint('File selection cancelled');
      }
    } catch (e) {
      debugPrint('Error picking file: $e');
      if (context.mounted) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
      }
    }
  }

  /// Show bottom sheet for file name input
  void _showFileNameBottomSheet(
    BuildContext context,
    WidgetRef ref,
    PlatformFile file,
  ) {
    UploadDocumentBottomSheet.show(
      context,
      file: file,
      onSuccess: () => ref.invalidate(documentsProvider),
    );
  }

  /// Update user's totalDocument count if it has changed
  void _updateUserDocumentCount(int newDocumentCount) {
    final currentUser = ref.readCurrentUser;
    if (currentUser == null) return;

    // If document count has changed, update user's totalDocument
    if (currentUser.totalDocument != newDocumentCount) {
      final updatedUser = currentUser.copyWith(totalDocument: newDocumentCount);
      ref.updateCurrentUser(updatedUser);
    }
  }

  /// Validate if the file type is allowed
  bool _isValidFileType(String extension) {
    const allowedExtensions = [
      // PDF files
      'pdf',
      // Image files
      'jpg', 'jpeg', 'png',
    ];

    return allowedExtensions.contains(extension.toLowerCase());
  }
}
