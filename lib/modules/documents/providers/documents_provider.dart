import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import 'dart:io';
import 'package:maisour/shared/utils/dio_exception_mapper.dart';
import 'package:maisour/shared/enums/api_status.dart';
import '../documents_service.dart';
import '../models/document.dart';

final documentsProvider = FutureProvider.autoDispose<List<Document>>((
  ref,
) async {
  final documentsService = ref.watch(documentsServiceProvider);
  final mapper = _DioExceptionMapperHelper(); // Helper for mixin
  try {
    final response = await documentsService.getUserDocuments();
    if (response.status) {
      return response.data;
    } else {
      throw Exception(response.message);
    }
  } catch (error, stackTrace) {
    if (error is DioException) {
      final failure = mapper.mapDioExceptionToFailure(error, stackTrace);
      throw Exception(failure.message);
    } else {
      throw Exception(error.toString());
    }
  }
});

final documentsLoadingProvider = Provider.autoDispose<bool>((ref) {
  final documentsAsync = ref.watch(documentsProvider);
  return documentsAsync.isLoading;
});

final documentsErrorProvider = Provider.autoDispose<String?>((ref) {
  final documentsAsync = ref.watch(documentsProvider);
  return documentsAsync.hasError ? documentsAsync.error.toString() : null;
});

// Provider for document upload
final documentUploadProvider =
    StateNotifierProvider.autoDispose<
      DocumentUploadNotifier,
      DocumentUploadState
    >((ref) {
      final documentsService = ref.watch(documentsServiceProvider);
      return DocumentUploadNotifier(documentsService);
    });

// State for document upload
class DocumentUploadState {
  final ApiStatus status;
  final String? errorMessage;
  final String? errorKey;

  const DocumentUploadState({
    this.status = ApiStatus.initial,
    this.errorMessage,
    this.errorKey,
  });

  DocumentUploadState copyWith({
    ApiStatus? status,
    String? errorMessage,
    String? errorKey,
  }) {
    return DocumentUploadState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      errorKey: errorKey ?? this.errorKey,
    );
  }

  bool get isLoading => status == ApiStatus.loading;
  bool get isSuccess => status == ApiStatus.success;
  bool get isError => status == ApiStatus.error;
}

// Notifier for document upload
class DocumentUploadNotifier extends StateNotifier<DocumentUploadState>
    with DioExceptionMapper {
  final DocumentsService _documentsService;

  DocumentUploadNotifier(this._documentsService)
    : super(const DocumentUploadState());

  Future<void> uploadDocument({
    required String fileName,
    required File file,
  }) async {
    state = state.copyWith(
      status: ApiStatus.loading,
      errorMessage: null,
      errorKey: null,
    );

    try {
      await _documentsService.uploadDocument(fileName: fileName, file: file);
      state = state.copyWith(status: ApiStatus.success);
    } catch (e) {
      String errorMessage;
      String? errorKey;
      if (e is DioException) {
        final failure = mapDioExceptionToFailure(e, StackTrace.current);
        errorMessage = failure.message;
        errorKey = failure.errorKey;
      } else {
        errorMessage = e.toString();
      }
      state = state.copyWith(
        status: ApiStatus.error,
        errorMessage: errorMessage,
        errorKey: errorKey,
      );
      rethrow;
    }
  }

  void reset() {
    state = const DocumentUploadState();
  }
}

// Helper class for DioException mapping
class _DioExceptionMapperHelper with DioExceptionMapper {}
