import 'dart:io';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:maisour/shared/constants/api_constants.dart';
import '../models/document.dart';

part 'documents_api.g.dart';

@RestApi()
abstract class DocumentsApi {
  factory DocumentsApi(Dio dio, {String? baseUrl}) = _DocumentsApi;

  @GET(ApiEndpoints.getUserDocuments)
  Future<DocumentsResponse> getUserDocuments();

  @POST(ApiEndpoints.saveUserDocument)
  @MultiPart()
  Future<void> uploadDocument({
    @Part(name: 'fileName') required String fileName,
    @Part(name: 'file') required File file,
  });
}
