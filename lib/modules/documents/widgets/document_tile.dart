import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import '../models/document.dart';

class DocumentTile extends StatelessWidget {
  final Document document;
  final VoidCallback onTap;

  const DocumentTile({super.key, required this.document, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
      margin: EdgeInsets.only(bottom: 12.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.gray.shade200),
      ),
      child: Row(
        children: [
          // File type icon
          _buildDocumentIcon(document.documentUrl),
          8.w.widthBox,
          // Document title and subtitle
          Expanded(
            child: Text(
              document.title,
              style: AppTextStyles.text14.semiBold.dark900,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // Arrow icon
          Icon(Icons.chevron_right, size: 24.w, color: AppColors.gray.shade400),
        ],
      ).onTap(onTap),
    );
  }

  Widget _buildDocumentIcon(String url) {
    final fileExtension = _getFileExtension(url);

    switch (fileExtension) {
      case 'pdf':
        return Assets.icons.pdf.svg(height: 32.w, width: 32.w);
      case 'doc':
        return Assets.icons.doc.svg(height: 32.w, width: 32.w);
      case 'xls':
        return Assets.icons.excel.svg(height: 32.w, width: 32.w);
      case 'image':
        return Assets.icons.image.svg(height: 32.w, width: 32.w);
      default:
        return Assets.icons.doc.svg(height: 32.w, width: 32.w);
    }
  }

  String _getFileExtension(String url) {
    final uri = Uri.parse(url);
    final path = uri.path;
    final extension = path.split('.').last.toLowerCase();

    // Handle common file extensions
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'xls':
      case 'xlsx':
        return 'xls';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'image';
      default:
        return 'doc';
    }
  }
}
