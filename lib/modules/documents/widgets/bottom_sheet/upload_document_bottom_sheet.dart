import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:maisour/config/theme/app_text_styles.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/extensions/text_style_extensions.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/error_message_helper.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/shared/widgets/indicators/bottom_sheet_handle_bar.dart';
import '../../providers/documents_provider.dart';

class UploadDocumentBottomSheet extends ConsumerStatefulWidget {
  final PlatformFile file;
  final VoidCallback onSuccess;

  const UploadDocumentBottomSheet({
    super.key,
    required this.file,
    required this.onSuccess,
  });

  static Future<void> show(
    BuildContext context, {
    required PlatformFile file,
    required VoidCallback onSuccess,
  }) {
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      isDismissible: false, // Prevent closing when clicking outside
      enableDrag: false, // Prevent dragging to close
      backgroundColor: Colors.white,
      builder: (context) =>
          UploadDocumentBottomSheet(file: file, onSuccess: onSuccess),
    );
  }

  @override
  ConsumerState<UploadDocumentBottomSheet> createState() =>
      _UploadDocumentBottomSheetState();
}

class _UploadDocumentBottomSheetState
    extends ConsumerState<UploadDocumentBottomSheet> {
  final TextEditingController _fileNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    // Pre-fill with cleaned file name without extension
    final fileName = widget.file.name;
    final cleanedName = _cleanFileName(fileName);
    _fileNameController.text = cleanedName;
  }

  @override
  void dispose() {
    _fileNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final uploadState = ref.watch(documentUploadProvider);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        16.h.heightBox,
        const BottomSheetHandleBar(),
        12.h.heightBox,
        Text(
          LocaleKeys.uploadDocument.tr(),
          style: AppTextStyles.text18.bold.dark900,
        ),
        16.h.heightBox,
        Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  _buildDocumentIcon(widget.file.extension ?? ''),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.file.name,
                          style: AppTextStyles.text14.dark900,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '${(widget.file.size / 1024 / 1024).toStringAsFixed(2)} MB',
                          style: AppTextStyles.text12.gray500,
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              12.h.heightBox,

              AppTextField(
                controller: _fileNameController,
                labelText: LocaleKeys.fileName.tr(),
                validator: (value) =>
                    AppValidators.required(LocaleKeys.fileName.tr(), value),
                inputFormatters: InputFormatters.fileNameFormatters(),
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,
              ),
              SizedBox(height: 24.h),

              // Submit button
              AppButton(
                text: LocaleKeys.upload.tr(),
                onPressed: () => _onSubmit(uploadState),
                type: ButtonType.filled,
                isLoading: uploadState.isLoading,
              ),
              24.h.heightBox,
            ],
          ),
        ),
      ],
    ).paddingHorizontal(16.w);
  }

  /// Handle submit button tap
  Future<void> _onSubmit(DocumentUploadState uploadState) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final fileName = _fileNameController.text.trim();
    if (fileName.isEmpty) {
      context.showWarningToast(
        LocaleKeys.oops.tr(),
        LocaleKeys.fieldRequired.tr(
          namedArgs: {'field': LocaleKeys.fileName.tr()},
        ),
      );
      return;
    }

    try {
      // Convert PlatformFile to File
      final filePath = widget.file.path;
      if (filePath == null) {
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          LocaleKeys.somethingWentWrong.tr(),
        );
        return;
      }

      final fileFile = File(filePath);

      // Use the provider to upload document
      await ref
          .read(documentUploadProvider.notifier)
          .uploadDocument(fileName: fileName, file: fileFile);

      if (mounted) {
        context.showSuccessToast(
          LocaleKeys.success.tr(),
          LocaleKeys.fileUploadedSuccessfully.tr(),
        );
        // Call success callback to refresh documents list
        widget.onSuccess();
        // Close bottom sheet
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('Error uploading file: $e');
      if (mounted) {
        if (uploadState.errorKey != null) {
          final (title, message) = ErrorMessageHelper.getLocalizedErrorMessage(
            errorKey: uploadState.errorKey,
            fallbackMessage: uploadState.errorMessage ?? '',
          );
          context.showErrorToast(title, message);
        } else {
          context.showErrorToast(
            LocaleKeys.oops.tr(),
            LocaleKeys.somethingWentWrong.tr(),
          );
        }
      }
    }
  }

  /// Clean file name to match validation rules (only allow a-zA-Z0-9_\-.)
  String _cleanFileName(String fileName) {
    // Remove file extension
    final lastDotIndex = fileName.lastIndexOf('.');
    String nameWithoutExtension;

    if (lastDotIndex != -1) {
      nameWithoutExtension = fileName.substring(0, lastDotIndex);
    } else {
      nameWithoutExtension = fileName;
    }

    // Clean the file name to match validation rules (only allow a-zA-Z0-9_\-.)
    final cleanedName = nameWithoutExtension.replaceAll(
      RegExp(r'[^a-zA-Z0-9_\-.]'),
      '',
    );

    // If cleaned name is empty, use a default name
    if (cleanedName.isEmpty) {
      return 'document';
    }

    return cleanedName;
  }

  Widget _buildDocumentIcon(String url) {
    final fileExtension = _getFileExtension(url);

    switch (fileExtension) {
      case 'pdf':
        return Assets.icons.pdf.svg(height: 32.w, width: 32.w);
      case 'doc':
        return Assets.icons.doc.svg(height: 32.w, width: 32.w);
      case 'xls':
        return Assets.icons.excel.svg(height: 32.w, width: 32.w);
      case 'image':
        return Assets.icons.image.svg(height: 32.w, width: 32.w);
      default:
        return Assets.icons.doc.svg(height: 32.w, width: 32.w);
    }
  }

  String _getFileExtension(String url) {
    final uri = Uri.parse(url);
    final path = uri.path;
    final extension = path.split('.').last.toLowerCase();

    // Handle common file extensions
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'xls':
      case 'xlsx':
        return 'xls';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'image';
      default:
        return 'doc';
    }
  }
}
