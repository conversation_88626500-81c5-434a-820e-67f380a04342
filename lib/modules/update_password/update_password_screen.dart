import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:awesome_extensions/awesome_extensions_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:maisour/config/theme/app_colors.dart';
import 'package:maisour/gen/assets.gen.dart';
import 'package:maisour/shared/extensions/context_toast_extension.dart';
import 'package:maisour/shared/localization/generated/locale_keys.g.dart';
import 'package:maisour/shared/utils/app_validators.dart';
import 'package:maisour/shared/utils/input_formatters.dart';
import 'package:maisour/shared/widgets/buttons/app_button.dart';
import 'package:maisour/shared/widgets/form_fields/app_text_field.dart';
import 'package:maisour/modules/auth/providers/auth_provider.dart';
import 'package:maisour/shared/enums/api_status.dart';

class UpdatePasswordScreen extends ConsumerStatefulWidget {
  const UpdatePasswordScreen({super.key});

  @override
  ConsumerState<UpdatePasswordScreen> createState() =>
      _UpdatePasswordScreenState();
}

class _UpdatePasswordScreenState extends ConsumerState<UpdatePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isOldPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _onSave() {
    FocusManager.instance.primaryFocus?.unfocus();

    if (_formKey.currentState?.validate() ?? false) {
      // Call the change password API
      ref
          .read(authNotifierProvider('updatePassword').notifier)
          .changePassword(
            currentPassword: _oldPasswordController.text,
            newPassword: _newPasswordController.text,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider('updatePassword'));

    // Listen for auth state changes
    ref.listen<AuthState>(authNotifierProvider('updatePassword'), (
      previous,
      next,
    ) {
      if (next.status == ApiStatus.success) {
        // Show success message and pop
        context.showSuccessToast(
          LocaleKeys.success.tr(),
          LocaleKeys.passwordUpdatedSuccessfully.tr(),
        );
        GoRouter.of(context).pop();
      } else if (next.status == ApiStatus.error) {
        // Show error message
        context.showErrorToast(
          LocaleKeys.oops.tr(),
          next.errorMessage ?? LocaleKeys.somethingWentWrong.tr(),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.updatePassword.tr()),
        centerTitle: false,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Old Password Field
                      AppTextField(
                        labelText: '${LocaleKeys.oldPassword.tr()}*',
                        controller: _oldPasswordController,
                        obscureText: !_isOldPasswordVisible,
                        textInputAction: TextInputAction.next,
                        validator: (value) => AppValidators.required(
                          LocaleKeys.oldPassword.tr(),
                          value,
                        ),
                        inputFormatters: InputFormatters.passwordFormatters(),
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isOldPasswordVisible = !_isOldPasswordVisible;
                            });
                          },
                          child: _isOldPasswordVisible
                              ? Assets.icons.eye.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                )
                              : Assets.icons.eyeClosed.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                ),
                        ),
                      ),

                      16.h.heightBox,

                      // New Password Field
                      AppTextField(
                        labelText: '${LocaleKeys.newPassword.tr()}*',
                        controller: _newPasswordController,
                        obscureText: !_isNewPasswordVisible,
                        textInputAction: TextInputAction.next,
                        validator: (value) =>
                            AppValidators.strongPassword(value),
                        inputFormatters: InputFormatters.passwordFormatters(),
                        helperText: LocaleKeys.passwordHelperText.tr(
                          namedArgs: {
                            'minLength': '8',
                            'upperCaseCount': '1',
                            'lowerCaseCount': '1',
                            'numberCount': '1',
                            'specialCharacterCount': '1',
                          },
                        ),
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isNewPasswordVisible = !_isNewPasswordVisible;
                            });
                          },
                          child: _isNewPasswordVisible
                              ? Assets.icons.eye.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                )
                              : Assets.icons.eyeClosed.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                ),
                        ),
                      ),

                      16.h.heightBox,

                      // Confirm Password Field
                      AppTextField(
                        labelText: '${LocaleKeys.confirmPassword.tr()}*',
                        controller: _confirmPasswordController,
                        obscureText: !_isConfirmPasswordVisible,
                        textInputAction: TextInputAction.done,
                        validator: (value) => AppValidators.confirmPassword(
                          value,
                          _newPasswordController.text,
                        ),
                        inputFormatters: InputFormatters.passwordFormatters(),
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isConfirmPasswordVisible =
                                  !_isConfirmPasswordVisible;
                            });
                          },
                          child: _isConfirmPasswordVisible
                              ? Assets.icons.eye.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                )
                              : Assets.icons.eyeClosed.svg(
                                  width: 24.w,
                                  height: 24.w,
                                  fit: BoxFit.scaleDown,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.dark.shade300,
                                    BlendMode.srcIn,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Bottom Buttons
              Container(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  children: [
                    AppButton(
                      text: LocaleKeys.save.tr(),
                      onPressed: _onSave,
                      type: ButtonType.filled,
                      isLoading: authState.status == ApiStatus.loading,
                    ),
                    8.h.heightBox,
                    AppButton(
                      text: LocaleKeys.cancel.tr(),
                      onPressed: () => GoRouter.of(context).pop(),
                      type: ButtonType.text,
                      textColor: AppColors.dark.shade900,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ).onTap(() {
      FocusManager.instance.primaryFocus?.unfocus();
    });
  }
}
