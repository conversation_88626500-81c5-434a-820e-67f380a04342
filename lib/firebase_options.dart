// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAELV-pQoyYptn0c6nmBQisWR0BsE9a3Wc',
    appId: '1:176342959093:android:70bc81515eb2ff14f896bc',
    messagingSenderId: '176342959093',
    projectId: 'maisour-33fbb',
    storageBucket: 'maisour-33fbb.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBiPRqRQiCSGqFnupkchCh8EN3ljFC6OKE',
    appId: '1:176342959093:ios:7d3f12b152d826daf896bc',
    messagingSenderId: '176342959093',
    projectId: 'maisour-33fbb',
    storageBucket: 'maisour-33fbb.firebasestorage.app',
    androidClientId: '176342959093-c2vu6ujn3i13u57ab456ra1r6ea360rg.apps.googleusercontent.com',
    iosClientId: '176342959093-fmi3c2t1duaegom3dl35l6rdjfm62da7.apps.googleusercontent.com',
    iosBundleId: 'com.maisour.app',
  );

}