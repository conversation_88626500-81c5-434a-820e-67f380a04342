import 'package:envied/envied.dart';

part 'env.g.dart';

@Envied(path: '.env')
abstract class Env {
  // API Base URL Configuration
  @EnviedField(varName: 'BASE_URL_DEVELOPMENT')
  static const String baseUrlDevelopment = _Env.baseUrlDevelopment;

  @EnviedField(varName: 'BASE_URL_STAGING')
  static const String baseUrlStaging = _Env.baseUrlStaging;

  @EnviedField(varName: 'BASE_URL_PRODUCTION')
  static const String baseUrlProduction = _Env.baseUrlProduction;

  // IDWISE Configuration
  @EnviedField(varName: 'IDWISE_SANDBOX_CLIENT_KEY')
  static const String idwiseSandboxClientKey = _Env.idwiseSandboxClientKey;

  @EnviedField(varName: 'IDWISE_LIVE_CLIENT_KEY')
  static const String idwiseLiveClientKey = _Env.idwiseLiveClientKey;

  @EnviedField(varName: 'IDWISE_PASSPORT_SELFIE_FLOW_ID')
  static const String idwisePassportSelfieFlowId =
      _Env.idwisePassportSelfieFlowId;

  @EnviedField(varName: 'IDWISE_PASSPORT_SELFIE_FLOW_ID_SANDBOX')
  static const String idwisePassportSelfieFlowIdSandbox =
      _Env.idwisePassportSelfieFlowIdSandbox;

  @EnviedField(varName: 'IDWISE_PASSPORT_FLOW_ID')
  static const String idwisePassportFlowId = _Env.idwisePassportFlowId;

  @EnviedField(varName: 'IDWISE_PASSPORT_FLOW_ID_SANDBOX')
  static const String idwisePassportFlowIdSandbox =
      _Env.idwisePassportFlowIdSandbox;

  @EnviedField(varName: 'IDWISE_POA_FLOW_ID')
  static const String idwisePoaFlowId = _Env.idwisePoaFlowId;

  @EnviedField(varName: 'IDWISE_POA_FLOW_ID_SANDBOX')
  static const String idwisePoaFlowIdSandbox = _Env.idwisePoaFlowIdSandbox;

  // AppsFlyer Configuration
  @EnviedField(varName: 'AF_DEV_KEY')
  static const String afDevKey = _Env.afDevKey;

  @EnviedField(varName: 'AF_APP_ID')
  static const String afAppId = _Env.afAppId;

  // Add other environment variables here as needed
  // Example:
  // @EnviedField(varName: 'API_KEY')
  // static const String apiKey = _Env.apiKey;
}
