# AppsFlyer Events Documentation

This document lists all AppsFlyer events tracked in the Maisour app, their parameters, and when they are triggered.

## Event List

### af_login
- **Triggered when:** User logs in (any method: Google, Apple, Email)
- **Parameters:**
  - `login_type`: The login method used.
    - **Possible values:**
      - `google` (Google login)
      - `apple` (Apple login)
      - `email` (Manual email/password login)

### screen_view
- **Triggered when:** Any screen is viewed
- **Parameters:**
  - `screen_name`: The name of the screen being viewed. Example: `Home`, `Login`, etc.

### af_complete_registration
- **Triggered when:** User completes registration
- **Parameters:**
  - `registration_method`: The registration method used.
    - **Possible values:**
      - `google` (Google registration)
      - `apple` (Apple registration)
      - `email` (Manual email/password registration)

## Adding New Events

- When you add a new AppsFlyer event, **always update this document**.
- Provide event name, trigger, and all parameters.

## Usage Example

```dart
await _appsFlyerService.logEvent(
  AppsFlyerEvents.login,
  eventValues: {'login_type': 'google'},
);
``` 