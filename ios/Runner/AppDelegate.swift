import Flutter
import UIKit
import GoogleMaps

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("AIzaSyCBkpzFkB8cMwZQVPSE37Ft-S5ULhh5mVs")
    
    let controller: FlutterViewController = window?.rootViewController as! FlutterViewController
    let deviceChannel = FlutterMethodChannel(name: "ios_device_check",
                                             binaryMessenger: controller.binaryMessenger)

    deviceChannel.setMethodCallHandler { (call, result) in
        if call.method == "isIPad" {
            result(UIDevice.current.userInterfaceIdiom == .pad)
        } else {
            result(FlutterMethodNotImplemented)
        }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
