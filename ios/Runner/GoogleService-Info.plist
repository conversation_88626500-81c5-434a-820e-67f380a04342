<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>176342959093-fmi3c2t1duaegom3dl35l6rdjfm62da7.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.176342959093-fmi3c2t1duaegom3dl35l6rdjfm62da7</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>176342959093-c2vu6ujn3i13u57ab456ra1r6ea360rg.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBiPRqRQiCSGqFnupkchCh8EN3ljFC6OKE</string>
	<key>GCM_SENDER_ID</key>
	<string>176342959093</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.maisour.app</string>
	<key>PROJECT_ID</key>
	<string>maisour-33fbb</string>
	<key>STORAGE_BUCKET</key>
	<string>maisour-33fbb.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:176342959093:ios:7d3f12b152d826daf896bc</string>
</dict>
</plist>