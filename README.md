# Maisour

Maisour Mobile App - A Flutter application following Flat + Scalable (Hybrid Clean) Architecture

## Overview

Maisour is a Flutter mobile application built with a team-friendly architecture that maintains good organization without the complexity of traditional Clean Architecture patterns. The project uses modern Flutter development practices with Riverpod for state management, Go Router for navigation, and supports multiple environments.

## Architecture

This project follows **Flat + Scalable (Hybrid Clean) Architecture** - a simplified, team-friendly approach that maintains good organization without complex Clean Architecture patterns.

### Key Principles
- **Modules**: Feature-based organization with screens, providers, and services together
- **Shared**: Reusable components, utilities, and global functionality
- **Config**: App-level configuration and setup
- **Self-Contained**: Modules should be as independent as possible
- **Team-Friendly**: Easy to understand and navigate for developers

## Project Structure
```
lib/
├── config/                    # App configuration
│   ├── app_environment.dart   # Environment & flavor configuration
│   ├── app_router.dart        # GoRouter configuration & routes
│   └── theme/                 # Theme components
│       ├── app_colors.dart    # Color palette & semantic colors
│       ├── app_text_styles.dart # Typography & text styles
│       └── app_theme.dart     # Main theme exports & configuration
├── env/
│   ├── env.dart
│   └── env.g.dart
├── firebase_options.dart
├── gen/
│   └── assets.gen.dart
├── main.dart
├── main_dev.dart
├── main_production.dart
├── main_staging.dart
├── main_widget.dart
└── shared/                    # Shared components
    ├── constants/             # App & API constants
    │   ├── api_constants.dart
    │   └── app_constants.dart
    ├── enums/                 # All application enums (one per file)
    │   ├── account_status.dart
    │   ├── api_status.dart
    │   ├── social_media_platform.dart
    │   ├── splash_decision.dart
    │   └── verified_status.dart
    ├── extensions/            # Dart extensions for enhanced functionality
    │   ├── context_toast_extension.dart
    │   ├── text_style_extensions.dart
    │   └── user_provider_extension.dart
    ├── localization/          # Localization files
    │   ├── ar.json
    │   ├── en.json
    │   └── generated/
    │       ├── codegen_loader.g.dart
    │       └── locale_keys.g.dart
    ├── models/                # Shared data models & status classes
    │   ├── app_update_info.dart
    │   ├── app_user.dart
    │   ├── app_user.freezed.dart
    │   ├── app_user.g.dart
    │   ├── failure.dart
    │   ├── failure.freezed.dart
    │   ├── refresh_token_response.dart
    │   ├── refresh_token_response.freezed.dart
    │   ├── refresh_token_response.g.dart
    │   ├── user.dart
    │   ├── user.freezed.dart
    │   └── user.g.dart
    ├── observers/             # Provider observers for debugging
    │   └── app_route_observer.dart
    ├── providers/             # Global providers
    │   ├── app_provider_observer.dart
    │   ├── language_provider.dart
    │   ├── resend_timer_provider.dart
    │   └── user_provider.dart
    ├── services/              # Shared services
    │   ├── appsflyer/
    │   │   ├── appsflyer_service.dart
    │   │   └── appsflyer_service_impl.dart
    │   ├── connectivity/
    │   │   ├── connectivity_provider.dart
    │   │   ├── connectivity_provider.freezed.dart
    │   │   └── connectivity_service.dart
    │   ├── firebase_crashlytics_service.dart
    │   ├── firebase_remote_config_service.dart
    │   ├── logout/
    │   │   ├── logout_service.dart
    │   │   └── logout_service_impl.dart
    │   ├── network_service.dart
    │   ├── network_service_interceptor.dart
    │   ├── notifications/
    │   │   ├── notification_provider.dart
    │   │   ├── notification_service.dart
    │   │   └── notification_service_impl.dart
    │   ├── permissions/
    │   │   ├── permission_service.dart
    │   │   └── permission_service_impl.dart
    │   ├── preferences/
    │   │   ├── shared_prefs_manager.dart
    │   │   └── shared_prefs_manager_provider.dart
    │   ├── secure_storage/
    │   │   ├── flutter_secure_storage_provider.dart
    │   │   ├── secure_storage.dart
    │   │   ├── secure_storage_const.dart
    │   │   └── secure_storage_impl.dart
    │   ├── token/
    │   │   ├── token_service.dart
    │   │   └── token_service_impl.dart
    │   └── url_launcher_service.dart
    ├── utils/                 # Utility functions & helpers
    │   ├── app_haptic.dart
    │   ├── app_toast_notification.dart
    │   ├── app_validators.dart
    │   ├── dio_exception_mapper.dart
    │   ├── dio_interceptor.dart
    │   ├── error_message_helper.dart
    │   ├── input_formatters.dart
    │   └── screen_util_init.dart
    └── widgets/               # Reusable UI components
        ├── app_button.dart
        ├── app_circular_loader.dart
        ├── app_dropdown_field.dart
        ├── app_text_field.dart
        ├── bottom_sheet_handle_bar.dart
        ├── gradient_background.dart
        ├── no_internet_screen.dart
        └── dialogs/
            └── account_closed_dialog.dart
```

Descriptions:
- **config/**: App-level configuration (theme, router, environment)
- **env/**: Environment variable management
- **gen/**: Generated files (e.g., assets)
- **shared/**: All shared, reusable code (constants, enums, extensions, localization, models, observers, providers, services, utils, widgets)

## Tech Stack

### Core Technologies
- **Flutter**: Cross-platform mobile development
- **Dart**: Programming language
- **Riverpod**: State management with code generation
- **Go Router**: Declarative routing
- **Freezed**: Immutable data classes
- **Easy Localization**: Internationalization

### Key Dependencies
- **State Management**: flutter_riverpod ^2.6.1, riverpod_generator
- **Navigation**: go_router ^15.1.3
- **HTTP Client**: dio ^5.8.0+1
- **API Generation**: retrofit ^4.4.2, retrofit_generator
- **Serialization**: json_annotation, json_serializable
- **UI/UX**: flutter_screenutil ^5.9.3, google_fonts ^6.2.1, awesome_extensions ^2.0.21
- **Storage**: flutter_secure_storage ^9.2.4, shared_preferences ^2.5.3
- **Firebase**: firebase_core ^3.13.1, firebase_crashlytics ^4.3.6
- **Security**: safe_device ^1.2.1

## Getting Started

### Prerequisites
- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / VS Code
- iOS development setup (for iOS builds)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd maisour
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Setup environment variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit .env file with your actual values
   # Add your AppsFlyer Dev Key, App ID, and other configuration
   ```

4. **Generate environment code**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

5. **Generate translations**
   ```bash
   dart run easy_localization:generate -f json -S lib/shared/localization -O lib/shared/localization/generated -o codegen_loader.g.dart && dart run easy_localization:generate -f keys -S lib/shared/localization -O lib/shared/localization/generated -o locale_keys.g.dart
   ```

## 🔥 Flutter Flavor Configuration

This project uses **Flutter Flavors** to manage different environments with the `AppEnvironment` class for API endpoints and configuration.

### Available Environments
- **Development**
- **Staging**
- **Production**

### Running the App

#### Development
```bash
flutter run -t lib/main_dev.dart --flavor development
```

#### Staging
```bash
flutter run -t lib/main_staging.dart --flavor staging
```

#### Production
```bash
flutter run -t lib/main_production.dart --flavor production
```

## 🔐 Environment Variables Configuration

This project uses **envied** package for secure environment variable management.

### Setup Environment Variables

1. **Copy the example file**
   ```bash
   cp .env.example .env
   ```

2. **Edit .env file with your actual values**
   ```bash
   # API Configuration - Flavor Specific Base URLs
   BASE_URL_DEVELOPMENT=your_actual_development_api_url
   BASE_URL_STAGING=your_actual_staging_api_url
   BASE_URL_PRODUCTION=your_actual_production_api_url

   # AppsFlyer Configuration
   AF_DEV_KEY=your_actual_appsflyer_dev_key
   AF_APP_ID=your_actual_ios_app_store_id

   # IDWISE Configuration
   IDWISE_SANDBOX_CLIENT_KEY=your_actual_idwise_sandbox_key
   IDWISE_LIVE_CLIENT_KEY=your_actual_idwise_live_key
   # ... and other IDWISE variables
   ```

3. **Generate environment code**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

### Environment Variables Used

| Variable | Description | Example |
|----------|-------------|---------|
| `AF_DEV_KEY` | AppsFlyer Development Key | `your_appsflyer_dev_key_here` |
| `AF_APP_ID` | iOS App Store ID for AppsFlyer | `your_ios_app_store_id_here` |
| `BASE_URL_DEVELOPMENT` | API Base URL for Development | `your_development_api_url_here` |
| `BASE_URL_STAGING` | API Base URL for Staging | `your_staging_api_url_here` |
| `BASE_URL_PRODUCTION` | API Base URL for Production | `your_production_api_url_here` |
| `IDWISE_SANDBOX_CLIENT_KEY` | IDWISE Sandbox Client Key | `your_idwise_sandbox_client_key_here` |
| `IDWISE_LIVE_CLIENT_KEY` | IDWISE Live Client Key | `your_idwise_live_client_key_here` |
| `IDWISE_PASSPORT_SELFIE_FLOW_ID` | IDWISE Passport Selfie Flow ID | `your_passport_selfie_flow_id_here` |
| `IDWISE_PASSPORT_FLOW_ID` | IDWISE Passport Flow ID | `your_passport_flow_id_here` |
| `IDWISE_POA_FLOW_ID` | IDWISE Proof of Address Flow ID | `your_poa_flow_id_here` |

### Important Notes

- **Never commit .env file** - It's already in .gitignore
- **Use .env.example** - For sharing configuration structure
- **Generate code after changes** - Run build_runner after updating .env
- **Environment specific** - Each developer/environment has their own .env file

### Adding New Environment Variables

1. **Add to .env file**
   ```bash
   NEW_VARIABLE=your_value
   ```

2. **Add to lib/env/env.dart**
   ```dart
   @EnviedField(varName: 'NEW_VARIABLE')
   static const String newVariable = _Env.newVariable;
   ```

3. **Generate code**
   ```bash
   dart run build_runner build --delete-conflicting-outputs
   ```

4. **Use in your code**
   ```dart
   import 'package:maisour/env/env.dart';

   final value = Env.newVariable;
   ```

## Development Workflow

### Adding a New Module

1. **Create module folder**
   ```bash
   mkdir lib/modules/feature_name
   ```

2. **Add required files**
   - `feature_name_screen.dart` - UI screen
   - `feature_name_provider.dart` - Riverpod providers
   - `feature_name_service.dart` - Business logic (if needed)
   - `feature_name_models.dart` - Data models (if needed)

3. **Update router**
   - Add route in `lib/config/app_router.dart`

### Code Generation Commands

#### Watch mode (recommended during development)
```bash
dart run build_runner watch --delete-conflicting-outputs
```

#### One-time build
```bash
dart run build_runner build --delete-conflicting-outputs
```

#### Clean and rebuild
```bash
flutter clean && flutter pub get && dart run build_runner build --delete-conflicting-outputs
```

## Project Guidelines

### Module Organization
- **One Feature Per Module**: Each module contains everything related to one feature
- **Self-Contained**: Modules should be as independent as possible
- **Naming Convention**: Use descriptive names (auth, home, profile, etc.)
- **File Naming**: Use snake_case (login_screen.dart, auth_provider.dart)

### Shared Components
- **Widgets**: Reusable UI components in `shared/widgets/`
- **Services**: Shared business logic in `shared/services/`
- **Models**: Common data models in `shared/models/`
- **Utils**: Helper functions in `shared/utils/`
- **Constants**: App constants in `shared/constants/`

### State Management
- **Module Providers**: Keep providers close to their related screens
- **Global Providers**: Place in `shared/providers/` for app-wide state
- **Code Generation**: Use riverpod_generator for providers
- **Immutability**: Use Freezed for immutable data classes

## Localization

The app supports multiple languages with easy_localization:

- **English** (`en`): Default language
- **Arabic** (`ar`): RTL support

Translation files are located in `lib/shared/localization/`

## Contributing

1. Follow the established architecture patterns
2. Use the provided code generation tools
3. Maintain consistent naming conventions
4. Add appropriate documentation for new features
5. Test on multiple devices and orientations

## License

[Add your license information here]
