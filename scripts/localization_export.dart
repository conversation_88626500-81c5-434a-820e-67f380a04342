import 'dart:convert';
import 'dart:io';
import 'dart:developer';

/// <PERSON>ript to export localization strings to CSV for translation
/// Usage: dart run scripts/localization_export.dart
void main() async {
  try {
    // Read English localization file
    final enFile = File('lib/shared/localization/en.json');
    final arFile = File('lib/shared/localization/ar.json');

    if (!enFile.existsSync()) {
      log('❌ English localization file not found!', name: 'LocalizationExport');
      return;
    }

    // Parse English strings
    final enContent = await enFile.readAsString();
    final Map<String, dynamic> enStrings = jsonDecode(enContent);

    // Parse existing Arabic strings (if any)
    Map<String, dynamic> arStrings = {};
    if (arFile.existsSync()) {
      final arContent = await arFile.readAsString();
      arStrings = jsonDecode(arContent);
    }

    // Create CSV content
    final csvLines = <String>[];
    csvLines.add('Key,English,Arabic'); // Header

    // Add all English strings with existing Arabic translations
    enStrings.forEach((key, englishValue) {
      final arabicValue = arStrings[key] ?? '';
      // Escape commas and quotes in CSV
      final escapedEnglish = _escapeCsvValue(englishValue.toString());
      final escapedArabic = _escapeCsvValue(arabicValue.toString());
      csvLines.add('$key,$escapedEnglish,$escapedArabic');
    });

    // Write CSV file
    final csvFile = File('localization_export.csv');
    await csvFile.writeAsString(csvLines.join('\n'));

    final absolutePath = csvFile.absolute.path;

    log('✅ Localization exported successfully', name: 'LocalizationExport');
    log('📁 File location: $absolutePath', name: 'LocalizationExport');
    log('📊 Total strings: ${enStrings.length}', name: 'LocalizationExport');
    log(
      '🔄 Missing Arabic translations: ${enStrings.length - arStrings.length}',
      name: 'LocalizationExport',
    );
    log('📋 Next steps:', name: 'LocalizationExport');
    log(
      '1. Share localization_export.csv with your translator',
      name: 'LocalizationExport',
    );
    log('2. Ask them to fill the Arabic column', name: 'LocalizationExport');
    log(
      '3. Run: dart run scripts/localization_import.dart',
      name: 'LocalizationExport',
    );
  } catch (e) {
    log('❌ Error: $e', name: 'LocalizationExport');
  }
}

String _escapeCsvValue(String value) {
  // If value contains comma, newline, or quote, wrap in quotes and escape quotes
  if (value.contains(',') || value.contains('\n') || value.contains('"')) {
    return '"${value.replaceAll('"', '""')}"';
  }
  return value;
}
