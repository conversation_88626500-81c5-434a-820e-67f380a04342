import 'dart:convert';
import 'dart:io';
import 'dart:developer';

/// <PERSON>ript to import translated CSV back to Arabic JSON
/// Usage: dart run scripts/localization_import.dart
void main() async {
  try {
    final csvFile = File('localization_export.csv');

    if (!csvFile.existsSync()) {
      log(
        '❌ CSV file not found! Run export script first.',
        name: 'LocalizationImport',
      );
      return;
    }

    // Read CSV content
    final csvContent = await csvFile.readAsString();
    final lines = csvContent.split('\n');

    if (lines.isEmpty) {
      log('❌ CSV file is empty!', name: 'LocalizationImport');
      return;
    }

    // Skip header line
    final dataLines = lines.skip(1).where((line) => line.trim().isNotEmpty);

    final Map<String, String> arabicStrings = {};
    int translatedCount = 0;

    for (final line in dataLines) {
      final parts = _parseCsvLine(line);
      if (parts.length >= 3) {
        final key = parts[0].trim();
        final arabic = parts[2].trim();

        if (key.isNotEmpty) {
          arabicStrings[key] = arabic;
          if (arabic.isNotEmpty) {
            translatedCount++;
          }
        }
      }
    }

    // Create formatted JSON
    final jsonEncoder = JsonEncoder.withIndent('    ');
    final formattedJson = jsonEncoder.convert(arabicStrings);

    // Write to Arabic localization file
    final arFile = File('lib/shared/localization/ar.json');
    await arFile.writeAsString(formattedJson);

    final absolutePath = arFile.absolute.path;

    log('✅ Arabic localization updated!', name: 'LocalizationImport');
    log('📁 Updated file: $absolutePath', name: 'LocalizationImport');
    log('📊 Total keys: ${arabicStrings.length}', name: 'LocalizationImport');
    log('✅ Translated: $translatedCount', name: 'LocalizationImport');
    log(
      '⚠️  Missing translations: ${arabicStrings.length - translatedCount}',
      name: 'LocalizationImport',
    );

    // Show missing translations
    final missingKeys = arabicStrings.entries
        .where((entry) => entry.value.isEmpty)
        .map((entry) => entry.key)
        .toList();

    if (missingKeys.isNotEmpty) {
      log(
        '🔍 Keys still missing Arabic translation:',
        name: 'LocalizationImport',
      );
      for (final key in missingKeys) {
        log('  - $key', name: 'LocalizationImport');
      }
    }
  } catch (e) {
    log('❌ Error: $e', name: 'LocalizationImport');
  }
}

List<String> _parseCsvLine(String line) {
  final List<String> result = [];
  bool inQuotes = false;
  String currentField = '';

  for (int i = 0; i < line.length; i++) {
    final char = line[i];

    if (char == '"') {
      if (inQuotes && i + 1 < line.length && line[i + 1] == '"') {
        // Escaped quote
        currentField += '"';
        i++; // Skip next quote
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
      }
    } else if (char == ',' && !inQuotes) {
      // Field separator
      result.add(currentField);
      currentField = '';
    } else {
      currentField += char;
    }
  }

  // Add last field
  result.add(currentField);

  return result;
}
