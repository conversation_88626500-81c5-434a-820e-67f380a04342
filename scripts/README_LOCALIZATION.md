# 🌍 Localization Management Scripts

This directory contains scripts to manage English-Arabic translations using Google Sheets workflow.

## 📁 File Locations

### Generated Files (Project Root)
- **`google_sheets_template.csv`** - Template file for Google Sheets sharing
- **Location**: `/Users/<USER>/Development/maisour/google_sheets_template.csv`

### Source Files
- **English**: `lib/shared/localization/en.json`
- **Arabic**: `lib/shared/localization/ar.json`

## 🚀 Workflow Steps

### 1. Generate Template
```bash
dart run scripts/generate_google_sheets_template.dart
```
- Creates `google_sheets_template.csv` in project root
- Preserves existing Arabic translations (marked as EXISTING)
- Shows new strings that need translation (marked as NEW)

### 2. Share with Translator
1. Upload `google_sheets_template.csv` to Google Sheets
2. Share with translator (give edit permissions)
3. Translator fills ONLY empty "Arabic Translation" cells (marked as NEW)
4. Translator should NOT modify cells marked as EXISTING

### 3. Download & Replace
1. Download completed file as CSV from Google Sheets
2. Replace `google_sheets_template.csv` in project root with downloaded file

### 4. Import Translations
```bash
dart run scripts/localization_import_from_template.dart
```
- Updates `lib/shared/localization/ar.json`
- Shows completion percentage
- Lists any missing translations

## 📊 Current Status
- **Total strings**: 34
- **Existing translations**: 18 (53%)
- **New strings to translate**: 16 (47%)

## 🔄 Data Safety
✅ **No data loss**: Existing Arabic translations are preserved  
✅ **Version control**: All changes are tracked in Git  
✅ **Backup**: Original files remain unchanged until import  

## 📝 CSV Format
```csv
Key,English Text,Arabic Translation,Status
deviceIsRooted,Your device might be rooted,قد يكون جهازك مكسور الحماية,EXISTING
createAccountWith,Create Account With,,NEW
```

## 🛠️ Alternative Scripts
- `localization_export.dart` - Simple export without Google Sheets format
- `localization_import.dart` - Simple import from basic CSV

## 📞 Support
If you encounter issues, check:
1. File exists in project root: `google_sheets_template.csv`
2. CSV format is correct (4 columns)
3. No extra commas or quotes in translations
