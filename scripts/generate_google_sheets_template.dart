import 'dart:convert';
import 'dart:io';
import 'dart:developer' as developer;

/// <PERSON>ript to generate a Google Sheets compatible CSV template
/// Usage: dart run scripts/generate_google_sheets_template.dart
void main() async {
  try {
    // Read English localization file
    final enFile = File('lib/shared/localization/en.json');
    final arFile = File('lib/shared/localization/ar.json');

    if (!enFile.existsSync()) {
      developer.log(
        '❌ English localization file not found!',
        name: 'LocalizationExport',
      );
      return;
    }

    // Parse English strings
    final enContent = await enFile.readAsString();
    final Map<String, dynamic> enStrings = jsonDecode(enContent);

    // Parse existing Arabic strings (preserve existing translations)
    Map<String, dynamic> arStrings = {};
    if (arFile.existsSync()) {
      final arContent = await arFile.readAsString();
      arStrings = jsonDecode(arContent);
      developer.log(
        '📖 Found existing Arabic translations: ${arStrings.length}',
        name: 'LocalizationExport',
      );
    }

    // Create Google Sheets template
    final csvLines = <String>[];

    // Add header with instructions
    csvLines.add('Key,English Text,Arabic Translation,Status');
    csvLines.add(
      'INSTRUCTIONS,"Please fill empty Arabic Translation cells","يرجى ملء خلايا الترجمة العربية الفارغة","EXISTING = Already translated, NEW = Needs translation"',
    );
    csvLines.add(''); // Empty row for separation

    int existingCount = 0;
    int newCount = 0;

    // Add all English strings with existing Arabic translations
    enStrings.forEach((key, englishValue) {
      final escapedEnglish = _escapeCsvValue(englishValue.toString());
      final arabicValue = arStrings[key] ?? '';
      final escapedArabic = _escapeCsvValue(arabicValue.toString());

      String status;
      if (arabicValue.isNotEmpty) {
        status = 'EXISTING';
        existingCount++;
      } else {
        status = 'NEW';
        newCount++;
      }

      csvLines.add('$key,$escapedEnglish,$escapedArabic,$status');
    });

    // Write template file to project root
    final templateFile = File('google_sheets_template.csv');
    await templateFile.writeAsString(csvLines.join('\n'));

    final absolutePath = templateFile.absolute.path;

    developer.log(
      '✅ Google Sheets template created',
      name: 'LocalizationExport',
    );
    developer.log(
      '📁 File location: $absolutePath',
      name: 'LocalizationExport',
    );
    developer.log(
      '📊 Total strings: ${enStrings.length}',
      name: 'LocalizationExport',
    );
    developer.log(
      '✅ Existing translations: $existingCount',
      name: 'LocalizationExport',
    );
    developer.log(
      '🆕 New strings to translate: $newCount',
      name: 'LocalizationExport',
    );

    developer.log('\n📋 WORKFLOW STEPS:', name: 'LocalizationExport');
    developer.log(
      '1. UPLOAD: Upload google_sheets_template.csv to Google Sheets',
      name: 'LocalizationExport',
    );
    developer.log(
      '2. SHARE: Share with translator (edit permissions)',
      name: 'LocalizationExport',
    );
    developer.log(
      '3. TRANSLATE: Translator fills ONLY empty Arabic cells (marked as NEW)',
      name: 'LocalizationExport',
    );
    developer.log(
      '4. DOWNLOAD: Download completed file as CSV from Google Sheets',
      name: 'LocalizationExport',
    );
    developer.log(
      '5. REPLACE: Put downloaded CSV in project root as google_sheets_template.csv',
      name: 'LocalizationExport',
    );
    developer.log(
      '6. IMPORT: Run: dart run scripts/localization_import_from_template.dart',
      name: 'LocalizationExport',
    );

    developer.log('\n📍 IMPORTANT FILE LOCATIONS:', name: 'LocalizationExport');
    developer.log(
      '• Generated template: $absolutePath',
      name: 'LocalizationExport',
    );
    developer.log(
      '• After download from Google Sheets: Replace the same file',
      name: 'LocalizationExport',
    );
    developer.log(
      '• Final Arabic JSON: lib/shared/localization/ar.json',
      name: 'LocalizationExport',
    );
  } catch (e) {
    developer.log('❌ Error: $e', name: 'LocalizationExport');
  }
}

String _escapeCsvValue(String value) {
  // If value contains comma, newline, or quote, wrap in quotes and escape quotes
  if (value.contains(',') || value.contains('\n') || value.contains('"')) {
    return '"${value.replaceAll('"', '""')}"';
  }
  return value;
}
