# Maisour Project Guidelines

## Project Overview
Maisour is a Flutter mobile application following Flat + Scalable (Hybrid Clean) architecture - a simplified, team-friendly approach that maintains good organization without complex Clean Architecture patterns.

## Architecture & Patterns

### Flat + Scalable (Hybrid Clean) Architecture
- **Modules**: Feature-based organization with screens, providers, and services together
- **Shared**: Reusable components, utilities, and global functionality
- **Config**: App-level configuration and setup

### Project Structure
```
lib/
├── config/                    # App configuration
│   ├── app_environment.dart   # Environment & flavor configuration
│   ├── app_router.dart        # GoRouter configuration & routes
│   └── theme/                 # Theme components
│       ├── app_colors.dart    # Color palette & semantic colors
│       ├── app_text_styles.dart # Typography & text styles
│       └── app_theme.dart     # Main theme exports & configuration
├── env/
│   ├── env.dart
│   └── env.g.dart
├── firebase_options.dart
├── gen/
│   └── assets.gen.dart
├── main.dart
├── main_dev.dart
├── main_production.dart
├── main_staging.dart
├── main_widget.dart
└── shared/                    # Shared components
    ├── constants/             # App & API constants
    │   ├── api_constants.dart
    │   └── app_constants.dart
    ├── enums/                 # All application enums (one per file)
    │   ├── account_status.dart
    │   ├── api_status.dart
    │   ├── social_media_platform.dart
    │   ├── splash_decision.dart
    │   └── verified_status.dart
    ├── extensions/            # Dart extensions for enhanced functionality
    │   ├── context_toast_extension.dart
    │   ├── text_style_extensions.dart
    │   └── user_provider_extension.dart
    ├── localization/          # Localization files
    │   ├── ar.json
    │   ├── en.json
    │   └── generated/
    │       ├── codegen_loader.g.dart
    │       └── locale_keys.g.dart
    ├── models/                # Shared data models & status classes
    │   ├── app_update_info.dart
    │   ├── app_user.dart
    │   ├── app_user.freezed.dart
    │   ├── app_user.g.dart
    │   ├── failure.dart
    │   ├── failure.freezed.dart
    │   ├── refresh_token_response.dart
    │   ├── refresh_token_response.freezed.dart
    │   ├── refresh_token_response.g.dart
    │   ├── user.dart
    │   ├── user.freezed.dart
    │   └── user.g.dart
    ├── observers/             # Provider observers for debugging
    │   └── app_route_observer.dart
    ├── providers/             # Global providers
    │   ├── app_provider_observer.dart
    │   ├── language_provider.dart
    │   ├── resend_timer_provider.dart
    │   └── user_provider.dart
    ├── services/              # Shared services
    │   ├── appsflyer/
    │   │   ├── appsflyer_service.dart
    │   │   └── appsflyer_service_impl.dart
    │   ├── connectivity/
    │   │   ├── connectivity_provider.dart
    │   │   ├── connectivity_provider.freezed.dart
    │   │   └── connectivity_service.dart
    │   ├── firebase_crashlytics_service.dart
    │   ├── firebase_remote_config_service.dart
    │   ├── logout/
    │   │   ├── logout_service.dart
    │   │   └── logout_service_impl.dart
    │   ├── network_service.dart
    │   ├── network_service_interceptor.dart
    │   ├── notifications/
    │   │   ├── notification_provider.dart
    │   │   ├── notification_service.dart
    │   │   └── notification_service_impl.dart
    │   ├── permissions/
    │   │   ├── permission_service.dart
    │   │   └── permission_service_impl.dart
    │   ├── preferences/
    │   │   ├── shared_prefs_manager.dart
    │   │   └── shared_prefs_manager_provider.dart
    │   ├── secure_storage/
    │   │   ├── flutter_secure_storage_provider.dart
    │   │   ├── secure_storage.dart
    │   │   ├── secure_storage_const.dart
    │   │   └── secure_storage_impl.dart
    │   ├── token/
    │   │   ├── token_service.dart
    │   │   └── token_service_impl.dart
    │   └── url_launcher_service.dart
    ├── utils/                 # Utility functions & helpers
    │   ├── app_haptic.dart
    │   ├── app_toast_notification.dart
    │   ├── app_validators.dart
    │   ├── dio_exception_mapper.dart
    │   ├── dio_interceptor.dart
    │   ├── error_message_helper.dart
    │   ├── input_formatters.dart
    │   └── screen_util_init.dart
    └── widgets/               # Reusable UI components
        ├── app_button.dart
        ├── app_circular_loader.dart
        ├── app_dropdown_field.dart
        ├── app_text_field.dart
        ├── bottom_sheet_handle_bar.dart
        ├── gradient_background.dart
        ├── no_internet_screen.dart
        └── dialogs/
            └── account_closed_dialog.dart
```

Descriptions:
- **config/**: App-level configuration (theme, router, environment)
- **env/**: Environment variable management
- **gen/**: Generated files (e.g., assets)
- **shared/**: All shared, reusable code (constants, enums, extensions, localization, models, observers, providers, services, utils, widgets)

### State Management
- **Primary**: Riverpod (flutter_riverpod ^2.6.1)
- **Code Generation**: Use riverpod_generator for providers
- **Organization**: Module-specific providers + global providers in shared/
- **AutoDispose**: Use autoDispose for providers that don't need to persist globally
- **Provider Observer**: AppProviderObserver for debugging provider lifecycle
- **Provider Names**: All providers must have descriptive names for debugging

#### User Access Pattern
- **Current User Access**: Use the `UserProviderExtension` (in `lib/shared/extensions/user_provider_extension.dart`) for convenient access to the current user and related actions in widgets. Example:
  ```dart
  final appUser = ref.currentAppUser;
  final user = ref.currentUser;
  final isLoggedIn = ref.currentAppUser != null;
  ```
  This extension provides easy access to user data and actions, and should be preferred over manual provider watching/reading for user-related state.

### Navigation
- **Router**: Go Router (go_router ^15.1.3)
- **Configuration**: Centralized in `config/app_router.dart`

### API & Data
- **HTTP Client**: Dio (^5.8.0+1)
- **API Generation**: Retrofit (^4.4.2) with retrofit_generator
- **Serialization**: json_annotation + json_serializable
- **Immutable Models**: Freezed (^3.0.6) - use only when needed (see Freezed Guidelines)
- **Organization**: Module-specific services + shared API config

## Development Guidelines

### File Organization Principles
1. **Analyze Before Acting**: Always check existing project structure before making changes
2. **Follow Established Patterns**: Look at how similar files are organized in the project
3. **Minimal Changes**: Only restructure what's necessary, don't add extra functionality
4. **Consistent Placement**: Files should be placed according to their actual purpose, not generic names
5. **Read PROJECT.md First**: Always consult this document before making structural changes

### Enum Organization Rules
- **Dedicated Directory**: All enums go in `lib/shared/enums/` directory
- **Separate Files**: Each enum gets its own file (e.g., `api_status.dart`, `app_flavor.dart`)
- **Clear Naming**: File names should match the enum name in snake_case
- **Single Responsibility**: One enum per file for better organization
- **Import Strategy**: Import enums from their dedicated files, not from mixed files

### Freezed Usage Guidelines
**Use Freezed ONLY when you need:**
- **Immutability**: Data classes that should never change after creation
- **Copy Methods**: Need `copyWith()` functionality for state updates
- **Union Types**: Multiple variants of the same data type
- **JSON Serialization**: Complex API response models with nested objects
- **Equality**: Automatic `==` and `hashCode` implementation

**DON'T use Freezed for:**
- **Simple Enums**: Use regular Dart enums instead
- **Simple Data Classes**: Use regular classes if no special features needed
- **Constants**: Use abstract classes with static constants
- **Configuration Classes**: Use regular classes for app configuration

**Examples:**
```dart
// ✅ Good use of Freezed - API response with JSON serialization
@freezed
class UserResponse with _$UserResponse {
  const factory UserResponse({
    required String id,
    required String name,
    String? email,
  }) = _UserResponse;

  factory UserResponse.fromJson(Map<String, dynamic> json) =>
      _$UserResponseFromJson(json);
}

// ❌ Don't use Freezed - simple enum
enum ApiStatus { initial, loading, success, error } // Use regular enum

// ❌ Don't use Freezed - simple constants
abstract class ApiConstants {
  static const String baseUrl = 'https://api.example.com';
}
```

### Module Organization Rules
- **One Feature Per Module**: Each module contains everything related to one feature
- **Self-Contained**: Modules should be as independent as possible
- **Naming Convention**: Module names should be descriptive (auth, home, profile, etc.)
- **File Naming**: Use descriptive names (login_screen.dart, auth_provider.dart)
- **Widget Organization**: Create `widgets/` directory when planning future complexity (animations, etc.)

### SafeArea Usage Guidelines (MANDATORY)
**Always use SafeArea for proper device compatibility across iOS, Android, and other devices:**

**✅ REQUIRED for all screens:**
```dart
// ✅ Correct - Wrap body content in SafeArea
Scaffold(
  body: SafeArea(
    child: YourScreenContent(),
  ),
)

// ✅ Alternative - Scaffold handles SafeArea automatically for most cases
Scaffold(
  appBar: AppBar(), // AppBar automatically handles top SafeArea
  body: YourScreenContent(),
)
```

**❌ AVOID - Content without SafeArea protection:**
```dart
// ❌ Wrong - Content may be hidden behind notch/status bar
Scaffold(
  body: Column(
    children: [
      Text('This might be hidden behind notch'),
      // ... rest of content
    ],
  ),
)
```

**📱 Device Compatibility Issues SafeArea Solves:**
- **iOS**: Notch, Dynamic Island, home indicator, status bar
- **Android**: Status bar, navigation bar, camera punch holes
- **Tablets**: Different aspect ratios and orientations
- **Foldables**: Unique screen configurations and cutouts

**🎯 When to use SafeArea:**
- ✅ **Full-screen content**: Always wrap main content in SafeArea
- ✅ **Custom layouts**: When not using standard Scaffold structure
- ✅ **Bottom content**: Protect from home indicator/navigation bar
- ✅ **Modal dialogs**: Ensure content is visible on all devices
- ✅ **Overlay content**: Floating buttons, bottom sheets, snackbars

**⚙️ SafeArea Configuration Options:**
```dart
SafeArea(
  top: true,     // Avoid status bar/notch (default: true)
  bottom: true,  // Avoid home indicator/nav bar (default: true)
  left: true,    // Avoid side notches/cutouts (default: true)
  right: true,   // Avoid side notches/cutouts (default: true)
  child: content,
)
```

**🚨 Common Mistakes to Avoid:**
- ❌ Forgetting SafeArea on full-screen content
- ❌ Double SafeArea (Scaffold + manual SafeArea)
- ❌ Not testing on devices with notches/cutouts
- ❌ Ignoring landscape orientation SafeArea needs

### Color Management & TextStyle Extensions
**Centralized color system with easy-to-use TextStyle extensions:**

**✅ Color Organization:**
- **Centralized Colors**: All colors defined in `AppColors` class
- **Semantic Naming**: Use meaningful names (primary, secondary, background, etc.)
- **Material Color System**: Use MaterialColor for shade variations
- **Theme Integration**: Colors referenced in theme configuration
- **TextStyle Extensions**: Use extensions from `shared/extensions/text_style_extensions.dart` for easy color application

**✅ TextStyle Color Usage:**
```dart
// ✅ Easy color application with extensions
Text('Title', style: TextStyle(fontSize: 24.sp).primary)
Text('Subtitle', style: TextStyle(fontSize: 16.sp).gray600)
Text('Error', style: TextStyle(fontSize: 14.sp).danger)

// ✅ Chain with other TextStyle properties
Text('Bold Primary', style: TextStyle(
  fontSize: 18.sp,
  fontWeight: FontWeight.bold,
).primary500)

// ✅ Available TextStyle color extensions
.primary, .primary100, .primary200, ... .primary900
.secondary, .secondary100, .secondary200, ... .secondary900
.dark, .dark100, .dark200, ... .dark900
.gray, .gray100, .gray200, ... .gray900
.danger, .warning, .info
.background, .surface

// ❌ Avoid hardcoded colors
Text('Title', style: TextStyle(color: Colors.blue)) // Wrong
Text('Title', style: TextStyle(color: Color(0xFF2484C8))) // Wrong
```

**✅ Color Extensions Usage:**
```dart
// ✅ Alpha transparency with percentage
Container(color: AppColors.primary.opacityPercent(50))  // 50% transparent
Container(color: AppColors.dark.shade900.opacityPercent(10))  // 10% transparent

// ✅ Color utilities
String hexColor = AppColors.primary.toHex();  // "#2484C8"
bool isLight = AppColors.primary.isLight;     // true/false
Color textColor = backgroundColor.contrastingTextColor;  // black/white

// ✅ Available Color extensions
.opacityPercent(0-100)
.toHex(), .isLight, .isDark, .contrastingTextColor
```

**❌ Avoid Direct Color Usage:**
```dart
// Wrong - Don't use hardcoded colors
Container(color: Colors.blue)
Container(color: Color(0xFF2484C8))

// Wrong - Don't create colors without extensions
Container(color: AppColors.primary.withOpacity(0.5))  // Use opacityPercent instead
```

**🎯 Benefits:**
- ✅ **Consistent Colors**: All text uses centralized color system
- ✅ **Easy to Use**: Simple extension syntax
- ✅ **Maintainable**: Change colors in one place
- ✅ **Type Safe**: IDE autocomplete and error checking
- ✅ **Readable Code**: Clear semantic color names

### Text Styling Guidelines (MANDATORY)
**Always use AppTextStyles with extensions for consistent text styling:**

**✅ Correct Text Styling Pattern:**
```dart
// For regular Text widgets
Text(
  LocaleKeys.welcome.tr(),
  style: AppTextStyles.text16.medium.dark700,
)

// For TextSpan in RichText
TextSpan(
  text: LocaleKeys.login.tr(),
  style: AppTextStyles.text14.bold.primary,
)

// For different text sizes and weights
Text('Title', style: AppTextStyles.text24.bold.dark800)
Text('Subtitle', style: AppTextStyles.text16.medium.gray600)
Text('Body', style: AppTextStyles.text14.regular.dark600)
Text('Caption', style: AppTextStyles.text12.light.gray500)
```

**❌ Avoid Direct TextStyle Creation:**
```dart
// Wrong - Don't create TextStyle directly
Text(
  'Hello',
  style: TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
    color: AppColors.primary,
  ),
)

// Wrong - Don't use hardcoded colors
Text('Hello', style: TextStyle(color: Colors.blue))
```

**🎯 Benefits:**
- ✅ **Consistent Typography**: All text follows design system
- ✅ **Easy Maintenance**: Change text styles in one place
- ✅ **Type Safety**: IDE autocomplete for available styles
- ✅ **Color Consistency**: Uses centralized color system
- ✅ **Responsive**: Automatically scales with ScreenUtil

### Spacing Guidelines (MANDATORY)
**Always use ScreenUtil extensions for consistent spacing across devices:**

**✅ Correct Spacing Patterns:**
```dart
// For vertical spacing - use heightBox extension
20.h.heightBox,
16.h.heightBox,
8.h.heightBox,

// For horizontal spacing - use widthBox extension
24.w.widthBox,
16.w.widthBox,
8.w.widthBox,

// Alternative SizedBox syntax (also acceptable)
SizedBox(height: 20.h),
SizedBox(width: 24.w),
```

**❌ Avoid Direct SizedBox Without ScreenUtil:**
```dart
// Wrong - Not responsive
SizedBox(height: 20),
SizedBox(width: 24),

// Wrong - Hardcoded values
SizedBox(height: 20.0),
SizedBox(width: 24.0),
```

**🎯 Benefits:**
- ✅ **Responsive Design**: Automatically scales with screen size
- ✅ **Cleaner Code**: More concise syntax with extensions
- ✅ **Consistent Spacing**: Uses ScreenUtil for all dimensions
- ✅ **Better Readability**: Clear intent with heightBox/widthBox
- ✅ **Device Compatibility**: Works across all screen sizes

**📏 Common Spacing Values:**
```dart
// Small spacing
4.h.heightBox,   // 4dp vertical
8.h.heightBox,   // 8dp vertical
8.w.widthBox,    // 8dp horizontal

// Medium spacing
12.h.heightBox,  // 12dp vertical
16.h.heightBox,  // 16dp vertical
16.w.widthBox,   // 16dp horizontal

// Large spacing
20.h.heightBox,  // 20dp vertical
24.h.heightBox,  // 24dp vertical
24.w.widthBox,   // 24dp horizontal

// Extra large spacing
32.h.heightBox,  // 32dp vertical
40.h.heightBox,  // 40dp vertical
```

### Environment Management
- **Development**: `flutter run -t lib/main_dev.dart --flavor development`
- **Staging**: Use `main_staging.dart`
- **Production**: Use `main_production.dart`
- **Configuration**: Environment-specific configs in `config/app_config.dart`

### Code Generation
- **Build Runner**: `dart run build_runner watch --delete-conflicting-outputs`
- **Localization**: Use provided script for translation key generation
- **Assets**: Auto-generated with flutter_gen in `gen/` folder

### Dependencies
- **Package Management**: Always use `flutter pub add` instead of manual pubspec.yaml editing
- **Version Control**: Respect existing version constraints
- **Dev Dependencies**: Use appropriate dev_dependencies for code generation tools

### UI/UX Standards
- **Responsive Design**: flutter_screenutil (^5.9.3) for screen adaptation
- **Typography**: Google Fonts (^6.2.1)
- **Extensions**: awesome_extensions (^2.0.21) for utility extensions
- **Localization**: easy_localization (^3.0.7+1)

### Security & Storage
- **Secure Storage**: flutter_secure_storage (^9.2.4) for sensitive data
- **Preferences**: shared_preferences (^2.5.3) for app settings
- **Device Security**: safe_device (^1.2.1) for security checks

### Firebase Integration
- **Core**: firebase_core (^3.13.1)
- **Crashlytics**: firebase_crashlytics (^4.3.6)
- **Configuration**: firebase.json and firebase_options.dart
- **User Identification**: Crashlytics automatically sets user ID and email after successful login for better error tracking

### AppsFlyer Integration
- **SDK**: appsflyer_sdk (^6.15.1)
- **Permissions**: permission_handler (^11.3.1) - unified permission handling for all app permissions
- **Configuration**: AppsFlyer Dev Key and iOS App ID in AppEnvironment
- **iOS ATT Handling**: Automatically requests App Tracking Transparency permission on iOS via PermissionService
- **User Attribution**: Sets customer user ID after successful login for better attribution tracking
- **Privacy Compliant**: Respects user tracking preferences and disables tracking if permission denied

### Permission Handler Integration
- **Package**: permission_handler (^11.3.1) - currently used only for App Tracking Transparency
- **Minimal Implementation**: Only includes ATT permission for AppsFlyer (other permissions added when needed)
- **Cross-Platform**: Handles iOS ATT permission with Android compatibility
- **Extensible**: Easy to add new permission types when specific features require them

#### **AppsFlyer Configuration Setup**
**✅ Environment Variables Configuration (using envied):**

The project uses `envied` package for secure environment variable management. Configuration is loaded from `.env` file:

```dart
/// AppsFlyer Configuration
/// Values loaded from environment variables (.env file)
static String get appsFlyerDevKey {
  return Env.afDevKey;
}

/// iOS App ID for AppsFlyer (only needed for iOS)
/// Value loaded from environment variables (.env file)
static String get appsFlyerAppId {
  return Env.afAppId;
}

/// Base URL Configuration
/// Flavor-specific URLs loaded from environment variables
static String get baseUrl {
  switch (flavor) {
    case AppFlavor.development:
      return Env.baseUrlDevelopment;
    case AppFlavor.staging:
      return Env.baseUrlStaging;
    case AppFlavor.production:
      return Env.baseUrlProduction;
  }
}
```

**✅ Setup .env file:**
```bash
# Copy example file
cp .env.example .env

# Edit .env with your actual values
# API Configuration - Flavor Specific Base URLs
BASE_URL_DEVELOPMENT=your_actual_development_api_url
BASE_URL_STAGING=your_actual_staging_api_url
BASE_URL_PRODUCTION=your_actual_production_api_url

# AppsFlyer Configuration
AF_DEV_KEY=your_actual_appsflyer_dev_key
AF_APP_ID=your_actual_ios_app_store_id

# IDWISE Configuration
IDWISE_SANDBOX_CLIENT_KEY=your_actual_idwise_sandbox_key
IDWISE_LIVE_CLIENT_KEY=your_actual_idwise_live_key
# ... and other IDWISE variables

# Generate environment code
dart run build_runner build --delete-conflicting-outputs
```

#### **AppsFlyer Features**
**✅ Automatic Initialization:**
- Initializes on app startup in MainWidget
- Handles iOS App Tracking Transparency permission request
- Configures SDK based on tracking permission status
- Graceful fallback if initialization fails

**✅ User Attribution:**
- Sets customer user ID after successful login (email, Google, Apple)
- Clears customer user ID on logout
- Tracks user attribution across app sessions

**✅ Privacy Compliance:**
- **iOS**: Requests App Tracking Transparency permission
- **Android**: Tracking enabled by default (no ATT required)
- **Tracking Denied**: Disables advertising identifier and Apple Search Ads collection
- **Tracking Enabled**: Full attribution and analytics functionality

**✅ Event Logging:**
- `logEvent(eventName, eventValues)` method available
- Respects tracking permission (skips events if tracking disabled)
- Debug logging in development mode

#### **AppsFlyer Usage Examples**
**✅ Log custom events:**
```dart
final appsFlyerService = ref.read(appsFlyerServiceProvider);

// Log purchase event
await appsFlyerService.logEvent('af_purchase', {
  'af_revenue': 9.99,
  'af_currency': 'USD',
  'af_content_id': 'product_123',
});

// Log registration event
await appsFlyerService.logEvent('af_complete_registration', {
  'af_registration_method': 'email',
});
```

**✅ Check tracking status:**
```dart
final appsFlyerService = ref.read(appsFlyerServiceProvider);
final isTrackingEnabled = await appsFlyerService.isTrackingEnabled();
if (isTrackingEnabled) {
  // Tracking is enabled, can log events
} else {
  // Tracking disabled, events will be skipped
}
```

**✅ Screen tracking (automatic via GoRouter):**
```dart
// Screen tracking happens automatically via AppRouteObserver
// No manual code needed - just navigate normally:

context.pushNamed(RouteName.login.name); // Automatically logs "login" screen
context.pushNamed(RouteName.home.name);  // Automatically logs "home" screen

// Manual screen tracking (if needed):
final appsFlyerService = ref.read(appsFlyerServiceProvider);
await appsFlyerService.logScreenView('custom_screen_name');
```

#### **iOS Configuration for Paid User Acquisition**
**✅ Privacy Manifest (PrivacyInfo.xcprivacy):**
- NSPrivacyTracking disabled (commented out) for App Store approval
- Only declares crash data that YOUR app directly collects
- SKAdNetwork attribution doesn't require tracking declarations

**✅ Info.plist Configuration:**
- NSUserTrackingUsageDescription with user-friendly message
- SKAdNetwork IDs for iOS 14+ attribution (AppsFlyer, Google Ads, Facebook/Meta only)
- CFBundleLocalizations for supported languages

**✅ permission_handler Implementation:**
- Uses permission_handler package for both Flutter and native ATT handling
- PERMISSION_APP_TRACKING_TRANSPARENCY=1 enabled in Podfile
- ATT permission requested automatically during app splash
- AppsFlyer initialized after ATT permission decision

**✅ App Store Requirements:**
- Privacy Manifest required for iOS 17+
- App Tracking Transparency permission handling
- Clear user consent flow for tracking
- SKAdNetwork attribution for paid campaigns

#### **Permission Service Usage (Currently ATT Only)**
**✅ App Tracking Transparency for AppsFlyer:**
```dart
final permissionService = ref.read(permissionServiceProvider);

// Check if ATT permission is already granted
final isATTGranted = await permissionService.isAppTrackingTransparencyGranted();

// Request ATT permission (iOS only, Android returns true)
final attGranted = await permissionService.requestAppTrackingTransparency();

// Open app settings if user needs to manually enable permissions
if (!attGranted) {
  await permissionService.openAppSettings();
}
```

**✅ Current permission methods (minimal for AppsFlyer):**
- `requestAppTrackingTransparency()` - iOS App Tracking Transparency (Android always returns true)
- `isAppTrackingTransparencyGranted()` - Check ATT permission status
- `openAppSettings()` - Open device settings for manual permission management

**✅ Adding new permissions when needed:**
When new features require additional permissions (camera, photos, location, etc.), add them to:
1. `PermissionService` interface - add new method signature
2. `PermissionServiceImpl` - implement the method using `permission_handler.Permission.xxx`
3. Update this documentation with the new permission

**Example for future camera permission:**
```dart
// In PermissionService interface:
Future<bool> requestCamera();

// In PermissionServiceImpl:
@override
Future<bool> requestCamera() async {
  final status = await permission_handler.Permission.camera.request();
  return status.isGranted;
}
```

### Logout Functionality (MANDATORY)
**Comprehensive logout implementation with proper cleanup:**

#### **UserProvider.logout() Method**
The `UserProvider` includes a comprehensive `logout()` method that handles all user-related cleanup:

```dart
/// Comprehensive logout method - clears all user-related data
/// This method should be called when implementing logout functionality
/// It ensures all user data is properly cleared from the app
Future<void> logout() async {
  // Clear user state
  state = const UserState();

  // Clear user identification from Crashlytics
  await _crashlyticsService.clearUserIdentification();

  // TODO: Add other cleanup methods here when implementing logout
  // Examples of what should be cleared on logout:
  // - Clear cached user preferences
  // - Clear any user-specific local storage
  // - Clear notification tokens
  // - Clear any temporary user files
  // - Reset any user-specific settings

  // Note: Token clearing and auth state reset should be handled
  // by the AuthNotifier.signOut() method, not here
}
```

#### **Logout Implementation Guidelines**
**✅ SIMPLIFIED: Just call AuthNotifier.signOut() - it handles everything!**

The `AuthNotifier.signOut()` method now uses a dedicated `LogoutService` for comprehensive cleanup, maintaining clean architecture:

**✅ Complete logout implementation example:**
```dart
// In your logout button/method
Future<void> performLogout() async {
  final authNotifier = ref.read(authNotifierProvider.notifier);

  // This single call handles EVERYTHING:
  // - Signs out from social platforms (Google, Apple)
  // - Clears JWT tokens from secure storage
  // - Clears user state
  // - Clears Crashlytics user identification
  // - Clears user-specific SharedPreferences
  // - Clears ALL secure storage data
  // - Resets authentication state
  await authNotifier.signOut();

  // Navigate to login/welcome screen
  context.goNamed('/login');
}
```

**✅ What happens during logout:**
1. **AuthService.signOut()** - Signs out from Google/Apple platforms
2. **TokenService.clearTokens()** - Clears JWT and refresh tokens
3. **LogoutService.performComprehensiveLogout()** - Dedicated service handles:
   - Clears user state via UserProvider
   - Clears Crashlytics user identification
   - Clears user-specific SharedPreferences
   - Clears ALL secure storage data
4. **AuthState reset** - Resets authentication state

**✅ Architecture Benefits:**
- **Memory Efficient**: UserProvider stays lightweight (only essential dependencies)
- **Separation of Concerns**: Logout logic isolated in dedicated service
- **Easy to Extend**: Add new cleanup services to LogoutService only
- **Clean Dependencies**: No circular dependencies or bloated providers

#### **Services That Clear Data on Logout**
**Current services that automatically clear data:**
- ✅ **AuthService**: Signs out from Google/Apple platforms
- ✅ **TokenService**: Clears JWT and refresh tokens from secure storage
- ✅ **LogoutService**: Dedicated service that orchestrates comprehensive cleanup
- ✅ **CrashlyticsService**: Clears user identification (ID and email)
- ✅ **AppsFlyerService**: Clears customer user ID for attribution tracking
- ✅ **SharedPrefsManager**: Clears user-specific preferences (preserves language settings)
- ✅ **SecureStorage**: Clears ALL secure storage data (comprehensive cleanup)
- ✅ **UserProvider**: Lightweight - only clears user state (no heavy dependencies)

**Future services to integrate (TODO):**
- 🔄 **Notification Service**: Clear user-specific notification tokens
- 🔄 **File Storage**: Clear temporary user files
- 🔄 **Local Database**: Clear user-specific cached data
- 🔄 **Analytics**: Clear user identification from analytics services
- 🔄 **API Cache**: Clear cached API responses

#### **Adding New Services to Logout**
**When adding new services that store user data, follow this pattern:**

1. **Add cleanup method to the service:**
```dart
class YourService {
  Future<void> clearUserData() async {
    // Clear user-specific data
  }
}
```

2. **Add the cleanup call to UserProvider.logout():**
```dart
Future<void> logout() async {
  // ... existing cleanup code ...

  // Add your service cleanup
  await _yourService.clearUserData();
}
```

3. **Update this documentation** with the new service in the "Services That Clear Data" list

#### **Benefits of Centralized Logout**
- ✅ **Complete Cleanup**: Ensures all user data is properly cleared
- ✅ **Consistent Implementation**: Single method to call for logout
- ✅ **Easy Maintenance**: Add new cleanup logic in one place
- ✅ **Security**: Prevents data leakage between user sessions
- ✅ **User Experience**: Clean state for next user login

## Coding Standards

### Naming Conventions
- **Files**: snake_case (login_screen.dart, auth_provider.dart)
- **Classes**: PascalCase (LoginScreen, AuthProvider)
- **Variables/Functions**: camelCase (userName, getUserData)
- **Constants**: SCREAMING_SNAKE_CASE (API_BASE_URL)

### Module Organization
- **Screens**: All UI screens for the module (login_screen.dart, signup_screen.dart)
- **Providers**: Riverpod providers for state management (auth_provider.dart)
- **Services**: API calls and business logic (auth_service.dart)
- **Models**: Data models specific to the module (auth_models.dart)

### Code Organization
- **Imports**: Group by dart, flutter, packages, relative
- **Module Exports**: Create index.dart files for easy imports
- **Documentation**: Document public APIs and complex business logic

### State Management Patterns
- **Module Providers**: Keep providers close to their related screens
- **Global Providers**: Place in `shared/providers/` for app-wide state
- **Immutability**: Use Freezed only when needed (see Freezed Guidelines)
- **Error Handling**: Implement consistent error handling patterns

### Riverpod Provider Guidelines (MANDATORY)

#### **AutoDispose Usage Rules:**
**✅ ALWAYS use `autoDispose` for:**
- **Module-specific providers**: Auth, feature-specific state
- **Temporary providers**: Form state, UI state, loading states
- **API call providers**: FutureProvider, StreamProvider for API calls
- **Computed providers**: Providers that derive from other providers
- **Family providers**: Providers with parameters that create multiple instances

**❌ DON'T use `autoDispose` for:**
- **Global app state**: Theme, language, user session
- **Persistent services**: Network service, storage services
- **Configuration providers**: App environment, constants
- **Shared preferences**: Settings that persist across app lifecycle

#### **Provider Types & AutoDispose Examples:**
```dart
// ✅ Module-specific providers (USE autoDispose)
final authNotifierProvider = StateNotifierProvider.autoDispose<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

final authServiceProvider = Provider.autoDispose<AuthService>((ref) {
  return AuthService();
});

// ✅ API call providers (USE autoDispose)
final userProfileProvider = FutureProvider.autoDispose.family<User, String>((ref, userId) {
  final apiService = ref.watch(apiServiceProvider);
  return apiService.getUserProfile(userId);
});

// ✅ Computed providers (USE autoDispose)
final currentLanguageProvider = Provider.autoDispose<String>((ref) {
  return ref.watch(languageProvider).languageCode;
});

// ❌ Global providers (DON'T use autoDispose)
final languageProvider = StateNotifierProvider<LanguageNotifier, Locale>((ref) {
  final prefsManager = ref.watch(sharedPrefsManagerProvider);
  return LanguageNotifier(prefsManager);
});

final networkServiceProvider = Provider<Dio>((ref) {
  // Global network service - don't dispose
  return createDioInstance();
});
```

#### **Benefits of AutoDispose:**
- **Memory Management**: Automatically disposes providers when no longer needed
- **Performance**: Reduces memory usage and prevents memory leaks
- **Clean State**: Ensures fresh state when navigating back to screens
- **Resource Cleanup**: Automatically cancels streams, timers, and subscriptions

#### **AutoDispose Best Practices:**
1. **Default to AutoDispose**: Use autoDispose unless you specifically need global persistence
2. **Family Providers**: Always use autoDispose with family providers to prevent memory leaks
3. **API Calls**: Use autoDispose for all API call providers (FutureProvider, StreamProvider)
4. **Module Isolation**: Module providers should be autoDispose to ensure clean state
5. **Testing**: AutoDispose providers are easier to test as they start with fresh state

#### **Provider Naming & Debugging (MANDATORY):**
**✅ ALWAYS add descriptive names to all providers:**
```dart
// ✅ Correct - All providers have descriptive names
final authNotifierProvider = StateNotifierProvider.autoDispose<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
}, name: 'authNotifierProvider');

final networkServiceProvider = Provider<Dio>((ref) {
  return createDioInstance();
}, name: 'networkServiceProvider');

final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  return UserNotifier();
}, name: 'userProvider');

// ❌ Wrong - Missing provider names
final authProvider = StateNotifierProvider.autoDispose<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
}); // Missing name parameter
```

**🔍 AppProviderObserver for Debugging:**
The project includes `AppProviderObserver` in `shared/providers/app_provider_observer.dart` that logs provider lifecycle events:

```dart
class AppProviderObserver extends ProviderObserver {
  @override
  void didUpdateProvider(ProviderBase provider, Object? previousValue, Object? newValue, ProviderContainer container) {
    debugPrint('🔄 Provider updated: \${provider.name} → \$newValue');
  }

  @override
  void didDisposeProvider(ProviderBase provider, ProviderContainer container) {
    debugPrint('❌ Provider disposed: \${provider.name}');
  }

  @override
  void didAddProvider(ProviderBase provider, Object? value, ProviderContainer container) {
    debugPrint('✅ Provider added: \${provider.name} → \$value');
  }
}
```

**📊 Benefits of Provider Names & Observer:**
- ✅ **Debug Visibility**: Clear logs showing which providers are created, updated, and disposed
- ✅ **Memory Leak Detection**: Easy to spot providers that aren't being disposed properly
- ✅ **State Flow Tracking**: Monitor how data flows through your provider dependency graph
- ✅ **Performance Monitoring**: Identify providers that update too frequently
- ✅ **Development Efficiency**: Faster debugging and troubleshooting

### API Integration
- **Module Services**: Each module has its own service file
- **Shared API Config**: Common API configuration in `config/api_config.dart`
- **Retrofit**: Use for type-safe API clients
- **Error Handling**: Consistent error handling across all API calls

## Performance Considerations
- **Screen Adaptation**: Use ScreenUtil for responsive design
- **Image Optimization**: Optimize assets in `assets/images/`
- **State Management**: Avoid unnecessary rebuilds with proper provider usage

## Security Guidelines
- **Sensitive Data**: Use flutter_secure_storage for tokens, passwords
- **Device Security**: Implement safe_device checks for rooted/jailbroken devices
- **API Security**: Implement proper authentication and authorization

## Localization
- **Files**: Store in `shared/localization/` or keep current structure
- **Generation**: Use provided script for key generation
- **Support**: Multi-language support with easy_localization

## Build & Deployment
- **Flavors**: Use appropriate flavor for target environment
- **Code Generation**: Run build_runner before building
- **Assets**: Ensure all assets are properly referenced in pubspec.yaml

## Module Development Workflow

### Creating a New Module
1. Create folder in `lib/modules/module_name/`
2. Add screen files (e.g., `module_screen.dart`)
3. Add provider file (e.g., `module_provider.dart`)
4. Add service file if API calls needed (e.g., `module_service.dart`)
5. Add models file if needed (e.g., `module_models.dart`)
6. Update router in `config/app_router.dart`

### Adding Shared Components
1. **Widgets**: Add to `shared/widgets/`
2. **Services**: Add to `shared/services/`
3. **Models**: Add to `shared/models/`
4. **Utilities**: Add to `shared/utils/`
5. **Constants**: Add to `shared/constants/`

## AI Assistant Behavior Guidelines

### Restructuring Workflow (MANDATORY)
1. **Read PROJECT.md First**: Always consult this document before making any structural changes
2. **Analyze Current Structure**: Use codebase-retrieval to understand existing patterns
3. **Follow Established Patterns**: Don't create new patterns, follow existing ones
4. **Suggest Based on Project Structure**: Recommendations should align with documented architecture
5. **Minimal Changes Only**: Only move/restructure what's necessary, don't add extra functionality

### File Organization Rules (STRICT)
1. **Enums**: ALL enums go in `lib/shared/enums/` - one enum per file
2. **Services**: Network/API services go in `lib/shared/services/` not config
3. **Models**: Data classes and response models go in `lib/shared/models/`
4. **Constants**: API endpoints and app constants go in `lib/shared/constants/`
5. **Config**: Only app-level configuration (theme, router, environment) in `lib/config/`
6. **Freezed**: Use ONLY when actually needed - not for simple enums or constants
7. **SafeArea**: ALWAYS use SafeArea for screen content to ensure device compatibility

### Code Implementation Workflow (MANDATORY)
1. **Show First, Write Second**: When user provides coding instructions:
   - **ALWAYS** first show the proposed code changes using `<augment_code_snippet>` tags
   - Explain the approach and implementation details
   - Wait for user approval before making any actual file changes
   - **NEVER** proceed with writing/editing files without explicit user approval
   - If guidelines are missing, add them to PROJECT.md first

2. **Code Review Process**:
   - Present code snippets with proper syntax highlighting
   - Explain the reasoning behind implementation choices
   - Highlight any potential impacts or dependencies
   - Ask for confirmation before proceeding with file modifications

3. **Planning and Communication**:
   - Always create a detailed plan before implementation
   - Gather necessary information using codebase-retrieval tool
   - Communicate changes clearly and wait for approval
   - Be conservative and respect existing codebase patterns
   - Follow Flat + Scalable architecture principles

## Common Commands
```bash
# Development run
flutter run -t lib/main_dev.dart --flavor development

# Code generation
dart run build_runner watch --delete-conflicting-outputs

# Translation generation
dart run easy_localization:generate -f json -S lib/shared/localization -O lib/shared/localization/generated -o codegen_loader.g.dart && dart run easy_localization:generate -f keys -S lib/shared/localization -O lib/shared/localization/generated -o locale_keys.g.dart

# Clean and get dependencies
flutter clean && flutter pub get

# Add new dependency
flutter pub add package_name

# Add new dev dependency
flutter pub add --dev package_name
```

## Task Management

### Configuration
- **Task Manager Path**: `/Users/<USER>/Development/shrimp-data`
- **Tasks File**: `tasks.json`
- **Memory Directory**: `memory/`

## AppsFlyer Events Documentation
- All AppsFlyer events and their parameters are documented in `APPSFLYER_EVENTS.md` in the project root.
- **Rule:** When you add a new AppsFlyer event, always update `APPSFLYER_EVENTS.md` with the event name, trigger, and parameters.
