name: maisour
description: "Maisour Mobile App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.5.2+152

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State Management
  # https://pub.dev/packages/flutter_riverpod
  flutter_riverpod: ^2.6.1

  # Routing
  # https://pub.dev/packages/go_router
  go_router: ^16.0.0

  # Api Call Helper
  # https://pub.dev/packages/retrofit
  retrofit: ^4.4.2

  # Api Call
  # https://pub.dev/packages/dio
  dio: ^5.8.0+1

  # Freezed
  # https://pub.dev/packages/freezed_annotation
  freezed_annotation: ^3.0.0

  # Serialization
  # https://pub.dev/packages/json_annotation
  json_annotation: ^4.9.0

  # Date Formatting
  # https://pub.dev/packages/intl
  intl: ^0.20.2

  # Secure Storage
  # https://pub.dev/packages/flutter_secure_storage
  flutter_secure_storage: ^9.2.4

  # Shared Preferences
  # https://pub.dev/packages/shared_preferences
  shared_preferences: ^2.5.3

  # Check Device Security
  # https://pub.dev/packages/safe_device
  safe_device: ^1.3.5

  # Internet connectivity detection
  # https://pub.dev/packages/connectivity_plus
  connectivity_plus: ^6.1.5

  # Localization
  # https://pub.dev/packages/easy_localization
  easy_localization: ^3.0.8

  # Adapting screen and font size.
  # https://pub.dev/packages/flutter_screenutil
  flutter_screenutil: ^5.9.3

  # For Extension
  # https://pub.dev/packages/awesome_extensions
  awesome_extensions: ^2.0.25

  # For google fonts
  # https://pub.dev/packages/google_fonts
  google_fonts: ^6.2.1

  # For svg image and icon
  # https://pub.dev/packages/flutter_svg
  flutter_svg: ^2.1.0

  # For Show Toast
  # https://pub.dev/packages/toastification
  # fluttertoast: ^8.2.12
  toastification: ^3.0.3

  # https://pub.dev/packages/firebase_core
  firebase_core: ^3.13.1
  firebase_crashlytics: ^4.3.6
  firebase_remote_config: ^5.4.5

  # Firebase Cloud Messaging for push notifications
  # https://pub.dev/packages/firebase_messaging
  firebase_messaging: ^15.2.7

  # Local notifications for iOS foreground handling
  # https://pub.dev/packages/flutter_local_notifications
  flutter_local_notifications: ^19.2.1

  # allows Sign in with Google
  # https://pub.dev/packages/google_sign_in
  google_sign_in: ^6.3.0

  # allows Sign in with Apple
  # https://pub.dev/packages/sign_in_with_apple
  sign_in_with_apple: ^7.0.1

  # URL Launcher for opening external links
  # https://pub.dev/packages/url_launcher
  url_launcher: ^6.3.1

  # For checxk current app version
  # https://pub.dev/packages/package_info_plus
  package_info_plus: ^8.3.0

  # AppsFlyer SDK for attribution and analytics
  # https://pub.dev/packages/appsflyer_sdk
  appsflyer_sdk: ^6.17.1

  # Permission handler for all permissions (including App Tracking Transparency)
  # https://pub.dev/packages/permission_handler
  permission_handler: ^12.0.0+1

  # Environment variables management
  # https://pub.dev/packages/envied
  envied: ^1.2.0

  # Dropdown search
  # https://pub.dev/packages/dropdown_search
  dropdown_search: ^6.0.2

  # IDWise SDK for passport and address verification
  # https://pub.dev/packages/idwise_flutter_sdk
  idwise_flutter_sdk: ^5.7.0

  # For html widget
  # https://pub.dev/packages/flutter_html
  flutter_html: ^3.0.0

  # For country code picker
  # https://pub.dev/packages/phone_form_field
  phone_form_field: ^10.0.8

  # For OTP input fields
  # https://pub.dev/packages/pinput
  pinput: ^5.0.1

  # For lottie animation
  # https://pub.dev/packages/lottie
  lottie: ^3.3.1

  # For caching network images
  # https://pub.dev/packages/cached_network_image
  cached_network_image: ^3.4.1

  # For map
  # https://pub.dev/packages/google_maps_flutter
  google_maps_flutter: ^2.12.3

  # For webview
  # https://pub.dev/packages/webview_flutter
  webview_flutter: ^4.13.0

  # For file picker
  # https://pub.dev/packages/file_picker
  file_picker: ^10.3.1

  # For sharing
  # https://pub.dev/packages/share_plus
  share_plus: ^11.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

  # Environment variables code generation
  # https://pub.dev/packages/envied
  envied_generator: ^1.1.1

  # https://pub.dev/packages/build_runner
  build_runner: ^2.5.4

  # https://pub.dev/packages/retrofit_generator
  retrofit_generator: ^9.2.0

  # https://pub.dev/packages/json_serializable
  json_serializable: ^6.9.5

  # https://pub.dev/packages/freezed
  freezed: ^3.1.0

  # https://pub.dev/packages/riverpod_generator
  riverpod_generator: ^2.6.5

  # https://pub.dev/packages/flutter_gen_runner
  flutter_gen_runner: ^5.10.0

  # https://pub.dev/packages/flutter_launcher_icons
  flutter_launcher_icons: "^0.14.4"

flutter_launcher_icons:
  android: "ic_launcher"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/lottie/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_gen:
  output: lib/gen/ # default, you can customize
  line_length: 80
  integrations:
    image: true
    flutter_svg: true
    lottie: true
    # rive: true
